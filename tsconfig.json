{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": false,
    "module": "es6",
    "skipLibCheck": true /* <PERSON><PERSON> type checking all .d.ts files. */,
    "baseUrl": "." /* Specify the base directory to resolve non-relative module names. */,
    "paths": {
      "*": ["./*"],
      "@/*": ["src/*"],
      "@env": ["src/configs-files/@env.ts"],
      // "@components/*": ["src/components/*"],
      // "@assets/*": ["src/assets/*"],
      // "@screens/*": ["src/screens/*"],
      // "@theme/*": ["src/theme/*"],
      // "@store/*": ["src/store/*"],
      // "@utils/*": ["src/utils/*"],
      // "@navigation/*": ["src/navigation/*"],
      // "@hooks/*": ["src/hooks/*"],
      // "@constant/*": ["src/constant/*"]
    },
  },
}
