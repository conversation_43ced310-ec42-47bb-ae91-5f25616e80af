module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // [
      //   'module:react-native-dotenv',
      //   {
      //     moduleName: '@env',
      //     path: `env.${process.env.APP_ENV}`,
      //   },
      // ],
      [
        'babel-plugin-inline-import',
        {
          extensions: ['.svg'],
        },
      ],
      [
        require.resolve('babel-plugin-module-resolver'),
        {
          extensions: ['.ts', '.tsx', '.js', '.ios.js', '.android.js'],
          root: ['./'],
          alias: {
            '@/*': 'src/*',
            "@env": ["src/configs-files/@env.ts"],
            // '@components': './src/components',
            // '@assets': './src/assets',
            // '@screens': './src/screens',
            // '@theme': './src/theme',
            // '@store': './src/store',
            // '@utils': './src/utils',
            // '@navigation': './src/navigation',
            // '@hooks': './src/hooks',
            // '@constant': './src/constant',
          },
        },
      ],
      'nativewind/babel',
      'react-native-reanimated/plugin',
    ],
  };
};
