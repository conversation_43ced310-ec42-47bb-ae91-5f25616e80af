import * as Linking from 'expo-linking';

// Get the Expo development server URL in development
const prefix = [Linking.createURL('/'), 'https://app.catlog.shop', 'https://app.stx.catlog.shop', 'catlog://'];

export const linking = {
  prefixes: prefix,
  enabled: true,
  config: {
    screens: {
      ManageInvite: {
        // path: 'invite/:inviteId',
        path: 'join-store',
        parse: {
          invite: (invite: string) => invite,
        },
      },
      AuthNavigation: {
        path: 'auth',
        screens: {
          Login: '',
          Signup: 'signup',
          EnterEmail: 'enter-email',
          ResetPassword: 'reset-password',
        },
      },
      AppNavigation: {
        path: '',
        screens: {
          SetupBusinessStack: {
            path: 'setup',
            screens: {
              CreateStore: 'create-store',
              SetupAddProducts: 'add-products',
              SelectAddingMethod: 'select-method',
              SelectProductsImage: 'select-images',
              FillProductsInfo: {
                path: 'fill-info/:action',
                parse: {
                  action: (action: string) => action,
                },
              },
              SelectPlan: 'select-plan',
            },
          },
          MainNavigation: {
            path: '',
            screens: {
              HomeTab: {
                path: '',
                screens: {
                  Home: '',
                },
              },
              CatlogCredits: 'catlog-credits',
              ProductDetails: {
                path: 'product/:id',
                parse: {
                  id: (id: string) => id,
                },
              },
              RecordOrder: 'order/record',
              OrderInfo: {
                path: 'order/:orderId',
                parse: {
                  orderId: (orderId: string) => orderId,
                },
              },
              GetStarted: 'get-started',
              SelectCategorization: 'categories/select',
              Verify: {
                path: 'verify/:type?',
                parse: {
                  type: (type: string | null) => type,
                },
              },
              Storefront: 'storefront',
              ProductImages: {
                path: 'product/images',
                parse: {
                  images: (images: string) => images.split(','),
                  thumbnail: Number,
                  activeIndex: Number,
                },
              },
              Search: 'search',
              CreateProducts: 'products/create',
              OrderAnalytics: 'analytics/orders',
              ProductAnalytics: 'analytics/products',
              CreateDiscount: 'discount/create',
              CreateCoupon: 'coupon/create',
              LatestOrder: 'order/latest',
            },
          },
          StoreSettingsStack: {
            path: 'settings',
            screens: {
              StoreSettings: 'store',
              StoreInformation: 'store/info',
              StoreConfigurations: 'store/config',
              CheckoutOptions: 'checkout/options',
              DeliveryAreas: 'delivery/areas',
              PaymentSettings: 'payment/settings',
            },
          },
          CustomersStack: {
            path: 'customers',
            screens: {
              Customer: 'list',
              CustomersAnalytics: 'analytics',
            },
          },
          InvoicesStack: {
            path: 'invoices',
            screens: {
              CreateInvoice: 'create',
            },
          },
          DeliveriesStack: {
            path: 'deliveries',
            screens: {
              DeliveriesAnalytics: 'analytics',
              DeliveryDetails: {
                path: 'details/:id?',
                parse: {
                  id: (id: string | undefined) => id,
                },
              },
              InitiateDelivery: {
                path: 'new',
                parse: {
                  deliveryData: JSON.parse,
                },
              },
            },
          },
        },
      },
    },
  },
};
