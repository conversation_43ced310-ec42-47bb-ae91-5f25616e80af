import { createNavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from 'src/@types/navigation';

// export const navRef = createNavigationContainerRef(); //todo: recondisder this

// export function navigationRef() {
//   if (navRef.isReady()) {
//     return navRef;
//   }
// }

// RootNavigation.js

export const navigationRef = createNavigationContainerRef();

export function navigate(name, params?) {
  if (navigationRef.isReady()) {
    navigationRef.navigate<keyof ReactNavigation.RootParamList>(name, params);
  }
}

export function resetRoutes(name) {
  if (navigationRef.isReady()) {
    navigationRef.reset({
      index: 0,
      routes: [{ name: name }],
    });
  }
}
