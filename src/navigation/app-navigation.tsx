/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import CustomBottomTab, { AppTabs } from '@/components/ui/layouts/custom-bottom-tab';
import {
  Home,
  Customers,
  Payments,
  ProductDetails,
  QuickActions,
  CatlogCredits,
  GetStarted,
  ProductPagesLists,
  // SelectAddingMethod,
  // SelectProductsImage,
  // FillProductsInfo,
  ProductImages,
  CreateDiscount,
  Feedback,
  CreateCoupon,
  Orders,
  OrderInfo,
  RecordOrder,
  StoreSettings,
  UserProfile,
  ManageSubscription,
  StoreManagers,
  InvoicesDashboard,
  Deliveries,
  GetHelp,
  CustomersAnalytics,
  StoreInformation,
  StoreConfigurations,
  CheckoutOptions,
  DeliveryAreas,
  Search,
  OrderAnalytics,
  ProductAnalytics,
  CreateStore,
  SetupAddProducts,
  LoadingScreen,
  CreateProducts,
  SelectCategorization,
  Verify,
  CreateInvoice,
  DeliveriesAnalytics,
  PaymentSettings,
  DeliveryDetails,
  InitiateDelivery,
  KYC,
  PaymentAnalytics,
  PaymentLinksList,
  CreatePaymentLink,
  SearchProducts,
  SearchOrders,
  SearchDiscounts,
  SearchCoupons,
  NotificationList,
  SearchInvoices,
  SearchTransactions,
  EditProduct,
  Carts,
  QuantityUpdate,
  PriceUpdate,
  SearchCustomers,
  PaymentInfo,
  AccountDeactivated,
  SortProducts,
  AllAffiliates,
  SearchAffiliates,
  CreatePricingTier,
  BulkUpdateOrders,
  WelcomeBack,
  WelcomeContinueSetup,
  WelcomePickPlan,
  WithdrawCash,
} from '@/screens';
// import { QuickActions } from '@/screens/quick-actions';
// import {useSafeAreaInsets} from 'react-native-safe-area-context';
import { createDrawerNavigator } from '@react-navigation/drawer';
import CustomDrawer from '@/components/ui/layouts/custom-drawer';
import { getActiveRouteName, wp } from '@/assets/utils/js';
// import { AppNavigationProps } from './navigation-props';
import useAuthContext from '@/contexts/auth/auth-context';
import EditInvoices from 'src/screens/invoices/edit-invoices';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { RootStackParamList } from 'src/@types/navigation';
import SetupBusiness from 'src/screens/setup/setup-business';
import SetupProgress from 'src/screens/setup/setup-progress';
import PickPlan from 'src/screens/setup/pick-plan';
import WatchTutorial from 'src/screens/setup/watch-tutorial';
import SetupComplete from 'src/screens/setup/setup-complete';
import { useNavigation } from '@react-navigation/core';
import ChangePlan from 'src/screens/manage-subscription/change-plan';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

const AppNavigation = () => {
  //WE CAN ONLY HAVE ONE USEAUTHCONTEXT BEFORE THE MAIN APP
  const { userHasSetup, appIsSetup, redirectTo, stores, userAccountDeactivated, isAuthenticated, decideNextRoute } =
    useAuthContext();
  const navigation = useNavigation();
  const { isFeatureEnabled } = useFeatureFlags();

  if (!appIsSetup) {
    return <LoadingScreen />;
  }

  if (!userHasSetup) {
    return <SetupBusinessDrawer />;
  }

  const initialRoute = userHasSetup ? 'MainNavigation' : redirectTo?.path;

  return (
    <Drawer.Navigator
      initialRouteName={initialRoute}
      screenOptions={{
        drawerPosition: 'right',
        headerShown: false,
        drawerType: 'front',
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerStyle: { width: wp(265) },
      }}
      drawerContent={props => <CustomDrawer {...props} showRoutes={!userAccountDeactivated} />}>
      <>
        {userAccountDeactivated ? (
          <Drawer.Screen name={'AccountDeactivatedStack'} component={AccountDeactivatedStack} />
        ) : (
          <>
            <Drawer.Screen name={'MainNavigation'} component={MainAppStack} />
            <Drawer.Screen name={'StoreSettingsStack'} component={StoreSettingsStack} />
            <Drawer.Screen name={'UserProfile'} component={UserProfile} />
            {isFeatureEnabled('subscriptions') && (
              <Drawer.Screen name={'ManageSubscription'} component={ManageSubscription} />
            )}
            <Drawer.Screen name={'StoreManagers'} component={StoreManagers} />
            <Drawer.Screen name={'Customers'} component={CustomersStack} />
            <Drawer.Screen name={'InvoicesStack'} component={InvoicesStack} />
            <Drawer.Screen name={'AffiliateStack'} component={AffiliateStack} />
            {isFeatureEnabled('deliveries') && <Drawer.Screen name={'DeliveriesStack'} component={DeliveriesStack} />}
            <Drawer.Screen name={'GetHelp'} component={GetHelp} />
          </>
        )}
      </>
    </Drawer.Navigator>
  );
};

//NOTE: Not using this because the setup business stack can handle welcome backs sufficiently
const WelcomeBackStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'WelcomeBack'} component={WelcomeBack} />
      <Stack.Screen name={'WelcomeContinueSetup'} component={WelcomeContinueSetup} />
      <Stack.Screen name={'WelcomePickPlan'} component={WelcomePickPlan} />
      <Stack.Screen name={'SetupComplete'} component={SetupComplete} />
    </Stack.Navigator>
  );
};

const AccountDeactivatedStack = () => {
  const { isAuthenticated } = useAuthContext();
  const { initialize } = useWalletContext();

  useEffect(() => {
    if (isAuthenticated) {
      initialize();
    }
  }, [isAuthenticated]);
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'AccountDeactivated'} component={AccountDeactivated} />
      <Stack.Screen name={'DeactivatedPayments'} component={Payments} />
      <Stack.Screen name={'ChangePlan'} component={ChangePlan} />
      <Stack.Screen name={'PaymentInfo'} component={PaymentInfo} />
    </Stack.Navigator>
  );
};

const SetupBusinessDrawer = () => {
  return (
    <Drawer.Navigator
      initialRouteName={'SetupBusinessStack'}
      screenOptions={{
        drawerPosition: 'right',
        headerShown: false,
        drawerType: 'front',
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerStyle: { width: wp(265) },
      }}
      drawerContent={props => <CustomDrawer {...props} showRoutes={false} />}>
      <Drawer.Screen name={'SetupBusinessStack'} component={SetupBusinessStack} />
    </Drawer.Navigator>
  );
};

const SetupBusinessStack = () => {
  const { redirectTo } = useAuthContext();

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName={redirectTo?.path}>
      <Stack.Screen name={'SetupProgress'} component={SetupProgress} />
      <Stack.Screen name={'CreateStore'} component={CreateStore} />
      <Stack.Screen name={'SetupBusiness'} component={SetupBusiness} />
      <Stack.Screen name={'WatchTutorial'} component={WatchTutorial} />
      <Stack.Screen name={'SetupAddProducts'} component={SetupAddProducts} />
      <Stack.Screen name={'PickPlan'} component={PickPlan} />
      <Stack.Screen name={'ProductImages'} component={ProductImages} options={{ presentation: 'transparentModal' }} />
      <Stack.Screen name={'Feedback'} component={Feedback} />
      <Stack.Screen name={'SetupComplete'} component={SetupComplete} />

      {/* welcome back */}

      <Stack.Screen name={'WelcomeBack'} component={WelcomeBack} />
      <Stack.Screen name={'WelcomeContinueSetup'} component={WelcomeContinueSetup} />
      {/* <Stack.Screen name={'WelcomePickPlan'} component={WelcomePickPlan} /> */}
    </Stack.Navigator>
  );
};

const MainAppStack = () => {
  const { isAuthenticated } = useAuthContext();
  const { initialize } = useWalletContext();

  useEffect(() => {
    if (isAuthenticated) {
      initialize();
    }
  }, [isAuthenticated]);

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }} detachInactiveScreens>
      <Stack.Screen name={'HomeTab'} component={HomeTab} />
      <Stack.Screen name={'CatlogCredits'} component={CatlogCredits} />
      <Stack.Screen name={'ProductDetails'} component={ProductDetails} />
      <Stack.Screen name={'EditProduct'} component={EditProduct} />
      <Stack.Screen name={'RecordOrder'} component={RecordOrder} />
      <Stack.Screen name={'OrderInfo'} component={OrderInfo} />
      <Stack.Screen name={'GetStarted'} component={GetStarted} />
      <Stack.Screen name={'SelectCategorization'} component={SelectCategorization} />
      <Stack.Screen name={'Verify'} component={Verify} />
      <Stack.Screen name={'ProductPagesLists'} component={ProductPagesLists} />
      <Stack.Screen name={'PaymentInfo'} component={PaymentInfo} />
      {/* <Stack.Screen name={'Search'} component={Search} /> */}
      <Stack.Screen name={'CreateProducts'} component={CreateProducts} />
      <Stack.Screen name={'OrderAnalytics'} component={OrderAnalytics} />
      <Stack.Screen name={'ProductAnalytics'} component={ProductAnalytics} />
      <Stack.Screen name={'CreateDiscount'} component={CreateDiscount} />
      <Stack.Screen name={'CreateCoupon'} component={CreateCoupon} />
      <Stack.Screen name={'SearchProducts'} component={SearchProducts} />
      <Stack.Screen name={'SearchOrders'} component={SearchOrders} />
      <Stack.Screen name={'SearchInvoices'} component={SearchInvoices} />
      <Stack.Screen name={'SearchTransactions'} component={SearchTransactions} />
      <Stack.Screen name={'SearchDiscounts'} component={SearchDiscounts} />
      <Stack.Screen name={'SearchCoupons'} component={SearchCoupons} />
      <Stack.Screen name={'Feedback'} component={Feedback} />
      <Stack.Screen name={'ProductImages'} component={ProductImages} options={{ presentation: 'transparentModal' }} />
      <Stack.Screen name={'KYC'} component={KYC} />
      <Stack.Screen name={'PaymentAnalytics'} component={PaymentAnalytics} />
      <Stack.Screen name={'CreateStore'} component={CreateStore} />
      <Stack.Screen name={'PaymentLinksList'} component={PaymentLinksList} />
      <Stack.Screen name={'CreatePaymentLink'} component={CreatePaymentLink} />
      <Stack.Screen name={'NotificationList'} component={NotificationList} />
      <Stack.Screen name={'RequestDelivery'} component={InitiateDelivery} />
      <Stack.Screen name={'TrackDelivery'} component={DeliveryDetails} />
      <Stack.Screen name={'Carts'} component={Carts} />
      <Stack.Screen name={'QuantityUpdate'} component={QuantityUpdate} />
      <Stack.Screen name={'PriceUpdate'} component={PriceUpdate} />
      <Stack.Screen name={'BulkUpdateOrders'} component={BulkUpdateOrders} />
      <Stack.Screen name={'ChangePlan'} component={ChangePlan} />
      <Stack.Screen name={'SortProducts'} component={SortProducts} />
      <Stack.Screen name={'CreatePricingTier'} component={CreatePricingTier} />
      <Stack.Screen name={'WithdrawCash'} component={WithdrawCash} />
    </Stack.Navigator>
  );
};

const HomeTab = () => {
  // const inserts = useSafeAreaInsets();
  return (
    <Tab.Navigator tabBar={props => <CustomBottomTab {...props} />} screenOptions={{ headerShown: false }}>
      {tabs.map((tab, index) => (
        <Tab.Screen name={tab.name as any} component={tab.component as any} key={tab.name} /> //Todo: come back to this
      ))}
    </Tab.Navigator>
  );
};

const CustomersStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'Customer'} component={Customers} />
      <Stack.Screen name={'CustomersAnalytics'} component={CustomersAnalytics} />
      <Stack.Screen name={'SearchCustomers'} component={SearchCustomers} />
    </Stack.Navigator>
  );
};

const InvoicesStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'InvoicesList'} component={InvoicesDashboard} />
      <Stack.Screen name={'CreateInvoice'} component={CreateInvoice} />
      <Stack.Screen name={'EditInvoice'} component={EditInvoices} />
    </Stack.Navigator>
  );
};

const DeliveriesStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'Deliveries'} component={Deliveries} />
      <Stack.Screen name={'DeliveriesAnalytics'} component={DeliveriesAnalytics} />
      <Stack.Screen name={'DeliveryDetails'} component={DeliveryDetails} />
      <Stack.Screen name={'InitiateDelivery'} component={InitiateDelivery} />
    </Stack.Navigator>
  );
};

const AffiliateStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'AllAffiliates'} component={AllAffiliates} />
      <Stack.Screen name={'SearchAffiliates'} component={SearchAffiliates} />
    </Stack.Navigator>
  );
};

const StoreSettingsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name={'StoreSettings'} component={StoreSettings} />
      <Stack.Screen name={'StoreInformation'} component={StoreInformation} />
      <Stack.Screen name={'StoreConfigurations'} component={StoreConfigurations} />
      <Stack.Screen name={'CheckoutOptions'} component={CheckoutOptions} />
      <Stack.Screen name={'DeliveryAreas'} component={DeliveryAreas} />
      <Stack.Screen name={'PaymentSettings'} component={PaymentSettings} />
    </Stack.Navigator>
  );
};

const tabs = [
  { name: AppTabs.HOME, component: Home },
  { name: AppTabs.ORDERS, component: Orders },
  { name: AppTabs.QUICKACTIONS, component: QuickActions },
  { name: AppTabs.PRODUCTS, component: ProductPagesLists },
  { name: AppTabs.PAYMENTS, component: Payments },
];

export default AppNavigation;
