import { createStackNavigator } from '@react-navigation/stack';
import React, { useCallback, useEffect } from 'react';
import AuthNavigation from './auth-navigation';
import { navigationRef } from './root-navigation';
import AppNavigation from './app-navigation';
import useAuthStore from '@/contexts/auth/store';
import { View, Text } from 'react-native';
import { LoadingScreen, ManageInvite } from 'src/screens';
import useWalletStore from 'src/contexts/wallet/store';
import CustomImage from 'src/components/ui/others/custom-image';
import * as SplashScreen from 'expo-splash-screen';
import { toastConfig } from '@/components/ui/others/toast-notification';
import Toast from 'react-native-toast-message';
import { ForceUpdate } from 'src/screens/update';
import useAuthContext from 'src/contexts/auth/auth-context';
import zustandStorage, { mmkvStorage } from 'src/assets/utils/js/zustand-mmkv-storage';
import useFeatureFlagsStore from 'src/contexts/feature-flags/store';

const Stack = createStackNavigator();

interface BaseAppProps {
  fontsLoaded: boolean;
}

// @refresh reset
const BaseApp = ({ fontsLoaded }: BaseAppProps) => {
  const { visitorCountry } = useAuthStore();

  // Hide splash screen once fonts are loaded
  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded && visitorCountry?.code) {
      try {
        await SplashScreen.hideAsync();
      } catch (e) {
        console.error('Error hiding splash screen:', e);
      }
    }
  }, [fontsLoaded, visitorCountry]);

  return (
    <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
      <MainApp />
      <Toast config={toastConfig} topOffset={60} />
    </View>
  );
};

const MainApp = () => {
  useWalletStore();
  const { isInitialized, isAuthenticated } = useAuthStore();
  const { forceUpdateApp } = useFeatureFlagsStore();

  // mmkvStorage.clearAll()
  // zustandStorage.removeItem('auth-storage')

  if (!isInitialized) {
    return <LoadingScreen />;
  }

  if (forceUpdateApp) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}>
        <Stack.Screen name="ForceUpdate" component={ForceUpdate} />
      </Stack.Navigator>
    );
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        // presentation: 'transparentModal',
      }}>
      {/* <Stack.Screen name={'AppNavigation'} component={AppNavigation} /> */}
      {!isAuthenticated ? (
        <Stack.Screen name="AuthNavigation" component={AuthNavigation} />
      ) : (
        <Stack.Screen name="AppNavigation" component={AppNavigation} />
      )}
      {/* we need to move this inside auth navigation @feranmi */}
      {/* <Stack.Screen name="ManageInvite" component={ManageInvite} /> */}
    </Stack.Navigator>
  );
};

export default BaseApp;

export const FallBack = () => (
  <View className="flex-1 items-center justify-center bg-white">
    <CustomImage imageProps={{ source: require('@/assets/gif/loader.gif') }} className="w-40 h-40 rounded-8" />
  </View>
);
