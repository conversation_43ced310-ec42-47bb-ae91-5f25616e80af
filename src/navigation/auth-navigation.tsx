import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Login, ResetPassword, Signup, EnterEmail, Onboarding } from '@/screens';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import SignupV2 from 'src/screens/auth/sign-up-v2';
import { mmkvStorage } from 'src/assets/utils/js/zustand-mmkv-storage';

const Stack = createStackNavigator();

const UserAuth = () => {
  const isOnboarded = mmkvStorage.getString('isOnboarded');

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      {!isOnboarded && <Stack.Screen name="Onboarding" component={Onboarding} />}
      {/* <Stack.Screen name="Onboarding" component={Onboarding} /> */}
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="Signup" component={SignupV2} />
      <Stack.Screen name="EnterEmail" component={EnterEmail} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
    </Stack.Navigator>
  );
};

export default UserAuth;
