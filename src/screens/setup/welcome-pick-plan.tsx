import { delay, showLoader, to<PERSON><PERSON><PERSON>cy, to<PERSON><PERSON><PERSON>, wp } from '@/assets/utils/js';
import PlanCard from '@/components/select-plan/plan-card';
import PlanFeatureModal from '@/components/select-plan/plan-feature-modal';
import { BaseText, CircledIcon, SelectionPill } from '@/components/ui';
import Container from '@/components/ui/container';
import Row from '@/components/ui/row';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import colors from '@/theme/colors';
import { CURRENCIES, CountryInterface, GET_PLANS, GetPlansParams, PLAN_TYPE, Plan, PlanOption } from 'catlog-shared';
import { Gift, InfoCircle } from 'iconsax-react-native/src';
import React, { useMemo, useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import PickPlanModal from 'src/components/select-plan/pick-plan-modal';
import SelectPlanDurationModal from 'src/components/select-plan/select-plan-duration-modal';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ApiData, useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import Pressable from 'src/components/ui/base/pressable';
import { ArrowUpRight } from 'src/components/ui/icons';
import SubscriptionBenefitModal from 'src/components/select-plan/subscription-benefit-modal';
import { PickPlanMain } from './pick-plan';

const WelcomePickPlan = () => {
  const { store } = useAuthContext();
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
    },
    {
      country: typeof store?.country === 'string' ? store?.country : store?.country?.code,
    },
  );

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Select Preference',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}
      isLoading={getPlansReq.isLoading}>
      <View className="flex-1">
        {/* <ScreenInfoHeader
          iconElement={
            <Image
              source={require('@/assets/images/giftBox.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[60px]"
            />
          }
          colorPalette={ColorPaletteType.RED}
          pageTitleTop={'Final Step'}
          pageTitleBottom={'Select a Plan'}
        /> */}
        <PickPlanMain getPlansReq={getPlansReq} isSetup isWelcomeBack />
      </View>
    </DashboardLayout>
  );
};

export default WelcomePickPlan;
