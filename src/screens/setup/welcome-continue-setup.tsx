import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { Gift, TickCircle } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import useAuthContext from 'src/contexts/auth/auth-context';
import colors from 'src/theme/colors';
import * as Animatable from 'react-native-animatable';
import { useNavigation } from '@react-navigation/native';
import Separator from 'src/components/ui/others/separator';
import { wp } from 'src/assets/utils/js';

const WelcomeContinueSetup = () => {
  const { user, userLoading } = useAuthContext();
  const navigation = useNavigation();

  const handleContinue = async () => {
    navigation.navigate('PickPlan');
  };

  return (
    <DashboardLayout
      waitOnReady={false}
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Continue your setup',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <View className="flex-1 justify-center px-20">
        <Animatable.View
          className={`bg-accentGreen-pastel2 self-center rounded-full p-15`}
          animation={'zoomIn'}
          duration={300}>
          <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
            <CircledIcon className={`bg-accentGreen-main p-25`}>
              <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                <Gift variant="Bold" color={colors.white} size={wp(40)} />
              </Animatable.View>
            </CircledIcon>
          </Animatable.View>
        </Animatable.View>
        <View className={`items-center justify-center mt-20`}>
          <BaseText fontSize={20} type="heading">
            You're almost there, {user?.name?.split(' ')[0]}!
          </BaseText>
          <BaseText fontSize={14} weight="medium" className="text-center text-black-placeholder mt-10">
            To continue, you'll need to pick a plan. And,{'\n'}since you came back, you get a special offer:
          </BaseText>
        </View>

        <View className={`px-15 mt-20 bg-grey-bgTwo rounded-15`}>
          <BaseText fontSize={14} type="heading" classes=" text-black-secondary mt-15">
            You can choose to:
          </BaseText>
          <Separator className="mx-0 my-12" />
          <Row className={'justify-start mb-12'}>
            <CircledIcon className="p-6">
              <TickCircle size={wp(14)} variant="Bold" color={colors?.accentGreen.main} />
            </CircledIcon>
            <View className="mr-5">
              <BaseText weight={'medium'} classes={'text-black-muted pl-8'}>
                Try for 14 days free
              </BaseText>
            </View>
          </Row>
          <Row className={'justify-start mb-12'}>
            <CircledIcon className="p-6">
              <TickCircle size={wp(14)} variant="Bold" color={colors?.accentGreen.main} />
            </CircledIcon>
            <View className="mr-5">
              <BaseText weight={'medium'} classes={'text-black-muted pl-8'}>
                Pay a small fee to use for 30 days
              </BaseText>
            </View>
          </Row>
        </View>
      </View>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Activate My Account',
            onPress: () => handleContinue(),
            isLoading: userLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default WelcomeContinueSetup;
