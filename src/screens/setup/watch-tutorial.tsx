import { useNavigation } from '@react-navigation/native';
import { useEvent } from 'expo';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Gift, Play } from 'iconsax-react-native/src';
import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import VideoCongratsModal from 'src/components/get-started/video-congrats-modal';
import { BaseText } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import CircledIcon from 'src/components/ui/circled-icon';
import Container from 'src/components/ui/container';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import SectionContainer from 'src/components/ui/section-container';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';

const videoSource = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
const WatchTutorial = () => {
  const { store } = useAuthContext();
  const { navigate } = useNavigation();
  const { modals, toggleModal, switchModals } = useModals(['completed']);
  const [isLoading, setIsLoading] = useState(false);
  const [isSkipped, setIsSkipped] = useState(false);
  const lastTimeRef = useRef(null);

  const player = useVideoPlayer(videoSource, player => {
    player.play();
  });

  useEffect(() => {
    if (player) {
      player.addListener('statusChange', ({ status }) => {
        if (status === 'loading') setIsLoading(true);
        if (status === 'readyToPlay') setIsLoading(false);
      });

      return () => {
        player.removeAllListeners('statusChange');
      };
    }
  }, [player]);

  const { isPlaying } = useEvent(player, 'playingChange', { isPlaying: player.playing });

  useEffect(() => {
    if (isPlaying == true && lastTimeRef.current === null) {
      lastTimeRef.current = Date.now();
    } else if (isPlaying == false && lastTimeRef.current) {
      const playDuration = Date.now() - lastTimeRef.current;
      if (playDuration / 1000 > 2) {
        // call api
        toggleModal('completed');
      }
    }
  }, [isPlaying]);

  const handlePlay = () => {
    player.play();
  };

  const handleRewatch = () => {
    lastTimeRef.current = null;
    player.replay();
    player.play();
    toggleModal('completed');
  };

  const handleSkip = () => {
    player.pause()
    setIsSkipped(true);
    navigate('SetupProgress');
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Start Setup',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <ScrollView className="flex-1">
        <Container>
          <SectionContainer className="py-20">
            <CircledIcon className="w-40 h-40 bg-white">
              <Gift size={25} variant="Bold" color={colors.accentRed.main} />
            </CircledIcon>
            <BaseText fontSize={15} weight="semiBold" type="heading" classes="text-black-main mt-15">
              Watch the video below to see how Catlog works
            </BaseText>
            <BaseText classes="text-black-placeholder">
              If you watch this video fully today, you'll earn NGN 500 as a discount towards your first subscription!
            </BaseText>
          </SectionContainer>
          <SectionContainer className="rounded-30 py-15 h-[250px] overflow-hidden">
            <View className="rounded-[20px] overflow-hidden">
              {!isPlaying && (
                <View className="flex justify-center items-center absolute z-10 w-full h-full">
                  <Pressable onPress={handlePlay} className="">
                    <CircledIcon className="w-50 h-50 bg-white ">
                      <Play size={25} variant="Outline" color={colors.primary.main} />
                    </CircledIcon>
                  </Pressable>
                </View>
              )}
              {!isSkipped && (
                <VideoView
                  player={player}
                  className="w-full h-full"
                  contentFit={isPlaying ? 'contain' : 'cover'}
                  nativeControls={true}
                  // nativeControls={false}
                  allowsFullscreen
                  allowsPictureInPicture
                />
              )}
            </View>
          </SectionContainer>
        </Container>
      </ScrollView>

      <FixedBtnFooter
        buttons={[
          {
            text: false ? '..' : 'Skip Tutorial',
            disabled: false,
            onPress: () => handleSkip(),
            variant: ButtonVariant.LIGHT,
          },
        ]}
      />
      {!isSkipped && (
        <VideoCongratsModal
          rewatch={handleRewatch}
          continueSetup={() => {
            toggleModal('completed', false);
            navigate('SetupProgress');
          }}
          closeModal={() => toggleModal('completed', false)}
          isVisible={modals.completed}
        />
      )}
    </DashboardLayout>
  );
};

export default WatchTutorial;
