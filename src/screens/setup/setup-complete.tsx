import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { Copy, Link, Link2 } from 'iconsax-react-native/src';
import { ScrollView, View, ViewStyle } from 'react-native';
import { copyToClipboard, wp } from 'src/assets/utils/js';
import PreviewStoreFront from 'src/components/payments/preview-store';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import { useStoreReview } from 'src/hooks/use-store-review';
import { useEffect, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import Confetti from 'react-native-confetti';

const SetupComplete = () => {
  const { storeLink, refetchSession, decideNextRoute, userLoading, store } = useAuthContext();
  const { toggleModal, modals } = useModals(['preview']);
  const navigation = useNavigation();
  const confettiRef = useRef<any>(null);

  const { isLoading, canRequestReview, requestReview, markDeclined, markNeverAskAgain, getDaysUntilNextRequest } =
    useStoreReview();

  useEffect(() => {
    requestReview();

    const interval = setInterval(() => {
      if (confettiRef.current) {
        confettiRef.current.startConfetti();
        clearInterval(interval);
      }
    }, 50);

    return () => clearInterval(interval);
  }, []);

  const handleContinue = async () => {
    // await refetchSession();
    const { nextRoute } = await decideNextRoute({ skipProgress: true, fromSetupComplete: true });
    navigation.navigate(nextRoute as any);
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Your Store is Live!',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <ScrollView className="flex-1">
        <Container className="items-center mt-40">
          <SuccessCheckmark variant="lg" />
          <View className={`items-center justify-center mt-10`}>
            <BaseText fontSize={20} classes={`mt-10 font-fhOscarLight text-black-main`} type="heading">
              You’re all setup
            </BaseText>
            <BaseText fontSize={20} weight={'bold'} classes={`text-black-main mt-3`} type="heading">
              Your Store is Live!🎉
            </BaseText>
            <BaseText className="text-center text-black-placeholder mt-15 max-w-[300px]">
              Your Catlog store is now ready and you're all set to start selling to customers!
            </BaseText>
          </View>
          <View className={`items-center justify-center mt-20 bg-grey-bgTwo rounded-15`}>
            <BaseText fontSize={12} weight="medium" className="text-center text-black-muted mt-15">
              Your Store Link:
            </BaseText>
            <Pressable onPress={() => copyToClipboard(storeLink ?? '')}>
              <Row className={'justify-start py-15 px-20 mt-10 rounded-full border-t border-grey-border'}>
                <View className="mr-5">
                  <BaseText fontSize={12} weight={'medium'} classes={'text-primary-main'}>
                    {storeLink}
                  </BaseText>
                </View>
                <Copy size={wp(12)} color={colors?.primary.main} strokeWidth={2} />
              </Row>
            </Pressable>
          </View>
        </Container>
      </ScrollView>
      <Confetti ref={confettiRef} untilStopped={false} duration={3000} size={1.5} timeout={0} />
      <PreviewStoreFront toggle={() => toggleModal('preview')} show={modals.preview} />
      <FixedBtnFooter
        buttons={[
          {
            text: 'Preview Store',
            onPress: () => toggleModal('preview'),
            variant: ButtonVariant.LIGHT,
          },
          {
            text: userLoading ? 'Loading...' : 'Continue',
            onPress: () => handleContinue(),
            isLoading: userLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default SetupComplete;
