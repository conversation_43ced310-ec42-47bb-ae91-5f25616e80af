import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { EmojiHappy, TickCircle } from 'node_modules/iconsax-react-native/src';
import * as Animatable from 'react-native-animatable';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import Confetti from 'src/components/ui/confetti';
import Separator from 'src/components/ui/others/separator';

const WelcomeBack = () => {
  const { user, userLoading, decideNextRoute } = useAuthContext();

  const navigation = useNavigation();

  const handleContinue = async () => {
    const { nextRoute } = await decideNextRoute({ skipWelcomeBack: true });
    navigation.navigate(nextRoute as any);
  };

  return (
    <DashboardLayout
      waitOnReady={false}
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Welcome Back',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}>
      <View className="flex-1 items-center justify-center px-20 -mt-50">
        {/* <SuccessCheckmark /> */}

        <Animatable.View
          className={`bg-accentGreen-pastel2 self-center rounded-full p-15`}
          animation={'zoomIn'}
          duration={300}>
          <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
            <CircledIcon className={`bg-accentGreen-main p-25`}>
              <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                <EmojiHappy variant="Bold" color={colors.white} size={wp(40)} />
              </Animatable.View>
            </CircledIcon>
          </Animatable.View>
        </Animatable.View>
        <View className={`items-center justify-center mt-20`}>
          <BaseText fontSize={20} type="heading">
            Welcome back, {user?.name?.split(' ')[0]}!
          </BaseText>
          <BaseText fontSize={14} weight="medium" className="text-center text-black-placeholder mt-10">
            We have missed you. Let's {'\n'} get you back on track!
          </BaseText>
        </View>

        <View className={`px-15 mt-20 bg-grey-bgTwo rounded-15 w-full`}>
          <BaseText fontSize={14} type="heading" classes=" text-black-secondary mt-15">
            Catlog is now packed with new features:
          </BaseText>
          <Separator className="mx-0 my-12" />

          {features.map((feature, index) => (
            <Row className={'justify-start mb-12'} key={index}>
              <CircledIcon className="p-6">
                <TickCircle size={wp(14)} variant="Bold" color={colors?.accentGreen.main} />
              </CircledIcon>
              <View className="mr-5">
                <BaseText weight={'medium'} classes={'text-black-muted pl-8'}>
                  {feature}
                </BaseText>
              </View>
            </Row>
          ))}
        </View>
      </View>
      <Confetti />
      <FixedBtnFooter
        buttons={[
          {
            text: 'Continue',
            onPress: () => handleContinue(),
            isLoading: userLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

const features = [
  'Brand New Storefront',
  'Inventory Management',
  'International Payments',
  'Product Videos on Storefront',
  'Low Stock Notifications',
  'Custom Domains ...and more!',
];

export default WelcomeBack;
