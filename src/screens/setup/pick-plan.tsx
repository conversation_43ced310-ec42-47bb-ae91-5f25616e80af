import { delay, showLoader, to<PERSON><PERSON><PERSON>cy, to<PERSON><PERSON><PERSON>, wp } from '@/assets/utils/js';
import PlanCard from '@/components/select-plan/plan-card';
import PlanFeatureModal from '@/components/select-plan/plan-feature-modal';
import { BaseText, CircledIcon, SelectionPill } from '@/components/ui';
import Container from '@/components/ui/container';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import colors from '@/theme/colors';
import { CountryInterface, GET_PLANS, GetPlansParams, PLAN_TYPE, Plan, PlanOption } from 'catlog-shared';
import { InfoCircle } from 'iconsax-react-native/src';
import React, { useMemo, useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import PickPlanModal from 'src/components/select-plan/pick-plan-modal';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ApiData, useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import SubscriptionBenefitModal from 'src/components/select-plan/subscription-benefit-modal';

const PickPlan = () => {
  const { store, isDroppedOffUser } = useAuthContext();
  const getPlansReq = useApi(
    { apiFunction: GET_PLANS, key: GET_PLANS.name, method: 'GET', autoRequest: true },
    { country: typeof store?.country === 'string' ? store?.country : store?.country?.code },
  );

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Select Preference',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: false,
        showBackButton: false,
      }}
      isLoading={getPlansReq.isLoading}>
      <View className="flex-1">
        <PickPlanMain getPlansReq={getPlansReq} isSetup={!isDroppedOffUser} isWelcomeBack={isDroppedOffUser} />
      </View>
    </DashboardLayout>
  );
};

export default PickPlan;

export interface GroupedPlan {
  options: { [key: string]: PlanOption };
  name: string;
  description: { title: string; features: string[] }[];
  interval: number;
  type: PLAN_TYPE;
  id: string;
  interval_text: string;
  country: CountryInterface;
  is_paid_plan?: boolean;
  amount: number;
}

function groupPlans(plans: Plan[]) {
  const thePlans: Plan[] = [];

  if (!plans) {
    return [];
  }

  plans.forEach(plan => {
    if (plan.type === PLAN_TYPE.KITCHEN || plan.type === PLAN_TYPE.STARTER) return;

    const planDuration = (() => {
      switch (plan.interval) {
        case 30:
          return 'MONTHLY';
        case 90:
          return 'QUARTERLY';
        case 180:
          return 'BI-ANNUALLY';
        default:
          return 'YEARLY';
      }
    })();

    const planIndex = thePlans.findIndex(p => p.type === plan.type);
    const planExists = planIndex !== -1;

    //add plan to plan options if plan already exists
    if (planExists) {
      thePlans[planIndex].options[planDuration] = {
        id: plan.plan_option_id,
        interval: plan.interval,
        interval_text: plan.interval_text,
        amount: plan.amount,
        discount: plan?.discount,
        plan_id: plan.id,
        actual_amount: plan?.actual_amount,
        plan_type: plan.type,
        currency: plan.currency,
        country: plan.country,
        created_at: '',
        updated_at: '',
      } as PlanOption;

      return;
    }

    //add fresh plan if plan doesnt exist
    thePlans.push({
      ...plan,
      options: {
        [planDuration]: {
          id: plan.plan_option_id,
          interval: plan.interval,
          interval_text: plan.interval_text,
          amount: plan.amount,
          discount: plan?.discount,
          plan_id: plan.id,
          actual_amount: plan?.actual_amount,
          plan_type: plan.type,
          currency: plan.currency,
          country: plan.country,
          created_at: '',
          updated_at: '',
        } as PlanOption,
      },
    });
  });

  return thePlans;
}
interface Props {
  getPlansReq: ApiData<GetPlansParams, any>;
  isSetup?: boolean;
  isWelcomeBack?: boolean;
}

enum SelectionPillType {
  TEST_FOR_30_DAYS = 'Test for 30 days',
  TRY_FOR_7_DAYS = 'Try free for 7 days',
  TRY_FOR_14_DAYS = 'Try free for 14 days',
}

const lingo = {
  [SelectionPillType.TEST_FOR_30_DAYS]: {
    title: 'Test for 30 days',
    description: 'Pay a small amount to test Catlog for 30 days',
    buttonText: 'Continue',
  },
  [SelectionPillType.TRY_FOR_7_DAYS]: {
    title: 'Try free for 7 days',
    description: 'Try for 7 days free. Payment required after',
    buttonText: 'Try Free for 7 Days',
  },
  [SelectionPillType.TRY_FOR_14_DAYS]: {
    title: 'Try free for 14 days',
    description: 'Try for 14 days free. Payment required after',
    buttonText: 'Try Free for 14 Days',
  },
};

const planDescription = {
  [PLAN_TYPE.BASIC]: {
    title: 'Basic Plan',
    description: 'For growing businesses looking to handle their sales better',
  },
  [PLAN_TYPE.BUSINESS_PLUS]: {
    title: 'Business Plus Plan',
    description: 'For bigger businesses looking to sell more efficiently',
  },
};

const PickPlanMain: React.FC<Props> = ({ getPlansReq, isSetup = true, isWelcomeBack = false }) => {
  const isInApp = !isSetup && !isWelcomeBack;
  const [selectedPill, setSelectedPill] = useState<SelectionPillType>(SelectionPillType.TEST_FOR_30_DAYS);

  const { store } = useAuthContext();
  const { modals, toggleModal, switchModals } = useModals(['features', 'pick_plan', 'subscriptionBenefit']);
  const [selectedPlan, setSelectedPlan] = useState<GroupedPlan>(null);
  const { refetchSession, decideNextRoute, isLoadingSession, setIsDroppedOffUser } = useAuthContext();
  const navigation = useNavigation();

  const currentPlan = store?.subscription?.plan?.type ?? null;

  const plans: Plan[] = useMemo(() => {
    if (getPlansReq.response) {
      const thePlans = getPlansReq.response?.data?.plans ?? [];
      thePlans.sort((a, b) => (a?.amount < b?.amount ? -1 : 1));
      return thePlans;
    }
    return [];
  }, [getPlansReq.response]);

  const groupedPlans = useMemo(() => {
    return groupPlans(plans) as GroupedPlan[];
  }, [plans]);

  const showFeatures = (plan: GroupedPlan) => {
    setSelectedPlan(plan);
    toggleModal('features');
  };

  const selectPlan = (plan: GroupedPlan) => {
    setSelectedPlan(plan);
    toggleModal('pick_plan');
  };

  const handlePaymentSuccess = async () => {
    toggleModal('pick_plan', false);
    setIsDroppedOffUser(false);
    // await delay(1000);
    showLoader('Updating store...', false, true);
    await refetchSession();

    if (isSetup || isWelcomeBack) {
      const { nextRoute } = await decideNextRoute({ skipProgress: true, fromSetupComplete: true });
      navigation.navigate(nextRoute as any);
    }
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        {!isInApp && (
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/subscription.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[80px]"
              />
            }
            colorPalette={ColorPaletteType.RED}
            pageTitleTop={'Final Step'}
            pageTitleBottom={'Select a Plan'}
          />
        )}
        <Container className="pt-24 pb-24">
          {!isInApp && (
            <View className="flex-row">
              <SelectionPill
                onPress={() => setSelectedPill(SelectionPillType.TEST_FOR_30_DAYS)}
                selected={selectedPill === SelectionPillType.TEST_FOR_30_DAYS}
                title={SelectionPillType.TEST_FOR_30_DAYS}
              />
              {isSetup && (
                <SelectionPill
                  onPress={() => setSelectedPill(SelectionPillType.TRY_FOR_7_DAYS)}
                  selected={selectedPill === SelectionPillType.TRY_FOR_7_DAYS}
                  title={SelectionPillType.TRY_FOR_7_DAYS}
                />
              )}

              {isWelcomeBack && (
                <SelectionPill
                  onPress={() => setSelectedPill(SelectionPillType.TRY_FOR_14_DAYS)}
                  selected={selectedPill === SelectionPillType.TRY_FOR_14_DAYS}
                  title={SelectionPillType.TRY_FOR_14_DAYS}
                />
              )}
            </View>
          )}
          {!isInApp && (
            <View className="flex-row items-center mt-10 py-10 px-12 bg-grey-bgOne rounded-12">
              <CircledIcon className="bg-white p-3">
                <InfoCircle variant="Bold" size={wp(14)} color={colors.accentRed.light} />
              </CircledIcon>
              <View className="flex-1 ml-10">
                <BaseText fontSize={12} classes="text-black-secondary">
                  {lingo[selectedPill].description}
                </BaseText>
              </View>
            </View>
          )}
          <View className="mt-15" style={{ gap: wp(15) }}>
            {groupedPlans.map((plan, idx) => (
              <PlanCard
                key={plan?.name!}
                onPress={() => selectPlan(plan)}
                onPressPlansFeatures={() => showFeatures(plan)}
                selected={selectedPlan?.name === plan?.name}
                greatChoice={!isInApp ? idx == 0 : false}
                planDescription={planDescription[plan.type].description}
                buttonText={!isInApp ? lingo[selectedPill].buttonText : null}
                plan={plan}
                isSetup={!isInApp}
                isUpfront={!isInApp && selectedPill === SelectionPillType.TEST_FOR_30_DAYS}
                isFreeTrial={!isInApp && selectedPill === SelectionPillType.TRY_FOR_7_DAYS}
                ctaDisabled={isLoadingSession}
                isCurrentPlan={currentPlan === plan.type}
              />
            ))}
          </View>
        </Container>

        {selectedPlan && (
          <>
            <PlanFeatureModal
              planName={selectedPlan.name}
              features={[...selectedPlan.description[0].features]}
              isVisible={modals.features}
              closeModal={() => toggleModal('features', false)}
            />
            <PickPlanModal
              isSetup={!isInApp}
              plans={plans}
              isVisible={modals.pick_plan}
              closeModal={() => toggleModal('pick_plan', false)}
              plan={selectedPlan}
              onComplete={handlePaymentSuccess}
              payUpfront={!isInApp && selectedPill === SelectionPillType.TEST_FOR_30_DAYS}
              freeTrialInterval={isSetup ? 7 : 14}
              currentPlanOption={Object.values(selectedPlan?.options).find(
                option => option.id === store?.subscription?.plan_option?.id,
              )}
            />
          </>
        )}
        <SubscriptionBenefitModal
          isVisible={modals.subscriptionBenefit}
          closeModal={() => toggleModal('subscriptionBenefit', false)}
        />
      </ScrollView>
      {/* <FixedBtnFooter
        buttons={[
          {
            text: false ? '..' : 'Pick Plan',
            disabled: !Boolean(selectedPlan),
            onPress: () => toggleModal('pick_plan'),
          },
        ]}
      /> */}
    </View>
  );
};
export { PickPlanMain };
