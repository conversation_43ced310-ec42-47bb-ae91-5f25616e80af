import React, { useMemo, useRef, useState } from 'react';
import { ScrollView, View, Image, ActivityIndicator } from 'react-native';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { BaseText, CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import Button from '@/components/ui/buttons/button';
import CheckBox from '@/components/ui/buttons/check-box';
import Container from '@/components/ui/container';
import { CloudUpload, GhanaFlag, NigeriaFlag } from '@/components/ui/icons';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import Input from '@/components/ui/inputs/input';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import useStatusbar from '@/hooks/use-statusbar';
import UploadImageBtn from '@/components/ui/buttons/upload-image-btn';
import { useNavigation } from '@react-navigation/native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useAuthStore from '@/contexts/auth/store';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import { delay, getFieldvalues, wp } from '@/assets/utils/js';
import useAuthContext from '@/contexts/auth/auth-context';
import ScreenModal from '@/components/ui/modals/screen-modal';
import {
  COUNTRIES,
  COUNTRY_CURRENCY_MAP,
  CREATE_STORE,
  CreateStoreParams,
  GET_COUNTRIES,
  GET_SLUG_SUGGESTION,
  PhoneInterface,
  phoneObjectFromString,
  StoreInterface,
} from 'catlog-shared';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import useGuestContext from 'src/contexts/auth/guest-context';
import { SUPPORTED_COUNTRIES } from '../auth/sign-up-v2';
import { ArrowDown2, TickCircle } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';
import { currencyDetails } from 'src/components/payments/wallet-cards';
import { RootStackParamList } from 'src/@types/navigation';

interface CreateStoreFormParams extends Omit<CreateStoreParams, 'phone'> {
  phone: PhoneInterface;
  slug?: string;
}

const CreateStore = () => {
  const { user, updateUser, getNewToken, stores, switchStore, isSwitchingStore, decideNextRoute } = useAuthContext();
  const { visitorCountry } = useGuestContext();
  const navigation = useNavigation();
  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);

  const [useSamePhone, setUseSamePhone] = useState(false);
  const [selectedItem, setSelectedItem] = useState('');
  const [errorText, setErrorText] = useState(null);

  const countryOptionRef = useRef<DropDownMethods>();

  const firstStore = (user?.stores as StoreInterface[]).filter(s => s.owner === user?.id)?.[0];

  const createStoreReq = useApi({ key: 'create-store', apiFunction: CREATE_STORE, method: 'POST' });
  const supportedCountriesReq = useApi({ key: 'supported-countries', apiFunction: GET_COUNTRIES, method: 'GET' });
  const slugSuggestionReq = useApi({
    apiFunction: GET_SLUG_SUGGESTION,
    method: 'GET',
    key: 'suggest-store-slug',
    autoRequest: false,
  });
  const supportedCountries: { name: string; emoji: string; code: string }[] =
    supportedCountriesReq?.response?.data ?? [];

  const countryOptions = useMemo(
    () =>
      supportedCountries.map(c => ({
        label: c.name,
        inputLabel: `${c.name} ${c.emoji}`,
        value: c.code,
        leftElement: <View className="w-full h-full">{currencyDetails[COUNTRY_CURRENCY_MAP[c.code]].icon()}</View>,
      })),
    [supportedCountries],
  );

  const prefilledCountry = useMemo(() => {
    if (visitorCountry && visitorCountry.code && SUPPORTED_COUNTRIES.includes(visitorCountry.code)) {
      return visitorCountry.code;
    }

    return SUPPORTED_COUNTRIES[0];
  }, [visitorCountry]);

  const form = useFormik<CreateStoreFormParams>({
    initialValues: {
      name: '',
      phone: phoneObjectFromString(user?.phone),
      country: prefilledCountry ? prefilledCountry : COUNTRIES.NG,
      description: '',
      store_type: STORE_TYPES.REGULAR,
      logo: '',
      copy_config: !!firstStore,
      slug: '',
    },
    validationSchema,
    onSubmit: async values => {
      // if (values.store_type === STORE_TYPES.RESTAURANT) {
      //   toggleModal('confirm_chowbot_selection');
      //   return;
      // }
      createStore();
    },
  });

  const createStore = async () => {
    const { slug, ...values } = form.values;
    const phone = `${values.phone.code}-${values.phone.digits}`;
    values.store_type = values.country === 'NG' ? values.store_type : STORE_TYPES.REGULAR;
    const [response, error] = await createStoreReq.makeRequest({ ...values, phone });

    if (error) {
      if (error.fields && Object.keys(error.fields).length > 0) {
        form.setErrors({ ...error.fields });
      } else {
        setErrorText(error.message);
      }
    } else {
      const storesData = response.data.stores;
      if (hasMultipleStores) {
        updateUser(response.data);
        await delay(500);
        switchStore(storesData[storesData?.length - 1].id, true);
        return;
      } else {
        updateUser(response.data, response.data.stores[0].id);
        await delay(500);
        getNewToken(response.data.stores[0].id);
      }

      const { nextRoute } = await decideNextRoute({
        skipProgress: true,
      });
      navigation.navigate(nextRoute as any);
    }
  };

  const handleUseSamePhoneToggle = () => {
    const nextState = !useSamePhone;

    if (nextState) {
      let phone = user?.phone.split('-') ?? '';

      form.setFieldValue('phone', { code: phone[0], digits: phone[1] });
    } else {
      form.setFieldValue('phone', { code: '+234', digits: '' });
    }

    setUseSamePhone(nextState);
  };

  const handleBusinessNameBlur = async (name: string) => {
    const [res, err] = await slugSuggestionReq.makeRequest({ store_name: name });
    if (res) {
      form.setFieldValue('slug', res.data.slug);
    }
  };

  const hasMultipleStores = stores?.length ?? 0 > 1;
  const selectedCountry = countryOptions.find(c => c.value === (form?.values?.country ?? 'NG'));

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Create Store',
        variant: hasMultipleStores ? HeaderVariants.SUB_LEVEL : HeaderVariants.ROOT_LEVEL,
        showNotifications: false,
      }}>
      <AvoidKeyboard>
        <ScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/storefront.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[80px]"
              />
            }
            pageTitleTop={`Welcome ${user?.name?.split(' ')[0]},`}
            pageTitleBottom={'Create your Store'}
            customElements={
              <View>
                {supportedCountriesReq.isLoading ? (
                  <View className="flex-row items-center bg-white py-8 px-10 rounded-full mt-10 -mb-8">
                    <ActivityIndicator size={'small'} color={colors.grey.muted} />
                  </View>
                ) : (
                  <Pressable
                    className="flex-row items-center bg-white py-8 px-10 rounded-full mt-10 -mb-8"
                    onPress={() => countryOptionRef?.current.open()}>
                    <View className="w-14 h-14">{selectedCountry?.leftElement}</View>
                    <BaseText weight="medium" classes="text-black-muted ml-4 mr-5">
                      {selectedCountry?.label}
                    </BaseText>
                    <ArrowDown2 size={wp(14)} strokeWidth={2.5} color={colors.grey.muted} />
                  </Pressable>
                )}
              </View>
            }
          />
          <Container className="pt-30">
            <Input
              label="Business Name"
              {...getFieldvalues('name', form)}
              onBlur={e => handleBusinessNameBlur(e.nativeEvent.text)}
              rightAccessory={
                <Row>
                  {slugSuggestionReq?.isLoading && <ActivityIndicator size="small" color={colors.grey.muted} />}
                </Row>
              }
            />
            {form.values?.slug && (
              <Row disableSpread className="mt-8">
                <TickCircle size={wp(12)} color={colors.accentGreen.main} />
                <BaseText fontSize={12} classes="ml-5 text-grey-mutedDark -mt-2">
                  {form.values?.slug}.catlog.shop{' '}
                  <BaseText fontSize={12} classes="ml-5 text-grey-muted -mt-2">
                    (Editable later)
                  </BaseText>
                </BaseText>
              </Row>
            )}
            <SelectDropdown
              showLabel
              ref={countryOptionRef}
              showAnchor={false}
              items={countryOptions.map(o => ({
                ...o,
                leftElement: <View className="w-24 h-24">{o.leftElement}</View>,
              }))}
              label={'Where is this business based?'}
              onPressItem={value => form.setFieldValue('country', value)}
              selectedItem={form.values.country}
              containerClasses="mt-15"
            />
            <PhoneNumberInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
              label="Business Phone"
            />
            <Input
              label={'Describe your business - What you sell'}
              multiline
              className="h-[80]"
              containerClasses="mt-15"
              {...getFieldvalues('description', form)}
            />
            <UploadImageBtn setTempImage={() => null} setImageUrl={url => form.setFieldValue('logo', url)} />
            {firstStore && (
              <Pressable onPress={() => form.setFieldValue('copy_config', !form.values.copy_config)} className="mt-15">
                <Row classes="justify-start">
                  <CheckBox checked={form.values.copy_config} />
                  <BaseText classes="text-black-placeholder ml-10">
                    Copy configurations from {firstStore?.name}
                  </BaseText>
                </Row>
              </Pressable>
            )}
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: createStoreReq?.isLoading ? 'Creating Business...' : 'Create Business',
            disabled: createStoreReq?.isLoading,
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
      <ScreenModal show={isSwitchingStore} toggleModal={() => null}>
        <View className="min-h-[25%] w-full rounded-xl bg-transparent justify-center items-center">
          <ActivityIndicator className="text-white" size="small" animating />
          <BaseText classes="mt-10 text-white">{'Switching Store...'}</BaseText>
        </View>
      </ScreenModal>
    </DashboardLayout>
  );
};

enum STORE_TYPES {
  REGULAR = 'REGULAR',
  RESTAURANT = 'RESTAURANT',
}

const storeTypeOptions: DropDownItem[] = [
  {
    label: 'Regular - Storefronts & Business Management',
    value: STORE_TYPES.REGULAR,
  },
  {
    label: 'Chowbot + Everything in Regular (Restaurant)',
    value: STORE_TYPES.RESTAURANT,
  },
];

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Store name is required'),
  phone: phoneValidation(),
  description: Yup.string()
    .required('Store description is required')
    .max(150, 'Description cannot be more than 150 characters'),
  store_type: Yup.string().required("Please select how you'll like to use Catlog"),
});

export default CreateStore;
