import React, { useState } from 'react';
import { BaseText } from '@/components/ui';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import CreateProductsController from '@/components/products/create-products/controller';
import { ProductUploadStep } from '@/components/products/create-products/types';
import { useNavigation } from '@react-navigation/native';

const SetupAddProducts = () => {
  const [currentStep, setCurrentStep] = useState<ProductUploadStep>('method');
  const [currentProduct, setCurrentProduct] = useState(0);
  const [isImport, setIsImport] = useState(false);
  const maxUploadable = 10;

  const navigation = useNavigation();

  const goBack = () => {
    switch (currentStep) {
      case 'method':
        navigation.goBack();
        break;
      case 'images':
        setCurrentStep('method');
        break;
      case 'import':
        setCurrentStep('method');
        break;
      case 'details':
        setCurrentStep(isImport ? 'method' : 'images');
        break;
      case 'response':
        navigation.goBack();
        break;
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: currentStep === 'details' ? `Product ${currentProduct + 1}` : pageTitleStepMap[currentStep],
        variant: HeaderVariants.ROOT_LEVEL,
        onBackPress: goBack,
        showNotifications: false,
      }}>
      <CreateProductsController
        {...{
          maxUploadable,
          setIsImport,
          currentStep,
          isSetup: true,
          setCurrentStep,
          currentProduct,
          setCurrentProduct,
          success: { label: 'View all products', route: '/products' },
          footerBackBtn: true,
        }}
      />
    </DashboardLayout>
  );
};

const pageTitleStepMap = {
  method: 'Add Products',
  images: 'Upload Images',
  import: 'Import Products',
  details: 'Product Details',
  response: 'Products Uploaded',
};

export default SetupAddProducts;
