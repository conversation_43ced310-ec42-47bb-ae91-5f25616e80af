import { useMemo, useState } from 'react';
import { Coin, Coin1, <PERSON><PERSON>, <PERSON>, Profile2User, SearchNormal1 } from 'iconsax-react-native/src';
import { FlatList, RefreshControl, ScrollView, Share, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import colors from '@/theme/colors';
import { Row, CircledIcon, SelectionPill, BaseText } from '@/components/ui';
import ReferredFriendsModal from '@/components/catlog-credits/referred-friends-modal';
import { ArrowUpRight } from '@/components/ui/icons';
import ScrollableActionPills from '@/components/ui/buttons/scrollable-action-pills';
import CatlogCreditsInfoCards from '@/components/catlog-credits/credit-info-cards';
import TransactionsSection from '@/components/payments/transactions-section';
import useModals from '@/hooks/use-modals';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import usePagination from 'src/hooks/use-pagination';
import { delay, groupTransactionsByDate } from 'src/assets/utils/js';
import { GroupedTransactions } from 'src/@types/utils';
import EmptyState from '@/components/ui/empty-states/empty-state';
import dayjs from 'dayjs';
import {
  GET_CREDITS_TRANSACTIONS,
  GET_STORE_REFERRALS,
  CatlogCreditsTransaction,
  StoreInterface,
  Transaction,
  TransactionSearchParams,
  GET_STORE_CREDITS,
} from 'catlog-shared'; //todo: @silas look at this
import React from 'react';
import TransactionSkeletonLoader from '@/components/payments/transaction-skeleton-loader';
import CatlogCreditInformationModal from 'src/components/catlog-credits/catlog-credit-information-modal';
import WithdrawCreditModal from 'src/components/catlog-credits/withdraw-credit-modal';
import useWalletContext from 'src/contexts/wallet/wallet-context';

export interface GetReferralResponse {
  message: string;
  data: Data;
}

export interface Data {
  owner: string;
  referral_code: string;
  referrals: Referral[];
  created_at: Date;
  updated_at: Date;
  id: string;
}

export interface Referral {
  has_claimed: boolean;
  user: User;
}

export interface User {
  name: string;
  date_joined: Date;
}

interface TransactionResponse
  extends ResponseWithPagination<{ store: StoreInterface; transactions: CatlogCreditsTransaction[] }> {}
const PER_PAGE = 10;

const CatlogCredits = () => {
  const [groupedCreditTransactions, setGroupedCreditTransactions] = useState<
    GroupedTransactions<CatlogCreditsTransaction>[]
  >([]);
  const [transactionType, setTransactionType] = useState<'debit' | 'credit' | ''>('');
  const { modals, toggleModal } = useModals(['referrals', 'catlogCreditInfo', 'withdraw']);
  const { currentPage, goNext, setPage } = usePagination();

  const getReferralRequest = useApi<void, GetReferralResponse>({
    key: 'get-store-referrals',
    apiFunction: GET_STORE_REFERRALS,
    method: 'GET',
  });

  const getCreditTransactionRequest = useApi<TransactionSearchParams, TransactionResponse>(
    {
      key: 'get-store-referrals',
      apiFunction: GET_CREDITS_TRANSACTIONS,
      method: 'GET',
      onSuccess: response => {
        const groupedTransactions = groupTransactionsByDate<CatlogCreditsTransaction>(response?.data?.transactions);
        setGroupedCreditTransactions(groupedTransactions);
      },
    },
    {
      filter: { search: '', type: transactionType },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const referralData = getReferralRequest?.response?.data;

  const handlePullToRefresh = () => {
    try {
      setGroupedCreditTransactions([]);
      setPage(1);
      getCreditTransactionRequest.refetch();
      getReferralRequest.refetch();
      getReferralRequest.refetch();
    } catch (error) {}
  };

  const actionPills = useMemo(
    () => [
      {
        LeftIcon: () => <Coin1 color={colors?.black.muted} size={hp(15)} />,
        title: 'What is Catlog Credits?',
        onPress: () => toggleModal('catlogCreditInfo'),
      },
      {
        LeftIcon: () => <Profile2User color={colors?.black.muted} size={hp(15)} />,
        title: 'Referred Friends',
        onPress: () => toggleModal('referrals'),
      },
      {
        LeftIcon: () => <Link21 color={colors?.black.muted} size={hp(15)} />,
        title: 'Share Referral Code',
        showArrow: true,
        addon: <ArrowUpRight currentColor={colors.black.secondary} strokeWidth={1.5} size={wp(18)} />,
        onPress: () =>
          Share.share({
            url: `https://app.catlog.shop/sign-up?ref=${referralData?.referral_code}`,
            message: `Join catlog with my link https://app.catlog.shop/sign-up?ref=${referralData?.referral_code}`,
          }),
      },
    ],
    [],
  );

  const changeTxnType = async (type: '' | 'debit' | 'credit') => {
    setTransactionType(type);
    await delay(100);
    setGroupedCreditTransactions([]);
  };

  return (
    <DashboardLayout headerProps={{ headerBg: 'bg-accentOrange-pastel', pageTitle: 'Catlog Credits' }}>
      <View className="flex-1">
        <FlatList
          data={groupedCreditTransactions}
          refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
          ListHeaderComponent={
            <>
              <CatlogCreditsInfoCards
                referralInfo={referralData}
                onPressWithdraw={() => toggleModal('withdraw')}
                onPressFriends={() => toggleModal('referrals')}
              />
              <View className="mt-16">
                <ScrollableActionPills pills={actionPills} title="Quick Actions" />
              </View>
              <Row className="px-20 mt-20">
                <BaseText type="heading" fontSize={15}>
                  Credit History
                </BaseText>
                <CircledIcon iconBg="bg-grey-bgOne">
                  <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
                </CircledIcon>
              </Row>
              <Row classes="justify-start mt-5 px-20">
                <SelectionPill selected={transactionType === ''} title={'All Txs'} onPress={() => changeTxnType('')} />
                <SelectionPill
                  selected={transactionType === 'debit'}
                  title={'Debits'}
                  onPress={() => changeTxnType('debit')}
                />
                <SelectionPill
                  selected={transactionType === 'credit'}
                  title={'Credits'}
                  onPress={() => changeTxnType('credit')}
                />
              </Row>
              {/* <TransactionSkeletonLoader /> */}
            </>
          }
          keyExtractor={(item, index) => item.date.toString() + index}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={() =>
            getCreditTransactionRequest.isLoading ? (
              <View className="mt-15">
                <TransactionSkeletonLoader />
              </View>
            ) : (
              <EmptyState
                icon={<Money variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
                showBtn={false}
                text={'No transactions yet'}
              />
            )
          }
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          onEndReached={() => {
            if (!getCreditTransactionRequest?.isLoading) {
              goNext(getCreditTransactionRequest?.response?.total_pages);
            }
          }}
          renderItem={({ item }) => (
            <TransactionsSection
              key={dayjs(item?.date).format('DD-MM-YYYY')}
              title={dayjs(item?.date).format('DD-MM-YYYY')}
              content={item.transactions}
            />
          )}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {getCreditTransactionRequest?.isLoading && <TransactionSkeletonLoader />}
            </View>
          }
        />
      </View>
      <ReferredFriendsModal
        closeModal={() => toggleModal('referrals', false)}
        isVisible={modals.referrals}
        referrals={referralData?.referrals}
      />
      <CatlogCreditInformationModal
        closeModal={() => toggleModal('catlogCreditInfo', false)}
        isVisible={modals.catlogCreditInfo}
      />
      <WithdrawCreditModal closeModal={() => toggleModal('withdraw', false)} isVisible={modals.withdraw} />
    </DashboardLayout>
  );
};

export default CatlogCredits;
