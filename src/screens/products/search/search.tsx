import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Container, Row } from '@/components/ui';
import { ScrollView, View } from 'react-native';
import Input from '@/components/ui/inputs/input';
import { Search as SearchIcon } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { wp } from 'src/assets/utils/js';
import { Setting, Setting3, Setting4, Setting5, Settings } from 'iconsax-react-native/src';
import Pressable from '@/components/ui/base/pressable';
import { ProductItemInterface } from 'catlog-shared';


const Search = () => {
  const navigation = useNavigation();

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Search Products',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView>
        <Container className="pt-25">
          <Row>
            <View className="flex-1 mr-8">
              <Input
                containerClasses="bg-white"
                placeholder={'Search product'}
                rightAccessory={<SearchIcon size={wp(18)} primaryColor={colors?.black.muted} />}
              />
            </View>
            <Pressable className="border border-grey-border rounded-12 p-15 -rotate-90">
              <Setting5 size={wp(20)} color={colors.black.secondary} />
            </Pressable>
          </Row>
        </Container>
      </ScrollView>
    </DashboardLayout>
  );
};

export default Search;
