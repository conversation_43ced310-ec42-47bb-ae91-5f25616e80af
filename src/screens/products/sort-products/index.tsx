import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FlatList, Image, ScrollView, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import { useNavigation } from '@react-navigation/native';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { useApi } from '@/hooks/use-api';
import {
  cx,
  delay,
  ensureUniqueItems,
  hideLoader,
  paginateArray,
  showError,
  showLoader,
  showSuccess,
  wp,
} from 'src/assets/utils/js';
import Can from 'src/components/ui/permissions/can';
import { SCOPES } from 'src/assets/utils/js/permissions';
import {
  GET_ITEMS,
  GET_SORT_ITEMS,
  GetItemsParams,
  ProductItemInterface,
  StoreInterface,
  UPDATE_SORT_ITEMS,
} from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';
import DraggableFlatList from 'react-native-draggable-flatlist';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import { ArrowLeft, ArrowRight, Box2 } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import SortProductCard, { SortProductsSkeletonLoader } from 'src/components/products/sort-products/sort-product-card';
import usePagination from 'src/hooks/use-pagination';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { ButtonVariant, TextColor } from 'src/components/ui/buttons/button';

const PER_PAGE = 10;

export interface SortItemsResponse {
  items: ProductItemInterface[];
}

const SortProducts = () => {
  const [items, setItems] = useState<Partial<ProductItemInterface>[]>([]);
  const [selected, setSelected] = useState<string[]>([]);
  const [itemsChanged, setItemsChanged] = useState(false);

  const selectedRef = useRef<string[]>(selected);
  const listRef = useRef<FlatList>(null);

  const pagesListRef = useRef<DropDownMethods>(null);

  const { store } = useAuthContext();
  const navigation = useNavigation();

  const { currentPage, goNext, goPrevious, setPage } = usePagination();
  const getSortItemsRequest = useApi<GetItemsParams, SortItemsResponse>(
    {
      key: 'fetch-products',
      apiFunction: GET_SORT_ITEMS,
      method: 'GET',
      onSuccess: response => {
        setItems(ensureUniqueItems(response?.items));
      },
    },
    { filter: {} },
  );

  const updateSortItemsRequest = useApi({ key: 'update-sort-items', apiFunction: UPDATE_SORT_ITEMS, method: 'PUT' });

  const paginatedItems = useMemo(() => {
    const data = paginateArray(items, currentPage, PER_PAGE);
    if (!itemsChanged) {
    }
    return data;
  }, [items, currentPage, itemsChanged]);

  useEffect(() => {
    selectedRef.current = selected;
  }, [selected]);

  // useEffect(() => {
  //   if (paginatedItems.totalItems > 0) {
  //     listRef.current?.scrollToIndex?.({ index: 0, animated: true });
  //   }
  // }, [paginatedItems]);

  const scrollToTop = () => {
    listRef.current?.scrollToIndex?.({ index: 0, animated: true });
  };

  const dropdownItems: DropDownItem[] = useMemo(
    () =>
      Array.from({ length: paginatedItems.totalPages }, (_, i) => i + 1)
        .filter(page => page !== currentPage)
        .map(page => ({
          label: 'Page ' + page,
          value: String(page),
          // icon: <RoundActionBtn size="sm" icon="add_circle" />,
        })),
    [paginatedItems.totalPages, currentPage],
  );

  const handleCheckboxChange = useCallback((id: string) => {
    setSelected(prevSelected => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter(item => item !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  }, []);

  const handleDragEnd = useCallback(
    async ({ data, from, to }) => {
      if (from === to) return;

      setItems(prev => {
        const pages = splitItemsIntoPages(prev, PER_PAGE);
        const currentPageIndex = currentPage - 1;

        pages[currentPageIndex] = data;

        const newList = pages.flat();

        return newList;
      });
      setItemsChanged(true);
    },
    [setItems],
  );

  const moveToPage = (page: number) => {
    // Split items into pages
    const currentSelected = selectedRef.current;
    const pages = splitItemsIntoPages(items, PER_PAGE);
    const currentPageIndex = currentPage - 1;
    const targetPageIndex = page - 1;

    let currentPageItems = pages[currentPageIndex] || [];
    let targetPageItems = pages[targetPageIndex] || [];

    let [selectedItems, updatedCurrentPageItems] = currentPageItems.reduce(
      ([selected, updated], item) =>
        currentSelected.includes(item.id) ? [[...selected, item], updated] : [selected, [...updated, item]],
      [[], []],
    );

    if (page > currentPage) {
      //remove & replace the selected items from the current page
      const removedItemsFromTargetPage = targetPageItems.splice(0, selectedItems.length, ...selectedItems);
      targetPageItems = [...removedItemsFromTargetPage, ...targetPageItems]; //add the removed items back in front of the same page - when regrouping happens they'll be automatically moved to a page before
    } else {
      const noOfItemsToMove = selectedItems.length;
      // Remove items from the end of the target page and replace with selected items
      const removedItemsFromTargetPage = targetPageItems.splice(
        targetPageItems.length - noOfItemsToMove,
        noOfItemsToMove,
        ...selectedItems,
      );
      targetPageItems = [...targetPageItems, ...removedItemsFromTargetPage];
    }

    pages[currentPageIndex] = updatedCurrentPageItems;
    pages[targetPageIndex] = targetPageItems;

    setItems(pages.flat());
    setItemsChanged(true);
    setSelected([]);
    setPage(page);
  };

  const handleUpdateSort = async () => {
    showLoader('Updating sort order...');
    const sortedItems = items.map((item, index) => ({ id: item.id, sort_index: paginatedItems.totalItems - index }));
    const [res, error] = await updateSortItemsRequest.makeRequest({ items: sortedItems });
    hideLoader();
    await delay(800);
    if (res) {
      showSuccess('Sort order updated successfully');
      setItemsChanged(false);
    } else {
      showError(error);
    }
  };

  const Header = useCallback(() => {
    return (
      <View>
        <ScreenInfoHeader
          iconElement={
            <Image
              className="w-[80px] h-[80px]"
              source={require('@/assets/images/stacked-boxes.png')}
              resizeMode={'contain'}
            />
          }
          colorPalette={ColorPaletteType.YELLOW}
          pageTitleBottom={'Sort Products'}
          isTextFollowPalette={false}
        />
        <View className="mt-10 mx-20 mb-10">
          <InfoBadge text={'Drag and drop to reorder products, select items to move them to another page'} />
          <Row className="mt-15">
            <BaseText type="heading" fontSize={15}>
              Page {currentPage}
            </BaseText>
            <View className="bg-grey-bgOne px-10 py-8 rounded-full">
              <BaseText fontSize={12} classes="text-black-secondary">
                Showing{' '}
                <BaseText fontSize={12} weight="semiBold" classes="text-black-secondary">
                  {PER_PAGE * (currentPage - 1) + 1} - {PER_PAGE * (currentPage - 1) + paginatedItems?.items?.length}
                </BaseText>{' '}
                of{' '}
                <BaseText fontSize={12} weight="semiBold" classes="text-black-secondary">
                  {items.length}{' '}
                </BaseText>
                Products
              </BaseText>
            </View>
          </Row>
        </View>
      </View>
    );
  }, [currentPage, paginatedItems]);

  const Footer = useCallback(() => {
    const total_pages = paginatedItems.totalPages;
    const prev_page = currentPage > 1 ? currentPage - 1 : undefined;
    const next_page = paginatedItems.hasNextPage;

    const getPages = (maxVisiblePages: number) => {
      const isFirstSection = currentPage <= maxVisiblePages - 1;
      const isMidSection = currentPage > maxVisiblePages - 1 && currentPage <= total_pages - (maxVisiblePages - 1);
      const isLastSection = currentPage > total_pages - (maxVisiblePages - 1);
      const isMany = total_pages > maxVisiblePages + 1;
      const pages = [{ text: '1', value: 1, isActive: currentPage === 1, isPage: true }];

      const addPage = (text: string, value: number, isActive: boolean, isPage: boolean) =>
        pages.push({ text, value, isActive, isPage });
      const addElipses = () => addPage('...', null, false, false);

      if (isMany)
        for (let i = 1; i < maxVisiblePages - 1; i++) {
          const isLastLoop = i === maxVisiblePages - 2;
          if (isFirstSection) {
            const value = i + 1;
            addPage(value.toString(), value, currentPage === i + 1, true);
            if (isLastLoop) addElipses();
          } else if (isMidSection) {
            if (i === 1) addElipses();
            const value = currentPage - 2 + i;
            addPage(value.toString(), value, currentPage === value, true);
            if (isLastLoop) addElipses();
          } else if (isLastSection) {
            if (i === 1) addElipses();
            const value = total_pages - (maxVisiblePages - (i + 1));
            addPage(value.toString(), value, currentPage === value, true);
          }
          if (isLastLoop) addPage(total_pages.toString(), total_pages, currentPage === total_pages, true);
        }
      else
        for (let i = 2; i <= total_pages; i++) {
          addPage(i.toString(), i, currentPage === i, true);
        }
      return pages;
    };

    return (
      <View className="items-end py-15 px-20 mb-40">
        <Row disableSpread style={{ gap: 10 }}>
          {prev_page && (
            <Pressable
              onPress={() => {
                goPrevious();
                scrollToTop();
              }}>
              <CircledIcon className="bg-grey-bgOne p-6 w-32 h-32">
                <ArrowLeft size={wp(18)} color={colors.black.placeholder} strokeWidth={2} />
              </CircledIcon>
            </Pressable>
          )}
          {getPages(4).map(({ value, isActive, isPage, text }, idx) => (
            <>
              {isPage ? (
                <Pressable
                  onPress={() => {
                    setPage(value);
                    scrollToTop();
                  }}>
                  <CircledIcon className={cx('bg-grey-bgOne p-6  w-32 h-32', { 'bg-primary-main': isActive })}>
                    <BaseText classes={cx({ 'text-white': isActive, 'text-black-placeholder': !isActive })}>
                      {text}
                    </BaseText>
                  </CircledIcon>
                </Pressable>
              ) : (
                <BaseText>{text}</BaseText>
              )}
            </>
          ))}
          {next_page && (
            <Pressable
              onPress={() => {
                goNext();
                scrollToTop();
              }}>
              <CircledIcon className="bg-grey-bgOne p-6  w-32 h-32">
                <ArrowRight size={wp(18)} color={colors.black.placeholder} strokeWidth={2} />
              </CircledIcon>
            </Pressable>
          )}
        </Row>
      </View>
    );
  }, [currentPage, goNext, goPrevious, setPage, paginatedItems]);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Sort Products',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <Can data={{ planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_ITEMS }}>
        <View className="flex-1">
          <DraggableFlatList
            data={paginatedItems.items}
            keyExtractor={(item, index) => item.id ?? index.toString()}
            onDragEnd={handleDragEnd}
            ref={listRef}
            ListHeaderComponent={<Header />}
            containerStyle={{ flex: 1 }}
            ListEmptyComponent={() =>
              getSortItemsRequest?.isLoading ? (
                <View className="mt-15">
                  <SortProductsSkeletonLoader />
                </View>
              ) : (
                <EmptyState
                  classes="mt-20"
                  icon={<Box2 variant="Bold" color={colors.grey.muted} />}
                  title="No Products Yet"
                  text="No products found. Add products to your store to start sorting them."
                  showBtn={false}
                />
              )
            }
            renderItem={({ item, getIndex, drag, isActive }) => (
              <SortProductCard
                item={item}
                key={getIndex()}
                onPressCheckbox={() => handleCheckboxChange(item.id)}
                onLongPress={drag}
                isSelected={selected.includes(item.id)}
                isActive={isActive}
              />
            )}
            renderPlaceholder={() => (
              <View className="px-20">
                <View className="w-full h-70 bg-grey-bgOne border-2 border-dashed border-grey-border rounded-12" />
              </View>
            )}
            // renderPlaceholder={() => <SortProductCard isPlaceholder={true} />}
            ListFooterComponent={<Footer />}
          />
        </View>
        {selected.length > 0 && (
          <FixedBtnFooter
            buttons={[
              {
                text: 'Clear Selection',
                variant: ButtonVariant.LIGHT,
                textColor: TextColor.NEGATIVE,
                onPress: () => setSelected([]),
              },

              { text: 'Move Items', onPress: () => pagesListRef.current.open() },
            ]}
          />
        )}
        {selected.length < 1 && itemsChanged && (
          <FixedBtnFooter
            buttons={[
              { text: 'Save Changes', onPress: () => handleUpdateSort(), isLoading: updateSortItemsRequest?.isLoading },
            ]}
          />
        )}
        <SelectDropdown
          showAnchor={false}
          ref={pagesListRef}
          items={dropdownItems}
          onPressItem={value => moveToPage(Number(value))}
        />
      </Can>
    </DashboardLayout>
  );
};

export default SortProducts;

const splitItemsIntoPages = (items: Partial<ProductItemInterface>[], perPage) => {
  const pages: Partial<ProductItemInterface>[][] = [];
  for (let i = 0; i < items.length; i += perPage) {
    pages.push(items.slice(i, i + perPage));
  }
  return pages;
};
