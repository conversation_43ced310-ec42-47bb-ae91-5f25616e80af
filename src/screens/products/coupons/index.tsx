import { GET_COUPONS, PaginateSearchParams, CouponItemInterface, StoreInterface } from 'catlog-shared';
import React, { useState } from 'react';
import { ScrollHandlerProcessed } from 'react-native-reanimated';

import { ensureUniqueItems } from '@/assets/utils/js';
import CouponsListSection from '@/components/products/discounts-and-coupons/coupons/coupons-list-section';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';

interface CouponsListProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

export interface CouponResponse
  extends ResponseWithPagination<{
    store: StoreInterface;
    items: CouponItemInterface[];
    total_pages: number;
    total: number;
    page: number;
  }> {}

const PER_PAGE = 10;
const CouponsList: React.FC<CouponsListProps> = ({ scrollHandler }) => {
  const [coupons, setCoupons] = useState<CouponItemInterface[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const { currentPage, goNext, setPage } = usePagination();

  const getCouponRequest = useApi<PaginateSearchParams, CouponResponse>(
    {
      apiFunction: GET_COUPONS,
      method: 'GET',
      key: 'get-coupons',
      onSuccess: response => {
        setCoupons(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const handlePullToRefresh = () => {
    setCoupons([]);

    if (currentPage === 1) {
      getCouponRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'desc',
      });
      return;
    }

    setPage(1);
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <QueryErrorBoundary
      error={getCouponRequest.error}
      isLoading={getCouponRequest.isLoading}
      refetch={handleRetry}
      isRetrying={isRetrying}
      padAround
      variant="fullPage"
      errorTitle="Failed to load coupons"
      customErrorMessage="We couldn't load your coupons. Please check your connection and try again."
    >
      <CouponsListSection
        coupons={coupons}
        setCoupons={setCoupons}
        scrollHandler={scrollHandler}
        pullToRefreshFunc={handlePullToRefresh}
        isReLoading={getCouponRequest.isReLoading}
        isLoading={getCouponRequest.isLoading}
        total_pages={getCouponRequest?.response?.data?.total_pages}
        {...{ currentPage, goNext, setPage }}
      />
    </QueryErrorBoundary>
  );
};

export default CouponsList;
