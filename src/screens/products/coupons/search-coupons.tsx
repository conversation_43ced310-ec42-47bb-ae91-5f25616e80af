import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { BaseText, Container, Row } from '@/components/ui';
import { View } from 'react-native';
import { ensureUniqueItems, getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import useModals from 'src/hooks/use-modals';
import SearchCouponsFilterModal from '@/components/search/search-coupons-filter-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import usePagination from 'src/hooks/use-pagination';
import { useApi } from 'src/hooks/use-api';
import { useFormik } from 'formik';
import SearchHeader from '@/components/search/search-header';
import { CouponResponse } from '.';
import DiscountListSection from '@/components/products/discounts-and-coupons/discounts/discount-list-section';
import CouponsListSection from '@/components/products/discounts-and-coupons/coupons/coupons-list-section';
import {
  ProductItemInterface,
  COUPON_STATUS_TAG,
  COUPON_TYPE_TAG,
  GET_COUPONS,
  GetCouponsParams,
  PaginateSearchParams,
  CouponItemInterface,
  DiscountItemInterface,
  StoreInterface,
} from 'catlog-shared';

const PER_PAGE = 10;

const SearchCoupons = () => {
  const [coupons, setCoupons] = useState<CouponItemInterface[]>([]);
  const { modals, toggleModal } = useModals(['SearchCouponsFilter']);

  const { currentPage, goNext, setPage } = usePagination();

  const getCouponRequest = useApi<GetCouponsParams, CouponResponse>(
    {
      apiFunction: GET_COUPONS,
      method: 'GET',
      key: 'search-coupons',
      onSuccess: response => {
        setCoupons(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const form = useFormik<SearchCouponsParams>({
    initialValues: {
      status: undefined,
      type: undefined,
    },
    onSubmit: values => {
      setCoupons([]);
      setPage(1);
      getCouponRequest.makeRequest({
        filter: { ...removeEmptyAndUndefined(values) },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'desc',
      });
    },
  });

  const submitForm = () => {
    toggleModal('SearchCouponsFilter', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Search Coupons',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        numberOfActiveFilters={Object.values(form.values).filter(Boolean).length}
        inputProps={{
          placeholder: 'Search for coupons',
          returnKeyType: 'search',
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
        }}
        onPressFilterButton={() => toggleModal('SearchCouponsFilter')}
      />
      <View className="flex-1">
        {coupons.length > 0 && (
          <BaseText classes="py-15 px-20 text-grey-mutedDark">
            {getCouponRequest?.response?.data?.total ?? 0} Coupons found
          </BaseText>
        )}
        <CouponsListSection
          coupons={coupons}
          setCoupons={setCoupons}
          isReLoading={getCouponRequest.isReLoading}
          isLoading={getCouponRequest.isLoading}
          total_pages={getCouponRequest?.response?.data?.total_pages}
          {...{ currentPage, goNext, setPage }}
        />
      </View>
      <SearchCouponsFilterModal
        onPressContinue={submitForm}
        isVisible={modals.SearchCouponsFilter}
        form={form}
        closeModal={() => toggleModal('SearchCouponsFilter', false)}
      />
    </DashboardLayout>
  );
};

export default SearchCoupons;

export interface SearchCouponsParams {
  search?: string;
  status?: COUPON_STATUS_TAG;
  type?: COUPON_TYPE_TAG;
}
