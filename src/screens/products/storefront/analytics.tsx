import { GET_PRODUCT_ANALYTICS, ProductAnalyticsInterface, ProductItemInterface } from 'catlog-shared';
import { useEffect, useState } from 'react';
import { ScrollView, View, Pressable, Image } from 'react-native';
import { ChevronDown } from 'src/components/ui/icons';

import { BaseText, Row, SelectionPill } from '@/components/ui';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useApi } from '@/hooks/use-api';
import colors from '@/theme/colors';
import ProductAnalyticsCards from 'src/components/products/product-analytics-cards';
import { RefreshControl } from 'react-native-gesture-handler';
import SectionContainer from 'src/components/ui/section-container';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import { useNavigation } from '@react-navigation/native';
import Shimmer from 'src/components/ui/shimmer';
import { hp, wp } from 'src/assets/utils/js';
import SectionEmptyState from 'src/components/ui/empty-states/section-empty-state';
import { Box1 } from 'iconsax-react-native/src';



const ProductAnalytics = () => {
  const [activeInsightTab, setActiveInsightTab] = useState<'top_sellers' | 'slowest_moving'>('top_sellers');
  const [activeInventoryTab, setActiveInventoryTab] = useState<'out_of_stock' | 'low_stock'>('out_of_stock');
  const navigation = useNavigation();

  const getStatistics = useApi<any, { data: ProductAnalyticsInterface }>({
    key: GET_PRODUCT_ANALYTICS.name,
    apiFunction: GET_PRODUCT_ANALYTICS,
    method: 'GET',
    // autoRequest: false,
  });

  // useEffect(() => {
  //   getStatistics.makeRequest({});
  // }, []);

  const stats = getStatistics?.response?.data;

  const renderProductItem = (product: ProductItemInterface, rightText: string, rightTextColor: StatusType) => (
    <Pressable key={product.id} onPress={() => navigation.navigate('ProductDetails', { id: product.id })}>
      <Row className="py-15 border-b flex-1 border-grey-border" style={{ flex: 1 }}>
        <Row spread={false}>
          <Image
            source={{ uri: product?.images![0] }}
            className="w-40 h-40 rounded-[10px]"
            style={{ resizeMode: 'cover' }}
          />
          <View className=" ml-10">
            <BaseText fontSize={12} weight="regular" classes="mb-4 text-black-muted">
              {product.name}
            </BaseText>
            <BaseText fontSize={12} weight="medium" classes="text-black-secondary">
              NGN {product.price?.toLocaleString()}
            </BaseText>
          </View>
        </Row>
        <View className="">
          <StatusPill statusType={rightTextColor} title={rightText} />
        </View>
      </Row>
    </Pressable>
  );

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Product Analytics',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {/* pull to referesh  */}
      <ScrollView
        className="flex-1"
        refreshControl={<RefreshControl refreshing={false} onRefresh={() => getStatistics.makeRequest({})} />}
        scrollIndicatorInsets={{ right: 1 }}>
        <View className="pt-20 mx-20">
          <Row className="mb-15">
            <Row>
              <BaseText fontSize={15} weight="bold" type="heading">
                Overview
              </BaseText>
              <ChevronDown size={15} primaryColor={colors.grey.muted} />
            </Row>
          </Row>
          <ProductAnalyticsCards loading={getStatistics.isLoading} analytics={stats} />

          <View className="mt-30">
            <BaseText fontSize={15} weight="bold" type="heading" classes="mb-15">
              Performance Insights
            </BaseText>

            <Row spread={false} className="mb-10">
              <SelectionPill
                key={0}
                className={activeInsightTab ? 'opacity-20' : ''}
                onPress={() => setActiveInsightTab('top_sellers')}
                selected={activeInsightTab === 'top_sellers'}
                title={'Top Sellers'}
              />
              <SelectionPill
                key={1}
                className={activeInsightTab ? 'opacity-20' : ''}
                onPress={() => setActiveInsightTab('slowest_moving')}
                selected={activeInsightTab === 'slowest_moving'}
                title={'Slowest-moving products'}
              />
            </Row>

            <SectionContainer>
              {getStatistics.isLoading ? (
                <>
                  {Array.from({ length: 3 }, (_, index) => (
                    <ProductCardShimmer key={index} />
                  ))}
                </>
              ) : (
                <>
                  {activeInsightTab === 'top_sellers' && (
                    <>
                      {stats?.top_selling_items && stats.top_selling_items.length > 0 ? (
                        stats.top_selling_items.map(product =>
                          renderProductItem(product, `${product.total_orders || 0} ORDERS`, StatusType.DEFAULT),
                        )
                      ) : (
                        <SectionEmptyState
                        
                          icon={<Box1 variant="Bulk" size={wp(30)} color={colors.grey.muted} />}
                          text="No top selling products yet"
                        />
                      )}
                    </>
                  )}
                  {activeInsightTab === 'slowest_moving' && (
                    <>
                      {stats?.slowest_moving_items && stats.slowest_moving_items.length > 0 ? (
                        stats.slowest_moving_items.map(product =>
                          renderProductItem(product, `${product.total_orders || 0} ORDERS`, StatusType.PROCESSING),
                        )
                      ) : (
                        <SectionEmptyState
                          icon={<Box1 variant="Bulk" size={wp(30)} color={colors.grey.muted} />}
                          text="No slowest moving products data"
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </SectionContainer>
          </View>

          <View className="mt-30 mb-40">
            <BaseText fontSize={15} weight="bold" type="heading" classes="mb-15">
              Inventory Alerts
            </BaseText>

            <Row spread={false} className="mb-10">
              <SelectionPill
                key={0}
                className={activeInventoryTab ? 'opacity-20' : ''}
                onPress={() => setActiveInventoryTab('out_of_stock')}
                selected={activeInventoryTab === 'out_of_stock'}
                title={'Out of Stock'}
              />
              <SelectionPill
                key={1}
                className={activeInventoryTab ? 'opacity-20' : ''}
                onPress={() => setActiveInventoryTab('low_stock')}
                selected={activeInventoryTab === 'low_stock'}
                title={'Low Stock'}
              />
            </Row>

            <SectionContainer>
              {getStatistics.isLoading ? (
                <>
                  {Array.from({ length: 3 }, (_, index) => (
                    <ProductCardShimmer key={index} />
                  ))}
                </>
              ) : (
                <>
                  {activeInventoryTab === 'out_of_stock' && (
                    <>
                      {stats?.out_stock_of_items && stats.out_stock_of_items.length > 0 ? (
                        stats.out_stock_of_items.map((product: any) => {
                          const computed = product.out_of_stock_variants
                            ? product.out_of_stock_variants.length
                            : product.quantity;
                          const hasVariants = product.out_of_stock_variants?.length > 0;
                          const rightText = hasVariants ? `${computed} Options` : `${computed} LEFT`;
                          return renderProductItem(product, rightText, StatusType.DANGER);
                        })
                      ) : (
                        <SectionEmptyState
                          icon={<Box1 variant="Bulk" size={wp(30)} color={colors.grey.muted} />}
                          text="No out of stock products"
                        />
                      )}
                    </>
                  )}

                  {activeInventoryTab === 'low_stock' && (
                    <>
                      {stats?.low_stock_items_details && stats.low_stock_items_details.length > 0 ? (
                        stats.low_stock_items_details.map((product: any) => {
                          const computed = product.low_stock_variants ? product.low_stock_variants.length : product.quantity;
                          const hasVariants = product.low_stock_variants?.length > 0;
                          const rightText = hasVariants ? `${computed} Options` : `${computed} LEFT`;
                          return renderProductItem(product, rightText, StatusType.DEFAULT);
                        })
                      ) : (
                        <SectionEmptyState
                          icon={<Box1 variant="Bulk" size={wp(30)} color={colors.grey.muted} />}
                          text="No low stock products"
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </SectionContainer>
          </View>
        </View>
      </ScrollView>
    </DashboardLayout>
  );
};

export default ProductAnalytics;
const ProductCardShimmer = () => (
  <View className="py-15 border-b border-grey-border">
    <Row className="flex-1">
      <Row spread={false}>
        <Shimmer
          width={wp(40)}
          height={hp(40)}
          borderRadius={10}
        />
        <View className="ml-10">
          <Shimmer
            width={wp(120)}
            height={hp(12)}
            borderRadius={6}
            className="mb-4"
          />
          <Shimmer
            width={wp(80)}
            height={hp(12)}
            borderRadius={6}
            marginTop={10}
          />
        </View>
      </Row>
      <View>
        <Shimmer
          width={wp(60)}
          height={hp(20)}
          borderRadius={12}
        />
      </View>
    </Row>
  </View>
);