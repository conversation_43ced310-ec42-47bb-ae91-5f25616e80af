import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useFormik } from 'formik';
import { useState } from 'react';
import SearchHeader from '@/components/search/search-header';
import SearchProductFilterModal from '@/components/search/search-product-filter-modal';
import useModals from 'src/hooks/use-modals';
import { StorefrontProducts } from '../storefront';
import { getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import {
  GetItemsParams,
  PRODUCT_AVAILABILITY_TAG,
  PRODUCT_DISCOUNT_TAG,
  PRODUCT_FEATURED_TAG,
  PRODUCT_VARIANT_TAG,
} from 'catlog-shared';
import { View } from 'react-native';

const SearchProducts = () => {
  const { modals, toggleModal } = useModals(['searchProductFilter']);
  const [filters, setFilters] = useState<GetItemsParams['filter']>({});
  const validFilters = removeEmptyAndUndefined(filters);

  const form = useFormik<SearchProductsParams>({
    initialValues: {
      search: '',
      categories: undefined,
      availability: undefined,
      variants: undefined,
      featured_product: undefined,
      discount: undefined,
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  const handleClearSearch = () => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    form.setFieldValue('search', '');
  };

  const submitForm = () => {
    toggleModal('searchProductFilter', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Search Products',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{ ...getFieldvalues('search', form), onSubmitEditing: () => form.submitForm() }}
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressClear={handleClearSearch}
        showFilter
        showClear={form.values?.search?.length > 0}
        onPressFilterButton={() => toggleModal('searchProductFilter')}
      />
      <View className="pt-10 flex-1">
        <StorefrontProducts externalFilters={validFilters} />
      </View>
      <SearchProductFilterModal
        isVisible={modals.searchProductFilter}
        onPressContinue={submitForm}
        form={form}
        closeModal={() => toggleModal('searchProductFilter', false)}
      />
    </DashboardLayout>
  );
};

export default SearchProducts;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};

export interface SearchProductsParams {
  search?: string;
  categories?: string[];
  availability?: PRODUCT_AVAILABILITY_TAG;
  variants?: PRODUCT_VARIANT_TAG;
  featured_product?: PRODUCT_FEATURED_TAG;
  discount?: PRODUCT_DISCOUNT_TAG;
}
