import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import CreateProductsController from '@/components/products/create-products/controller';
import { ProductUploadStep } from '@/components/products/create-products/types';

const CreateProducts = () => {
  const [currentStep, setCurrentStep] = useState<ProductUploadStep>('method');
  const [currentProduct, setCurrentProduct] = useState(0);
  const [isImport, setIsImport] = useState(false);
  const maxUploadable = 10;

  const navigation = useNavigation();

  const goBack = () => {
    switch (currentStep) {
      case 'method':
        navigation.goBack();
        break;
      case 'images':
        setCurrentStep('method');
        break;
      case 'import':
        setCurrentStep('method');
        break;
      case 'details':
        setCurrentStep(isImport ? 'method' : 'images');
        break;
      case 'response':
        navigation.goBack();
        break;
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: currentStep === 'details' ? `Product ${currentProduct + 1}` : pageTitleStepMap[currentStep],
        variant: HeaderVariants.SUB_LEVEL,
        onBackPress: goBack,
      }}>
      <CreateProductsController
        {...{
          maxUploadable,
          setIsImport,
          currentStep,
          setCurrentStep,
          currentProduct,
          setCurrentProduct,
          success: { label: 'View all products', route: '/products' },
        }}
      />
      {/* <ProductAddingMethodController
        screen={ProductAddingScreenType.ADD_PRODUCT}
        ScreenInfoHeaderProps={{
          pageTitleTop: 'Add New',
          pageTitleBottom: 'Storefront Products',
          colorPalette: ColorPaletteType.YELLOW,
        }}
      /> */}
    </DashboardLayout>
  );
};

const pageTitleStepMap = {
  method: 'Add Products',
  images: 'Upload Images',
  import: 'Import Products',
  details: 'Product Details',
  response: 'Products Uploaded',
};

export default CreateProducts;
