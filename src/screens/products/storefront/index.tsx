import ProductCard from '@/components/products/storefront-products/product-card';
import ProductsSkeletonLoader from '@/components/products/storefront-products/storefront-skeleton-loader';
import { BaseText } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import useAuthContext from '@/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import { useNavigation } from '@react-navigation/native';
import { delay, ensureUniqueItems, updateOrDeleteItemFromList, wp } from 'src/assets/utils/js';
import WebView from 'react-native-webview';
import {
  GET_ITEMS,
  GetItemsParams,
  ProductItemInterface,
  StoreInterface,
  getProductOptionsLength
} from 'catlog-shared';
import React, { useEffect, useState } from 'react';
import { RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import useEventListener from 'src/hooks/use-event-emitter';
import { Box } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';

interface StorefrontProductsProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  externalFilters?: GetItemsParams['filter'];
}

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}
const PER_PAGE = 10;

const StorefrontProducts: React.FC<StorefrontProductsProps> = ({ scrollHandler, externalFilters }) => {
  const [products, setProducts] = useState<ProductItemInterface[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  const navigation = useNavigation();
  const { store } = useAuthContext();
  const { currentPage, goNext, setPage } = usePagination();

  const fromFiltersPage = externalFilters !== undefined;

  useEventListener('removeProduct', p => {
    setProducts(updateOrDeleteItemFromList(products, 'id', p.id, null));
  });
  
  useEventListener('productUpdate', p => {
    setProducts(updateOrDeleteItemFromList(products, 'id', p.id, p));
  });

  // useEventListener('productCreate', p => {
  //   setProducts(prev => ensureUniqueItems([p, ...prev]));
  // });

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      key: 'fetch-products',
      apiFunction: GET_ITEMS,
      method: 'GET',
      onSuccess: response => {
        setProducts(prev => ensureUniqueItems([...prev!, ...response?.data?.items]));
      },
    },
    {
      filter: { store: store?.id, ...(externalFilters ?? {}) },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'asc',
    },
  );

  useEffect(() => {
    const fn = async () => {
      await delay(1000);
      setProducts([]);
      setPage(1);
      getProductsRequest.refetch();
    };
    fn();
  }, [externalFilters]);

  useEffect(() => {
    setProducts([]);
    setPage(1);
    getProductsRequest.refetch();
  }, [store?.id]);

  const handlePullToRefresh = () => {
    try {
      setProducts([]);
      if (currentPage === 1) {
        getProductsRequest.reset();
        return;
      }
      setPage(1);
    } catch (error) {
    }
  };

  const updateProductCallback = (activeKey: string, updatedData?: Partial<ProductItemInterface>) => {
    setProducts(updateOrDeleteItemFromList(products, 'id', activeKey, updatedData ?? null));
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <QueryErrorBoundary
          padAround
          error={getProductsRequest.error}
          isLoading={getProductsRequest.isLoading}
          isRetrying={isRetrying}
          refetch={handleRetry}
          variant="fullPage"
          errorTitle='Failed to load products'
          customErrorMessage="We couldn't load your products. Please check your connection and try again."
        >
          <Animated.FlatList
          data={products}
          numColumns={2}
          keyExtractor={(item, index) => item.id + index}
          onScroll={scrollHandler}
          scrollEventThrottle={20}
          refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={() =>
            getProductsRequest?.isLoading ? (
              <ProductsSkeletonLoader />
            ) : (
              <EmptyState
                icon={<Box variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                showBtn={!fromFiltersPage}
                btnText={'Add Product'}
                text={fromFiltersPage ? 'No products to show' : 'No other products added yet.'}
              />
            )
          }
          ListHeaderComponent={() =>
            fromFiltersPage &&
            products.length > 0 && (
              <View className="py-10 px-5">
                <BaseText classes="text-grey-mutedDark">
                  {' '}
                  {getProductsRequest?.response?.total} Products found{' '}
                </BaseText>
              </View>
            )
          }
          className="flex-1 px-10 pb-40"
          contentContainerStyle={{ flexGrow: 1 }}
          onEndReached={() => {
            if (!getProductsRequest?.isLoading && currentPage < getProductsRequest?.response?.total_pages) {
              goNext(getProductsRequest?.response?.total_pages);
            }
          }}
          renderItem={({ item }) => (
            <ProductCard
              product={item}
              onPress={() => navigation.navigate('ProductDetails', { id: item.id! })}
              key={item.id}
              tagText={
                item?.variants?.options?.length > 0 ? `${getProductOptionsLength(item.variants)} options` : undefined
              }
              updateProductCallback={updateProductCallback}
            />
          )}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {getProductsRequest?.isLoading && (
                <View className="mt-5">
                  <ProductsSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
        </QueryErrorBoundary>
      </View>
      <FAB onPress={() => navigation.navigate('CreateProducts')} />
    </View>
  );
};
export { StorefrontProducts };

export default React.memo(StorefrontProducts);
