import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ArrowCircleLeft2, Notepad, Star1, TickCircle, Trash } from 'iconsax-react-native/src';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, FlatList, Image } from 'react-native';
import { SafeAreaView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { alertPromise, hideLoader, hp, showLoader, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Header, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { Star } from '@/components/ui/icons';
import MoreOptions, { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import colors from '@/theme/colors';
import cx from 'classnames';
import useStatusbar from '@/hooks/use-statusbar';
import useRouteParams from '@/hooks/use-route-params';
import CustomImage from '@/components/ui/others/custom-image';
import { useApi } from 'src/hooks/use-api';
import { EDIT_ITEM } from 'node_modules/catlog-shared/dist';
import Toast from 'node_modules/react-native-toast-message/lib';
import eventEmitter from 'src/assets/utils/js/event-emitter';

const { width } = Dimensions.get('window');

const ProductImages = () => {
  const [images, setImages] = useState<string[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(null);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const { setStatusBar } = useStatusbar();
  const navigation = useNavigation();
  const { top, bottom } = useSafeAreaInsets();

  setStatusBar('light', 'transparent', true);
  const params = useRouteParams<'ProductImages'>();

  const editItemRequest = useApi({
    apiFunction: EDIT_ITEM,
    method: 'PUT',
    key: 'edit-product',
  });

  useEffect(() => {
    if (params?.activeIndex) {
      setTimeout(() => {
        setCarouselIndex(params?.activeIndex);
        handleScrollToIndex(params?.activeIndex);
      }, 100);
    }
  }, [params?.activeIndex]);

  useEffect(() => {
    if (params?.images) {
      setImages(params?.images);
      setThumbnailIndex(params?.thumbnail);
    }
  }, [params]);

  const handleScrollToIndex = (index: number) => {
    if (flatListRef.current) {
      flatListRef.current.scrollToItem({ item: params?.images[index], animated: false });
    }
  };

  const updateItem = async (data: any, callBack?: VoidFunction, loadingMessage?: string) => {
    showLoader(loadingMessage ?? 'Updating Item');
    const [res, error] = await editItemRequest.makeRequest({ id: params?.itemId, item: data });
    hideLoader();
    if (error) {
      Toast.show({ text1: 'Error updating product Images', type: 'error' });
      return;
    }

    // const updatedProduct = { ...res.data, category: categories?.find(c => res.data.category === c.id) };
    // updateProduct?.(updatedProduct);
    eventEmitter.emit('productImage', res.data);

    callBack && callBack();
  };

  const changeThumbnail = async () => {
    if (carouselIndex === thumbnailIndex) return;

    const alertResponse = await alertPromise(
      'Change product thumbnail',
      `Clicking on "Yes, Change" would update this image to product thumbnail`,
      'Yes, Change',
      'Cancel',
      false,
    );
    if (alertResponse === false) {
      return;
    }

    updateItem(
      {
        thumbnail: carouselIndex,
      },
      () => {
        setThumbnailIndex(carouselIndex);
        // setProduct({ ...product, thumbnail: index });
      },
    );
  };

  const removePickedImage = () => {
    if (carouselIndex === thumbnailIndex) {
      Toast.show({ text1: 'Cannot delete thumbnail image.', type: 'error' });
      return;
    }

    const imagesCopy = [...images];

    imagesCopy.splice(carouselIndex, 1);

    updateItem(
      {
        images: imagesCopy,
      },
      () => {
        setImages(imagesCopy)
      },
      'Removing Image',
    );
  };

  const moreOptions: MoreOptionElementProps[] = [
    ...(thumbnailIndex !== carouselIndex
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<Star primaryColor={colors?.black.placeholder} width={wp(14)} height={hp(14)} />}
                label="Make Thumbnail"
              />
            ),
            title: 'Make Thumbnail',
            onPress: () => changeThumbnail(),
          },
        ]
      : []),
    {
      optionElement: (
        <OptionWithIcon icon={<Notepad size={wp(15)} color={colors.black.placeholder} />} label="Download Image" />
      ),
      title: 'Download Image',
      onPress: () => {},
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label="Delete Image" />
      ),
      title: 'Delete Image',
      onPress: removePickedImage,
    },
  ];

  const onViewCallBack = useCallback((viewableItems: any) => {
    if (viewableItems?.viewableItems) {
      setCarouselIndex(viewableItems?.viewableItems[0]?.index);
    }
  }, []);

  return (
    <View className="flex-1" style={{ backgroundColor: '#222222f7' }}>
      <View
        className={`flex-row justify-between items-center px-20 pb-16`}
        style={{ paddingTop: top + 16, backgroundColor: '#1E1E1E' }}>
        <Pressable
          className="rounded-full bg-white py-8 pl-8 pr-16 flex-row items-center justify-center"
          onPress={() => navigation.goBack()}
          style={{ backgroundColor: '#ffffff10' }}>
          <ArrowCircleLeft2 variant="Bold" color={colors?.white} size={wp(18)} />
          <BaseText classes="text-white ml-4">Go Back</BaseText>
        </Pressable>
        <Pressable
          className="p-10 h-40 w-40  rounded-full flex items-center justify-center"
          style={{ backgroundColor: '#ffffff10' }}>
          <MoreOptions options={moreOptions} />
        </Pressable>
      </View>
      <View className="flex-1 items-center justify-center">
        <FlatList
          data={images}
          ref={flatListRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          onViewableItemsChanged={onViewCallBack}
          pagingEnabled
          onScrollToIndexFailed={info => {
            const wait = new Promise(resolve => setTimeout(resolve, 30));
            wait.then(() => {
              flatListRef.current?.scrollToIndex({
                index: info.index,
                animated: false,
              });
            });
          }}
          renderItem={({ item, index }) => (
            <View className="flex-1 items-center justify-center">
              <View className="w-full h-full" style={{ width }}>
                <CustomImage
                  imageProps={{ source: item, contentFit: 'contain' }}
                  className="w-full h-full"
                  style={{ backgroundColor: 'transparent' }}
                />
              </View>
            </View>
          )}
        />
      </View>
      <View
        className={`flex-row justify-between items-center px-20 pt-16 border-t border-t-grey-border`}
        style={{ paddingBottom: bottom, backgroundColor: '#1E1E1E' }}>
        <BaseText classes="text-white">
          {carouselIndex + 1} of {images?.length} images
        </BaseText>
        <View
          className={cx('rounded-full bg-accentGreen-pastel py-8 px-10 flex-row items-center justify-center', {
            'opacity-0': carouselIndex !== thumbnailIndex,
            'opacity-1': carouselIndex === thumbnailIndex,
          })}>
          <BaseText classes="text-accentGreen.main mr-4 font-interMedium text-accentGreen-main">
            Current Thumbnail
          </BaseText>
          <Star primaryColor={colors?.accentGreen.main} width={wp(14)} height={hp(14)} />
        </View>
      </View>
    </View>
  );
};

export default ProductImages;
