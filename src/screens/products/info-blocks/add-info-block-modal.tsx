import { ScrollView, Text, View } from 'react-native';
import { useFormik } from 'formik';
import { useApi } from '@/hooks/use-api';
import {
  delay,
  getFieldvalues,
  hideLoader,
  showError,
  showLoader,
  showSuccess,
  updateOrDeleteItemFromList,
  wp,
} from 'src/assets/utils/js';
import * as Yup from 'yup';
import useAuthContext from 'src/contexts/auth/auth-context';
import Toast from 'react-native-toast-message';
import {
  UpdateCustomItemParams,
  CustomItemInterface,
  InfoBlockInterface,
  CREATE_STORE_INFO_BLOCK,
  UPDATE_STORE_INFO_BLOCKS,
  UpdateStoreInfoBlocksParams,
} from 'catlog-shared';
import MoneyInput from 'src/components/ui/inputs/money-input';
import Input from 'src/components/ui/inputs/input';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import { useEffect, useRef, useState } from 'react';
import { Image } from 'src/@types/utils';
import useImageUploads from 'src/hooks/use-file-uploads';
import { Row } from 'src/components/ui';
import CustomImage from 'src/components/ui/others/custom-image';
import Pressable from 'src/components/ui/base/pressable';
import { Plus } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import * as ImagePicker from 'expo-image-picker';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import ImageCard from 'src/components/ui/cards/image-card';

interface AddInfoBlockModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  onPressSave?: VoidFunction;
  activeItem?: InfoBlockInterface;
  setInfoBlocks: React.Dispatch<React.SetStateAction<InfoBlockInterface[]>>;
}

const AddInfoBlockModal = ({
  closeModal,
  onPressSave,
  setInfoBlocks,
  activeItem,
  ...props
}: AddInfoBlockModalProps) => {
  const inputRef = useRef<any>(null);
  const updateInfoBlockRequest = useApi<UpdateStoreInfoBlocksParams>({
    key: UPDATE_STORE_INFO_BLOCKS.name,
    apiFunction: UPDATE_STORE_INFO_BLOCKS,
    method: 'PUT',
  });

  const createInfoBlockRequest = useApi<InfoBlockInterface>({
    key: CREATE_STORE_INFO_BLOCK.name,
    apiFunction: CREATE_STORE_INFO_BLOCK,
    method: 'POST',
  });

  const isEdit = Object.entries(activeItem).length > 0;

  const form = useFormik<InfoBlockInterface>({
    initialValues: {
      title: activeItem?.title ?? '',
      text_content: activeItem?.text_content ?? '',
      content_type: activeItem?.content_type ?? null,
      image_content: activeItem?.image_content ?? [],
      is_visible: activeItem?.is_visible ?? true,
    },
    validationSchema,
    onSubmit: async values => {
      if (values.content_type === 'IMAGES' && values.image_content.length < 1) {
        showError(undefined, 'At least one image URL is required');
        return;
      }

      showLoader(`${isEdit ? 'Updating info block...' : 'Creating info block...'}`);
      let response: any;
      let error: any;

      if (!isEdit) {
        [response, error] = await createInfoBlockRequest.makeRequest(form.values);
      }

      if (isEdit) {
        [response, error] = await updateInfoBlockRequest.makeRequest({ id: activeItem.id, block: form.values });
      }

      hideLoader();
      await delay(700);
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors(error.fields);
        }
      }

      if (response) {
        if (isEdit) {
          form.resetForm();
          closeModal?.();
          setInfoBlocks?.(prev => updateOrDeleteItemFromList(prev, 'id', activeItem?.id, response.data));
          showSuccess('Info block updated successfully');
        }

        if (!isEdit) {
          form.resetForm();
          closeModal?.();
          setInfoBlocks?.(prev => [response.data, ...prev]);
          showSuccess('Info block created successfully');
        }
      }
    },
  });


  const [images, setImages] = useState<Image[]>(
    form.values.image_content.map((url, idx) => ({
      url,
      key: idx.toString(),
      file: null,
      isUploading: false,
      uploadProgress: 100,
      name: '',
      src: '',
      lastModified: 0,
    })),
  );
  useImageUploads(images, setImages);

  useEffect(() => {
    form.setFieldValue(
      'image_content',
      images.map(img => img.url),
    );
  }, [images]);

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      aspect: [1, 1],
      quality: 0.5,
      allowsMultipleSelection: true,
      selectionLimit: 5,
      // base64: true,
    });

    if (!result.canceled) {
      const newImages = result.assets.map((item, index) => {
        const uriParts = item.uri.split('/');
        const name = uriParts[uriParts.length - 1];
        const currentTime = new Date().getTime();
        return {
          file: null,
          url: '',
          name: item.fileName ?? name,
          src: item.uri,
          key: item.assetId ?? currentTime.toString(),
          isUploading: false,
          uploadProgress: 0,
          error: false,
          newFile: true,
        };
      });
      setImages([...images, ...newImages]);
      showLoader('Uploading images...', false, true);
    }
  };

  const handleSaveUpdates = () => {
    form.handleSubmit();
  };

  return (
    <BottomModal
      {...props}
      onModalShow={() => form.resetForm()}
      closeModal={closeModal}
      enableDynamicSizing
      enableSnapPoints={false}
      useChildrenAsDirectChild
      buttons={[
        {
          text: isEdit ? 'Update Block' : 'Create Block',
          onPress: handleSaveUpdates,
          isLoading: updateInfoBlockRequest?.isLoading || createInfoBlockRequest?.isLoading,
          loadingText: isEdit ? 'Updating Block...' : 'Creating Block...',
        },
      ]}
      title={isEdit ? `Edit ${activeItem?.title ? activeItem?.title : ''}` : 'Create New Product Info Block'}>
      <BottomSheetScrollView enableFooterMarginAdjustment>
        <View className="px-20 pt-5">
          <Input label={'Block Name'} autoFocus useBottomSheetInput {...getFieldvalues('title', form)} />
          <SelectDropdown
            label="Content Type"
            containerClasses="mt-15"
            items={[
              { value: 'TEXT', label: 'Text' },
              { value: 'IMAGES', label: 'Images' },
            ]}
            {...getFieldvalues('content_type', form, 'select')}
          />
          {form.values.content_type === 'TEXT' && (
            <Pressable onPress={() => inputRef.current.focus()}>
              <Input
                ref={inputRef}
                label={'Block Content'}
                multiline
                className="min-h-[100px]"
                useBottomSheetInput
                containerClasses="mt-15"
                {...getFieldvalues('text_content', form)}
              />
            </Pressable>
          )}
          {form.values.content_type === 'IMAGES' && (
            <Row className="flex-wrap justify-start mt-15" style={{ gap: wp(10) }}>
              {images.map((image, idx) => (
                <ImageCard key={image.key} image={image} index={idx} onRemove={removeImage} />
              ))}
              <Pressable
                className="rounded-12 w-80 h-80  border border-grey-border border-dashed items-center justify-center"
                // onPress={pickImage}
                onPress={() => pickMultipleImages(images, setImages)}>
                <Plus primaryColor={colors.black.placeholder} height={wp(24)} width={wp(24)} strokeWidth={1.5} />
              </Pressable>
            </Row>
          )}
          <View className="h-40" />
        </View>
      </BottomSheetScrollView>
    </BottomModal>
  );
};

export default AddInfoBlockModal;

const validationSchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  content_type: Yup.string().oneOf(['TEXT', 'IMAGES'], 'Invalid content type').required('Content type is required'),
  // text_content: Yup.string().when('content_type', {
  //   is: 'TEXT',
  //   then: Yup.string().required('Text content is required'),
  //   otherwise: Yup.string().notRequired(),
  // }),
  text_content: Yup.string().when('content_type', {
    is: 'TEXT',
    then: schema => schema.required('Text content is required'),
    otherwise: schema => schema.nullable(),
  }),
});
