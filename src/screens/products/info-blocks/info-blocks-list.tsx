import {
  PaginateSearchParams,
  CouponItemInterface,
  StoreInterface,
  GET_STORE_INFO_BLOCKS,
  InfoBlockInterface,
} from 'catlog-shared';
import React, { useState } from 'react';
import { ScrollHandlerProcessed } from 'react-native-reanimated';

import { delay, ensureUniqueItems } from '@/assets/utils/js';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import InfoBlocksListSection from './info-blocks-list-section';
import Can from 'src/components/ui/permissions/can';
import { SCOPES } from 'src/assets/utils/js/permissions';
import SubscriptionPermissionFallback from 'src/components/ui/permissions/subscription-permission-fallback';

interface InfoBlocksListProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

interface InfoBlock extends InfoBlockInterface {
  id: string;
}

export interface InfoBlocksResponse
  extends ResponseWithPagination<{
    // store: StoreInterface;
    info_blocks: InfoBlock[];
    total_pages: number;
    total: number;
    page: number;
  }> {}

const PER_PAGE = 10;
const InfoBlocksList: React.FC<InfoBlocksListProps> = ({ scrollHandler }) => {
  const [infoBlocks, setInfoBlocks] = useState<InfoBlock[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const { currentPage, goNext, setPage } = usePagination();

  const getInfoBlocksRequest = useApi<PaginateSearchParams, InfoBlocksResponse>(
    {
      apiFunction: GET_STORE_INFO_BLOCKS,
      method: 'GET',
      key: GET_STORE_INFO_BLOCKS.name,
      onSuccess: response => {
        setInfoBlocks(prev => ensureUniqueItems([...prev, ...response?.data?.info_blocks]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const handlePullToRefresh = async () => {
    if (currentPage === 1) {
      getInfoBlocksRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'desc',
      });
      await delay(800);
      setInfoBlocks([]);
      return;
    }

    setPage(1);
    await delay(600);
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <Can
      data={{ planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_HIGHLIGHTS }}
      fallback={<SubscriptionPermissionFallback actionName="create product info blocks" />}>
      <QueryErrorBoundary
        error={getInfoBlocksRequest.error}
        isLoading={getInfoBlocksRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        padAround
        variant="fullPage"
        errorTitle="Failed to load Info blocks"
        customErrorMessage="We couldn't load your Info blocks. Please check your connection and try again.">
        <InfoBlocksListSection
          scrollHandler={scrollHandler}
          {...{
            goNext,
            setPage,
            scrollHandler,
            currentPage,
            setInfoBlocks,
            infoBlocks,
            isLoading: getInfoBlocksRequest.isLoading,
            pullToRefreshFunc: handlePullToRefresh,
            error: getInfoBlocksRequest.error,
          }}
        />
      </QueryErrorBoundary>
    </Can>
  );
};

export default InfoBlocksList;
