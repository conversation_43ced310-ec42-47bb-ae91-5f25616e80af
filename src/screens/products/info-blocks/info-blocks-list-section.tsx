import { useNavigation } from '@react-navigation/native';
import {
  DELETE_COUPON,
  UPDATE_COUPON,
  CouponItemInterface,
  StoreInterface,
  InfoBlockInterface,
  DELETE_STORE_INFO_BLOCK,
} from 'catlog-shared';
import { Box, Box1, BoxAdd, Edit2, InfoCircle, TicketDiscount, Trash } from 'iconsax-react-native/src';
import React, { useRef, useState } from 'react';
import { Alert, View, RefreshControl } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import {
  delay,
  formatDate,
  hideLoader,
  hp,
  showError,
  showLoader,
  showSuccess,
  toCurrency,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import CouponDetailsModal from '@/components/products/discounts-and-coupons/coupons/coupon-details-modal';
import EditCouponModal from '@/components/products/discounts-and-coupons/coupons/edit-coupon-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import MoreOptions, { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import Row from '@/components/ui/row';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from 'src/components/ui/base/pressable';
import Shimmer from 'src/components/ui/shimmer';
import CustomImage from 'src/components/ui/others/custom-image';
import { MoreHorizontal } from 'src/components/ui/icons';
import AddInfoBlockModal from './add-info-block-modal';
import AssignProductManager, { AssignProductManagerRef } from './assign-product-manager';

interface InfoBlock extends InfoBlockInterface {
  id: string;
}

interface InfoBlocksListSectionProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  infoBlocks: InfoBlock[];
  setInfoBlocks: React.Dispatch<React.SetStateAction<InfoBlock[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading?: boolean;
  isLoading?: boolean;
  total_pages?: number;
  currentPage?: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  error?: any;
}

const InfoBlocksListSection: React.FC<InfoBlocksListSectionProps> = ({
  scrollHandler,
  infoBlocks,
  pullToRefreshFunc,
  setInfoBlocks,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  error,
}) => {
  const [activeBlock, setActiveBlock] = useState<InfoBlock>({} as InfoBlock);
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['addInfoBlockModal']);

  const productManagerRef = useRef<AssignProductManagerRef>(null);

  const deleteInfoBlockRequest = useApi({
    apiFunction: DELETE_STORE_INFO_BLOCK,
    method: 'DELETE',
    key: DELETE_STORE_INFO_BLOCK.name,
  });

  const handleOpenEditBlock = async (item?: InfoBlock) => {
    if (item) {
      setActiveBlock(item);
    }
    await delay(500);
    toggleModal('addInfoBlockModal', false);
    setTimeout(() => {
      toggleModal('addInfoBlockModal');
    }, 600);
  };

  const handleAssignProducts = async (item: InfoBlock) => {
    setActiveBlock(item);
    await delay(500);
    productManagerRef.current?.openSelectProductsModal();
  };

  const handleViewAssignProducts = async (item: InfoBlock) => {
    setActiveBlock(item);
    await delay(500);
    productManagerRef.current?.openAssignedProductsModal();
  };

  const promptDelete = (id: string) => {
    Alert.alert(
      'Do you want to delete this block?',
      'This block would be deleted and removed from all related items.',
      [
        {
          text: 'Delete',
          onPress: () => handleDelete(id),
          style: 'destructive',
        },
        {
          text: 'Cancel',
          onPress: () => {},
          isPreferred: true,
        },
      ],
    );
  };

  const handleDelete = async (id: string) => {
    showLoader('Deleting Block');
    const [response, error] = await deleteInfoBlockRequest.makeRequest({
      id,
    });
    hideLoader();
    await delay(600);

    if (response) {
      setInfoBlocks(updateOrDeleteItemFromList(infoBlocks, 'id', id, null));
      showSuccess('Block deleted successfully');
    }
    if (error) {
      showError(error);
    }
  };

  const moreOptions = (item: InfoBlock) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Box1 size={wp(15)} color={colors.black.placeholder} />} label="View Assigned Products" />
      ),
      onPress: () => handleViewAssignProducts(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<BoxAdd size={wp(15)} color={colors.black.placeholder} />} label="Assign to Products" />
      ),
      onPress: () => handleAssignProducts(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label="Edit Block" />
      ),
      onPress: () => handleOpenEditBlock(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label="Delete Block" />
      ),
      onPress: () => promptDelete(item.id),
    },
  ];

  const shouldShowLoader = isLoading || (infoBlocks.length === 0 && currentPage === 1 && !error);

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <Animated.FlatList
          data={infoBlocks}
          refreshControl={
            pullToRefreshFunc ? <RefreshControl refreshing={false} onRefresh={pullToRefreshFunc} /> : undefined
          }
          ListEmptyComponent={() =>
            shouldShowLoader ? (
              <InfoBlockSkeletalLoader />
            ) : (
              <View className="py-30">
                <EmptyState
                  btnText="Add Info Block"
                  icon={<InfoCircle variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                  text="No info blocks to show"
                  // onPressBtn={() => navigation.navigate('CreateCoupon')}
                />
              </View>
            )
          }
          onScroll={scrollHandler}
          className="flex-1 px-20 pb-40"
          // contentContainerStyle={{ flexGrow: 1 }}
          ItemSeparatorComponent={() => <View className="border-b border-b-grey-border my-15" />}
          renderItem={({ item, index }) => (
            <InfoBlocksCard infoBlockItem={item} moreOptions={moreOptions(item)} index={index} key={item.id} /> //Todo: Figure out how to merge InfoBlocksCard & listitemcard because they're essentially the same
          )}
          onEndReached={() => {
            if (!isLoading && infoBlocks?.length > 0 && currentPage < total_pages) {
              goNext(total_pages);
            }
          }}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {infoBlocks.length > 0 && isLoading && (
                <View className="mt-20">
                  <InfoBlockSkeletalLoader />
                </View>
              )}
            </View>
          }
        />
      </View>
      <FAB onPress={() => toggleModal('addInfoBlockModal')} />
      {modals.addInfoBlockModal && (
        <AddInfoBlockModal
          isVisible={modals.addInfoBlockModal}
          closeModal={() => toggleModal('addInfoBlockModal', false)}
          setInfoBlocks={setInfoBlocks}
          activeItem={activeBlock}
          onModalHide={() => setActiveBlock({} as InfoBlock)}
        />
      )}
      <AssignProductManager ref={productManagerRef} activeBlock={activeBlock} setInfoBlocks={setInfoBlocks} />
    </View>
  );
};

export default InfoBlocksListSection;

interface InfoBlocksCardProps extends Partial<PressableProps> {
  infoBlockItem: InfoBlock;
  moreOptions: MoreOptionElementProps[];
  index?: number;
}

const InfoBlocksCard = ({ infoBlockItem, moreOptions = [], index, ...props }: InfoBlocksCardProps) => {
  const isImageType = infoBlockItem?.content_type === 'IMAGES';

  return (
    <View className={`bg-grey-bgOne rounded-12`} {...props}>
      <Row className={'px-12 py-6'}>
        <BaseText fontSize={14} type="heading" classes="mr-4 text-black-secondary" numberOfLines={1}>
          {infoBlockItem?.title}
        </BaseText>
        {infoBlockItem?.tag && (
          <View className="flex-1 mx-5 items-start">
            <View className="bg-white rounded-full py-6 px-12">
              <BaseText fontSize={12} classes="text-black-muted" numberOfLines={1}>
                {infoBlockItem?.tag}
              </BaseText>
            </View>
          </View>
        )}
        <MoreOptions
          options={moreOptions}
          customMenuElement={
            <CircledIcon className="bg-white p-6">
              <MoreHorizontal strokeWidth={2} currentColor={colors.grey.muted} fill={colors.grey.muted} />
            </CircledIcon>
          }
        />
      </Row>
      <View className={'border border-grey-border bg-white rounded-12 p-15'}>
        {isImageType && (
          <Row className="justify-start flex-wrap" style={{ gap: wp(10) }}>
            {infoBlockItem?.image_content?.map((image, index) => (
              <CustomImage
                key={index}
                imageProps={{ source: { uri: image } }}
                className="h-[58px] w-[58px] rounded-[6px]"
              />
            ))}
          </Row>
        )}
        {!isImageType && (
          <View className="">
            <BaseText lineHeight={20} classes="leading-[16px] text-black-muted">
              {infoBlockItem?.text_content}
            </BaseText>
          </View>
        )}
      </View>
    </View>
  );
};

const InfoBlocksCardSkeleton = ({ index }: { index?: number }) => {
  const isIndexEven = index % 2 === 0;

  return (
    <View className="bg-grey-bgOne rounded-12 mb-15">
      <Row className={'px-12 py-6'}>
        <Shimmer {...{ height: 15, width: 120, borderRadius: 8 }} className="mr-4" />
        <Shimmer {...{ height: 24, width: 24, borderRadius: 12 }} />
      </Row>
      <View className={'border border-grey-border bg-white rounded-12 p-15'}>
        {/* Image content skeleton */}
        {!isIndexEven && (
          <Row className="justify-start flex-wrap" style={{ gap: wp(10) }}>
            {Array.from({ length: 3 }, (_, i) => (
              <Shimmer key={i} {...{ height: 58, width: 58, borderRadius: 6 }} />
            ))}
          </Row>
        )}

        {/* Text content skeleton (alternative) */}
        {isIndexEven && (
          <View className="mt-10">
            <Shimmer {...{ height: 12, width: 250, borderRadius: 6 }} />
            <Shimmer {...{ height: 12, width: 250, borderRadius: 6 }} marginTop={5} />
            <Shimmer {...{ height: 12, width: 120, borderRadius: 6 }} marginTop={5} />
          </View>
        )}
      </View>
    </View>
  );
};

const InfoBlockSkeletalLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <InfoBlocksCardSkeleton key={i} index={i} />
      ))}
    </View>
  );
};
