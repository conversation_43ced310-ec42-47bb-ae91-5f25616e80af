import {
  GET_COUPONS,
  PaginateSearchParams,
  CouponItemInterface,
  StoreInterface,
  InfoBlockInterface,
  GetItemsParams,
  GET_ITEMS,
  ProductItemInterface,
  ASSIGN_ITEMS_TO_INFO_BLOCK,
  AssignItemsParams,
} from 'catlog-shared';
import React, { useState, forwardRef, useImperativeHandle, useEffect, useRef, useCallback, useMemo } from 'react';
import { ScrollHandlerProcessed } from 'react-native-reanimated';

import {
  cx,
  delay,
  ensureUniqueItems,
  hideLoader,
  showError,
  showLoader,
  showSuccess,
  toCurrency,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { Dimensions, View } from 'react-native';
import SelectSpecificProductsModal from 'src/components/products/storefront-products/select-specific-product-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import EmptyState, { EmptyStateProps } from '@/components/ui/empty-states/empty-state';
import StorefrontSkeletonLoader from '@/components/products/storefront-products/storefront-skeleton-loader';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';
import Pressable from 'src/components/ui/base/pressable';
import CustomImage from 'src/components/ui/others/custom-image';
import colors from 'src/theme/colors';
import { Box1, CloseCircle } from 'node_modules/iconsax-react-native/src';
import Row from 'src/components/ui/row';
import BaseText from 'src/components/ui/base/base-text';
import { Close } from 'src/components/ui/icons';
import Toast from 'react-native-toast-message';
import { toastConfig } from 'src/components/ui/others/toast-notification';

interface AssignProductManagerProps {
  activeBlock: InfoBlockInterface;
  setInfoBlocks: React.Dispatch<React.SetStateAction<InfoBlockInterface[]>>;
}

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

export interface AssignProductManagerRef {
  openSelectProductsModal: () => void;
  openAssignedProductsModal: () => void;
}

const AssignProductManager = forwardRef<AssignProductManagerRef, AssignProductManagerProps>(
  ({ activeBlock, setInfoBlocks }, ref) => {
    const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

    const { store } = useAuthContext();
    const { modals, toggleModal } = useModals(['selectProducts', 'assignedProducts']);

    useEffect(() => {
      if (activeBlock) {
        setSelectedProducts(activeBlock?.items?.map(i => i.id) ?? []);
      }
    }, [activeBlock]);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      openSelectProductsModal: () => {
        toggleModal('selectProducts', true);
      },
      openAssignedProductsModal: () => {
        toggleModal('assignedProducts', true);
      },
    }));

    const assignItemsToInfoBlockRequest = useApi<AssignItemsParams>({
      apiFunction: ASSIGN_ITEMS_TO_INFO_BLOCK,
      method: 'PUT',
      key: 'assign-items-to-info-block',
    });

    const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
      {
        apiFunction: GET_ITEMS,
        method: 'GET',
        key: 'fetch-custom-items',
      },
      {
        per_page: 9007199254740991,
        filter: { store: store?.id },
      },
    );

    const handleAssign = async () => {
      if (!activeBlock?.id) {
        return;
      }

      toggleModal('selectProducts', false);
      await delay(700);

      showLoader('Updating assigned products');
      const [res, err] = await assignItemsToInfoBlockRequest.makeRequest({
        id: activeBlock?.id,
        items: selectedProducts,
      });
      hideLoader();
      if (res) {
        await delay(700);
        showSuccess('Products have been successfully assigned to this info block.');
        const itemsLeft = activeBlock?.items.filter(i => selectedProducts?.includes(i.id));
        setInfoBlocks(prev =>
          updateOrDeleteItemFromList(prev, 'id', activeBlock?.id, { ...activeBlock, items: itemsLeft }),
        );
        return;
      }

      showError(err);
    };

    const updateAssign = async (items?: string[]) => {
      if (!activeBlock?.id) {
        return;
      }

      toggleModal('assignedProducts', false);

      await delay(700);
      showLoader('Updating assigned products');

      const [res, err] = await assignItemsToInfoBlockRequest.makeRequest({
        id: activeBlock?.id,
        items: items,
      });

      if (res) {
        showSuccess('Assigned products have been successfully updated.');

        const itemsLeft = activeBlock?.items.filter(i => items?.includes(i.id));
        setInfoBlocks(prev =>
          updateOrDeleteItemFromList(prev, 'id', activeBlock?.id, { ...activeBlock, items: itemsLeft }),
        );
        return;
      }

      showError(err);
    };

    return (
      <View>
        <SelectSpecificProductsModal
          products={getProductsRequest?.response?.data?.items ?? []}
          isVisible={modals.selectProducts}
          closeModal={() => toggleModal('selectProducts', false)}
          getProductsRequest={getProductsRequest}
          loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
          selectedProducts={selectedProducts ? selectedProducts : []}
          setSelectedProducts={setSelectedProducts}
          onPressContinue={handleAssign}
        />
        <AssignedProductsModal
          isVisible={modals.assignedProducts}
          closeModal={() => toggleModal('assignedProducts', false)}
          items={activeBlock?.items ?? []}
          updateAssign={updateAssign}
          isLoading={assignItemsToInfoBlockRequest.isLoading}
        />
      </View>
    );
  },
);

export default AssignProductManager;

interface AssignedProductsModalModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  items: ProductItemInterface[];
  isLoading?: boolean;
  updateAssign: (items?: string[]) => Promise<void>;
}
const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const AssignedProductsModal = ({
  closeModal,
  items,
  isLoading,
  updateAssign,
  ...props
}: AssignedProductsModalModalProps) => {
  const [itemsId, setItemsId] = useState<string[]>(items ? items.map(i => i.id) : []);
  const itemsRef = useRef<string[]>(items?.map(i => i.id));

  useEffect(() => {
    if (items) {
      setItemsId(items?.map(i => i.id));
      itemsRef.current = items?.map(i => i.id);
    }
  }, [items]);

  const dataHasChanged = useMemo(() => itemsRef.current.length !== itemsId.length, [itemsId]);

  const removeItem = useCallback(
    (id: string) => {
      const itemsIdCopy = [...itemsId];
      const newIds = itemsIdCopy.filter(i => i !== id);
      setItemsId(newIds);
    },
    [itemsId],
  );

  const productItems = useMemo(() => {
    return items.filter(i => itemsId.includes(i.id));
  }, [itemsId]);

  return (
    <BottomModal
      {...props}
      enableDynamicSizing
      closeModal={closeModal}
      useChildrenAsDirectChild
      buttons={
        dataHasChanged
          ? [{ text: 'Save Update', onPress: () => updateAssign(itemsId), isLoading: isLoading }]
          : undefined
      }
      title={'Assigned Products'}>
      <BottomSheetFlashList
        data={productItems}
        numColumns={2}
        extraData={itemsId}
        contentContainerStyle={{ paddingBottom: 50 }}
        ListEmptyComponent={() => (
          <View className="mt-32">
            <EmptyState showBtn={false} text={'No products to show'} />
          </View>
        )}
        renderItem={({ item, index }: { item: ProductItemInterface; index: number }) => {
          const hasImages = item.images !== undefined && item?.images?.length > 0 && item?.images?.some(i => i !== '');

          return (
            <View
              style={{
                flex: 1,
                // width: cardWidth,
                minWidth: cardWidth,
                maxWidth: cardWidth,
              }}
              className={cx('m-10')}>
              <View
                className={cx('rounded-[15px] overflow-hidden justify-end items-center')}
                // source={{ uri: product?.images![0] }}
                style={{
                  height: cardWidth,
                }}>
                {hasImages ? (
                  <CustomImage
                    className={cx('w-full h-full absolute')}
                    imageProps={{
                      source: { uri: (item as any)?.image ?? item?.images![item?.thumbnail ?? 0] },
                    }}
                  />
                ) : (
                  <View className="w-full h-full bg-accentYellow-pastel justify-center items-center">
                    <Box1 color={colors.accentYellow.main} variant="Bulk" size={wp(60)} />
                  </View>
                )}
                <Pressable
                  className="absolute top-10 right-10 rounded-full"
                  style={{ backgroundColor: colors.accentRed.main + 50 }}
                  onPress={() => removeItem(item.id)}>
                  <View className="h-24 w-24 flex items-center justify-center rounded-full bg-black-placeholder">
                    <Close currentColor={colors.white} size={wp(10)} />
                  </View>
                </Pressable>
              </View>
              <Row className={cx('items-start')} style={{ maxWidth: '100%' }}>
                <View className={cx('flex-1 mt-5')} style={{ maxWidth: '100%' }}>
                  <BaseText
                    fontSize={13}
                    type="heading"
                    classes={cx('text-black-muted leading-snug mb-2')}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {item?.name}
                  </BaseText>
                  <BaseText fontSize={11} weight={'semiBold'} classes={'text-black-main'}>
                    {toCurrency(item?.price ?? 0)}
                  </BaseText>
                </View>
              </Row>
            </View>
          );
        }}
      />
    </BottomModal>
  );
};
