import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { BaseText, Container, Row } from '@/components/ui';
import { FlatList, RefreshControl, ScrollView, View } from 'react-native';
import Input from '@/components/ui/inputs/input';
import { Search as SearchIcon } from '@/components/ui/icons';
import colors from '@/theme/colors';
import {
  dateDuration,
  ensureUniqueItems,
  getFieldvalues,
  removeEmptyAndUndefined,
  updateOrDeleteItemFromList,
  wp,
} from 'src/assets/utils/js';
import { Setting, Setting3, Setting4, Setting5, Settings } from 'iconsax-react-native/src';
import Pressable from '@/components/ui/base/pressable';
import useModals from 'src/hooks/use-modals';
import searchDiscountsFilterModal from '@/components/search/search-product-filter-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import usePagination from 'src/hooks/use-pagination';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import { useFormik } from 'formik';
import SearchHeader from '@/components/search/search-header';
import { DiscountsResponse } from '.';
import SearchDiscountsFilterModal from '@/components/search/search-discounts-filter-modal';
import DiscountListSection from '@/components/products/discounts-and-coupons/discounts/discount-list-section';
import {
  ProductItemInterface,
  DISCOUNT_STATUS_TAG,
  DISCOUNT_TYPE_TAG,
  GET_DISCOUNTS,
  GET_ITEMS,
  GetDiscountsParams,
  GetItemsParams,
  PaginateSearchParams,
  DiscountItemInterface,
  StoreInterface,
} from 'catlog-shared';

const PER_PAGE = 10;

const SearchDiscounts = () => {
  const { modals, toggleModal } = useModals(['searchDiscountsFilter']);
  const [discounts, setDiscounts] = useState<DiscountItemInterface[]>([]);

  const { currentPage, goNext, setPage } = usePagination();

  const getDiscountRequest = useApi<GetDiscountsParams, DiscountsResponse>(
    {
      key: 'search-discounts',
      apiFunction: GET_DISCOUNTS,
      method: 'GET',
      onSuccess: response => {
        setDiscounts(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const form = useFormik<SearchDiscountsParams>({
    initialValues: {
      status: undefined,
      type: undefined,
    },
    onSubmit: values => {
      setDiscounts([]);
      setPage(1);
      getDiscountRequest.makeRequest({
        filter: { ...removeEmptyAndUndefined(values) },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'desc',
      });
    },
  });

  const submitForm = () => {
    toggleModal('searchDiscountsFilter', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Search Discounts',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        numberOfActiveFilters={Object.values(form.values).filter(Boolean).length}
        inputProps={{
          placeholder: 'Search for discounts',
          returnKeyType: 'search',
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
        }}
        showFilter
        onPressFilterButton={() => toggleModal('searchDiscountsFilter')}
      />
      <View className="flex-1">
        {discounts.length > 0 && (
          <BaseText classes="py-15 px-20 text-grey-mutedDark">
            {getDiscountRequest?.response?.data?.total ?? 0} Discounts found
          </BaseText>
        )}
        <DiscountListSection
          discounts={discounts}
          setDiscounts={setDiscounts}
          isReLoading={getDiscountRequest.isReLoading}
          isLoading={getDiscountRequest.isLoading}
          total_pages={getDiscountRequest?.response?.data?.total_pages}
          fromFiltersPage
          {...{ currentPage, goNext, setPage }}
        />
      </View>
      <SearchDiscountsFilterModal
        isVisible={modals.searchDiscountsFilter}
        form={form}
        closeModal={() => toggleModal('searchDiscountsFilter', false)}
        onPressContinue={submitForm}
      />
    </DashboardLayout>
  );
};

export default SearchDiscounts;

export interface SearchDiscountsParams {
  search?: string;
  status?: DISCOUNT_STATUS_TAG;
  type?: DISCOUNT_TYPE_TAG;
}
