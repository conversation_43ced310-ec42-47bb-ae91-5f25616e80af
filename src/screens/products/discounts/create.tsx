import { FlatList, Image, ScrollView, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import { useNavigation } from '@react-navigation/native';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import DiscountForm, {
  DiscountFormData,
  DiscountFormScreen,
  discountValidationSchema,
} from '@/components/products/discounts-and-coupons/discounts/discount-form';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { useFormik } from 'formik';
import { useApi } from '@/hooks/use-api';
import { CREATE_DISCOUNT, CreateDiscountParams } from 'catlog-shared';
import { showError, showSuccess } from 'src/assets/utils/js';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import { useStoreReview } from 'src/hooks/use-store-review';

const CreateDiscount = () => {
  const navigation = useNavigation();
  const createDiscountRequest = useApi({ key: 'create-discount', apiFunction: CREATE_DISCOUNT, method: 'POST' });
  const { handleReviewRequest } = useStoreReview();

  const form = useFormik<DiscountFormData>({
    initialValues: {
      label: '',
      active: true,
      percentage: 0,
      start_date: null,
      end_date: null,
      items: [],
      duration: '',
      discount_cap: undefined,
    },
    validationSchema: discountValidationSchema,
    onSubmit: async values => {
      const { duration, ...rest } = values;

      const requestData = {
        ...rest,
        percentage: Number(values.percentage),
        start_date: values.start_date?.toISOString() ?? '',
        end_date: duration === 'till-deleted' ? null : (values.end_date?.toISOString() ?? ''),
        ...(values?.discount_cap && { discount_cap: Number(values?.discount_cap) }),
      };

      const [response, error] = await createDiscountRequest.makeRequest(requestData);
      if (response) {
        eventEmitter.emit('discountCreate', response.data)
        showSuccess('You have successfully created a new discount');
        navigation.goBack();
        handleReviewRequest()
      }
      if (error) {
        showError(error);
      }
    },
  });

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Create Discount',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <AvoidKeyboard>
        <ScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                className="w-[80px] h-[60px]"
                source={require('@/assets/images/discount.png')}
                resizeMode={'contain'}
              />
            }
            colorPalette={ColorPaletteType.YELLOW}
            pageTitleTop={'Create Discounts'}
            pageTitleBottom={'For Multiple Products'}
          />
          <Container className={'mt-20'}>
            <DiscountForm form={form} />
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: createDiscountRequest.isLoading ? 'Loading...' : 'Create Discount',
            onPress: () => form.handleSubmit(),
            disabled: createDiscountRequest.isLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default CreateDiscount;
