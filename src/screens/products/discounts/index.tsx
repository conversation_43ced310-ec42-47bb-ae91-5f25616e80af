import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { Alert, RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import { ensureUniqueItems, updateOrDeleteItemFromList } from '@/assets/utils/js';
import usePagination from '@/hooks/use-pagination';
import { ResponseWithoutPagination, ResponseWithPagination, useApi } from '@/hooks/use-api';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { format } from 'date-fns';
import Toast from 'react-native-toast-message';
import DiscountListSection from '@/components/products/discounts-and-coupons/discounts/discount-list-section';
import {
  DELETE_DISCOUNT,
  GET_DISCOUNTS,
  UPDATE_DISCOUNT,
  PaginateSearchParams,
  UpdateDiscountParams,
  DiscountItemInterface,
  StoreInterface,
} from 'catlog-shared';
import useEventListener from 'src/hooks/use-event-emitter';

interface DiscountsListProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

export interface DiscountsResponse
  extends ResponseWithPagination<{
    store: StoreInterface;
    items: DiscountItemInterface[];
    total_pages: number;
    total: number;
    page: number;
  }> {}

const PER_PAGE = 10;
const DiscountsList: React.FC<DiscountsListProps> = ({ scrollHandler }) => {
  const [discounts, setDiscounts] = useState<DiscountItemInterface[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const { currentPage, goNext, setPage } = usePagination();

  const getDiscountRequest = useApi<PaginateSearchParams, DiscountsResponse>(
    {
      key: 'fetch-discounts',
      apiFunction: GET_DISCOUNTS,
      method: 'GET',
      onSuccess: response => {
        setDiscounts(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'DESC',
    },
  );

  useEventListener('discountCreate', discount => {
    setDiscounts(prev => ensureUniqueItems([discount, ...prev]));
  });

  useEventListener('discountEdit', data => {
    setDiscounts(updateOrDeleteItemFromList(discounts, 'id', data?.id, data.discount));
  });

  const pullToRefresh = () => {
    setDiscounts([]);

    if (currentPage === 1) {
      getDiscountRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'DESC',
      });
      return;
    }

    setPage(1);
  };

  const handleRetry = () => {
    setIsRetrying(true);
    pullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <QueryErrorBoundary
      error={getDiscountRequest.error}
      isLoading={getDiscountRequest.isLoading}
      refetch={handleRetry}
      isRetrying={isRetrying}
      variant="fullPage"
      padAround
      errorTitle="Failed to load discounts"
      customErrorMessage="We couldn't load your discounts. Please check your connection and try again."
    >
      <DiscountListSection
        discounts={discounts}
        setDiscounts={setDiscounts}
        scrollHandler={scrollHandler}
        pullToRefreshFunc={pullToRefresh}
        isReLoading={getDiscountRequest.isReLoading}
        isLoading={getDiscountRequest.isLoading}
        total_pages={getDiscountRequest?.response?.data?.total_pages}
        {...{ currentPage, goNext, setPage }}
      />
    </QueryErrorBoundary>
  );
};

export default DiscountsList;
