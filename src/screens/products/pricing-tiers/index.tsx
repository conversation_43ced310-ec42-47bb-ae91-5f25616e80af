import {
  GET_HIGHLIGHTS,
  PaginateSearchParams,
  CouponItemInterface,
  StoreInterface,
  GET_STORE_INFO_BLOCKS,
  InfoBlockInterface,
  HighlightInterface,
  GET_TIERED_PRICING,
  GetTieredPricingParams,
  PricingTierInterface,
} from 'catlog-shared';
import React, { useState } from 'react';
import { ScrollHandlerProcessed } from 'react-native-reanimated';

import { delay, ensureUniqueItems } from '@/assets/utils/js';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import PricingTiersList from 'src/components/products/pricing-tiers/pricing-tiers-list';
import Can from 'src/components/ui/permissions/can';
import { SCOPES } from 'src/assets/utils/js/permissions';
import SubscriptionPermissionFallback from 'src/components/ui/permissions/subscription-permission-fallback';

interface PricingTiersProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

export interface PricingTiersResponse extends ResponseWithPagination<PricingTierInterface[]> {}

const PER_PAGE = 10;

const PricingTiers: React.FC<PricingTiersProps> = ({ scrollHandler }) => {
  const [tieredPricing, setTieredPricing] = useState<PricingTierInterface[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const { currentPage, goNext, setPage } = usePagination();

  const getPricingTiersRequest = useApi<GetTieredPricingParams, PricingTiersResponse>(
    {
      apiFunction: GET_TIERED_PRICING,
      method: 'GET',
      key: GET_TIERED_PRICING.name,
      onSuccess: response => {
        setIsRetrying(false);
        setTieredPricing(prev => ensureUniqueItems([...prev, ...response?.data]));
      },
    },
    {
      filter: {},
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const handlePullToRefresh = async () => {
    if (currentPage === 1) {
      getPricingTiersRequest.makeRequest({
        filter: {},
        page: currentPage,
        per_page: PER_PAGE,
      });
      await delay(800);
      setTieredPricing([]);
      return;
    }

    setPage(1);
    await delay(600);
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
  };

  return (
    <Can
      data={{ planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS }}
      fallback={<SubscriptionPermissionFallback actionName="create and view pricing tiers" />}>
      <QueryErrorBoundary
        error={getPricingTiersRequest.error}
        isLoading={getPricingTiersRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        padAround
        variant="fullPage"
        errorTitle="Failed to load pricing tiers"
        customErrorMessage="We couldn't load pricing tiers. Please check your connection and try again.">
        <PricingTiersList
          scrollHandler={scrollHandler}
          {...{
            goNext,
            setPage,
            scrollHandler,
            currentPage,
            setTieredPricing,
            tieredPricing,
            isLoading: getPricingTiersRequest.isLoading,
            pullToRefreshFunc: handlePullToRefresh,
            error: getPricingTiersRequest.error,
            // total_pages: getPricingTiersRequest.response?.data?.total_pages,
          }}
        />
      </QueryErrorBoundary>
    </Can>
  );
};

export default PricingTiers;
