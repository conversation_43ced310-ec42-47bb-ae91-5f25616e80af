import React, { useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  CREATE_TIERED_PRICING,
  PricingTierInterface,
  ProductItemInterface,
  UPDATE_TIERED_PRICING,
} from 'catlog-shared';
import { useApi } from 'src/hooks/use-api';
import PricingTiersForm, { PricingTiersFormRef } from 'src/components/products/pricing-tiers/pricing-tiers-form';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import Button from '@/components/ui/buttons/button';
import { Layer } from 'iconsax-react-native/src';
import { hp, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import useStorefrontItems from '@/hooks/use-storefront-items';
import { BaseText, CircledIcon, Container } from '@/components/ui';
import CustomImage from '@/components/ui/others/custom-image';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import eventEmitter from '@/assets/utils/js/event-emitter';

const CreatePricingTier = () => {
  const navigation = useNavigation();

  // Form ref to trigger submit from screen buttons
  const formRef = useRef<PricingTiersFormRef>(null);

  // Get store items
  const { items: storeItems, getItem } = useStorefrontItems();

  // API hooks for create and update
  const createPricingTierRequest = useApi({
    apiFunction: CREATE_TIERED_PRICING,
    method: 'POST',
    key: 'create-pricing-tier',
  });

  const updatePricingTierRequest = useApi({
    apiFunction: UPDATE_TIERED_PRICING,
    method: 'PUT',
    key: 'update-pricing-tier',
  });

  // Handle successful form submission
  const handleFormCallback = (data?: PricingTierInterface) => {
    if (data) {
      // Emit event to update pricing tiers list
      eventEmitter.emit('pricingTierCreate', data);

      // Navigate back to pricing tiers list
      navigation.goBack();
    }
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Create Pricing Tier',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView className="flex-1" keyboardShouldPersistTaps={'handled'}>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.YELLOW}
          hideIconOnKeyboardActive
          iconElement={
            <CircledIcon className="bg-accentYellow-main p-15">
              <Layer variant="Bold" size={wp(60)} color={colors.white} />
            </CircledIcon>
          }
          customElements={
            <BaseText fontSize={22} lineHeight={hp(27)} classes="mt-20 leading-[30px]" type="heading">
              Create Pricing Tier
            </BaseText>
          }
        />
        <Container className="pb-100">
          <View className="mt-20">
            <PricingTiersForm
              ref={formRef}
              showItemsSection={false}
              showActiveToggle={false}
              closeModal={() => navigation.goBack()}
              callBack={handleFormCallback}
              getItem={getItem}
              storeItems={storeItems}
              createPricingTierRequest={createPricingTierRequest}
              updatePricingTierRequest={updatePricingTierRequest}
            />
          </View>
        </Container>
      </ScrollView>

      <FixedBtnFooter
        buttons={[
          {
            text: 'Save Tier',
            onPress: () => formRef.current?.submitForm(),
            isLoading: createPricingTierRequest.isLoading || updatePricingTierRequest.isLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default CreatePricingTier;
