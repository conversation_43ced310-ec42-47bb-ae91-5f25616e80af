import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { mockCategories } from '@/constant/mock-data';
import { Edit2, Tag } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import useModals from '@/hooks/use-modals';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CircledIcon from '@/components/ui/circled-icon';
import ManageCategoriesModal from '@/components/products/manage-categories-modal';
import { BaseText, Row } from '@/components/ui';
import useAuthContext from '@/contexts/auth/auth-context';
import { useApi } from '@/hooks/use-api';
import { GET_STORE_CATEGORIES } from 'catlog-shared';
import { colorAlternates } from 'src/constant/static-data';
import Shimmer from 'src/components/ui/shimmer';

interface CategoriesListProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

const CategoriesList: React.FC<CategoriesListProps> = ({ scrollHandler }) => {
  const [categories, setCategories] = useState([]);
  const [isRetrying, setIsRetrying] = useState(false);
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['categories']);

  const { storeId } = useAuthContext();
  const categoriesRequest = useApi(
    {
      apiFunction: GET_STORE_CATEGORIES,
      method: 'GET',
      key: 'fetch-categories',
    },
    {
      id: storeId!,
    },
  );

  // const categories = categoriesRequest?.response?.data ?? [];

  useEffect(() => {
    setCategories(categoriesRequest?.response?.data ?? []);
  }, [categoriesRequest?.response?.data]);

  const handlePullToRefresh = async () => {
    await categoriesRequest.makeRequest({
      id: storeId!,
    });
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <View style={{ flex: 1 }}>
      <QueryErrorBoundary
        error={categoriesRequest.error}
        isLoading={categoriesRequest.isLoading}
        refetch={handleRetry}
        padAround
        isRetrying={isRetrying}
        variant="fullPage"
        errorTitle="Failed to load categories"
        customErrorMessage="We couldn't load your categories. Please check your connection and try again.">
        <Animated.FlatList
          data={categories}
          ListEmptyComponent={() => (
            <View>
              {categoriesRequest.isLoading ? (
                <SkeletalLoader />
              ) : (
                <EmptyState
                  btnText={'Create Categories'}
                  text={'No categories to show'}
                  icon={<Tag variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                />
              )}
            </View>
          )}
          className="flex-1 px-20"
          refreshControl={
            <RefreshControl refreshing={categoriesRequest?.isLoading!} onRefresh={() => handlePullToRefresh()} />
          }
          ListFooterComponent={<View className="mb-100" />}
          contentContainerStyle={{ flexGrow: 1 }}
          ItemSeparatorComponent={() => <View className="border-b border-b-grey-border my-15" />}
          renderItem={({ item, index }) => {
            return (
              <ListItemCard
                className="py-0"
                title={item?.name}
                leftElement={
                  <CircledIcon style={{ backgroundColor: colorAlternates[index % colorAlternates.length].bgColor }}>
                    {item?.emoji ? (
                      <View style={{ width: wp(22), height: wp(22) }} className="flex items-center justify-center">
                        <BaseText fontSize={14} className="leading-none">
                          {item.emoji}
                        </BaseText>
                      </View>
                    ) : (
                      <Tag
                        size={wp(20)}
                        color={colorAlternates[index % colorAlternates.length].iconColor}
                        variant={'Bold'}
                      />
                    )}
                  </CircledIcon>
                }
                description={`${item.items_count} products`}
                disabled
                titleProps={{ type: 'heading', fontSize: 14 }}
                titleClasses="text-black-secondary"
                descriptionClasses="mt-2"
                key={item.id}
              />
            );
          }}
        />
      </QueryErrorBoundary>
      <FAB onPress={() => toggleModal('categories')}>
        <Edit2 size={wp(20)} color={colors.white} />
      </FAB>
      {modals.categories && (
        <ManageCategoriesModal
          storeCategories={categories ? categories : []}
          isVisible={modals.categories}
          updateCallback={c => setCategories(c)}
          closeModal={() => toggleModal('categories', false)}
        />
      )}
    </View>
  );
};

export default CategoriesList;

const CardSkeleton = () => {
  return (
    <View className={`flex-row items-center border-b border-b-grey-border py-15`}>
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className={'mx-12'}>
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 90, borderRadius: 50 }} className="mr-2" />
        </Row>
        <Shimmer {...{ height: 10, width: 150, borderRadius: 50 }} />
      </View>
    </View>
  );
};

const SkeletalLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <CardSkeleton key={i} />
      ))}
    </View>
  );
};
