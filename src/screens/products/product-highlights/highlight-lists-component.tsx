import { useNavigation } from '@react-navigation/native';
import {
  DELETE_COUPON,
  UPDATE_COUPON,
  CouponItemInterface,
  StoreInterface,
  HighlightInterface,
  DELETE_STORE_INFO_BLOCK,
  UPDATE_HIGHLIGHT,
  DELETE_HIGHLIGHT,
} from 'catlog-shared';
import {
  Box,
  Box1,
  BoxAdd,
  Edit2,
  InfoCircle,
  Link2,
  Send2,
  TicketDiscount,
  Trash,
} from 'iconsax-react-native/src';
import React, { useRef, useState } from 'react';
import { Alert, View, RefreshControl, Dimensions, Share } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import {
  copyToClipboard,
  delay,
  formatDate,
  hideLoader,
  hp,
  openLinkInBrowser,
  showError,
  showLoader,
  showSuccess,
  toCurrency,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import CouponDetailsModal from '@/components/products/discounts-and-coupons/coupons/coupon-details-modal';
import EditCouponModal from '@/components/products/discounts-and-coupons/coupons/edit-coupon-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import MoreOptions, { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import Row from '@/components/ui/row';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from 'src/components/ui/base/pressable';
import Shimmer from 'src/components/ui/shimmer';
import CustomImage from 'src/components/ui/others/custom-image';
import { MoreHorizontal } from 'src/components/ui/icons';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { toAppUrl } from 'node_modules/catlog-shared/src/utils';
import { EXPO_PUBLIC_PUBLIC_URL } from 'src/configs-files/@env';
import useAuthContext from 'src/contexts/auth/auth-context';
import AddProductHighlightModal from 'src/components/products/product-hghlights/add-product-highlight-modal';

interface HighlightListProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  highlights: HighlightInterface[];
  setHighlights: React.Dispatch<React.SetStateAction<HighlightInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading?: boolean;
  isLoading?: boolean;
  total_pages?: number;
  currentPage?: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  error?: any;
}

const HighlightList: React.FC<HighlightListProps> = ({
  scrollHandler,
  highlights,
  pullToRefreshFunc,
  setHighlights,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  error,
}) => {
  const [activeHighlight, setActiveHighlight] = useState<HighlightInterface>({} as HighlightInterface);
  const [isEdit, setIsEdit] = useState(false);
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['addHighlightModal']);

  const deleteHighlightRequest = useApi({
    apiFunction: DELETE_HIGHLIGHT,
    method: 'DELETE',
    key: DELETE_HIGHLIGHT.name,
  });

  const handleOpenEditBlock = async (item?: HighlightInterface) => {
    if (!item) {
      return;
    }
    setIsEdit(true);
    setActiveHighlight(item);
    await delay(500);
    toggleModal('addHighlightModal', false);
    setTimeout(() => {
      toggleModal('addHighlightModal');
    }, 600);
  };

  const handleAssignProducts = async (item: HighlightInterface) => {
    setActiveHighlight(item);
    await delay(500);
  };

  const handleViewAssignProducts = async (item: HighlightInterface) => {
    setActiveHighlight(item);
    await delay(500);
  };

  const promptDelete = (id: string) => {
    Alert.alert(
      'Do you want to delete this highlight?',
      'This highlight would be deleted and removed from all related items.',
      [
        {
          text: 'Delete',
          onPress: () => handleDelete(id),
          style: 'destructive',
        },
        {
          text: 'Cancel',
          onPress: () => {},
          isPreferred: true,
        },
      ],
    );
  };

  const handleDelete = async (id: string) => {
    showLoader('Deleting Highlight');
    const [response, error] = await deleteHighlightRequest.makeRequest({
      id,
    });
    hideLoader();
    await delay(600);

    if (response) {
      setHighlights(updateOrDeleteItemFromList(highlights, 'id', id, null));
      showSuccess('Highlight deleted successfully');
    }
    if (error) {
      showError(error);
    }
  };

  const copyLink = (item: HighlightInterface) => {
    const link = toAppUrl(`highlights/${item.slug}`, true, EXPO_PUBLIC_PUBLIC_URL);
    copyToClipboard(link);
  };

  const shareHighlight = (item: HighlightInterface) => {
    const link = toAppUrl(`highlights/${item.slug}`, true, EXPO_PUBLIC_PUBLIC_URL);
    Share.share({
      // url: link,
      message: `Check out this highlight on catlog: ${link}`,
    });
  };

  const moreOptions = (item: HighlightInterface) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Send2 size={wp(15)} color={colors.black.placeholder} />} label="Share Highlight" />
      ),
      onPress: () => shareHighlight(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Link2 size={wp(15)} color={colors.black.placeholder} />} label="Copy Link" />
      ),
      onPress: () => copyLink(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label="Edit Highlight" />
      ),
      onPress: () => handleOpenEditBlock(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.accentRed.main} />} label="Delete Highlight" labelClasses="text-accentRed-main" />
      ),
      onPress: () => promptDelete(item.id),
    },
  ];

  const onSaveHighlight = (highlight: HighlightInterface) => {
    setHighlights(prev => updateOrDeleteItemFromList(prev, 'id', highlight.id, highlight));
  };

  const shouldShowLoader = isLoading || (highlights.length === 0 && currentPage === 1 && !error);

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <Animated.FlatList
          data={highlights}
          refreshControl={
            pullToRefreshFunc ? <RefreshControl refreshing={false} onRefresh={pullToRefreshFunc} /> : undefined
          }
          ListEmptyComponent={() =>
            shouldShowLoader ? (
              <HighlightSkeletalLoader />
            ) : (
              <View className="py-30">
                <EmptyState
                  btnText="Add Highlight"
                  icon={<InfoCircle variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                  text="No highlights to show"
                  // onPressBtn={() => navigation.navigate('CreateCoupon')}
                />
              </View>
            )
          }
          onEndReachedThreshold={0.3}
          onScroll={scrollHandler}
          className="flex-1 px-20 pb-40"
          ItemSeparatorComponent={() => <View className="h-15" />}
          renderItem={({ item, index }) => (
            <HighlightsCard
              item={item}
              moreOptions={moreOptions(item)}
              index={index}
              key={item.id}
              setHighlights={setHighlights}
            />
          )}
          onEndReached={() => {
            if (!isLoading && highlights?.length > 0 && currentPage < total_pages) {
              goNext(total_pages);
            }
          }}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {highlights.length > 0 && isLoading && (
                <View className="mt-20">
                  <HighlightSkeletalLoader />
                </View>
              )}
            </View>
          }
        />
      </View>
      <FAB onPress={() => toggleModal('addHighlightModal')} />
      {modals.addHighlightModal && (
        <AddProductHighlightModal
          isVisible={modals.addHighlightModal}
          isEdit={isEdit}
          highlightToEdit={activeHighlight}
          onSuccess={onSaveHighlight}
          onModalHide={() => setIsEdit(false)}
          closeModal={() => toggleModal('addHighlightModal', false)}
        />
      )}
    </View>
  );
};

export default HighlightList;

interface HighlightsCardProps extends Partial<PressableProps> {
  item: HighlightInterface;
  moreOptions: MoreOptionElementProps[];
  index?: number;
  setHighlights: React.Dispatch<React.SetStateAction<HighlightInterface[]>>;
}

const HighlightsCard = ({ item, moreOptions = [], setHighlights, index, ...props }: HighlightsCardProps) => {
  const updateHighlightRequest = useApi({
    apiFunction: UPDATE_HIGHLIGHT,
    method: 'PUT',
    key: UPDATE_HIGHLIGHT.name,
  });

  const handleToggleHighlightActive = async (data: HighlightInterface, active: boolean) => {
    showLoader('Updating highlight availability status');
    const [res, err] = await updateHighlightRequest.makeRequest({ id: data.id, active });

    hideLoader();
    if (res) {
      setHighlights(prev => updateOrDeleteItemFromList(prev, 'id', item.id, { ...data, active }));
      showSuccess('Highlight updated successfully', 700);
    }
  };

  const handlePreview = () => {
    const link = toAppUrl(`highlights/${item.slug}`, true, EXPO_PUBLIC_PUBLIC_URL);
    openLinkInBrowser(link);
  };

  const totalProducts = item.videos.map(video => video.products?.length || 0).reduce((sum, length) => sum + length, 0);
  return (
    <View className={`rounded-12 border border-grey-border p-6`} {...props}>
      <CustomImage imageProps={{ source: { uri: item?.videos[0]?.thumbnail } }} className="h-[200px] w-full rounded-8" />
      <Row classes="items-start px-6 mt-15">
        <View>
          <BaseText fontSize={14} type="heading" classes=" text-black-secondary" numberOfLines={1}>
            {item?.title}
          </BaseText>
          <Row disableSpread className="mt-6">
            <BaseText fontSize={12} classes="text-black-placeholder" numberOfLines={1}>
              {item?.videos.length} Videos
            </BaseText>
            <View className="w-3 h-3 rounded-full bg-black-muted mx-6" />
            <BaseText fontSize={12} classes="text-black-placeholder" numberOfLines={1}>
              {totalProducts} Products
            </BaseText>
          </Row>
        </View>
        <CustomSwitch value={item.active} onValueChange={value => handleToggleHighlightActive(item, value)} />
      </Row>
      <Row className={'mt-12 px-6'}>
        <Button
          text="Preview Highlight"
          variant={ButtonVariant.LIGHT}
          size={ButtonSize.SMALL}
          onPress={handlePreview}
        />
        <MoreOptions
          options={moreOptions}
          customMenuElement={
            <CircledIcon className="bg-white p-6">
              <MoreHorizontal strokeWidth={2} currentColor={colors.grey.muted} fill={colors.grey.muted} />
            </CircledIcon>
          }
        />
      </Row>
    </View>
  );
};

const HighlightsCardSkeleton = () => {
  const width = Dimensions.get('window').width - (40 + 12);

  return (
    <View className="rounded-12 mb-15 border border-grey-border p-6">
      <Shimmer {...{ height: hp(180), width: width, borderRadius: 8 }} className="mr-4" />
      <Row className={'px-6 mt-10'}>
        <View>
          <Shimmer {...{ height: hp(15), width: wp(120), borderRadius: 8 }} className="mr-4" />
          <Shimmer {...{ height: hp(15), width: wp(140), borderRadius: 12 }} marginTop={10} />
        </View>
        <Shimmer {...{ height: hp(24), width: wp(48), borderRadius: 12 }} />
      </Row>
      <Row className="mt-10 px-6">
        <Shimmer {...{ height: hp(30), width: wp(110), borderRadius: 6 }} />
        <Shimmer {...{ height: hp(24), width: wp(24), borderRadius: 24 }} />
      </Row>
    </View>
  );
};

const HighlightSkeletalLoader = () => {
  return (
    <View>
      {Array.from({ length: 5 }, (_, i) => i).map(i => (
        <HighlightsCardSkeleton key={i} />
      ))}
    </View>
  );
};
