import {
  GET_HIGHLIGHTS,
  PaginateSearchParams,
  CouponItemInterface,
  StoreInterface,
  GET_STORE_INFO_BLOCKS,
  InfoBlockInterface,
  HighlightInterface,
} from 'catlog-shared';
import React, { useState } from 'react';
import { ScrollHandlerProcessed } from 'react-native-reanimated';

import { delay, ensureUniqueItems, wp } from '@/assets/utils/js';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import HighlightList from './highlight-lists-component';
import Can from 'src/components/ui/permissions/can';
import { SCOPES } from 'src/assets/utils/js/permissions';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import colors from 'src/theme/colors';
import { Danger } from 'node_modules/iconsax-react-native/src';
import Button, { ButtonSize } from 'src/components/ui/buttons/button';
import SubscriptionPermissionFallback from 'src/components/ui/permissions/subscription-permission-fallback';

interface ProductHighlightListProps {
  scrollHandler: ScrollHandlerProcessed<Record<string, unknown>>;
}

export interface ProductHighlightsResponse
  extends ResponseWithPagination<{
    items: HighlightInterface[];
    total_pages: number;
    next_page: number | null;
    sort?: string;
    total: number;
    page: number;
    prev_page?: number | null;
    per_page: number;
  }> {}

const PER_PAGE = 10;

const ProductHighlightList: React.FC<ProductHighlightListProps> = ({ scrollHandler }) => {
  const [highlights, setHighlights] = useState<HighlightInterface[]>([]);
  const [isRetrying, setIsRetrying] = useState(false);

  const { currentPage, goNext, setPage } = usePagination();

  const getHighlightsRequest = useApi<PaginateSearchParams, ProductHighlightsResponse>(
    {
      apiFunction: GET_HIGHLIGHTS,
      method: 'GET',
      key: GET_HIGHLIGHTS.name,
      onSuccess: response => {
        setHighlights(prev => ensureUniqueItems([...prev, ...response?.data?.items]));
      },
    },
    {
      filter: { search: '' },
      page: currentPage,
      per_page: PER_PAGE,
      sort: 'desc',
    },
  );

  const handlePullToRefresh = async () => {
    if (currentPage === 1) {
      getHighlightsRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        sort: 'desc',
      });
      await delay(800);
      setHighlights([]);
      return;
    }

    setPage(1);
    await delay(600);
  };

  const handleRetry = () => {
    setIsRetrying(true);
    handlePullToRefresh();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  return (
    <Can
      data={{ planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_HIGHLIGHTS }}
      fallback={<SubscriptionPermissionFallback actionName="create and view product highlights" />}>
      <QueryErrorBoundary
        error={getHighlightsRequest.error}
        isLoading={getHighlightsRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        padAround
        variant="fullPage"
        errorTitle="Failed to load Product highlights"
        customErrorMessage="We couldn't load your Product highlights. Please check your connection and try again.">
        <HighlightList
          scrollHandler={scrollHandler}
          {...{
            goNext,
            setPage,
            scrollHandler,
            currentPage,
            setHighlights,
            highlights,
            isLoading: getHighlightsRequest.isLoading,
            pullToRefreshFunc: handlePullToRefresh,
            error: getHighlightsRequest.error,
            total_pages: getHighlightsRequest.response?.data?.total_pages,
          }}
        />
      </QueryErrorBoundary>
    </Can>
  );
};

export default ProductHighlightList;
