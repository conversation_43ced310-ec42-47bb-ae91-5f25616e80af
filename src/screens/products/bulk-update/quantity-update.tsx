import { wp } from '@/assets/utils/js';
import { CircledIcon, Container } from '@/components/ui';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useRouteParams from '@/hooks/use-route-params';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { Add } from 'iconsax-react-native/src';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import SelectProductStep from 'src/components/orders/bulk-update/select-product-step';
import UpdateQtyStep from 'src/components/orders/bulk-update/update-qty-step';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useBulkUpdate from 'src/hooks/use-bulk-update';
import useModals from 'src/hooks/use-modals';
import useSteps from 'src/hooks/use-steps';
import { BulkUpdateMethod, UPDATE_QTY_STEPS } from '../types';

const BATCH_SIZE = 10;

const QuantityUpdate = () => {
  const params = useRouteParams<'QuantityUpdate'>();
  const { modals, toggleModal } = useModals(['customerInfo']);
  const [method, setMethod] = useState<BulkUpdateMethod>(BulkUpdateMethod.MANUAL);
  const scrollRef = useRef<ScrollView>(null);

  // const fromLowStockNotification = params?.fromLowStockNotification ?? false;
  // const isOutOfStockUpdate = params?.isOutOfStockUpdate ?? false;
  // const isLowStockUpdate = params?.isLowStockUpdate ?? false;
  const updateType = params?.updateType ?? 'bulkUpdate';
  const fromLowStockNotification = updateType !== 'bulkUpdate';
  const lowStockProducts = params?.lowStockProducts ?? [];

  const {
    getProductsRequest,
    bulkUpdateRequest,
    changeBatch,
    handleItemsSelect,
    store,
    handleFormUpdate,
    form,
    setForm,
    batchData,
    totalBatches,
    currentBatch,
    selected,
    completeUpdate,
  } = useBulkUpdate(scrollRef, lowStockProducts ?? []);

  const formSteps = useSteps(Object.values(UPDATE_QTY_STEPS), 0);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;

  useEffect(() => {
    if (fromLowStockNotification) {
      changeStep(UPDATE_QTY_STEPS.INCREASE_QTY_FORM);
    }
  }, [fromLowStockNotification]);

  const navigation = useNavigation();

  const onPressBack = () => {
    if (canPrevious && !fromLowStockNotification) {
      previous();
      return;
    } else {
      navigation.goBack();
    }
  };

  const disableBtn = useMemo(() => {
    if (step === UPDATE_QTY_STEPS.SELECT_PRODUCT) {
      if (selected.length < 1) return true;
    }
    return false;
  }, [step, selected]);

  const onPressContinue = () => {
    if (isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM)) {
      completeUpdate();
    } else {
      next();
    }
  };

  const buttons = [
    ...(method === BulkUpdateMethod.MANUAL &&
    isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM) &&
    !bulkUpdateRequest.isLoading &&
    currentBatch > 1
      ? [
          {
            text: 'Go Back',
            onPress: () => changeBatch('-'),
            disabled: currentBatch === 1,
            variant: ButtonVariant.LIGHT,
          },
        ]
      : []),
    ...(method === BulkUpdateMethod.AUTO || totalBatches === currentBatch || isActive(UPDATE_QTY_STEPS.SELECT_PRODUCT)
      ? [{ text: 'Continue', onPress: onPressContinue, disabled: disableBtn, isLoading: bulkUpdateRequest.isLoading }]
      : [{ text: 'Next Batch', onPress: () => changeBatch('+'), disabled: currentBatch === totalBatches }]),
    ,
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: typeConfigs.title[updateType],
        variant: HeaderVariants.SUB_LEVEL,
        onBackPress: onPressBack,
      }}>
      <ScrollView keyboardShouldPersistTaps={'handled'} ref={scrollRef}>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.YELLOW}
          isTextFollowPalette={false}
          iconElement={
            <Image
              source={require('@/assets/images/stacked-boxes.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[80px]"
            />
          }
          pageTitleTop={typeConfigs.heading[updateType][0]}
          pageTitleBottom={typeConfigs.heading[updateType][1]}
        />
        <Container className={'mt-20'}>
          <View style={{ display: isActive(UPDATE_QTY_STEPS.SELECT_PRODUCT) ? 'flex' : 'none' }}>
            <SelectProductStep
              selectedItems={selected.map(i => i.id)}
              isQuantity={true}
              {...{ method, handleItemsSelect, getProductsRequest, setMethod }}
            />
          </View>
          <View style={{ display: isActive(UPDATE_QTY_STEPS.INCREASE_QTY_FORM) ? 'flex' : 'none' }}>
            <UpdateQtyStep
              {...{
                selected,
                method,
                handleFormUpdate,
                batchData,
                getProductsRequest,
                isLowStockUpdate: fromLowStockNotification,
                updateType,
              }}
              currency={store.currencies.default}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={buttons} />
    </DashboardLayout>
  );
};

const typeConfigs = {
  heading: {
    lowStock: ['Update', 'Low Stock Items'],
    outOfStock: ['Update', 'Out of Stock Items'],
    bulkUpdate: ['Update Quantities', 'For Multiple Products'],
  },
  title: {
    lowStock: 'Update Low Stock Items',
    outOfStock: 'Update Out of Stock Items',
    bulkUpdate: 'Bulk Update Quantities',
  },
};

export default QuantityUpdate;
