import { BulkUpdateFormItem } from 'catlog-shared';


export enum UPDATE_PRICE_STEPS {
  SELECT_PRODUCT = 'SELECT_PRODUCT',
  INCREASE_PRICE_FORM = 'INCREASE_PRICE_FORM',
  SUCCESS = 'SUCCESS',
  // SELECT_UPDATE_TYPE = 'SELECT_UPDATE_TYPE',
}

export enum UPDATE_QTY_STEPS {
  SELECT_PRODUCT = 'SELECT_PRODUCT',
  INCREASE_QTY_FORM = 'INCREASE_QTY_FORM',
}

export enum BulkUpdateMethod {
  MANUAL = 'manual',
  AUTO = 'auto',
}

export type BulkUpdateForm = {
  [id: string]: BulkUpdateFormItem;
};

export interface AutoUpdateFormProps {
  price_action: 'increase' | 'decrease';
  update_method: 'percentage' | 'fixed';
  percentage: number;
  amount: number;
}