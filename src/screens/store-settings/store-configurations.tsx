import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText } from '@/components/ui';
import TopTabs from '@/components/ui/others/top-tabs';
import { useMemo, useState } from 'react';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import Customization from '@/components/store-settings/store-configuration/customization';
import OpeningClosingHours from '@/components/store-settings/store-configuration/opening-closing-hours';
import Currencies from '@/components/store-settings/store-configuration/currencies';
import ProductConfigurations from '@/components/store-settings/store-configuration/product-configurations';
import StoreMessages from '@/components/store-settings/store-configuration/store-messages';
import CustomDomain from '@/components/store-settings/store-configuration/custom-domain';
import { FormikProps, useFormik } from 'formik';
import useAuthContext from 'src/contexts/auth/auth-context';
import * as Yup from 'yup';
import useKeyboard from 'src/hooks/use-keyboard';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import { StoreConfiguration, UPDATE_STORE_DETAILS, UpdateStoreConfigParams, UpdateStoreParams } from 'catlog-shared';
import useRouteParams from 'src/hooks/use-route-params';
import useTabs from 'src/hooks/use-tabs';
import { StoreConfigurationTabs } from 'src/components/store-settings/types';
import CustomImage from 'src/components/ui/others/custom-image';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';
import ThirdPartyIntegrations from 'src/components/store-settings/store-configuration/third-party-integration';

export interface SharedStoreConfigProps {
  // form: FormikProps<UpdateStoreConfigParams>;
  form: FormikProps<UpdateStoreParams>;
  isLoading: boolean;
}

enum SelectionPillType {
  CUSTOMIZATION = 'Customization',
  OPENING_CLOSING_HOURS = 'Opening & Closing Hours',
  CURRENCIES = 'Currencies',
  PRODUCT_CONFIGURATIONS = 'Product Configurations',
  STORE_MESSAGES = 'Store Messages',
  THIRD_PARTY_INTEGRATIONS = 'Third Party Integrations',
  CUSTOM_DOMAINS = 'Custom Domains',
  // STORE_CONFIGURATIONS = 'Store Configurations',
  // STORE_CONFIGURATIONS = 'Store Configurations',
}

function StoreConfigurations() {
  const { store, updateStore } = useAuthContext();
  const params = useRouteParams<'StoreConfigurations'>();
  const { tabs, active, switchTab } = useTabs({
    tabs: Object.values(StoreConfigurationTabs),
    initialActiveTab: params?.tab ?? 0,
  });
  const keyboardIsVisible = useKeyboard();
  const { isFeatureEnabled } = useFeatureFlags();

  const updateStoreConfigRequest = useApi({
    key: 'update-store-config',
    apiFunction: UPDATE_STORE_DETAILS,
    method: 'PUT',
  });

  const form = useFormik<UpdateStoreConfigParams>({
    initialValues: {
      id: store?.id!,
      configuration: { ...store?.configuration },
    },
    validationSchema,
    onSubmit: async values => {
      const transformValues = {
        id: values.id,
        configuration: {
          ...values.configuration,
          hours: (values.configuration as any).hours === null ? {} : (values.configuration as any).hours,
        },
      };

      delete transformValues.configuration.low_stock_notifications_enabled
      delete transformValues.configuration.global_stock_threshold

      const [response, error] = await updateStoreConfigRequest.makeRequest(transformValues);

      if (error) {
        Toast.show({ type: 'error', text1: error?.message });
      } else {
        updateStore({ ...response.data });
        Toast.show({ text1: 'Store details updated successfully' });
      }
    },
  });

  const tabItems = [
    {
      key: StoreConfigurationTabs.CUSTOMIZATION,
      title: SelectionPillType.CUSTOMIZATION,

      component: <Customization form={form} isLoading={updateStoreConfigRequest.isLoading} />,
    },
    {
      key: StoreConfigurationTabs.OPENING_CLOSING_HOURS,
      title: SelectionPillType.OPENING_CLOSING_HOURS,
      component: <OpeningClosingHours form={form} isLoading={updateStoreConfigRequest.isLoading} />,
    },
    ...(isFeatureEnabled('subscriptions')
      ? [
        {
          key: StoreConfigurationTabs.CURRENCIES,
          title: SelectionPillType.CURRENCIES,
          component: <Currencies />,
        },
      ]
      : []),
    {
      key: StoreConfigurationTabs.PRODUCT_CONFIGURATIONS,
      title: SelectionPillType.PRODUCT_CONFIGURATIONS,
      component: <ProductConfigurations form={form} isLoading={updateStoreConfigRequest.isLoading} />,
    },
    {
      key: StoreConfigurationTabs.STORE_MESSAGES,
      title: SelectionPillType.STORE_MESSAGES,
      component: <StoreMessages form={form} isLoading={updateStoreConfigRequest.isLoading} />,
    },
    {
      key: StoreConfigurationTabs.THIRD_PARTY_INTEGRATIONS,
      title: SelectionPillType.THIRD_PARTY_INTEGRATIONS,
      component: <ThirdPartyIntegrations form={form} isLoading={updateStoreConfigRequest.isLoading} />,
      func: () => {},
    },
    {
      key: StoreConfigurationTabs.CUSTOM_DOMAINS,
      title: SelectionPillType.CUSTOM_DOMAINS,
      component: <CustomDomain />,
    },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Store Configuration',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {!keyboardIsVisible && (
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.RED}
          iconElement={
            <CustomImage
              transparentBg
              className="w-[80px] h-[80px]"
              imageProps={{ source: screenImage[tabItems[active].title], contentFit: 'contain' }}
            />
          }
          customElements={
            <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
              {tabItems[active].title}
            </BaseText>
          }
        />
      )}
      <TopTabs setIndex={switchTab} currentIndex={active} tabItems={tabItems} />
    </DashboardLayout>
  );
}

const validationSchema = Yup.object().shape({
  configuration: Yup.object().shape({
    view_modes: Yup.object().shape({
      default: Yup.string().required('Please select a default mode'),
    }),
  }),
});

export default StoreConfigurations;

const screenImage = {
  [SelectionPillType.CUSTOMIZATION]: require('@/assets/images/customization.png'),
  [SelectionPillType.OPENING_CLOSING_HOURS]: require('@/assets/images/store-hour.png'),
  [SelectionPillType.CURRENCIES]: require('@/assets/images/currency.png'),
  [SelectionPillType.PRODUCT_CONFIGURATIONS]: require('@/assets/images/product-settings.png'),
  [SelectionPillType.STORE_MESSAGES]: require('@/assets/images/store-messages.png'),
  [SelectionPillType.CUSTOM_DOMAINS]: require('@/assets/images/product-settings.png'),
};
