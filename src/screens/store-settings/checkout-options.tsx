import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText } from '@/components/ui';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { FormikProps, useFormik } from 'formik';
import { useState } from 'react';
import Configurations from '@/components/store-settings/checkout-option/configurations';
import TopTabs from '@/components/ui/others/top-tabs';
import CustomCheckoutForm from '@/components/store-settings/checkout-option/custom-checkout/custom-checkout-form';
import {
  UpdateStoreParams,
  InstagramCheckoutOption,
  StoreInterface,
  WhatsappCheckoutOption,
  getRandString,
  removeCountryCode,
} from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';

enum SelectionPillType {
  CONFIGURATIONS = 'Configurations',
  CUSTOM_CHECKOUT_FORMS = 'Custom Checkout Form',
}

export interface SharedStoreConfigProps {
  form: FormikProps<CustomUpdateStoreParams>;
}

export interface CustomUpdateStoreParams extends Omit<UpdateStoreParams, 'id'> {
  whatsapp?: WhatsappCheckoutOption[];
  instagram?: InstagramCheckoutOption;
}

function CheckoutOptions() {
  const [tabIndex, setTabIndex] = useState(0);

  const getIndex = (index: number) => {
    setTabIndex(index);
  };

  const tabItems = [
    {
      key: SelectionPillType.CONFIGURATIONS,
      title: SelectionPillType.CONFIGURATIONS,
      component: <Configurations />,
    },
    {
      key: SelectionPillType.CUSTOM_CHECKOUT_FORMS,
      title: SelectionPillType.CUSTOM_CHECKOUT_FORMS,
      component: <CustomCheckoutForm />,
    },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Checkout Options',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        pageTitleBottom="Checkout Options"
        isTextFollowPalette={false}
        iconElement={
          <CustomImage
            transparentBg
            className="w-80 h-80"
            imageProps={{ source: require('@/assets/images/checkout-option.png'), contentFit: 'cover' }}
          />
        }
      />
      <TopTabs setIndex={getIndex} currentIndex={tabIndex} tabItems={tabItems} />
    </DashboardLayout>
  );
}

export const generateOption = (countryCode?: string): WhatsappCheckoutOption => {
  const code = countryCode ? countryCode : '+234';

  return {
    id: getRandString(10),
    label: '',
    type: 'WHATSAPP',
    phone: `${code}-`,
    primary: false,
  };
};

export default CheckoutOptions;
