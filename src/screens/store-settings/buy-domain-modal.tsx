import { CloseCircle, Copy, InfoCircle, TickCircle } from 'iconsax-react-native/src';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { cx, delay, hideLoader, hp, showLoader, showSuccess, toCurrency, wp, Yup } from 'src/assets/utils/js';
import { BaseText, Row, SelectionPill } from 'src/components/ui';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import colors from 'src/theme/colors';
import Input from 'src/components/ui/inputs/input';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import {
  ADD_DOMAIN,
  CHECK_DOMAIN,
  DomainPurchase,
  INITIATE_DOMAIN_PURCHASE,
  InitiateDomainPurchaseParams,
  PAYMENT_TYPES,
} from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import useStatusbar from 'src/hooks/use-statusbar';
import Shimmer from 'src/components/ui/shimmer';
import PaymentsWidget from 'src/components/payments/payments-widget';
import useModals from 'src/hooks/use-modals';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  onAddDomainCompleted: VoidFunction;
}

const BuyDomainModal = ({ closeModal, onAddDomainCompleted, ...props }: Props) => {
  const [domain, setDomain] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [selectedPill, setSelectedPill] = useState<'suggestions' | 'others'>('suggestions');

  const [selectedDomain, setSelectedDomain] = useState<DomainPurchase>(null);

  const { modals, toggleModal } = useModals(['payments']);

  const activeDomain = useRef<string | null>(null);

  const checkDomainReq = useApi({
    apiFunction: CHECK_DOMAIN,
    key: CHECK_DOMAIN.name,
    autoRequest: false,
    method: 'GET',
  });

  const initiateDomainPurchase = useApi<InitiateDomainPurchaseParams, ResponseWithoutPagination<DomainPurchase>>({
    apiFunction: INITIATE_DOMAIN_PURCHASE,
    key: INITIATE_DOMAIN_PURCHASE.name,
    method: 'POST',
  });

  const hasError = checkDomainReq.error;
  const domainData = checkDomainReq.response?.data ?? null;
  const showAlternativesTabs = domainData?.alternatives.length > 0 && domainData?.recommended.length > 0;
  const suggestionData = useMemo(() => {
    return domainData?.[selectedPill === 'suggestions' ? 'recommended' : 'alternatives'];
  }, [selectedPill, domainData]);

  // Validate domain whenever it changes
  useEffect(() => {
    validateDomain(domain);
  }, [domain]);

  const validateDomain = async (value: string) => {
    try {
      await domainSchema.validate(value);
      setError(null);
      setIsValid(true);
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        setError(err.message);
      } else {
        setError('Invalid domain');
      }
      setIsValid(false);
    }
  };

  const handleCheckDomain = async () => {
    try {
      await domainSchema.validate(domain);
      activeDomain.current = domain;
      if (!isValid) return;
      const [response, error] = await checkDomainReq.makeRequest({ domain });
      // hideLoader();
      // if (response) {
      //   // await delay(1000);
      //   // showSuccess('Domain added successfully');
      // }
      // if (error) {
      //   console.log(error);
      // }
    } catch (error) {}
  };

  const handleBuyDomain = async (domain?: string) => {
    showLoader('Processing your request...');
    const [response, error] = await initiateDomainPurchase.makeRequest({
      domain: domain ? domain : activeDomain.current,
    });

    setSelectedDomain(response.data);
    hideLoader();
    await delay(700);
    toggleModal('payments');
  };

  // const handleBuyAlternativeDomain = async (domain: string) => {
  //   const [response, error] = await initiateDomainPurchase.makeRequest({
  //     domain: domain,
  //   });

  //   setSelectedDomain(response.data);
  //   await delay(700);
  //   toggleModal('payments');
  // };

  return (
    <BottomModal closeModal={closeModal} enableDynamicSizing useChildrenAsDirectChild title="Buy New Domain" {...props}>
      <BottomSheetScrollView style={{ paddingHorizontal: wp(20) }}>
        <View className="mt-15">
          <BaseText className="text-black-muted" lineHeight={20}>
            Enter the domain you'd like to buy (e.g. storename.com, storename.ng, etc) to check it's availability and
            pricing.
          </BaseText>
          <View className="mt-15">
            <BaseText fontSize={13} type="heading">
              Domain Name
            </BaseText>
            <Input
              hasError={!isValid}
              error={error}
              autoFocus
              keyboardType="url"
              placeholder="yourdomain.com"
              value={domain}
              useBottomSheetInput
              onSubmitEditing={handleCheckDomain}
              onChangeText={setDomain}
              returnKeyType="search"
              autoCapitalize="none"
              containerClasses="mt-15"
              rightAccessory={checkDomainReq.isLoading && <ActivityIndicator size={wp(14)} color={colors.grey.muted} />}
            />
          </View>
          {checkDomainReq.isLoading && <DomainCardSkeletonLoader />}
          {domainData?.available === false && !checkDomainReq.isLoading && (
            <DomainUnavailableMessage domain={activeDomain.current} />
          )}
          {domainData?.available && (
            <DomainAvailableMessage
              domain={activeDomain.current}
              price={domainData?.price}
              currency={domainData?.currency}
              loadingBuy={initiateDomainPurchase.isLoading}
              onClickBuyDomain={() => handleBuyDomain()}
              isUserRequestedDomain={true}
            />
          )}
        </View>
        {showAlternativesTabs && (
          <View>
            <View className="flex-row pt-20">
              <SelectionPill
                onPress={() => setSelectedPill('suggestions')}
                selected={selectedPill === 'suggestions'}
                title={'Suggested Domain'}
              />
              <SelectionPill
                onPress={() => setSelectedPill('others')}
                selected={selectedPill === 'others'}
                title={'Other Domains'}
              />
            </View>
            <View>
              {/* <DomainCardSkeletonLoader /> */}
              {suggestionData?.map(domain => (
                <DomainAvailableMessage
                  key={domain?.domain}
                  domain={domain.domain}
                  price={domain.price}
                  currency={domain.currency}
                  loadingBuy={initiateDomainPurchase.isLoading}
                  onClickBuyDomain={() => handleBuyDomain(domain?.domain)}
                />
              ))}
            </View>
          </View>
        )}
        <View className="h-80" />
      </BottomSheetScrollView>

      <PaymentsWidget
        onComplete={d => {
          onAddDomainCompleted?.();
          toggleModal('payments', false);
          closeModal();
        }}
        data={{
          paymentType: PAYMENT_TYPES.DOMAIN_PURCHASE,
          domain: selectedDomain,
          successMessage:
            'Your payment for this domain was successful. Your domain will be automatically linked to your store shortly, please check back after some time',
        }}
        show={modals.payments}
        toggle={s => toggleModal('payments', s)}
      />
    </BottomModal>
  );
};

export default BuyDomainModal;

// Define domain validation schema
const domainSchema = Yup.string()
  .required('Domain is required')
  .matches(
    /^(?!:\/\/)(?!www\.)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]$/,
    'Please enter a valid domain without http:// or www. (e.g., yourdomain.com)',
  );

interface DomainUnavailableProps {
  domain: string;
}

const DomainUnavailableMessage = ({ domain }: DomainUnavailableProps) => {
  return (
    <Row className="px-10 py-8 rounded-10 mt-10" style={{ backgroundColor: colors.notifications.error }}>
      <CloseCircle size={wp(15)} color={colors.accentRed.main} variant={'Bold'} />
      <View className="flex-1">
        <BaseText fontSize={12} classes="text-black-muted mx-8">
          <BaseText fontSize={12} classes="text-black-secondary mx-8" weight={'medium'}>
            {domain}
          </BaseText>{' '}
          is currently unavailable
        </BaseText>
      </View>
    </Row>
  );
};

interface DomainAvailableProps {
  domain: string;
  price: number;
  currency?: string;
  onClickBuyDomain: () => void;
  isUserRequestedDomain?: boolean;
  loadingBuy?: boolean;
}

const DomainAvailableMessage = ({
  domain,
  price,
  onClickBuyDomain,
  currency,
  isUserRequestedDomain,
  loadingBuy,
}: DomainAvailableProps) => {
  return (
    <Row
      className={cx(
        'p-10 rounded-10 mt-10',
        { 'bg-grey-bgOne': isUserRequestedDomain },
        { 'border border-grey-border': !isUserRequestedDomain },
      )}
      alignCenter={false}>
      {isUserRequestedDomain && <TickCircle size={wp(20)} color={colors.accentGreen.main} variant={'Bold'} />}
      <View className="flex-1 mx-8">
        <BaseText classes="text-black-muted">
          <BaseText classes="text-black-secondary" weight={'medium'}>
            {domain}
          </BaseText>{' '}
          {isUserRequestedDomain && 'is available'}
        </BaseText>
        <BaseText classes="text-black-placeholder mt-8" weight={'medium'}>
          {toCurrency(price, currency)}
        </BaseText>
      </View>
      <Button
        text="Buy Domain"
        size={ButtonSize.SMALL}
        onPress={onClickBuyDomain}
        variant={isUserRequestedDomain ? ButtonVariant.PRIMARY : ButtonVariant.LIGHT}
        isLoading={loadingBuy}
      />
    </Row>
  );
};

const DomainCardSkeletonLoader = ({ useBorder = false }: { useBorder?: boolean }) => {
  return (
    <Row
      className={cx('p-10 rounded-10 mt-10', { 'border border-grey-border': useBorder, 'bg-grey-bgOne': !useBorder })}
      alignCenter={false}>
      <Shimmer borderRadius={wp(20)} height={wp(20)} width={wp(20)} />
      <View className="flex-1 mx-8">
        <Shimmer borderRadius={wp(8)} height={hp(14)} width={wp(150)} />
        <Shimmer borderRadius={wp(8)} height={hp(12)} width={wp(100)} marginTop={hp(8)} />
      </View>
      <Shimmer borderRadius={wp(8)} height={hp(30)} width={wp(90)} />
    </Row>
  );
};
