import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText } from '@/components/ui';
import TopTabs from '@/components/ui/others/top-tabs';
import { useMemo, useState } from 'react';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import Customization from '@/components/store-settings/store-configuration/customization';
import OpeningClosingHours from '@/components/store-settings/store-configuration/opening-closing-hours';
import Currencies from '@/components/store-settings/store-configuration/currencies';
import ProductConfigurations from '@/components/store-settings/store-configuration/product-configurations';
import StoreMessages from '@/components/store-settings/store-configuration/store-messages';
import { FormikProps, useFormik } from 'formik';
import useAuthContext from 'src/contexts/auth/auth-context';
import * as Yup from 'yup';
import useKeyboard from 'src/hooks/use-keyboard';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import PaymentOptions from '@/components/store-settings/payment-settings/payment-options';
import SecurityPin from '@/components/store-settings/payment-settings/security-pin';
import { EmptyWallet } from 'iconsax-react-native/src';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import CircledIcon from '@/components/ui/circled-icon';
import { StoreConfiguration, UPDATE_STORE_DETAILS, UpdateStoreConfigParams } from 'catlog-shared';
import useTabs from 'src/hooks/use-tabs';
import { PaymentSettingsTabs } from 'src/components/store-settings/types';
import useRouteParams from 'src/hooks/use-route-params';
import CustomImage from 'src/components/ui/others/custom-image';

export interface SharedStoreConfigProps {
  form: FormikProps<UpdateStoreConfigParams>;
  isLoading: boolean;
}

function PaymentSettings() {
  const params = useRouteParams<'PaymentSettings'>();
  const { tabs, active, switchTab } = useTabs({
    tabs: Object.values(PaymentSettingsTabs),
    initialActiveTab: params?.tab ?? 0,
  });
  const keyboardIsVisible = useKeyboard();

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'Payments',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {!keyboardIsVisible && (
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.ORANGE}
          iconElement={
            <CustomImage
              className="w-[80px] h-[80px]"
              imageProps={{ source: require('@/assets/images/information.png'), contentFit: 'cover' }}
            />
          }
          customElements={
            <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
              {tabItems[active].title}
            </BaseText>
          }
        />
      )}
      <TopTabs setIndex={switchTab} currentIndex={active} tabItems={tabItems} />
    </DashboardLayout>
  );
}

const tabItems = [
  {
    key: PaymentSettingsTabs.PAYMENT_OPTIONS,
    title: 'Payment Options',
    component: <PaymentOptions />,
  },
  {
    key: PaymentSettingsTabs.SECURITY_PIN,
    title: 'Security Pin',
    component: <SecurityPin />,
  },
];

export default PaymentSettings;
