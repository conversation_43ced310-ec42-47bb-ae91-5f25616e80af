import { Dimensions, Image, Keyboard, ScrollView, Text, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ImageBackground } from 'react-native';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import colors from '@/theme/colors';
import { getFieldvalues, getWinDim, hp, toCurrency, wp } from '@/assets/utils/js';
import { useNavigation } from '@react-navigation/native';
import InfoBadge from '@/components/store-settings/info-badge';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import CustomizationCard from '@/components/ui/cards/customization-card';
import Pressable from '@/components/ui/base/pressable';
import Input from '@/components/ui/inputs/input';
import LeftLabelledInput from '@/components/ui/inputs/left-labelled-input';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import useAuthContext from 'src/contexts/auth/auth-context';
import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import Toast from 'react-native-toast-message';
import { Add, Edit2, Trash } from 'iconsax-react-native/src';
import {
  GET_DELIVERY_AREAS,
  GetDeliveryAreasParams,
  UPDATE_DELIVERY_AREAS,
  UpdateDeliveryAreasParams,
  DeliveryArea,
  CURRENCIES,
  getRandString,
} from 'catlog-shared';
import Shimmer from 'src/components/ui/shimmer';
import CustomImage from 'src/components/ui/others/custom-image';
import DeliveriesCard from '@/components/store-settings/delivery-areas/delivery-area-card';
import { generateOption } from '@/components/store-settings/delivery-areas/delivery-area-card';

function DeliveryAreas() {
  const { store } = useAuthContext();
  const navigation = useNavigation();
  const scrollRef = useRef<ScrollView>(null);

  const getDeliveriesArea = useApi<GetDeliveryAreasParams, ResponseWithoutPagination<DeliveryArea[]>>(
    {
      key: 'get-delivery-areas',
      apiFunction: GET_DELIVERY_AREAS,
      method: 'GET',
      onSuccess: response => {
        form.setFieldValue('data', [
          ...response?.data,
          {
            id: getRandString(10),
            fee: 0,
            name: '',
          },
        ]);
      },
    },
    { store: store?.id! },
  );

  const updateDeliveryAreaRequest = useApi<UpdateDeliveryAreasParams>({
    key: 'update-delivery-area',
    apiFunction: UPDATE_DELIVERY_AREAS,
    method: 'PUT',
  });

  const prevAreas = getDeliveriesArea?.response?.data ?? [];

  const form = useFormik<UpdateDeliveryAreasParams>({
    initialValues: {
      store: store?.id!,
      data: [],
    },
    validationSchema,
    onSubmit: async values => {
      const areasOption = values.data.filter(o => o.fee > 0 && o.name !== '');

      const [response, error] = await updateDeliveryAreaRequest.makeRequest({ store: values.store, data: areasOption });

      if (error) {
        Toast.show({ type: 'error', text1: error.body.message });
      } else {
        Toast.show({ text1: 'Delivery Areas updated successfully' });
      }
    },
  });

  const updateOption = (option: DeliveryArea, index: number) => {
    const optionsCopy = [...form.values.data];
    optionsCopy[index] = option;

    form.setFieldValue('data', optionsCopy);
  };

  const deleteOption = (index: number) => {
    const optionsCopy = [...form.values.data];
    optionsCopy.splice(index, 1);

    form.setFieldValue('data', optionsCopy);
  };

  const addOption = () => {
    const optionsCopy = [...form.values.data, generateOption()];
    form.setFieldValue('data', optionsCopy);
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Delivery Fees',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <AvoidKeyboard className='flex-1' behavior="padding">
        <ScrollView className="flex-1" ref={scrollRef} keyboardShouldPersistTaps={'handled'}>
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.YELLOW}
            iconElement={
              <CustomImage
                transparentBg
                className="w-[80px] h-[80px]"
                imageProps={{ source: require('@/assets/images/location.png'), contentFit: 'cover' }}
              />
            }
            customElements={
              <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
                Delivery Fees
              </BaseText>
            }
          />
          <View className="flex-1 px-20 mt-20 pb-20">
            <InfoBadge
              text={
                'Add areas you deliver to and how much it costs, so customers can select while checking out. Areas could be Ibeju Lekki, Lagos Island, Abuja, Greater Accra, e.t.c'
              }
            />
            <View className="mt-20 w-full">
              {getDeliveriesArea?.isLoading ? (
                <View style={{ rowGap: 10 }} className="">
                  {new Array(3).fill(null).map((i, idx) => (
                    <Shimmer key={idx} height={60} width={getWinDim(40)} classes="rounded-lg " />
                  ))}
                </View>
              ) : (
                form?.values?.data?.map((item, index) => (
                  <DeliveriesCard
                    key={index}
                    index={index}
                    currency={store?.currencies?.products}
                    deliveryAreaInfo={item}
                    updateOption={updateOption}
                    deleteOption={deleteOption}
                  />
                ))
              )}
              <Pressable className="self-start py-8 mt-5" onPress={addOption}>
                <Row className=" justify-start">
                  <Add size={wp(14)} color={colors.primary.main} />
                  <BaseText weight={'medium'} classes="text-primary-main ml-2">
                    Add Area
                  </BaseText>
                </Row>
              </Pressable>
            </View>
          </View>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save Delivery Areas',
            isLoading: updateDeliveryAreaRequest?.isLoading,
            onPress: () => form.submitForm(),
          },
        ]}
      />
    </DashboardLayout>
  );
}

const validationSchema = Yup.object().shape({
  store: Yup.string().required('Store is required'),
  data: Yup.array().required().min(1, 'Please Select at least one item'),
  tempDeliveryAreaName: Yup.string(),
  tempDeliveryAreaFee: Yup.string(),
  tempDeliveryAreaId: Yup.string(),
});

export default DeliveryAreas;
