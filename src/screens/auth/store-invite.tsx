import React from 'react';
import { ScrollView, View, Image } from 'react-native';
import AuthQuestion from '@/components/auth/auth-question';
import BaseScrollView from '@/components/ui/base/base-scrollview';
import PasswordInput from '@/components/ui/inputs/password-input';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import StoreInviteInfo from '@/components/auth/store-invite-info';
import Button from '@/components/ui/buttons/button';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';

const StoreInvite = () => {
  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-primary-pastel' }}>
      <BaseScrollView>
        <ScreenInfoHeader
          iconElement={
            <Image
              source={require('@/assets/images/store-invite.png')}
              resizeMode={'contain'}
              className="w-[80px] h-[60px]"
            />
          }
          pageTitleTop={'Reset your'}
          colorPalette={ColorPaletteType.ORANGE}
          pageTitleBottom={'Password'}
        />
        <Container className="pt-24">
          <Input keyboardType={'email-address'} label={'Email Address'} containerClasses="mt-15" />
          <PasswordInput label={'Password'} containerClasses="mt-15" />
        </Container>
      </BaseScrollView>
      <View className="pt-10 px-20 border-t border-t-grey-border">
        <Button onPress={() => {}} text={'Join store'} />
      </View>
    </AuthLayout>
  );
};

export default StoreInvite;
