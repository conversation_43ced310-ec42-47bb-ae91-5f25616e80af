import { delay, getFieldvalues, showError, wp } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { BaseText } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import { useApi } from '@/hooks/use-api';
import useStatusbar from '@/hooks/use-statusbar';
import { useNavigation } from '@react-navigation/native';
import {
  CHECK_REFERAL_CODE_VALIDITY,
  COUNTRIES,
  COUNTRY_CURRENCY_MAP,
  CheckReferralCodeValidityParams,
  CreateStoreParams,
  GET_COUNTRIES,
  GET_SLUG_SUGGESTION,
  PhoneInterface,
  SignUpParamsV2,
} from 'catlog-shared';
import { useFormik } from 'formik';
import { ArrowDown2, EmojiNormal, Shop, TickCircle } from 'iconsax-react-native/src';
import React, { useEffect, useMemo, useRef } from 'react';
import { ActivityIndicator, Image, NativeSyntheticEvent, TextInputFocusEventData, View } from 'react-native';
import AuthQuestion from 'src/components/auth/auth-question';
import ConfirmCountryModal from 'src/components/auth/confirm-country-modal';
import { currencyDetails } from 'src/components/payments/wallet-cards';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import Pressable from 'src/components/ui/base/pressable';
import { CardSelectorItem } from 'src/components/ui/cards/card-selector';
import CircledIcon from 'src/components/ui/circled-icon';
import PasswordInput from 'src/components/ui/inputs/password-input';
import AuthLayout from 'src/components/ui/layouts/auth-layout';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import useGuestContext from 'src/contexts/auth/guest-context';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import * as Yup from 'yup';

export const SUPPORTED_COUNTRIES = [COUNTRIES.NG, COUNTRIES.GH, COUNTRIES.ZA, COUNTRIES.KE];
export const COUNTRY_TO_DIAL_CODE = {
  [COUNTRIES.NG]: '+234',
  [COUNTRIES.GH]: '+233',
  [COUNTRIES.ZA]: '+27',
  [COUNTRIES.KE]: '+254',
};

interface CreateStoreFormParams extends Omit<CreateStoreParams, 'phone'> {
  store_name: string;
  phone: PhoneInterface;
  email: string;
  password: string;
  referral_code: string;
  slug: string;
}

interface ValidityData {
  exists: boolean;
  referral?: string;
  user?: {
    name: string;
    id: string;
  };
}

const SignupV2 = () => {
  const { registerV2, signupRequest2, visitorCountry } = useGuestContext();
  const { setStatusBar } = useStatusbar();
  const navigation = useNavigation();
  // const [step, setStep] = useState<'signup' | 'store-type'>('signup');
  const { modals, toggleModal } = useModals(['confirmCountry']);

  const countryOptionRef = useRef<DropDownMethods>();

  setStatusBar('dark', 'transparent', true);

  const slugSuggestionReq = useApi({
    apiFunction: GET_SLUG_SUGGESTION,
    method: 'GET',
    key: 'suggest-store-slug',
    autoRequest: false,
  });

  const refCodeValidityReq = useApi<CheckReferralCodeValidityParams>({
    apiFunction: CHECK_REFERAL_CODE_VALIDITY,
    method: 'GET',
    key: 'check-referral-code-validity',
    autoRequest: false,
    config: {
      cacheTime: 0,
      staleTime: 0,
    },
  });

  const supportedCountriesReq = useApi({
    key: 'supported-countries',
    apiFunction: GET_COUNTRIES,
    method: 'GET',
  });

  let validityData: ValidityData = refCodeValidityReq?.response?.data;
  const supportedCountries: { name: string; emoji: string; code: string }[] =
    supportedCountriesReq?.response?.data ?? [];

  const countryOptions = useMemo(
    () =>
      supportedCountries.map(c => ({
        label: c.name,
        inputLabel: `${c.name} ${c.emoji}`,
        value: c.code,
        leftElement: <View className="w-full h-full">{currencyDetails[COUNTRY_CURRENCY_MAP[c.code]].icon()}</View>,
      })),
    [supportedCountries],
  );

  const prefilledCountry = useMemo(() => {
    if (visitorCountry && visitorCountry.code && SUPPORTED_COUNTRIES.includes(visitorCountry.code)) {
      return visitorCountry.code;
    }

    return SUPPORTED_COUNTRIES[0];
  }, [visitorCountry]);

  const form = useFormik<CreateStoreFormParams>({
    initialValues: {
      store_name: '',
      name: '',
      email: '',
      phone: {
        digits: '',
        code: visitorCountry?.dial_code ?? '+234',
      },
      country: prefilledCountry ? prefilledCountry : COUNTRIES.NG,
      description: '',
      store_type: STORE_TYPES.REGULAR,
      password: '',
      logo: '',
      referral_code: '',
      slug: undefined,
    },
    validationSchema: validationSchema(),
    onSubmit: async _ => {
      if (validityErrorText) {
        showError(null, 'Referral code is invalid, please change or remove it');
        return;
      }
      toggleModal('confirmCountry', true);
    },
  });

  useEffect(() => {
    if (form.errors.store_type) {
      showError(null, form.errors.store_type.toString());
    }
  }, [form.errors]);

  useEffect(() => {
    form.setFieldValue('phone.code', COUNTRY_TO_DIAL_CODE[form.values.country]);
  }, [form.values.country]);

  const createStore = async () => {
    toggleModal('confirmCountry', false);
    const values = form.values;

    const requestData: SignUpParamsV2 = {
      business_name: values.store_name,
      country: values.country,
      email: values.email,
      name: values.name,
      password: values.password,
      referral_code: values.referral_code,
      phone: `${values.phone.code}-${values.phone.digits}`,
      store_type: values.country === 'NG' ? values.store_type : STORE_TYPES.REGULAR,
      recaptcha_token: '',
    };

    const [response, err] = await registerV2(requestData);
    const error = err.body;

    if (error) {
      showError(formatErrorResponse(error));
      if (error.fields && Object.keys(error.fields).length > 0) {
        // setStep('signup');
        const { phone, ...others } = error.fields;

        form.setErrors({
          ...others,
          phone: phone && { digits: phone },
        });
      }
    }
  };

  const validityError = refCodeValidityReq?.error || (validityData && !validityData?.exists);
  const validityErrorText = validityError
    ? 'Invalid Referral Code'
    : form.errors.referral_code && form.touched.referral_code
      ? form?.errors?.referral_code
      : null;

  const checkReferralCodeValidity = async (code: string) => {
    const [res, err] = await refCodeValidityReq.makeRequest({ code });

    if (err || !res?.data?.exists) {
      form.setFieldError('referral_code', 'Invalid Referral Code');
      await delay(2000);
      refCodeValidityReq.reset();

      form.setFieldValue('referral_code', '');
      form.setFieldTouched('referral_code', false);
      form.setFieldError('referral_code', undefined);
    }
  };

  const handleRefCodeBlur = async (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    const code = form.values.referral_code;
    form.setFieldTouched('referral_code', true);

    if (code) {
      if (!code.trim()) {
        form.setFieldError('referral_code', '');
        await refCodeValidityReq.reset();
        return;
      }

      checkReferralCodeValidity(code);
    }
  };

  const handleBusinessNameBlur = async (name: string) => {
    const [res, err] = await slugSuggestionReq.makeRequest({ store_name: name });
    if (res) {
      form.setFieldValue('slug', res.data.slug);
    }
  };

  const selectedCountry = countryOptions.find(c => c.value === (form?.values?.country ?? 'NG'));

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-primary-pastel' }}>
      <AvoidKeyboard className="flex-1">
        <BaseScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/storefront.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[80px]"
              />
            }
            pageTitleTop={`Create your Online`}
            pageTitleBottom={'Store in 5 minutes'}
            customElements={
              <View>
                {supportedCountriesReq.isLoading ? (
                  <View className="flex-row items-center bg-white py-8 px-10 rounded-full mt-10 -mb-8">
                    <ActivityIndicator size={'small'} color={colors.grey.muted} />
                  </View>
                ) : (
                  <Pressable
                    className="flex-row items-center bg-white py-8 px-10 rounded-full mt-10 -mb-8"
                    onPress={() => countryOptionRef?.current.open()}>
                    <View className="w-14 h-14">{selectedCountry?.leftElement}</View>
                    <BaseText weight="medium" classes="text-black-muted ml-4 mr-5">
                      {selectedCountry?.label}
                    </BaseText>
                    <ArrowDown2 size={wp(14)} strokeWidth={2.5} color={colors.grey.muted} />
                  </Pressable>
                )}
              </View>
            }
          />

          <Container className="pt-24">
            <Input label="Full Name" {...getFieldvalues('name', form)} />
            <Input
              containerClasses="mt-15"
              label="Business Name"
              {...getFieldvalues('store_name', form)}
              onBlur={e => handleBusinessNameBlur(e.nativeEvent.text)}
              rightAccessory={
                <Row>
                  {slugSuggestionReq?.isLoading && <ActivityIndicator size="small" color={colors.grey.muted} />}
                </Row>
              }
            />
            {form.values?.slug && (
              <Row disableSpread className="mt-8">
                <TickCircle size={wp(12)} color={colors.accentGreen.main} />
                <BaseText fontSize={12} classes="ml-5 text-grey-mutedDark -mt-2">
                  {form.values?.slug}.catlog.shop{' '}
                  <BaseText fontSize={12} classes="ml-5 text-grey-muted -mt-2">
                    (Editable later)
                  </BaseText>
                </BaseText>
              </Row>
            )}
            <Input
              keyboardType={'email-address'}
              label={'Email Address'}
              autoCapitalize="none"
              containerClasses="mt-15"
              {...getFieldvalues('email', form)}
            />
            <PhoneNumberInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
              label="Phone number (Whatsapp)"
            />
            <PasswordInput label={'Password'} containerClasses="mt-15" {...getFieldvalues('password', form)} />
            <Input
              label={'Referral Code'}
              containerClasses="mt-15"
              {...getFieldvalues('referral_code', form)}
              onBlur={handleRefCodeBlur}
              rightAccessory={
                <Row>
                  {refCodeValidityReq?.isLoading && <ActivityIndicator size="small" color={colors.primary.main} />}
                  {validityData?.exists && <TickCircle variant="Bold" size={22} color={colors.accentGreen.main} />}
                </Row>
              }
            />
          </Container>
        </BaseScrollView>
        <SelectDropdown
          showLabel
          ref={countryOptionRef}
          showAnchor={false}
          items={countryOptions.map(o => ({ ...o, leftElement: <View className="w-24 h-24">{o.leftElement}</View> }))}
          label={'Where is this business based?'}
          onPressItem={value => form.setFieldValue('country', value)}
          selectedItem={form.values.country}
          containerClasses="mt-15"
        />
        <AuthQuestion
          question={'Already have an account?'}
          actionText={'Login'}
          onPress={() => navigation.navigate('Login')}
        />
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: signupRequest2?.isLoading ? 'Creating Account...' : 'Continue',
            disabled: signupRequest2?.isLoading,
            // onPress: () => form.handleSubmit(),
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
      <ConfirmCountryModal
        isVisible={modals.confirmCountry}
        closeModal={() => toggleModal('confirmCountry', false)}
        selectedCountry={form.values.country}
        countries={countryOptions}
        onPressContinue={createStore}
        onselectCountry={value => form.setFieldValue('country', value)}
      />
    </AuthLayout>
  );
};

enum STORE_TYPES {
  REGULAR = 'REGULAR',
  RESTAURANT = 'RESTAURANT',
}

const storeTypeOptions: CardSelectorItem[] = [
  {
    title: 'Storefront',
    value: STORE_TYPES.REGULAR,
    description: 'Get an online store in just 5 minutes! Easily manage records, collect payments, and book deliveries.',
    icon: (
      <CircledIcon className=" bg-accentYellow-pastel w-40 h-40">
        <Shop variant="Bold" color={colors.accentYellow.main} />
      </CircledIcon>
    ),
  },
  {
    title: 'Chowbot ',
    value: STORE_TYPES.RESTAURANT,
    description: 'Ordering chatbot on Whatsapp for food businesses & cloud kitchens + everything in storefronts.',
    icon: (
      <CircledIcon className=" bg-accentGreen-pastel w-40 h-40">
        <EmojiNormal variant="Bold" color={colors.accentGreen.main} />
      </CircledIcon>
    ),
  },
];

const validationSchema = () =>
  Yup.object().shape({
    store_name: Yup.string().required('Store name is required'),
    phone: phoneValidation(),
    name: Yup.string().required('Name is required'),
    email: Yup.string().email('Invalid email address').required('Email address is required'),
    password: Yup.string().required('Please choose a password'),
    // store_type: step === 'store-type' ? Yup.string().required("Please select how you'll like to use Catlog") : null,
  });

export default SignupV2;

function formatErrorResponse(error) {
  // Create a copy of the original error object
  const formattedError = { ...error };

  // Try to get the first field error message from body.fields or top-level fields
  const bodyFields = error.body?.fields;
  const topLevelFields = error.fields;

  if (bodyFields && Object.keys(bodyFields).length > 0) {
    // Use the first field error from body.fields
    formattedError.message = bodyFields[Object.keys(bodyFields)[0]];
  } else if (topLevelFields && Object.keys(topLevelFields).length > 0) {
    // Use the first field error from top-level fields
    formattedError.message = topLevelFields[Object.keys(topLevelFields)[0]];
  }
  // If no fields exist, the original message is kept

  return formattedError;
}
