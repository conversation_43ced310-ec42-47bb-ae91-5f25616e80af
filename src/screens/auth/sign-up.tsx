import { getFieldvalues } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import AuthQuestion from '@/components/auth/auth-question';
import StoreInviteInfo from '@/components/auth/store-invite-info';
import BaseScrollView from '@/components/ui/base/base-scrollview';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import Input from '@/components/ui/inputs/input';
import PasswordInput from '@/components/ui/inputs/password-input';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import useGuestContext from '@/contexts/auth/guest-context';
import { useApi } from '@/hooks/use-api';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { CHECK_REFERAL_CODE_VALIDITY, CheckReferralCodeValidityParams, SignUpParams } from 'catlog-shared';
import { useFormik } from 'formik';
import { TickCircle } from 'iconsax-react-native/src';
import React, { useState } from 'react';
import { ActivityIndicator, Image, NativeSyntheticEvent, ScrollView, TextInputFocusEventData } from 'react-native';
import * as Yup from 'yup';

export interface SignUpFormParams extends Omit<SignUpParams, 'phone'> {
  phone: { code: string; digits: string };
}

interface ValidityData {
  exists: boolean;
  referral?: string;
  user?: {
    name: string;
    id: string;
  };
}

const Signup = () => {
  const navigation = useNavigation();
  const [errorText, setErrorText] = useState('');

  const { register, signupRequest } = useGuestContext();
  const refCodeValidityReq = useApi<CheckReferralCodeValidityParams>({
    apiFunction: CHECK_REFERAL_CODE_VALIDITY,
    method: 'GET',
    key: 'check-referral-code-validity',
    autoRequest: false,
    config: {
      cacheTime: 0,
      staleTime: 0,
    },
  });
  let validityData: ValidityData = refCodeValidityReq?.response?.data;

  //Todo: how do we handle linking from referrals

  const form = useFormik<SignUpFormParams>({
    initialValues: {
      name: '',
      email: '',
      phone: {
        code: '+234',
        digits: '',
      },
      password: '',
      referral_code: '',
      recaptcha_token: '',
    },
    validationSchema,
    onSubmit: async values => {
      const phone = `${values.phone.code}-${values.phone.digits}`;

      if (validityErrorText) {
        setErrorText('Referral code is invalid, please change or remove it');
        return;
      }

      const [response, error] = await register({ ...values, phone }, false);

      // if (response?.user) {
      //   fbq("track", "Lead");
      // }

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({
            ...error.fields,
            phone: error.fields.phone && { digits: error.fields.phone },
          });
        } else {
          setErrorText(error.message);
        }
      }
    },
  });

  const validityError = refCodeValidityReq?.error || (validityData && !validityData?.exists);
  const validityErrorText = validityError
    ? 'Invalid Referral Code'
    : form.errors.referral_code && form.touched.referral_code
      ? form?.errors?.referral_code
      : null;

  const checkReferralCodeValidity = async (code: string) => {
    const [res, err] = await refCodeValidityReq.makeRequest({ code });

    if (err || !res?.data?.exists) {
      form.setFieldError('referral_code', 'Invalid Referral Code');

      // setTimeout(() => {
      //   form.setFieldTouched('referral_code', true);
      // }, 500);
    }
  };

  const handleRefCodeBlur = async (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    const code = form.values.referral_code;
    form.setFieldTouched('referral_code', true);

    if (code) {
      if (!code.trim()) {
        form.setFieldError('referral_code', '');
        await refCodeValidityReq.reset();
        return;
      }

      checkReferralCodeValidity(code);
    }
  };

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-primary-pastel' }}>
      <AvoidKeyboard>
        <BaseScrollView>
          <ScreenInfoHeader
            iconElement={
              <Image
                source={require('@/assets/images/storefront.png')}
                resizeMode={'contain'}
                className="w-[80px] h-[60px]"
              />
            }
            pageTitleTop={'Start selling better'}
            pageTitleBottom={'Create your account'}
          />
          <Container className="pt-24 pb-24">
            <Input label="Full Name" {...getFieldvalues('name', form)} />
            <Input
              keyboardType={'email-address'}
              label={'Email Address'}
              containerClasses="mt-15"
              {...getFieldvalues('email', form)}
            />
            <PhoneNumberInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
            />
            <PasswordInput label={'Password'} containerClasses="mt-15" {...getFieldvalues('password', form)} />
            <Input
              label={'Referral Code'}
              containerClasses="mt-15"
              {...getFieldvalues('referral_code', form)}
              onBlur={handleRefCodeBlur}
              rightAccessory={
                <Row>
                  {refCodeValidityReq?.isLoading && <ActivityIndicator size="small" color={colors.primary.main} />}
                  {validityData?.exists && <TickCircle variant="Bold" size={22} color={colors.accentGreen.main} />}
                </Row>
              }
            />
            {validityData?.exists && validityData?.user?.name && (
              <StoreInviteInfo className="mt-15" name={validityData?.user?.name?.split(' ')[0]} />
            )}
          </Container>
        </BaseScrollView>
      </AvoidKeyboard>

      <AuthQuestion
        question={'Already have an account?'}
        actionText={'Login'}
        onPress={() => navigation.navigate('Login')}
      />
      <FixedBtnFooter
        buttons={[
          {
            text: signupRequest?.isLoading ? 'Creating Account...' : 'Create Account',
            onPress: () => form.handleSubmit(),
            disabled: signupRequest.isLoading,
          },
        ]}
      />
    </AuthLayout>
  );
};

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address').required('Email address is required'),
  password: Yup.string().required('Please choose a password'),
  // recaptcha_token: Yup.string().required("Recaptcha verification is required"),
});

export default Signup;
