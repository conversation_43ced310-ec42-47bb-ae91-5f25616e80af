import React, { useEffect, useState } from 'react';
import { View, Image, ActivityIndicator } from 'react-native';
import AuthLayout from '@/components/ui/layouts/auth-layout';
import colors from 'src/theme/colors';
import useRouteParams from 'src/hooks/use-route-params';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useNavigation } from '@react-navigation/native';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { ACCEPT_INVITE, AcceptInviteParams, GET_INVITE, GetInviteParams, StoreInviteDetails } from 'catlog-shared';
import { getFieldvalues, hp, showError, showSuccess, wp, Yup } from 'src/assets/utils/js';
import { ScrollView } from 'react-native';
import ScreenInfoHeader from 'src/components/ui/screen-info-header';
import { ColorPaletteType } from 'src/constant/static-data';
import { BaseText, Container } from 'src/components/ui';
import Input from 'src/components/ui/inputs/input';
import PasswordInput from 'src/components/ui/inputs/password-input';
import PhoneNumberInput from 'src/components/ui/inputs/phone-number-input';
import { useFormik } from 'node_modules/formik/dist';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import useGuestContext from 'src/contexts/auth/guest-context';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import Shimmer from 'src/components/ui/shimmer';
import Row from 'src/components/ui/row';

// to test

// do : npx uri-scheme open exp://192.168.0.181:8081/--/invite/636262687hwuhs237 --ios

// or xcrun simctl openurl booted "catlog://join-store?invite=65e642f0533b9700097091dc" if that did not work

// it should open this screen and the invite params should be console logged

interface ExtendedAcceptInviteParams extends Omit<AcceptInviteParams, 'phone' | 'id'> {
  phone: {
    code: string;
    digits: string;
  };
}

const ManageInvite = () => {
  const [invite, setInvite] = useState<StoreInviteDetails>(null);
  const { isAuthenticated, user } = useAuthContext();
  const { handleSuccess } = useGuestContext();
  const param = useRouteParams<'ManageInvite'>();

  const navigation = useNavigation();

  const phoneSplits = invite?.phone?.split('-');

  const getInvite = useApi<GetInviteParams, ResponseWithoutPagination<StoreInviteDetails>>({
    apiFunction: GET_INVITE,
    method: 'GET',
    key: 'get-invite',
    autoRequest: false,
  });

  const acceptInviteRequest = useApi<AcceptInviteParams>({
    apiFunction: ACCEPT_INVITE,
    method: 'PUT',
    key: 'accept-invite',
  });

  const form = useFormik<ExtendedAcceptInviteParams>({
    initialValues: {
      name: '',
      email: invite?.email ?? '',
      phone: {
        code: phoneSplits?.[0] ?? '+234',
        digits: phoneSplits?.[1] ?? '',
      },
      password: '',
    },
    validationSchema,
    onSubmit: async values => {
      const { name, email, phone, password } = values;
      const [res, error] = await acceptInviteRequest.makeRequest({
        name,
        email,
        phone: `${phone.code}-${phone.digits}`,
        password,
        id: invite.id,
      });
      if (res) {
        handleSuccess(res.data, false);
        showSuccess(`You've successfully joined ${invite?.store?.name}`);
        navigation.navigate('AppNavigation');
      }
    },
  });

  useEffect(() => {
    if (param.invite) {
      getInviteDetail();
    }
  }, [param.invite]);

  const getInviteDetail = async () => {
    const [res, err] = await getInvite.makeRequest({ id: param?.invite });

    if (err) {
      showError(err);
      if (isAuthenticated) {
        navigation.navigate('AppNavigation');
      } else {
        navigation.navigate('AuthNavigation');
      }
      return;
    }

    if (res.data.user) {
      if (isAuthenticated) {
        if (res.data.user === user?.id) {
          navigation.navigate('AppNavigation', {
            screen: 'MainNavigation',
            params: {
              screen: 'HomeTab',
              params: {
                screen: 'Home',
                params: res.data,
              },
            },
          });
        } else {
          showError('Sorry, this invite is not for you!!');
          navigation.navigate('AppNavigation');
        }
        return;
      }
      return;
    }

    // if (res.data.user && isAuthenticated) {
    //   navigation.navigate('AppNavigation', {
    //     screen: 'MainNavigation',
    //     params: {
    //       screen: 'HomeTab',
    //       params: {
    //         screen: 'Home',
    //         params: res.data,
    //       },
    //     },
    //   });
    // }

    form.setFieldValue('email', res.data.email);
    setInvite(res.data);
  };

  const handleInvite = (inviteData: StoreInviteDetails) => {
    if (isAuthenticated) {
      navigation.navigate('AppNavigation', {
        screen: 'MainNavigation',
        params: {
          screen: 'HomeTab',
          params: {
            screen: 'Home',
            params: inviteData,
          },
        },
      });
    }
  };

  return (
    <AuthLayout headerProps={{ backgrounColor: 'bg-accentOrange-pastel' }}>
      {getInvite.isLoading && <SkeletonLoader />}
      {!getInvite.isLoading && Boolean(!invite?.user) && (
        <View className="flex-1">
          <ScrollView>
            <ScreenInfoHeader
              iconElement={
                <Image
                  source={require('@/assets/images/storefront.png')}
                  resizeMode={'contain'}
                  className="w-[80px] h-[60px]"
                />
              }
              pageTitleTop={"You've been invited to join"}
              colorPalette={ColorPaletteType.ORANGE}
              customElements={
                <BaseText fontSize={20} weight={'bold'} classes={`text-accentOrange-main mt-10`} type="heading">
                  {invite?.store?.name}
                  <BaseText fontSize={20} weight={'light'} classes={`text-black-main`} type="heading">
                    {' '}
                    on Catlog
                  </BaseText>
                </BaseText>
              }
              // pageTitleBottom={'Password'}
            />
            <Container className="pt-24">
              <Input label={'Full name'} containerClasses="mt-15" {...getFieldvalues('name', form)} />
              <Input
                keyboardType={'email-address'}
                label={'Email Address'}
                containerClasses="mt-15"
                editable={false}
                {...getFieldvalues('email', form)}
              />
              <PhoneNumberInput
                containerClasses="mt-15"
                {...getFieldvalues('phone', form)}
                onChange={value => form.setFieldValue('phone', value)}
              />
              <PasswordInput label={'Password'} containerClasses="mt-15" {...getFieldvalues('password', form)} />
            </Container>
          </ScrollView>
          <FixedBtnFooter
            buttons={[
              { text: 'Join Store', onPress: () => form.submitForm(), isLoading: acceptInviteRequest.isLoading },
            ]}
          />
        </View>
      )}
    </AuthLayout>
  );
};

export default ManageInvite;

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address').required('Email Address is required'),
  password: Yup.string().required('Please choose a password'),
});

const SkeletonLoader = () => {
  return (
    <View className="items-center justify-center p-30">
      <Shimmer borderRadius={hp(800)} height={hp(80)} width={hp(80)} />
      <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(180)} marginTop={hp(10)} />
      <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(180)} marginTop={hp(5)} />
    </View>
  );
};
