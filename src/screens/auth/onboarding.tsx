import React, { useCallback, useEffect, useState } from 'react';
import { View, Image, FlatList, Dimensions, ImageBackground } from 'react-native';
import Button from '@/components/ui/buttons/button';
import { Animated } from 'react-native';
import { heightPercentageToDP, hp, wp } from 'src/assets/utils/js';
import CustomImage from 'src/components/ui/others/custom-image';
import { BaseText } from 'src/components/ui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from 'src/theme/colors';
import { useNavigation } from '@react-navigation/native';
import useStatusbar from 'src/hooks/use-statusbar';
import { mmkvStorage } from 'src/assets/utils/js/zustand-mmkv-storage';
import useGuestContext from 'src/contexts/auth/guest-context';
import { COUNTRIES } from 'catlog-shared';

const windowWidth = Dimensions.get('window').width;

const scrollX = new Animated.Value(0);

const Onboarding = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { setStatusBar } = useStatusbar();

  const { visitorCountry } = useGuestContext();

  setStatusBar('dark', 'transparent', true);

  useEffect(() => {
    mmkvStorage.clearAll();
  }, []);

  const renderItem = ({ item, index }: { item: OnboardingDataType; index: number }) => {
    return (
      <View
        className="flex-1"
        style={{
          flex: 1,
          width: windowWidth,
          paddingBottom: insets.bottom + 12,
        }}>
        <ImageBackground className={`flex-1 justify-end`} style={{ marginBottom: hp(40) }} source={item.imageBg}>
          <CustomImage
            className="w-full"
            style={{ height: heightPercentageToDP(65) }}
            imageProps={{ source: item.image }}
          />
          {item.otherElement}
        </ImageBackground>
        <View className="px-20 mt-20">
          <View className="mb-24">
            <BaseText type="heading" lineHeight={32} fontSize={24} classes="mt-10 text-center">
              {item.title}
            </BaseText>
            <BaseText fontSize={16} lineHeight={24} classes="text-center mt-10 text-black-muted">
              {item.description}
            </BaseText>
          </View>
          <Button text="Get Started" onPress={onPressGetStarted} />
        </View>
      </View>
    );
  };

  const onPressGetStarted = () => {
    mmkvStorage.set('isOnboarded', JSON.stringify(true));
    navigation.reset({ index: 0, routes: [{ name: 'Login' }] });
  };

  return (
    <View className="flex-1 bg-white">
      <FlatList
        pagingEnabled
        data={onboardingData(visitorCountry?.code ?? COUNTRIES.NG)}
        keyExtractor={item => item.title}
        horizontal
        scrollEnabled
        snapToInterval={windowWidth}
        decelerationRate='fast'
        showsHorizontalScrollIndicator={false}
        snapToAlignment={'center'}
        scrollEventThrottle={20}
        renderItem={renderItem}
        onScroll={Animated.event(
          [
            {
              nativeEvent: { contentOffset: { x: scrollX } },
            },
          ],
          { useNativeDriver: false },
        )}
      />
      {/* <InfoContainer /> */}
      <Dots scrollX={scrollX} length={3} />
    </View>
  );
};

export default Onboarding;

interface DotsProps {
  scrollX: Animated.Value;
  onboardingData?: typeof onboardingData;
  length?: number;
}

const Dots = (props: DotsProps) => {
  const { scrollX, length } = props;
  const stepPosition = Animated.divide(scrollX, windowWidth);

  const dotColors = { active: colors.primary.main, inactive: colors.primary.extraLight };

  return (
    <View className="w-full flex-row justify-center py-16 items-center absolute" style={{ bottom: hp(230) }}>
      {Array(length)
        .fill(null)
        .map((item, index) => {
          const backgroundColor = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [dotColors.inactive, dotColors.active, dotColors.inactive],
            extrapolate: 'clamp',
          });

          const height = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(6), hp(6), hp(6)],
            extrapolate: 'clamp',
          });

          const width = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(6), hp(30), hp(6)],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={`step-${index}`}
              className="mx-3 rounded-full"
              style={{ backgroundColor, height, width }}
            />
          );
        })}
    </View>
  );
};

interface CountryAssets {
  image: readonly [any, any, any];
  element: readonly [any, any, any];
}

const countryAssets = (country: COUNTRIES): CountryAssets => {
  switch (country) {
    case COUNTRIES.GH:
      return {
        image: [
          require('@/assets/images/onboarding/gh/gh-1.png'),
          require('@/assets/images/onboarding/gh/gh-2.png'),
          require('@/assets/images/onboarding/gh/gh-3.png'),
        ],
        element: [
          require('@/assets/images/onboarding/gh/gh-1-element.png'),
          require('@/assets/images/onboarding/gh/gh-2-element.png'),
          require('@/assets/images/onboarding/gh/gh-3-element.png'),
        ],
      };
    case COUNTRIES.NG:
      return {
        image: [
          require('@/assets/images/onboarding/ng/ng-1.png'),
          require('@/assets/images/onboarding/ng/ng-2.png'),
          require('@/assets/images/onboarding/ng/ng-3.png'),
        ],
        element: [
          require('@/assets/images/onboarding/ng/ng-1-element.png'),
          require('@/assets/images/onboarding/ng/ng-2-element.png'),
          require('@/assets/images/onboarding/ng/ng-3-element.png'),
        ],
      };
    case COUNTRIES.ZA:
      return {
        image: [
          require('@/assets/images/onboarding/za/za-1.png'),
          require('@/assets/images/onboarding/za/za-2.png'),
          require('@/assets/images/onboarding/za/za-3.png'),
        ],
        element: [
          require('@/assets/images/onboarding/za/za-1-element.png'),
          require('@/assets/images/onboarding/za/za-2-element.png'),
          require('@/assets/images/onboarding/za/za-3-element.png'),
        ],
      };

    default:
      return {
        image: [
          require('@/assets/images/onboarding/za/za-1.png'),
          require('@/assets/images/onboarding/za/za-2.png'),
          require('@/assets/images/onboarding/za/za-3.png'),
        ],
        element: [
          require('@/assets/images/onboarding/za/za-1-element.png'),
          require('@/assets/images/onboarding/za/za-2-element.png'),
          require('@/assets/images/onboarding/za/za-3-element.png'),
        ],
      };
  }
};

interface OnboardingDataType {
  title: string;
  description: string;
  imageBg: any;
  image: any;
  otherElement: React.ReactNode;
}

const onboardingData = (code: COUNTRIES): OnboardingDataType[] => {
  return [
    {
      title: 'Take orders without back-\nand-forths with customers',
      description: 'Get a customized online store with \n your IG page in 5 minutes',
      imageBg: require('@/assets/images/onboarding/bg-1.png'),
      image: countryAssets(code).image[0],
      otherElement: (
        <View className="absolute z-30 bottom-4 self-end items-end">
          <CustomImage
            style={{ height: hp(121), width: wp(206) }}
            imageProps={{
              source: countryAssets(code).element[0],

              contentFit: 'contain',
            }}
          />
        </View>
      ),
    },
    {
      title: 'Manage your business \nlike the big boys do',
      description: 'See records of all your orders and \n customers, book deliveries with ease',
      imageBg: require('@/assets/images/onboarding/bg-2.png'),
      image: countryAssets(code).image[1],
      otherElement: (
        <View className="absolute z-30 self-end bottom-24 items-end">
          <CustomImage
            style={{ height: hp(70), width: wp(209) }}
            imageProps={{
              source: countryAssets(code).element[1],
              contentFit: 'cover',
            }}
          />
        </View>
      ),
    },
    {
      title: 'Collect payments without\nbreaking a sweat',
      description: 'Get paid with different currencies & \n methods, create invoices and payment links',
      imageBg: require('@/assets/images/onboarding/bg-3.png'),
      image: countryAssets(code).image[2],
      otherElement: (
        <View className="absolute z-30 self-end bottom-24 items-end">
          <CustomImage
            style={{ height: hp(70), width: wp(264) }}
            imageProps={{
              source: countryAssets(code).element[2],
              contentFit: 'cover',
            }}
          />
        </View>
      ),
    },
  ];
};
