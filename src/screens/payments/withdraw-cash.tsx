import { BaseText } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import { ArrowUpRight } from '@/components/ui/icons';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import {
  COMPLETE_WITHDRAWAL,
  CURRENCIES,
  CompleteWithdrawalParams,
  Fees,
  GET_WITHDRAWAL_FEES,
  REQUEST_WITHDRAWAL,
  RequestWithdrawalParams,
  WithdrawalAccount,
} from 'catlog-shared';
import { useFormik } from 'formik';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Keyboard, ScrollView, View } from 'react-native';
import {
  calculateWithdrawalFee,
  delay,
  millify,
  showError,
  testYupValidation,
  toCurrency,
  to<PERSON>obo,
  to<PERSON>air<PERSON>,
  wp,
} from 'src/assets/utils/js';
import ConfirmWithdrawal from 'src/components/payments/withdraw-cash/confirm-withdrawal';
import AmountAccountStep from 'src/components/payments/withdraw-cash/enter-amount';
import EnterAmountSkeletonLoader from 'src/components/payments/withdraw-cash/enter-amount-skeleton-loader';
import WithdrawalFeesModal from 'src/components/payments/withdraw-cash/withdrawal-fees';
import WithdrawalSummary from 'src/components/payments/withdraw-cash/withdrawal-summary';
import WithdrawalSummaryModal from 'src/components/payments/withdraw-cash/withdrawal-summary-modal';
import InfoBadge from 'src/components/store-settings/info-badge';
import Pressable from 'src/components/ui/base/pressable';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import useRouteParams from 'src/hooks/use-route-params';
import useSteps from 'src/hooks/use-steps';
import { useStoreReview } from 'src/hooks/use-store-review';
import colors from 'src/theme/colors';
import * as Yup from 'yup';

enum MAKE_WITHDRAWAL_STEPS {
  AMOUNT_ACCOUNT = 'AMOUNT_ACCOUNT',
  // SUMMARY = 'SUMMARY',
  ENTER_OTP = 'ENTER_OTP',
  SUCCESS = 'SUCCESS',
}

export interface WithdrawalForm {
  amount: number;
  code: string;
  account: string;
  account_info: WithdrawalAccount;
  request_id?: string;
  email: '';
  fee: number;
  security_pin: string;
  rawInput?: string;
}

const WithdrawCash = () => {
  const navigation = useNavigation();
  const params = useRouteParams<'WithdrawCash'>();
  const currency = params?.currency || CURRENCIES.NGN;

  const { wallets, store, updateBalance, getWalletBalance } = useWalletContext();

  const { modals, toggleModal } = useModals(['addBankModal', 'feesModal', 'withdrawalSummary']);
  const formSteps = useSteps<MAKE_WITHDRAWAL_STEPS>(Object.values(MAKE_WITHDRAWAL_STEPS), 0);
  const { step, next, previous, changeStep } = formSteps;
  const [fees, setFees] = useState<Fees>();
  const [error, setError] = useState<string>();

  const wallet = useMemo(() => {
    return wallets.find(wallet => wallet.currency === currency);
  }, [wallets, currency]);

  const currentBalance = wallet?.balance ?? 0;

  const getFeesReq = useApi(
    {
      apiFunction: GET_WITHDRAWAL_FEES,
      key: GET_WITHDRAWAL_FEES.name,
      onSuccess: res => {
        setFees(res.data);
      },
      method: 'GET',
      autoRequest: true,
    },
    { currency },
  );

  const requestWithdrawalReq = useApi<RequestWithdrawalParams>({
    apiFunction: REQUEST_WITHDRAWAL,
    key: REQUEST_WITHDRAWAL.name,
    method: 'POST',
  });

  const completeWithdrawalReq = useApi<CompleteWithdrawalParams>({
    apiFunction: COMPLETE_WITHDRAWAL,
    key: COMPLETE_WITHDRAWAL.name,
    method: 'POST',
  });

  const { handleReviewRequest } = useStoreReview();

  const formValidation = useMemo(() => {
    return validationSchema(
      currentBalance,
      step,
      toNaira(fees?.min_withdrawal_amount ?? 0),
      toNaira(fees?.max_withdrawal_amount ?? 0),
      currency,
    );
  }, [step, fees, currency, currentBalance]);

  const form = useFormik<WithdrawalForm>({
    initialValues: {
      rawInput: '',
      amount: 0,
      code: '',
      account: '',
      account_info: null,
      request_id: '',
      email: '',
      fee: 0,
      security_pin: '',
    },
    validationSchema: formValidation,
    validateOnMount: true,
    onSubmit: async values => {
      Keyboard.dismiss();
      switch (step) {
        case MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT:
          toggleModal('withdrawalSummary', true);
          // next();
          break;
        // case MAKE_WITHDRAWAL_STEPS.SUMMARY:
        //   form.setFieldValue('fee', toNaira(calculateWithdrawalFee(form.values?.amount, fees.fees)));

        //   const [res, error] = await requestWithdrawalReq.makeRequest({
        //     withdrawal_account: values.account,
        //     amount: Number(values.amount),
        //     wallet: wallet?.id,
        //   });

        //   if (error) {
        //     setError(error?.message ?? 'Something went wrong! Reload page & retry');
        //     break;
        //   }

        //   if (res) {
        //     form.setFieldValue('request_id', res?.data?.request);
        //     form.setFieldValue('email', res?.data?.email);
        //     next();
        //   }
        //   break;
        case MAKE_WITHDRAWAL_STEPS.ENTER_OTP: {
          if (!values.code || !values.security_pin) {
            showError('Please enter your OTP and security pin');
            return;
          }
          const [res, err] = await completeWithdrawalReq.makeRequest({
            code: values.code,
            request_id: values.request_id,
            security_pin: values.security_pin,
          });

          if (err && (err as any).statusCode !== 412) {
            changeStep(MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT);
            form.resetForm();
            return;
          }

          if (!err) {
            debitBalance(Number(values.amount) + Number(values.fee));
            next();
            handleReviewRequest();
          }

          break;
        }

        case MAKE_WITHDRAWAL_STEPS.SUCCESS: {
          form.resetForm();
          changeStep(MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT);
          navigation.goBack();
          break;
        }
      }
    },
  });

  // console.log('form.errors: ', form.errors);

  /*   useEffect(() => {
    (async() => {
      await delay(1000);
      form.validateForm();
    })()
  }, [form]); */

  const CTAIsDisabled = useMemo(() => {
    switch (step) {
      case MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT:
        return !!form.errors.amount || !!form.errors.account;
      case MAKE_WITHDRAWAL_STEPS.ENTER_OTP:
        return !!form.errors.security_pin || !!form.errors.code;
      case MAKE_WITHDRAWAL_STEPS.SUCCESS:
        return true;
    }
  }, [form, step]);

  const debitBalance = (amount: number) => {
    updateBalance({ walletId: wallet.id, type: 'debit', amount: toKobo(amount) });
  };

  // const isLoading = getFeesReq.isLoading || form.isSubmitting;
  const isLoading = getFeesReq.isLoading;
  const showBackBtn = step !== MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && step !== MAKE_WITHDRAWAL_STEPS.SUCCESS;
  const feeString = toCurrency(toNaira(calculateWithdrawalFee(form.values?.amount, fees?.fees ?? [])), currency);

  const onPressBack = () => {
    if (showBackBtn) {
      previous();
    } else {
      navigation.goBack();
    }
  };

  const handleConfirmSummary = async () => {
    form.setFieldValue('fee', toNaira(calculateWithdrawalFee(form.values?.amount, fees.fees)));

    const [res, error] = await requestWithdrawalReq.makeRequest({
      withdrawal_account: form.values.account,
      amount: Number(form.values.amount),
      wallet: wallet?.id,
    });

    if (error) {
      setError(error?.message ?? 'Something went wrong! Reload page & retry');
      return;
    }

    if (res) {
      toggleModal('withdrawalSummary', false);
      form.setFieldValue('request_id', res?.data?.request);
      form.setFieldValue('email', res?.data?.email);
      next();
    }
  };

  return (
    <DashboardLayout
      waitOnReady={false}
      headerProps={{
        headerBg: 'bg-accentGreen-pastel',
        pageTitle: screenTitles[step],
        variant: HeaderVariants.SUB_LEVEL,
        onBackPress: onPressBack,
        showDrawer: false,
        showNotifications: false,
      }}>
      <AvoidKeyboard>
        <Container className={'mt-20'}>
          {getFeesReq.isLoading && (
            <View className="py-40">
              <EnterAmountSkeletonLoader />
            </View>
          )}

          {!getFeesReq.isLoading && (
            <ScrollView contentContainerStyle={{ paddingBottom: 20 }} keyboardShouldPersistTaps="always">
              <>
                {step === MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && (
                  <AmountAccountStep error={error} currency={currency} form={form} fees={fees} />
                )}
                {/* {step === MAKE_WITHDRAWAL_STEPS.SUMMARY && (
                  <WithdrawalSummary form={form} currency={currency} fee={feeString} />
                )} */}
                {step === MAKE_WITHDRAWAL_STEPS.ENTER_OTP && <ConfirmWithdrawal form={form} />}
                {step === MAKE_WITHDRAWAL_STEPS.SUCCESS && (
                  <View className="mx-20 mb-[60px] mt-[40]">
                    <View className="self-center">
                      <SuccessCheckmark />
                    </View>
                    <BaseText fontSize={20} weight="light" type={'heading'} classes="text-center mt-10">
                      Your withdrawal of
                    </BaseText>
                    <BaseText fontSize={20} weight="semiBold" type={'heading'} classes="text-center ">
                      {currency} {millify(Number(form.values.amount))} is processing
                    </BaseText>
                  </View>
                )}
              </>
            </ScrollView>
          )}
        </Container>

        {/* Footer with fee info and continue button */}
        {form.values.amount > 0 && step === MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT && (
          <View className="px-20 pb-20">
            <Pressable
              onPress={() => {
                Keyboard.dismiss();
                toggleModal('feesModal');
              }}>
              <InfoBadge
                text={`This withdrawal will cost ${feeString}`}
                className="mb-15 rounded-0 px-[20]"
                rounded={false}
                addon={
                  <View className="flex items-center flex-row">
                    <BaseText fontSize={11} weight="medium" classes="text-primary-main">
                      Learn More
                    </BaseText>
                    <ArrowUpRight size={wp(15)} currentColor={colors.primary.main} />
                  </View>
                }
              />
            </Pressable>
          </View>
        )}

        <FixedBtnFooter
          buttons={[
            showBackBtn
              ? { text: 'Back', onPress: () => previous(), disabled: false, variant: ButtonVariant.LIGHT }
              : null,
            {
              text: step === MAKE_WITHDRAWAL_STEPS.SUCCESS ? 'Done' : 'Continue',
              onPress: () => form.submitForm(),
              isLoading: isLoading || completeWithdrawalReq.isLoading,
              disabled: CTAIsDisabled,
            },
          ].filter(Boolean)}
        />
      </AvoidKeyboard>

      <WithdrawalFeesModal
        isVisible={modals.feesModal}
        closeModal={() => toggleModal('feesModal', false)}
        fees={fees}
        currency={currency}
      />
      {modals.withdrawalSummary && (
        <WithdrawalSummaryModal
          isVisible={modals.withdrawalSummary}
          closeModal={() => toggleModal('withdrawalSummary', false)}
          form={form}
          fee={feeString}
          isLoading={requestWithdrawalReq.isLoading}
          currency={currency}
          onPressContinue={handleConfirmSummary}
          onPressBack={() => {
            toggleModal('withdrawalSummary', false);
          }}
        />
      )}
    </DashboardLayout>
  );
};

export default WithdrawCash;

const screenTitles = {
  [MAKE_WITHDRAWAL_STEPS.AMOUNT_ACCOUNT]: 'Withdraw Funds',
  // [MAKE_WITHDRAWAL_STEPS.SUMMARY]: 'Withdrawal Summary',
  [MAKE_WITHDRAWAL_STEPS.ENTER_OTP]: 'Enter OTP',
  [MAKE_WITHDRAWAL_STEPS.SUCCESS]: 'Withdrawal Complete',
};

const validationSchema = (balance: number, step: string, min: number, max: number, currency) =>
  Yup.object().shape({
    account: Yup.string().typeError('Must be a string').required('Withdrawal account is required'),
    amount: Yup.number()
      .typeError('Amount must be a number')
      .required('Withdrawal amount is required')
      .min(min, `Amount should be at least ${currency} ${millify(min)}`)
      .max(max, `You can only withdraw ${currency} ${millify(max)} at a time`)
      .test('lower_than_balance', 'Insufficient funds', value => value <= toNaira(balance)),
    code:
      step === 'ENTER_TOKEN'
        ? Yup.string().required('OTP token is required').length(6, 'OTP token should be 6 digits')
        : undefined,
    security_pin: Yup.string().min(6, 'Security Pin must be at least 6 digits'),
  });
