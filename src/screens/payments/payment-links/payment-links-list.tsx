import { useMemo, useRef, useState } from 'react';
import { Animated, RefreshControl, ScrollView, Text, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import Container from '@/components/ui/container';
import { HeaderVariants } from '@/components/ui/layouts/header';
import FAB from '@/components/ui/buttons/fab';
import useModals from 'src/hooks/use-modals';
import { useNavigation } from '@react-navigation/native';
import PaymentLinkCard from '@/components/payments/payment-links/payment-link-card';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import { Edit2, Link21, LinkSquare, SearchNormal1, Trash } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { alertPromise, copyToClipboard, ensureUniqueItems, wp } from 'src/assets/utils/js';
import EditPaymentLinkModal from '@/components/payments/payment-links/edit-payment-link-modal';
import EmptyState from '@/components/ui/empty-states/empty-state';
import useAuthContext from 'src/contexts/auth/auth-context';
import usePagination from 'src/hooks/use-pagination';
import { useApi } from 'src/hooks/use-api';
import PaymentLinksSkeletonLoader from '@/components/payments/payment-links/skeleton-loader';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { DELETE_INVOICE, GET_INVOICES, InvoiceParams, INVOICE_TYPES, InvoiceInterface } from 'catlog-shared';
import { OptionWithIcon } from 'src/components/ui/more-options';

const PER_PAGE = 10;
const PaymentLinksList = () => {
  const navigation = useNavigation();
  const { store } = useAuthContext();
  const { modals, toggleModal } = useModals(['editPaymentLink']);

  const [paymentLinks, setPaymentLinks] = useState<InvoiceInterface[]>([]);
  const [currentInvoiceIndex, setCurrentInvoiceIndex] = useState<number>(undefined);
  const { currentPage, goNext, setPage } = usePagination();
  const selectedPaymentLink = paymentLinks[currentInvoiceIndex];

  const getPaymentLinksRequest = useApi<any>(
    {
      key: GET_INVOICES.name,
      apiFunction: GET_INVOICES,
      method: 'GET',
      onSuccess: response => {
        setPaymentLinks(prev => ensureUniqueItems([...prev, ...response?.data?.invoices]));
      },
      autoRequest: true,
    },
    {
      filter: { search: '', type: INVOICE_TYPES.PAYMENT_LINK },
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const deletePaymentLinksRequest = useApi<InvoiceParams>({
    key: DELETE_INVOICE.name,
    apiFunction: DELETE_INVOICE,
    method: 'DELETE',
  });

  const updateList = (paymentLink: InvoiceInterface) => {
    const paymentLinksCopy = [...paymentLinks];
    paymentLinksCopy[currentInvoiceIndex] = paymentLink;
    setPaymentLinks(paymentLinksCopy);
  };

  const deleteCurrent = () => {
    const paymentLinksCopy = [...paymentLinks];
    paymentLinksCopy.splice(currentInvoiceIndex, 1);
    setPaymentLinks(paymentLinksCopy);
  };

  const handleDelete = async (index: number) => {
    const canDelete = await alertPromise('Are you sure?', 'Do you want to delete this payment link?', 'Yes', 'No');
    if (canDelete) {
      const [res, error] = await deletePaymentLinksRequest.makeRequest({ invoiceId: selectedPaymentLink?.id });
      if (res) {
        deleteCurrent();
        Toast.show({ type: 'success', text1: 'Payment link deleted successfully' });
      }
      if (error) {
        Toast.show({ type: 'error', text1: error?.message });
      }
    }
  };

  const moreOptions = (invoiceIndex: number) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Link21 size={wp(15)} color={colors.black.placeholder} />} label="Copy Payment Link" />
      ),
      onPress: () =>
        copyToClipboard(
          `${process.env.EXPO_PUBLIC_PUBLIC_URL}/pay/${paymentLinks[invoiceIndex]?.invoice_id}?byCustomer=true`,
          'Copied to Clipboard',
        ),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label="Edit Payment Link" />
      ),
      onPress: () => {
        setTimeout(() => {
          setCurrentInvoiceIndex(invoiceIndex);
          toggleModal('editPaymentLink');
        }, 500);
      },
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label="Delete Payment Link" />
      ),

      onPress: () => handleDelete(invoiceIndex),
    },
  ];

  const handlePullToRefresh = () => {
    try {
      setPaymentLinks([]);
      if (currentPage === 1) {
        getPaymentLinksRequest.reset();
        return;
      }
      setPage(1);
    } catch (error) {
    }
  };

  const isLoading = getPaymentLinksRequest.isLoading || deletePaymentLinksRequest.isLoading;

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Payment Links',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <View className="bg-white" style={{ flex: 1 }}>
        <Animated.FlatList
          data={paymentLinks}
          numColumns={1}
          keyExtractor={(item, index) => item.id + index}
          refreshControl={<RefreshControl refreshing={false} onRefresh={handlePullToRefresh} />}
          stickyHeaderIndices={[0]}
          onEndReachedThreshold={0.3}
          ListHeaderComponent={() => (
            <Row className="py-16 bg-white">
              <BaseText type="heading" fontSize={15}>
                All Payment Links
              </BaseText>
              <CircledIcon iconBg="bg-grey-bgOne">
                <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
              </CircledIcon>
            </Row>
          )}
          ListEmptyComponent={() =>
            isLoading ? (
              <PaymentLinksSkeletonLoader />
            ) : (
              <EmptyState
                classes="mt-20"
                icon={<LinkSquare variant="Bold" color={colors.grey.muted} />}
                subText="You don't have any payment links yet"
                text="No Payment Links"
                showBtn
                onPressBtn={() => navigation.navigate('CreatePaymentLink')}
                btnText="Create Payment Link"
              />
            )
          }
          className="flex-1 pb-40 px-20"
          contentContainerStyle={{ flexGrow: 1 }}
          onEndReached={() => {
            if (!isLoading /* && transactionsByDay.length === 0 */) {
              goNext(getPaymentLinksRequest?.response?.total_pages);
            }
          }}
          renderItem={({ item, index }) => (
            <PaymentLinkCard
              key={index}
              index={index}
              item={{ name: item.title, amount: item.total_amount, status: item.status }}
              moreOptions={moreOptions(index)}
            />
          )}
          ListFooterComponent={
            <View style={{ marginBottom: 80 }}>
              {isLoading && (
                <View className="pt-10">
                  <PaymentLinksSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
      </View>

      {<FAB onPress={() => navigation.navigate('CreatePaymentLink')} />}
      {selectedPaymentLink && (
        <EditPaymentLinkModal
          isVisible={modals.editPaymentLink}
          selectedPaymentLink={selectedPaymentLink}
          closeModal={() => toggleModal('editPaymentLink', false)}
          updateInvoices={updateList}
        />
      )}
    </DashboardLayout>
  );
};

export default PaymentLinksList;
