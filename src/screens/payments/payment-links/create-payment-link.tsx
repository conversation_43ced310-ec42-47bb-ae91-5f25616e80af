import React from 'react';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { Copy, Link21, Send2, TickCircle } from 'iconsax-react-native/src';
import { useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { copyToClipboard, wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ListCard, ListItemCardProps } from '@/components/ui/cards/list-item-card';
import { ArrowRight } from '@/components/ui/icons';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import { PaymentLinkForm, GenericFormRef } from './payment-link-form';
import { useNavigation } from '@react-navigation/native';
import SendInvoiceModal from '@/components/invoices/send-invoice-modal';
import { InvoiceInterface } from 'catlog-shared';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import CustomImage from 'src/components/ui/others/custom-image';

const CreatePaymentLink = () => {
  const { modals, toggleModal } = useModals(['sendLink']);
  const [step, setStep] = useState<'form' | 'success'>('form');
  const [disableSubmission, setIsDisabled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<GenericFormRef>(null);
  const [paymentLink, setPaymentLink] = useState<InvoiceInterface>();
  const navigator = useNavigation();

  const actions: ListItemCardProps[] = [
    {
      description: 'Copy Payment Link',
      leftElement: (
        <CircledIcon className="bg-accentYellow-pastel2 p-10">
          <Link21 size={wp(15)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon iconBg="bg-white">
          <Copy size={wp(16)} color={colors?.primary.main} />
        </CircledIcon>
      ),
      onPress: () =>
        copyToClipboard(
          `${process.env.EXPO_PUBLIC_PUBLIC_URL}/pay/${paymentLink.invoice_id}?byCustomer=true`,
          'Copied to Clipboard',
        ),
    },
    {
      description: 'Send Payment Link',
      leftElement: (
        <CircledIcon className="bg-accentRed-pastel p-10">
          <Send2 size={wp(15)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      rightElement: (
        <CircledIcon iconBg="bg-white">
          <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors?.primary.main} />
        </CircledIcon>
      ),
      onPress: () => toggleModal('sendLink'),
    },
  ];

  const handleComplete = (paymentLink: InvoiceInterface) => {
    setPaymentLink(paymentLink);
    setStep('success');
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Create Payment Link',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <AvoidKeyboard className="flex-1">
        <ScrollView>
          {step === 'form' && (
            <>
              <ScreenInfoHeader
                colorPalette={ColorPaletteType.GREEN}
                iconElement={
                  <CustomImage
                    imageProps={{ source: require('@/assets/images/payment-link.png'), contentFit: 'cover' }}
                    className="w-80 h-80"
                  />
                }
                pageTitleTop={'Get Paid,'}
                pageTitleBottom={'With Payment Links'}
              />
              <PaymentLinkForm
                setIsSubmitting={setIsSubmitting}
                onComplete={handleComplete}
                ref={formRef}
                formValidityCallBack={setIsDisabled}
              />
            </>
          )}
          {step === 'success' && (
            <View className="px-20">
              <View className={'items-center justify-center mt-30'}>
                <CircledIcon className="bg-accentGreen-pastel p-15">
                  <CircledIcon className="bg-accentGreen-main p-25">
                    <TickCircle variant={'Bold'} size={wp(50)} color={colors.white} />
                  </CircledIcon>
                </CircledIcon>
                <BaseText fontSize={22} type={'heading'} classes="text-center mt-10 max-w-[325px]">
                  Your Payment Link has{'\n'}been created
                </BaseText>
              </View>
              <ListCard items={actions} />
            </View>
          )}
        </ScrollView>
      </AvoidKeyboard>

      <FixedBtnFooter
        buttons={[
          {
            text: step === 'success' ? 'See all Payment Links' : 'Create Payment Link',
            onPress: () => (step === 'form' ? formRef?.current?.submitForm() : navigator.navigate('PaymentLinksList')),
            isLoading: isSubmitting,
            disabled: disableSubmission,
          },
        ]}
      />
      {paymentLink && (
        <SendInvoiceModal
          activeInvoice={paymentLink}
          isVisible={modals.sendLink}
          closeModal={() => toggleModal('sendLink', false)}
        />
      )}
    </DashboardLayout>
  );
};

export default CreatePaymentLink;
