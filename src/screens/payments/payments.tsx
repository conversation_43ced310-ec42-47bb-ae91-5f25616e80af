import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import AccountsModal from 'src/components/payments/modals/accounts-modal';
import ConvertCurrencyModal from '@/components/payments/convert-currency/convert-currency-modal';
import GenerateStatementModal from '@/components/payments/modals/generate-statement-modal';
import WalletCard from 'src/components/payments/wallet-cards';
import RequestPaymentModal from '@/components/payments/modals/request-payment-modal';
import TodaysRateModal from '@/components/payments/modals/todays-rate-modal';
import TransactionHistory from '@/components/payments/transaction-history';
import WithdrawCashModal from '@/components/payments/withdraw-cash/withdraw-cash-modal';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import ScreenActionBtn from '@/components/ui/buttons/screen-action-btn';
import ScrollableActionPills from '@/components/ui/buttons/scrollable-action-pills';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import colors from '@/theme/colors';
import { NavigationProp, NavigationState, useNavigation } from '@react-navigation/native';
import {
  AttachSquare,
  DocumentText,
  Flash,
  MenuBoard,
  ReceiptText,
  SearchNormal1,
  Wallet2,
} from 'iconsax-react-native/src';
import {
  CURRENCIES,
  GET_KYC_STATUS,
  GET_PAYMENTS_SETUP_PROGRESS,
  GET_WITHDRAWAL_FEES,
  KYC_STATUSES,
} from 'catlog-shared';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, View } from 'react-native';
import Animated, {
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import Can from 'src/components/ui/permissions/can';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { useApi } from 'src/hooks/use-api';
import Pressable from 'src/components/ui/base/pressable';
import { ArrowUpRight } from 'src/components/ui/icons';
import CompletePaymentsSetupModal from 'src/components/payments/modals/complete-payments-setup';
import QueryErrorBoundary from 'src/components/ui/query-error-boundary';
import WithdrawAccountsModal from 'src/components/payments/withdraw-cash/withdrawal-accounts-modal';
import useStatusbar from 'src/hooks/use-statusbar';

const Payments = () => {
  const { userRole, userAccountDeactivated, store } = useAuthContext();
  const { wallets, getWalletsRequest, storeId } = useWalletContext();

  const navigation = useNavigation();
  const scrollY = useSharedValue(0);

  const [activeWalletCurrency, setActiveWalletCurrency] = useState<CURRENCIES>(
    store?.wallets[0]?.currency ?? store?.currencies?.default,
  );
  const [headerHeight, setHeaderHeight] = useState(0);
  const { modals, toggleModal } = useModals([
    'todaysRate',
    'generateStatement',
    'convertCurrency',
    'withdrawCash',
    'withdrawAccounts',
    'accounts',
    'requestPayment',
    'paymentsSetup',
  ]);

  // const wallet = wallets.find(wallet => wallet?.currency === activeWalletCurrency);

  const wallet = useMemo(() => {
    return wallets.find(wallet => wallet?.currency === activeWalletCurrency);
  }, [activeWalletCurrency, wallets]);

  const { response } = useApi({
    apiFunction: GET_PAYMENTS_SETUP_PROGRESS,
    key: GET_PAYMENTS_SETUP_PROGRESS.name,
    method: 'GET',
  });

  //Added this to make a pre-fetch of the fees before going to withdraw cash
  const getFeesReq = useApi(
    {
      apiFunction: GET_WITHDRAWAL_FEES,
      key: GET_WITHDRAWAL_FEES.name,
      onSuccess: res => {},
      method: 'GET',
      autoRequest: true,
    },
    { currency: activeWalletCurrency },
  );

  const setupCompleted = Object.values(response?.data || { placeholder: true }).every(Boolean);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollY.value = event.contentOffset.y / 2;
    },
  });

  const searchContainerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [150, 220], [0.5, 1], 'clamp');
    const marginTop = interpolate(scrollY.value, [150, 220], [-headerHeight / 2, headerHeight], 'clamp');

    return {
      position: 'absolute',
      zIndex: 49,
      width: '100%',
      marginTop,
      opacity,
    };
  });

  const actionPills = useMemo(
    () =>
      [
        !setupCompleted && wallet.has_completed_kyc
          ? {
              LeftIcon: ({ ...props }) => <Flash color={colors?.black.muted} size={hp(15)} />,
              title: 'Complete Setup',
              showArrow: true,
              onPress: () => toggleModal('paymentsSetup'),
            }
          : null,
        {
          LeftIcon: () => <Wallet2 color={colors?.black.muted} size={hp(15)} />,
          title: 'Withdrawal Accounts',
          onPress: () => toggleModal('withdrawAccounts', true),
        },
        wallets.length > 1
          ? {
              LeftIcon: () => <MenuBoard color={colors?.black.muted} size={hp(15)} />,
              title: "Today's rates",
              onPress: () => toggleModal('todaysRate'),
            }
          : null,
        {
          LeftIcon: () => <AttachSquare color={colors?.black.muted} size={hp(15)} />,
          title: 'Payment links',
          onPress: () => navigation.navigate('PaymentLinksList'),
        },
        {
          LeftIcon: () => <ReceiptText color={colors?.black.muted} size={hp(15)} />,
          title: 'Invoices',
          onPress: () => navigation.navigate('InvoicesStack'),
        },
        {
          LeftIcon: () => <DocumentText color={colors?.black.muted} size={hp(15)} />,
          title: 'Generate statements',
          onPress: () => toggleModal('generateStatement'),
        },
      ].filter(Boolean),
    [wallets, setupCompleted],
  );

  const SearchContainer = () => {
    return (
      <Animated.View className="py-12 bg-white px-20 border-b border-b-grey-border" style={searchContainerStyle}>
        <Row>
          <BaseText type="heading" weight="bold" fontSize={14}>
            Transaction History
          </BaseText>
          <Pressable onPress={() => navigation.navigate('SearchTransactions')}>
            <CircledIcon iconBg="bg-grey-bgOne">
              <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
            </CircledIcon>
          </Pressable>
        </Row>
      </Animated.View>
    );
  };

  const HistoryHeader = () => {
    return (
      <View>
        <View>
          {!store?.kyc_approved && !userAccountDeactivated && (
            <Can data={{ permission: SCOPES.WALLETS.CAN_MANAGE_WALLET }}>
              <KycCTA navigation={navigation} />
            </Can>
          )}
          {store?.kyc_approved && !userAccountDeactivated && (
            <Can data={{ permission: SCOPES.PAYMENTS.CAN_VIEW_ANALYTICS }}>
              <ScreenActionBtn
                title={'See Analytics'}
                className="bg-accentGreen-pastel3"
                onPress={() => navigation.navigate('PaymentAnalytics')}
              />
            </Can>
          )}
          <QueryErrorBoundary
            error={getWalletsRequest.error}
            isLoading={getWalletsRequest.isLoading}
            refetch={getWalletsRequest.refetch}
            variant="section"
            errorTitle="Failed to load wallet data">
            <WalletCard
              toggleModal={toggleModal}
              activeCurrency={activeWalletCurrency}
              userAccountDeactivated={userAccountDeactivated}
              setActiveCurrency={setActiveWalletCurrency}
              // isLoading={getWalletsRequest?.isLoading}
            />
          </QueryErrorBoundary>
          {!userAccountDeactivated && (
            <View className="mt-16 flex-1">
              <ScrollableActionPills pills={actionPills} title="Quick Actions" />
            </View>
          )}
        </View>
        <View className="py-16 bg-white px-20">
          <Row>
            <BaseText type="heading" weight="bold" fontSize={14}>
              Transaction History
            </BaseText>
            <Pressable onPress={() => navigation.navigate('SearchTransactions')}>
              <CircledIcon iconBg="bg-grey-bgOne">
                <SearchNormal1 variant="Linear" color={colors.black.muted} size={wp(18)} />
              </CircledIcon>
            </Pressable>
          </Row>
        </View>
      </View>
    );
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: userAccountDeactivated ? 'Wallet' : 'Payments',
        variant: userAccountDeactivated ? HeaderVariants.SUB_LEVEL : HeaderVariants.ROOT_LEVEL,
        handleLayout: e => setHeaderHeight(e.layout.height),
        showNotifications: userAccountDeactivated ? false : true,
      }}>
      <SearchContainer />
      <View className="flex-1">
        <TransactionHistory listHeader={<HistoryHeader />} scrollHandler={scrollHandler} wallet={wallet} />
      </View>
      <TodaysRateModal
        isVisible={modals.todaysRate}
        closeModal={() => toggleModal('todaysRate', false)}
        initialCurrency={activeWalletCurrency}
      />
      <GenerateStatementModal
        isVisible={modals.generateStatement}
        wallets={store?.wallets}
        closeModal={() => toggleModal('generateStatement', false)}
      />
      <ConvertCurrencyModal
        isVisible={modals.convertCurrency}
        closeModal={() => toggleModal('convertCurrency', false)}
        initialWallet={wallet}
      />
      <WithdrawCashModal
        isVisible={modals.withdrawCash}
        closeModal={() => toggleModal('withdrawCash', false)}
        currency={activeWalletCurrency}
      />
      <WithdrawAccountsModal
        isVisible={modals.withdrawAccounts}
        closeModal={() => toggleModal('withdrawAccounts', false)}
      />
      <AccountsModal
        isVisible={modals.accounts}
        closeModal={() => toggleModal('accounts', false)}
        activeCurrency={activeWalletCurrency}
      />
      <RequestPaymentModal isVisible={modals.requestPayment} closeModal={() => toggleModal('requestPayment', false)} />
      {!setupCompleted && wallet.has_completed_kyc && (
        <CompletePaymentsSetupModal
          isVisible={modals.paymentsSetup}
          closeModal={() => toggleModal('paymentsSetup', false)}
          country={store?.country?.code}
        />
      )}
    </DashboardLayout>
  );
};

interface KYCCTAProps {
  navigation: Omit<NavigationProp<ReactNavigation.RootParamList>, 'getState'> & {
    getState(): NavigationState | undefined;
  };
}
const KycCTA = ({ navigation }: KYCCTAProps) => {
  const getKycStatusReq = useApi({
    apiFunction: GET_KYC_STATUS,
    key: GET_KYC_STATUS.name,
    method: 'GET',
  });

  const status: KYC_STATUSES = getKycStatusReq?.response?.data?.status || KYC_STATUSES.NO_KYC;

  return (
    <ScreenActionBtn
      title={kycStatusCTAMap[status]}
      className="bg-accentGreen-pastel3"
      onPress={() => navigation.navigate('KYC')}
    />
  );
};

const kycStatusCTAMap = {
  [KYC_STATUSES.PENDING]: 'Verification Under Review',
  [KYC_STATUSES.DENIED]: 'Verification Rejected',
  [KYC_STATUSES.APPROVED]: 'Verification Successful',
  [KYC_STATUSES.IN_PROGRESS]: 'Continue Verification',
  [KYC_STATUSES.NO_KYC]: 'Verify Identity',
};

export default Payments;
