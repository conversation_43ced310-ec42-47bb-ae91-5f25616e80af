import { ScrollView, Share, Text, View } from 'react-native';
import { Check, ExportSquare } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import colors from '@/theme/colors';
import { AreaGraph, Container, ScrollableActionPills } from '@/components/ui';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import { useMemo, useState } from 'react';
import { getFilter } from '@/components/ui/graph/area-graph';
import DateRangeModal from '@/components/ui/modals/date-range-modal';
import useModals from 'src/hooks/use-modals';
import PaymentAnalyticsCards from '@/components/payments/payment-analytics-cards';
// import { useFileDownload } from 'src/hooks/use-file-download';
import {
  EXPORT_ORDERS,
  GET_STORE_ORDER_STATISTICS,
  ExportOrdersParams,
  GetOrderStatisticsParams,
  paramsFromObject,
  GET_PAYMENT_ANALYTICS,
  GetPaymentAnalyticsParams,
  PAYMENT_STATUS,
} from 'catlog-shared';
import { TimeRange } from 'src/@types/utils';
import useAnalyticsRange from 'src/hooks/use-analytics-range';

export interface DateRangeString {
  from: string | null;
  to: string | null;
}

export interface PaymentAnalyticsType {
  currencies: string[];
  grouped_data: { [key: string]: AnalyticsData };
}

export interface AnalyticsData {
  total_volume: number;
  total_payments: number;
  average_daily_volume: number;
  payments: Payments;
  days: Date[];
}

export interface Payments {
  volumes: Volume[];
  count: Count[];
}

export interface Count {
  time: Date;
  value: string;
}

export interface Volume {
  time: Date;
  value: number;
}

const PaymentAnalytics = () => {
  const {
    analyticsRange,
    customRange,
    range,
    setRange,
    handleSelectRange,
    setCustomRange,
    showCalendar,
    toggleCalendar,
  } = useAnalyticsRange();
  const { modals, toggleModal } = useModals(['dateRange']);

  const getPaymentAnalytics = useApi<GetPaymentAnalyticsParams, ResponseWithPagination<PaymentAnalyticsType>>(
    {
      key: 'get-order-statistics',
      apiFunction: GET_PAYMENT_ANALYTICS,
      method: 'GET',
    },
    { ...analyticsRange, status: PAYMENT_STATUS.SUCCESS },
  );

  const stats = getPaymentAnalytics?.response?.data;

  // const channels: OrderChannels = stats?.grouped_data[stats?.currencies[0]].channels ?? {};
  // const colorSet = generateColorSet(
  //   colors.accentGreen.main,
  //   Object.keys(channels).map(key => key),
  // );

  // const channelPieData = Object.keys(channels)
  //   .map((key, index) => ({
  //     value: channels[key].count,
  //     name: key,
  //     color: colorSet[index],
  //     percentage: channels[key].percentage,
  //   }))
  //   .sort((a, b) => {
  //     return b.value - a.value;
  //   });

  const summaryData: AnalyticsData = useMemo(() => {
    if (!stats) return {} as AnalyticsData;
    const statData = stats?.grouped_data[stats?.currencies[0]];
    return statData;
  }, [stats]);

  const graphRawData = stats?.grouped_data[stats?.currencies[0]]?.payments?.volumes ?? [];

  const actionPills = [
    {
      LeftIcon: ({ ...props }) => <ExportSquare {...props} />,
      title: 'Request Payment',
      showArrow: true,
      onPress: () => toggleModal('dateRange'),
    },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Payment Analytics',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView className={'flex-1'}>
        <Container className="pt-20 mb-0">
          <PaymentAnalyticsCards analytics={summaryData} loading={getPaymentAnalytics.isLoading} />
          {/* </Container>
        <View className="-mt-10">
          <ScrollableActionPills pills={actionPills} title="Quick Actions" />
        </View>
        <Container className=""> */}
          <View className="mt-20">
            <AreaGraph
              title={'Total Payments'}
              setCustomRange={setCustomRange}
              showCalendar={showCalendar}
              toggleCalendar={toggleCalendar}
              range={range}
              customRange={customRange}
              rawData={[graphRawData]}
              setRange={handleSelectRange}
              className="mt-5"
              theme="primary"
            />
          </View>
          {/* <View className="mt-20">
            <DoughnutChart
              title="Order Channels"
              pieData={
                channelPieData.length > 0
                  ? channelPieData
                  : [
                      {
                        value: 1,
                        color: colors.grey.bgOne,
                      },
                    ]
              }
            />
          </View>
          <SectionContainer>
            <Row className="py-10 border-b border-b-grey-border">
              <View className="flex-1">
                <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                  Payment Channel
                </BaseText>
              </View>
              <View className="flex-1 items-end">
                <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                  Volume
                </BaseText>
              </View>
              <View className="flex-1 items-end">
                <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                  %
                </BaseText>
              </View>
            </Row>
            <View className="gap-5 mt-5">
              {channelPieData.map((item, index) => (
                <InfoRow
                  key={item.name + index}
                  iconElement={
                    <Row className="flex-1 justify-start">
                      <View className="w-12 h-12 rounded-full mr-12" style={{ backgroundColor: item.color }} />
                      <BaseText lineHeight={20}>{item?.name}</BaseText>
                    </Row>
                  }
                  title={String(item.value)}
                  titleClasses="flex-1 self-end mx-0 text-right"
                  valueElement={
                    <View className="flex-1 items-end">
                      <BaseText lineHeight={20}>{item.percentage.toFixed(1)}%</BaseText>
                    </View>
                  }
                />
              ))}
            </View>
          </SectionContainer> */}
        </Container>
      </ScrollView>
    </DashboardLayout>
  );
};

export default PaymentAnalytics;
