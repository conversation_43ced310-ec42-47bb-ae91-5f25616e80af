import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import { CHECK_RELATED_KYC, GET_STORE_KYC, REMOVE_ID_INFO, SUBMIT_KYC } from 'catlog-shared';
import { COUNTRIES, KYC_STATUSES, KYCInfo as IKYCInfo } from 'catlog-shared/src/interfaces';
import { User } from 'node_modules/catlog-shared/dist';
import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import { cx } from 'src/assets/utils/js';
import KYCInfo from '@/components/payments/kyc/kyc-info';
import KycDataSummary from 'src/components/payments/kyc/kyc-data-summary';
import KycOptions from 'src/components/payments/kyc/kyc-options';
import { BaseText } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useSteps, { UseStepsData } from 'src/hooks/use-steps';
import colors from 'src/theme/colors';
import { GenericFormRef } from './payment-links/payment-link-form';
import KYCForms from 'src/components/payments/kyc/forms';
import KycStatus from 'src/components/payments/kyc/statuses';
import KYCProgress from 'src/components/payments/kyc/kyc-progress';
import { KYC_FORM_STEPS, KYC_MAIN_STEPS, SharedKYCStepProps } from 'src/components/payments/kyc/types';

const KYC = () => {
  const navigator = useNavigation();
  const { updateStore, store, userRole, user } = useAuthContext();

  const [CTAEnabled, setCTAEnabled] = useState(false);
  const [kycInfo, setKycInfo] = useState<IKYCInfo>(null);
  const [showKycOptions, setShowKycOptions] = useState(true);
  const [stepIsLoading, setStepIsLoading] = useState(false);
  const progressWidth = useSharedValue(0);

  const basicInfoFormRef = useRef<GenericFormRef>();
  const bvnPhoneInfoFormRef = useRef<GenericFormRef>();
  const addressFormRef = useRef<GenericFormRef>();

  const kycMainSteps = useSteps<KYC_MAIN_STEPS>(Object.values(KYC_MAIN_STEPS), 0);
  const kycFormSteps = useSteps<KYC_FORM_STEPS>(Object.values(KYC_FORM_STEPS), 0);

  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = kycMainSteps;

  const submitKYCReq = useApi({
    apiFunction: SUBMIT_KYC,
    key: SUBMIT_KYC.name,
    method: 'POST',
  });

  const relatedKycReq = useApi({
    apiFunction: CHECK_RELATED_KYC,
    key: CHECK_RELATED_KYC.name,
    method: 'GET',
  });

  const storeKYCReq = useApi({
    apiFunction: GET_STORE_KYC,
    key: GET_STORE_KYC.name,
    method: 'GET',
    onSuccess: response => {
      const info: IKYCInfo = response?.data ?? null;
      setKycInfo(info);

      if (info !== null) {
        const step = getStep(info, store?.country?.code);
        changeStep(step);
      }
    },
  });

  const removeIdReq = useApi({
    apiFunction: REMOVE_ID_INFO,
    key: REMOVE_ID_INFO.name,
    method: 'PUT',
  });

  useEffect(() => {
    const percentage = Math.ceil((kycFormSteps.stepIndex / kycFormSteps.steps.length) * 100);
    progressWidth.value = withDelay(300, withSpring(percentage, { overshootClamping: percentage > 0 ? false : true }));
  }, [kycFormSteps.stepIndex, kycFormSteps.steps]);

  useEffect(() => {
    if (step !== KYC_MAIN_STEPS.FORM) setCTAEnabled(true);
  }, [step]);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  const submitKycInfo = async () => {
    setStepIsLoading(true);
    const [res, error] = await submitKYCReq.makeRequest({});

    if (res) {
      const kycInfo = res?.data?.kyc;
      setKycInfo(kycInfo);

      const storeData = res?.data?.store;
      const kycStatus = kycInfo?.status;

      changeStep(KYC_MAIN_STEPS.STATUS);

      //update state for the rest of the dashboard
      if (storeData) {
        updateStore({
          payments_enabled: storeData?.payments_enabled,
          wallet: storeData?.wallet,
          payment_options: storeData?.payment_options,
          kyc_approved: kycStatus === KYC_STATUSES.APPROVED,
        });
      }
    }
    setStepIsLoading(false);
  };

  const formStepsActionMap = {
    [KYC_FORM_STEPS.PERSONAL_INFO]: () => basicInfoFormRef?.current?.submitForm(),
    [KYC_FORM_STEPS.BVN_PHONE_INFO]: () => bvnPhoneInfoFormRef?.current?.submitForm(),
    [KYC_FORM_STEPS.ADDRESS_INFO]: () => addressFormRef?.current?.submitForm(),
    [KYC_FORM_STEPS.ID_CARD_INFO]: () => kycFormSteps.next(),
  };

  const handleStepSubmit = async () => {
    switch (step) {
      case KYC_MAIN_STEPS.FORM:
        await formStepsActionMap[kycFormSteps.step]();
        break;
      case KYC_MAIN_STEPS.SUMMARY:
        await submitKycInfo();
        break;
      case KYC_MAIN_STEPS.STATUS:
        if (kycInfo?.status === KYC_STATUSES.DENIED) {
          const [res, error] = await removeIdReq.makeRequest({});
          setKycInfo(res?.data);
          changeStep(KYC_MAIN_STEPS.FORM);
          return;
        }
        navigator.navigate('Payments');
        break;
      default:
        next();
    }
  };

  const onCopySuccess = () => {
    storeKYCReq.reset();
    setShowKycOptions(false);
  };

  const relatedKycData: IKYCInfo[] = relatedKycReq?.response?.data;
  const showOptionsList = !kycInfo?.bvn && relatedKycData?.length > 0 && relatedKycData[0]?.id !== kycInfo?.id;
  const commonProps: SharedKYCStepProps = {
    kycInfo,
    setKycInfo,
    next,
    user,
    formSteps: kycFormSteps,
    setIsLoading: setStepIsLoading,
    country: store?.country?.code,
    CTAEnabled,
    setCTAEnabled,
  };
  const isLoading = storeKYCReq.isLoading || relatedKycReq.isLoading;
  const showPrevButton =
    step !== KYC_MAIN_STEPS.INTRO &&
    step !== KYC_MAIN_STEPS.STATUS &&
    (step === KYC_MAIN_STEPS.FORM ? kycFormSteps?.step !== KYC_FORM_STEPS.PERSONAL_INFO : true);

  if (showKycOptions && showOptionsList && !isLoading) {
    return <KycOptions {...{ relatedKycData, kycInfo, setShowKycOptions, onCopySuccess }} />;
  }

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'Verify Identity',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {isLoading && (
        <View className="h-full flex-1 justify-center">
          <ActivityIndicator color={colors?.primary.main} size="small" animating />
          <BaseText fontSize={12} weight={'medium'} classes={cx(`text-black-muted mt-10 text-center`)}>
            Loading your KYC information...
          </BaseText>
        </View>
      )}

      {!isLoading && (
        <>
          <ScrollView keyboardShouldPersistTaps="handled">
            {step === KYC_MAIN_STEPS.INTRO && (
              <>{kycInfo ? <KYCProgress kycInfo={kycInfo} changeStep={changeStep} /> : <KYCInfo next={next} />}</>
            )}
            {step === KYC_MAIN_STEPS.FORM && (
              <KYCForms
                {...commonProps}
                formRefs={{
                  basicInfo: basicInfoFormRef,
                  bvnPhoneInfo: bvnPhoneInfoFormRef,
                  addressInfo: addressFormRef,
                }}
              />
            )}
            {step === KYC_MAIN_STEPS.SUMMARY && <KycDataSummary {...commonProps} />}
            {step === KYC_MAIN_STEPS.STATUS && <KycStatus submitResponse={submitKycInfo} {...commonProps} />}
          </ScrollView>
          {step === KYC_MAIN_STEPS.INTRO && kycInfo && (
            <FixedBtnFooter
              buttons={[
                {
                  text: 'Continue Verification',
                  onPress: validateData(kycInfo).ADDRESS ? () => changeStep(KYC_MAIN_STEPS.SUMMARY) : next,
                },
              ]}
            />
          )}
          {step !== KYC_MAIN_STEPS.INTRO && (
            <View>
              {step === KYC_MAIN_STEPS.FORM && (
                <View className="mx-20 my-10">
                  <BaseText classes="text-black-muted">
                    {kycFormSteps.stepIndex} of {kycFormSteps.steps.length} Complete
                  </BaseText>
                  <View className="bg-grey-bgOne rounded-full mt-6">
                    <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-full w-2/5" />
                  </View>
                </View>
              )}
              <FixedBtnFooter
                buttons={[
                  showPrevButton
                    ? {
                        text: getPrevButtonText(step),
                        onPress: step === KYC_MAIN_STEPS.FORM ? kycFormSteps.previous : previous,
                        variant: ButtonVariant.LIGHT,
                      }
                    : null,
                  {
                    text: getNextButtonText(step, kycFormSteps.step, kycInfo?.status, kycInfo),
                    isLoading: stepIsLoading,
                    onPress: () => handleStepSubmit(),
                    disabled: stepIsLoading || !CTAEnabled,
                    loadingText: 'Saving...',
                  },
                ].filter(Boolean)}
              />
            </View>
          )}
        </>
      )}
    </DashboardLayout>
  );
};

export default KYC;

export const validateData = (kycInfo: IKYCInfo) => {
  return {
    BASIC: !!(kycInfo !== null && kycInfo?.first_name && kycInfo?.last_name),
    BVN: Boolean(kycInfo?.bvn) && Boolean(kycInfo?.phone) && Boolean(kycInfo?.bvn_verified_at) && Boolean(kycInfo?.dob),
    PHONE: Boolean(kycInfo?.phone_verified) && Boolean(kycInfo?.dob),
    ID: !!(kycInfo?.identity?.number && kycInfo?.identity?.type && kycInfo?.identity?.url),
    ADDRESS: !!(
      kycInfo?.address?.city &&
      kycInfo?.address?.lga &&
      kycInfo?.address?.address_line1 &&
      kycInfo?.address?.state
    ),
  };
};

const getStep = (kycInfo: IKYCInfo, country: COUNTRIES) => {
  if (!kycInfo || kycInfo?.status === KYC_STATUSES.IN_PROGRESS) return KYC_MAIN_STEPS.INTRO;

  return KYC_MAIN_STEPS.STATUS;
};

const getNextButtonText = (
  mainStep: KYC_MAIN_STEPS,
  formStep: KYC_FORM_STEPS,
  status: KYC_STATUSES,
  kycInfo: IKYCInfo,
) => {
  switch (mainStep) {
    case KYC_MAIN_STEPS.SUMMARY:
      return 'Submit';
    case KYC_MAIN_STEPS.FORM:
      if (formStep === KYC_FORM_STEPS.BVN_PHONE_INFO) {
        if (kycInfo?.bvn_verified_at || kycInfo?.phone_verified) return 'Next';
        return 'Verify';
      }
      return 'Next';
    case KYC_MAIN_STEPS.STATUS:
      if (status === KYC_STATUSES.DENIED) return 'Edit Information';
      return 'Go To Dashboard';
    default:
      return 'Next';
  }
};

const getPrevButtonText = (step: KYC_MAIN_STEPS) => {
  switch (step) {
    case KYC_MAIN_STEPS.SUMMARY:
      return 'Edit Information';
    default:
      return 'Go Back';
  }
};

/* 
  RELATED KYC SCREENS
  PROGRESS BAR
  ----------------
  TESTING 
  DISABLED STATES FOR KYC FORMS
  AUTO LOGOUT
*/
