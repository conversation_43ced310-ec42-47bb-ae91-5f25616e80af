import SearchHeader from '@/components/search/search-header';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import {
  CURRENCIES,
  GetItemsParams
} from 'catlog-shared';
import { useFormik } from 'formik';
import { useState } from 'react';
import { getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import TransactionHistory from 'src/components/payments/transaction-history';
import SearchTransactionsFilterModal, {
  SearchFiltersTransactionParams,
} from 'src/components/search/search-transactions-filter-modal';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import useModals from 'src/hooks/use-modals';
import { View } from 'react-native';

const SearchTransactions = () => {
  const { modals, toggleModal } = useModals(['filters']);
  const { store } = useWalletContext();
  const { wallets } = useWalletContext();
  const [filters, setFilters] = useState<SearchFiltersTransactionParams>({ search: '', type: '' as any });
  const validFilters = removeEmptyAndUndefined({ ...filters });
  const [activeWalletCurrency, setActiveWalletCurrency] = useState<CURRENCIES>(
    store?.wallets?.[0]?.currency ?? store?.currencies?.default,
  );
  const wallet = wallets.find(wallet => wallet?.currency === activeWalletCurrency);

  const form = useFormik<SearchFiltersTransactionParams>({
    initialValues: {
      type: undefined,
      search: '',
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  const submitForm = () => {
    toggleModal('filters', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel',
        pageTitle: 'Search Transactions',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
          placeholder: 'Search Transactions',
        }}
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressFilterButton={() => toggleModal('filters')}
        showFilter
        showClear
        onPressClear={() => {
          form.setFieldValue('search', '');
          form.submitForm();
        }}
      />
      <View className="pt-10 flex-1">
        {wallet && <TransactionHistory isSearchPage filters={filters} scrollHandler={() => null} wallet={wallet} />}
      </View>
      <SearchTransactionsFilterModal
        isVisible={modals.filters}
        onPressContinue={submitForm}
        form={form}
        closeModal={() => toggleModal('filters', false)}
      />
    </DashboardLayout>
  );
};

export default SearchTransactions;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};
