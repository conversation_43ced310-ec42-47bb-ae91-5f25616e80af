import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { useNavigation } from '@react-navigation/native';
import {
  CustomerInterface,
  GET_PUBLIC_INVOICE,
  GET_TRANSACTION,
  LINK_CUSTOMER_TO_PAYMENTS,
  LINK_INVOICE_TO_PAYMENTS,
  LINK_ORDER_TO_PAYMENTS,
  OrderInterface,
  PaymentInterface,
  Transaction,
  WithdrawalAccount,
  humanFriendlyDate,
  removeCountryCode,
} from 'catlog-shared';
import {
  ArrowCircleRight2,
  Calendar2,
  Eye,
  MoneyForbidden,
  Note1,
  Signpost,
  TickCircle,
} from 'iconsax-react-native/src';
import classNames from 'node_modules/classnames';
import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import {
  alertPromise,
  enumToHumanFriendly,
  getDocId,
  hp,
  showLoader,
  toCurrency,
  to<PERSON><PERSON><PERSON>,
  wp,
} from 'src/assets/utils/js';
import CustomerInformationModal from 'src/components/customer/customer-information-modal';
import SelectCustomer from 'src/components/customer/select-customer';
import { getBankImageElement } from 'src/components/payments/withdraw-cash/add-bank-modal';
import { TRANSACTION_TYPE } from 'src/components/search/search-transactions-filter-modal';
import { BaseText } from 'src/components/ui';
import WhiteCardBtn from 'src/components/ui/buttons/white-card-btn';
import CircledIcon from 'src/components/ui/circled-icon';
import Column from 'src/components/ui/column';
import { ArrowUpRight } from 'src/components/ui/icons';
import ImageTextPlaceholder from 'src/components/ui/image-text-placholder';
import { DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import InfoRow from 'src/components/ui/others/info-row';
import Separator from 'src/components/ui/others/separator';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import Row from 'src/components/ui/row';
import SectionContainer from 'src/components/ui/section-container';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import useRouteParams from 'src/hooks/use-route-params';
import colors from 'src/theme/colors';
import LoadingScreen from '../loading';
import SelectOrder from 'src/components/orders/modals/select-order';
import { useStoreReview } from 'src/hooks/use-store-review';

const PaymentInfo = () => {
  const navigation = useNavigation();
  const { userAccountDeactivated } = useAuthContext();
  const { modals, toggleModal } = useModals(['customerInfo', 'previewReceipt', 'selectOrder']);

  const params = useRouteParams<'PaymentInfo'>();
  const transactionId = params?.id ?? '67e9424211b21e00066db4ad';

  const [customer, setCustomer] = useState<CustomerInterface | null>(null);
  const [orderInfo, setOrderInfo] = useState({ order: null, items: [] });

  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  const dropdownRef = useRef<DropDownMethods>(null);
  const getTransactionReq = useApi(
    {
      apiFunction: GET_TRANSACTION,
      key: GET_TRANSACTION.name,
      method: 'GET',
    },
    { id: transactionId },
  );

  const linkCustomerToPaymentReq = useApi({
    apiFunction: LINK_CUSTOMER_TO_PAYMENTS,
    key: LINK_CUSTOMER_TO_PAYMENTS.name,
    method: 'PUT',
  });

  const linkOrderToPaymentReq = useApi({
    apiFunction: LINK_INVOICE_TO_PAYMENTS,
    key: LINK_INVOICE_TO_PAYMENTS.name,
    method: 'PUT',
  });

  const getInvoiceReq = useApi({
    apiFunction: GET_PUBLIC_INVOICE,
    key: GET_PUBLIC_INVOICE.name,
    method: 'GET',
    autoRequest: false,
  });

  const transaction: Transaction = getTransactionReq?.response?.data ?? null;
  const payment: PaymentInterface = (transaction as any)?.payment;
  const isDebit = transaction?.type === TRANSACTION_TYPE.DEBIT;
  const withdrawalAccount: WithdrawalAccount = (transaction as any)?.withdrawal_account;

  useStoreReview(true);

  const receiptIdExist = typeof (transaction as any)?.invoice?.receipt === 'string';

  useEffect(() => {
    if (payment && payment?.customer) {
      setCustomer(payment?.customer);
    }
    const invoice = payment?.meta?.invoice;
    if (invoice) {
      getInvoice(invoice);
    }
  }, [payment]);

  const getInvoice = async (invoice: string) => {
    const [res, error] = await getInvoiceReq.makeRequest({ id: invoice });
    if (res) {
      setOrderInfo({ items: res?.data?.items, order: res?.data?.order });
    }
  };

  const linkCustomerToPayment = async (customer: CustomerInterface) => {
    const alertResponse = await alertPromise(
      'Link Customer',
      `Are you sure you want to link ${customer.name} to this payment?`,
      'Yes, Link',
      'Cancel',
      false,
    );

    if (!alertResponse) return;

    dropdownRef.current.close();

    const [res, err] = await linkCustomerToPaymentReq.makeRequest({
      reference: payment?.reference,
      customer_id: customer.id,
    });

    if (res) {
      setCustomer(customer);
      Toast.show({ type: 'success', text1: 'Customer linked successfully' });
    } else {
      Toast.show({ type: 'error', text1: err?.message ?? 'Something went wrong, please try again' });
    }
  };

  const linkOrderToPayment = async (order: OrderInterface) => {
    const alertResponse = await alertPromise(
      'Link Order',
      `Are you sure you want to link ${order.id} to this payment?`,
      'Yes, Link',
      'Cancel',
      false,
    );

    if (!alertResponse) return;

    toggleModal('selectOrder', false);

    showLoader('Linking Order', true, true);
    const [res, err] = await linkOrderToPaymentReq.makeRequest({
      reference: payment?.reference,
      invoice_id: getDocId(order.invoice),
    });

    if (res.data) {
      await getInvoice(res.data.meta.invoice);
    }
    Toast.hide();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel',
        pageTitle: 'Payment Details',
        variant: HeaderVariants.SUB_LEVEL,
        showNotifications: !userAccountDeactivated,
      }}>
      {getTransactionReq?.isLoading && !transaction && <LoadingScreen />}

      {transaction && !getTransactionReq?.isLoading && (
        <ScrollView contentContainerStyle={{ paddingBottom: hp(20) }}>
          <View className="px-20 py-30 items-center bg-accentGreen-pastel">
            <StatusPill
              title={transaction?.type.toLocaleUpperCase()}
              className="bg-white"
              statusType={txTypeMap[transaction?.type]}
            />
            <BaseText fontSize={wp(22)} classes="mt-5 leading-[30px] max-w-[350px] text-center" type="heading">
              {transaction?.narration}
            </BaseText>
          </View>
          <View className="p-20">
            <Row className="border-b border-grey-border pb-20">
              <View>
                <BaseText classes="text-black-muted" fontSize={12}>
                  Amount
                </BaseText>
                <BaseText fontSize={16} weight="bold" type="heading" classes="uppercase">
                  {toCurrency(toNaira(transaction?.amount), transaction?.currency)}
                </BaseText>
              </View>
              <CircledIcon
                className={classNames({ 'bg-[#F4E2E6]': isDebit, 'bg-[#E7F3EF]': !isDebit })}
                iconBg="bg-grey-bgOne">
                <ArrowCircleRight2
                  color={isDebit ? colors.accentRed.main : colors.accentGreen.main}
                  style={{ transform: [{ rotate: isDebit ? '-45deg' : '135deg' }] }}
                  variant="Bold"
                  size={wp(24)}
                />
              </CircledIcon>
            </Row>
            {payment && customer && (
              <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                <ImageTextPlaceholder size="midi" text={customer?.name} />
                <View className="flex-1 mx-10">
                  <BaseText fontSize={14} type="heading" classes="capitalize">
                    {customer?.name}
                  </BaseText>
                  <BaseText classes="text-black-placeholder mt-2" fontSize={12}>
                    {customer?.phone ? removeCountryCode(customer?.phone) : '-'}
                  </BaseText>
                  {/* <ContactWidget phone={customer?.phone} /> */}
                </View>
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => toggleModal('customerInfo')}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  View Profile
                </WhiteCardBtn>
              </Row>
            )}

            {payment && !customer && (
              <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                <BaseText fontSize={14} type="heading" classes="capitalize">
                  Customer Information
                </BaseText>
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => dropdownRef.current.open()}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  Link Customer
                </WhiteCardBtn>
              </Row>
            )}

            {payment &&
              (orderInfo.order ? (
                <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                  <ImageTextPlaceholder size="midi" text={orderInfo.items[0].name ?? 'o'} />
                  <View className="flex-1 mx-10">
                    <BaseText fontSize={14} type="heading" classes="capitalize">
                      Order {orderInfo.order}
                    </BaseText>
                    <BaseText classes="text-black-placeholder mt-2" fontSize={12}>
                      <BaseText numberOfLines={1}>
                        {orderInfo.items[0].name ??
                          '' + (orderInfo.items.length > 1 ? `& ${orderInfo.items.length - 1} other items` : '')}
                      </BaseText>
                    </BaseText>
                  </View>
                  <WhiteCardBtn
                    className="bg-grey-bgOne rounded-full"
                    onPress={() => navigation.navigate('OrderInfo', { id: orderInfo.order })}
                    icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                    View Order
                  </WhiteCardBtn>
                </Row>
              ) : (
                <Row className="items-center mt-15 pb-15 border-b border-grey-border">
                  <BaseText fontSize={14} type="heading" classes="capitalize">
                    {getInvoiceReq.isLoading ? 'Loading Order Info...' : 'Order Information'}
                  </BaseText>
                  <WhiteCardBtn
                    className="bg-grey-bgOne rounded-full"
                    onPress={() => toggleModal('selectOrder')}
                    icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                    Link Order
                  </WhiteCardBtn>
                </Row>
              ))}

            <SectionContainer classes="pt-15">
              {transaction?.channel === 'WITHDRAWAL' && withdrawalAccount && (
                <>
                  <BaseText fontSize={12} type="heading" classes="mb-10 text-black">
                    Bank Account
                  </BaseText>
                  <Row classes="bg-white rounded-15 items-start py-10 px-15">
                    <Column classes="flex-1">
                      <BaseText fontSize={11} classes=" text-black-muted">
                        {enumToHumanFriendly(withdrawalAccount.bank_name, ' ')}
                      </BaseText>
                      <BaseText fontSize={14} type="heading" classes="mt-3 text-black-muted">
                        {withdrawalAccount.account_number}
                      </BaseText>
                      <BaseText fontSize={13} weight="medium" classes="mt-7 text-black">
                        {withdrawalAccount.account_name}
                      </BaseText>
                    </Column>
                    {getBankImageElement(withdrawalAccount.image, 'h-[50px] w-[50px]', 50)}
                  </Row>
                  <Separator className="mx-0" />
                </>
              )}
              <InfoRow
                iconBg="white"
                title="Status"
                icon={<TickCircle size={wp(15)} color={colors.black.placeholder} />}
                value={<StatusPill title="SUCCESS" whiteBg statusType={StatusType.SUCCESS} />}
              />

              <InfoRow
                iconBg="white"
                title="Fee"
                icon={<MoneyForbidden size={wp(15)} color={colors.black.placeholder} />}
                value={toCurrency(toNaira(transaction?.fee), transaction?.currency)}
              />

              <InfoRow
                iconBg="white"
                title="Date"
                icon={<Calendar2 size={wp(15)} color={colors.black.placeholder} />}
                value={humanFriendlyDate(transaction?.created_at, true)}
              />

              <InfoRow
                iconBg="white"
                title="Purpose"
                icon={<Note1 size={wp(15)} color={colors.black.placeholder} />}
                value={transaction?.source?.purpose}
              />

              {!isDebit && transaction?.source?.method && (
                <InfoRow
                  iconBg="white"
                  title="Method"
                  icon={<Signpost size={wp(15)} color={colors.black.placeholder} />}
                  value={transaction?.source?.method}
                />
              )}
              {receiptIdExist && (
                <InfoRow
                  iconBg="white"
                  title="Preview Receipt"
                  icon={<Eye size={wp(15)} color={colors.black.placeholder} />}
                  value={transaction?.source?.method}
                  valueElement={
                    <WhiteCardBtn
                      className="bg-grey-bgOne rounded-full py-0 px-0"
                      onPress={() => toggleModal('previewReceipt')}
                      icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                      View Receipt
                    </WhiteCardBtn>
                  }
                />
              )}
            </SectionContainer>
          </View>
        </ScrollView>
      )}
      <SelectCustomer
        showAnchor={false}
        onSelectCustomer={linkCustomerToPayment}
        selectedCustomer={selectedCustomer}
        showLeftAccessory={false}
        externalRef={dropdownRef}
        closeAfterSelection={false}
        showButton={true}
        buttons={[{ text: 'Continue', onPress: () => dropdownRef.current.close() }]}
        hideAddCustomerBtn
      />
      <SelectOrder
        closeModal={() => toggleModal('selectOrder', false)}
        onSelectOrder={order => linkOrderToPayment(order)}
        selectedOrder={selectedOrder}
        isVisible={modals.selectOrder}
      />
      {customer && (
        <CustomerInformationModal
          isVisible={modals.customerInfo}
          activeCustomer={{ id: customer?.id }}
          closeModal={() => toggleModal('customerInfo', false)}
          showButton={false}
        />
      )}
      {/* <PreviewReceiptModal
        receiptId={payment?.meta?.invoice}
        isVisible={modals.previewReceipt}
        closeModal={() => toggleModal('previewReceipt', false)}
      /> */}
    </DashboardLayout>
  );
};

const txTypeMap = {
  credit: StatusType.SUCCESS,
  debit: StatusType.DANGER,
};

export default PaymentInfo;
