import useRouteParams from 'src/hooks/use-route-params';
import { InvoiceFormMain, InvoiceFormParams } from './create-invoice';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { useState } from 'react';
import { HeaderVariants } from '@/components/ui/layouts/header';
import InvoiceCreatedSuccessfully from '@/components/invoices/create/invoice-success';
import { INVOICE_FEE_TYPES, InvoiceInterface, CURRENCIES } from 'catlog-shared';

const EditInvoices = () => {
  const [invoiceCreatedSuccessfully, setInvoiceCreatedSuccessfully] = useState(false);
  const params = useRouteParams<'EditInvoice'>();
  const [newInvoice, setNewInvoice] = useState<InvoiceInterface>();
  const invoice = params?.invoice as InvoiceInterface;

  const invoiceForm: InvoiceFormParams = {
    title: invoice.title,
    customer: invoice.receiver?.id,
    customerInfo: invoice.receiver,
    date_due: new Date(invoice.date_due),
    date_created: new Date(invoice.date_created),
    fees: invoice.fees.map((f: any) => ({
      type: f.type,
      amount: f.type === INVOICE_FEE_TYPES.DISCOUNT ? f.amount * -1 : f.amount,
    })),
    items: invoice.items,
    store_logo: invoice.sender.image,
    store_address: invoice.sender.address,
    currency: invoice?.currency ?? CURRENCIES.NGN,
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: invoiceCreatedSuccessfully ? 'Invoice Update' : 'Update Invoice',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {!invoiceCreatedSuccessfully && (
        <InvoiceFormMain
          onComplete={i => {
            setInvoiceCreatedSuccessfully(true);
          }}
          initialValues={invoiceForm}
          invoiceId={invoice.id}
        />
      )}

      {invoiceCreatedSuccessfully && newInvoice && <InvoiceCreatedSuccessfully invoice={newInvoice} />}
    </DashboardLayout>
  );
};
export default EditInvoices;
