import SearchHeader from '@/components/search/search-header';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import {
  CURRENCIES,
  GetInvoicesParams,
  GetItemsParams,
  INVOICE_STATUSES,
  INVOICE_TYPES,
  INVOICES_DRAFT_TAG,
} from 'catlog-shared';
import { useFormik } from 'formik';
import { useState } from 'react';
import { getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import InvoiceList from 'src/components/invoices/invoice-list';
import SearchInvoicesFilterModal from 'src/components/search/search-invoices-filter-modal';
import useModals from 'src/hooks/use-modals';
import { View } from 'react-native';

const SearchInvoices = () => {
  const { modals, toggleModal } = useModals(['filters']);
  const [filters, setFilters] = useState<GetInvoicesParams['filter']>({ search: '', products: [] });
  const validFilters = removeEmptyAndUndefined({ ...filters });

  const form = useFormik<SearchInvoicesParams>({
    initialValues: {
      status: undefined,
      type: undefined,
      search: '',
      currency: undefined,
      draft_status: undefined,
      products: [],
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  const submitForm = () => {
    toggleModal('filters', false);
    form.submitForm();
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel',
        pageTitle: 'Search Invoices',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
          placeholder: 'Search Invoices',
        }}
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressFilterButton={() => toggleModal('filters')}
        showFilter
        showClear
        onPressClear={() => {
          form.setFieldValue('search', '');
          form.submitForm();
        }}
      />
      <View className="pt-10 flex-1">
        <InvoiceList filters={filters} isSearchPage />
      </View>
      <SearchInvoicesFilterModal
        isVisible={modals.filters}
        onPressContinue={submitForm}
        form={form}
        closeModal={() => toggleModal('filters', false)}
      />
    </DashboardLayout>
  );
};

export default SearchInvoices;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};

export interface SearchInvoicesParams {
  search?: string;
  status?: INVOICE_STATUSES;
  type?: INVOICE_TYPES;
  products: string[];
  draft_status?: INVOICES_DRAFT_TAG;
  currency?: CURRENCIES;
}
