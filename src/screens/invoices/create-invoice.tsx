import React from 'react';
import { BaseText } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Container from '@/components/ui/container';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { AccordionMethod } from '@/components/ui/others/accordion';
import Separator from '@/components/ui/others/separator';
import useAuthContext from '@/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import { FormikErrors, FormikProps, useFormik } from 'formik';
import { useEffect, useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import Toast from 'react-native-toast-message';
import { id } from 'rn-emoji-keyboard';
import { optionalPhoneValidation } from 'src/assets/utils/js/common-validations';
import BasicInformationSection from '@/components/invoices/create/basic-info-section';
import ExtraInformationSection from '@/components/invoices/create/extra-info-section';
import InvoiceFeesSection from '@/components/invoices/create/invoice-fees-section';
import InvoiceItemsSection from '@/components/invoices/create/invoice-items-section';
import InvoiceCreatedSuccessfully from '@/components/invoices/create/invoice-success';
import { ButtonVariant } from '@/components/ui/buttons/button';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import * as Yup from 'yup';
import {
  CURRENCIES,
  ProductItemInterface,
  StoreInterface,
  CREATE_INVOICE,
  GET_ITEMS,
  GetItemsParams,
  UPDATE_INVOICE,
  CREATE_ORDER,
  DELIVERY_METHODS,
  INVOICE_FEE_TYPES,
  InvoiceForm,
  InvoiceInterface,
  DRAFT_INVOICE,
  CustomerInterface,
  GET_ALL_ITEMS,
  GetAllItemsParams,
  StrippedItem,
} from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';
import { useStoreReview } from 'src/hooks/use-store-review';

export enum CREATE_INVOICE_STEPS {
  BASIC_INFO,
  INVOICE_ITEMS,
  INVOICE_FEES,
  EXTRA_INFO,
}

export interface InvoiceFormParams extends InvoiceForm {
  customerInfo?: CustomerInterface;
  showOtherSteps?: boolean;
  store?: string;
}

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

const CreateInvoice = () => {
  const [invoiceCreatedSuccessfully, setInvoiceCreatedSuccessfully] = useState(false);
  const [newInvoice, setNewInvoice] = useState<InvoiceInterface>();

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: invoiceCreatedSuccessfully ? 'Invoice Created' : 'Create Invoice',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {!invoiceCreatedSuccessfully && (
        <InvoiceFormMain
          onComplete={i => {
            setNewInvoice(i);
            setInvoiceCreatedSuccessfully(true);
          }}
        />
      )}

      {invoiceCreatedSuccessfully && newInvoice && <InvoiceCreatedSuccessfully invoice={newInvoice} />}
    </DashboardLayout>
  );
};

interface Props {
  onComplete: (invoice: InvoiceInterface) => void;
  initialValues?: InvoiceFormParams;
  invoiceId?: string;
}
export const InvoiceFormMain: React.FC<Props> = ({ initialValues, invoiceId, onComplete }) => {
  const [isEdit] = useState(initialValues !== undefined);
  const [draftingInvoice, setDraftingInvoice] = useState(false);
  const [stepIndex, setStepIndex] = useState<number>(CREATE_INVOICE_STEPS.BASIC_INFO);
  const [stepsStatus, setStepsStatus] = useState({
    BASIC_INFO: false,
    ITEMS: false,
    FEES: false,
    EXTRA_INFO: false,
  });

  const invoiceItemAccordionRef = useRef<AccordionMethod>(null);
  const basicInfoAccordionRef = useRef<AccordionMethod>(null);
  const feesAccordionRef = useRef<AccordionMethod>(null);
  const extraInfoAccordionRef = useRef<AccordionMethod>(null);

    const { handleReviewRequest } = useStoreReview();
  

  const { store } = useAuthContext();

  const createInvoiceRequest = useApi({
    apiFunction: CREATE_INVOICE,
    method: 'POST',
    key: CREATE_INVOICE.name,
    autoRequest: false,
  });

  const draftInvoiceRequest = useApi({
    apiFunction: DRAFT_INVOICE,
    method: 'POST',
    key: DRAFT_INVOICE.name,
    autoRequest: false,
  });

  const updateInvoiceRequest = useApi({
    apiFunction: UPDATE_INVOICE,
    method: 'PUT',
    key: UPDATE_INVOICE.name,
    autoRequest: false,
  });

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      apiFunction: GET_ITEMS,
      method: 'GET',
      key: GET_ITEMS.name,
    },
    {
      per_page: Number.MAX_SAFE_INTEGER,
      filter: { store: store?.id },
    },
  );

  // const getAllProductsRequest = useApi<GetAllItemsParams, StrippedItem[]>(
  //   {
  //     apiFunction: GET_ALL_ITEMS,
  //     method: 'GET',
  //     key: GET_ITEMS.name,
  //   },
  //   {},
  // );


    const getAllProductsRequest = useApi<GetItemsParams, ProductsResponse>(
      {
        apiFunction: GET_ITEMS,
        method: 'GET',
        key: 'fetch-custom-items',
      },
      {
        per_page: 9007199254740991,
        filter: { store: store?.id },
      },
    );


  const form = useFormik<InvoiceFormParams>({
    initialValues: initialValues || {
      currency: CURRENCIES.NGN,
      customer: '',
      date_created: null as any,
      date_due: null as any,
      fees: [],
      items: [],
      title: '',
      //custom values
      showOtherSteps: false,
    },
    validationSchema: validationSchema(stepIndex),
    onSubmit: async values => {},
  });

  useEffect(() => {
    computeStepStatuses(form);
  }, [form.values]);

  const computeStepStatuses = async (form: FormikProps<InvoiceFormParams>) => {
    const formSteps = ['BASIC_INFO', 'ITEMS', 'FEES', 'EXTRA_INFO'];
    const statusCopy = { ...stepsStatus } as any;
    const errorStatuses = (await checkErrors(form)) as any;
    formSteps.forEach((value: any) => (statusCopy[value] = !errorStatuses[value]));

    setStepsStatus(statusCopy);
  };

  const handleOnPressSave = (step: CREATE_INVOICE_STEPS) => {
    switch (step) {
      case CREATE_INVOICE_STEPS.BASIC_INFO:
        form.setFieldValue('showOtherSteps', true);
        basicInfoAccordionRef.current?.toggleAccordion(false);

        setStepIndex(CREATE_INVOICE_STEPS.INVOICE_ITEMS);
        setTimeout(() => {
          invoiceItemAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;

      case CREATE_INVOICE_STEPS.INVOICE_ITEMS:
        invoiceItemAccordionRef.current?.toggleAccordion(false);

        setStepIndex(CREATE_INVOICE_STEPS.INVOICE_FEES);
        setTimeout(() => {
          feesAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;

      case CREATE_INVOICE_STEPS.INVOICE_FEES:
        feesAccordionRef.current?.toggleAccordion(false);

        setStepIndex(CREATE_INVOICE_STEPS.EXTRA_INFO);
        setTimeout(() => {
          extraInfoAccordionRef.current?.toggleAccordion(true);
        }, 100);
        break;

      case CREATE_INVOICE_STEPS.EXTRA_INFO:
        extraInfoAccordionRef.current?.toggleAccordion(false);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (asDraft = false) => {
    const values = form.values;
    const payload: InvoiceFormParams = { store: store?.id, ...values };

    setDraftingInvoice(asDraft);

    if (!asDraft) {
      try {
        await validationSchema(4).validate(form.values, { abortEarly: false });
      } catch (err) {
        const errors = transformYupErrorsIntoObject(err as any);
        form.setErrors(errors);
        Object.keys(errors).forEach(k => form.setFieldTouched(k, true));
        Toast.show({ type: 'error', text1: 'Please fix the errors in the form' });
        return;
      }
    }

    //Ensure discount fees are sent as negative numbers
    const payloadCopy = JSON.parse(JSON.stringify(payload));
    const discountFeeIndex = payloadCopy.fees.findIndex((f: any) => f.type === INVOICE_FEE_TYPES.DISCOUNT);

    if (discountFeeIndex > -1) {
      payloadCopy.fees[discountFeeIndex].amount = -1 * payload.fees[discountFeeIndex].amount;
    }

    if (isEdit) {
      const [res, err] = await updateInvoiceRequest.makeRequest({ invoiceId: invoiceId!, data: { ...payloadCopy } });
      if (res) {
        onComplete(res?.data);
      }

      if (err) {
        Toast.show({ type: 'error', text1: err.message });
      }
    }

    const request = asDraft ? draftInvoiceRequest : createInvoiceRequest;
    const [res, err] = await request.makeRequest(payloadCopy);

    if (res) {
      onComplete(res?.data);
      handleReviewRequest();
    }

    if (err) {
      Toast.show({ type: 'error', text1: err.message });
    }
  };

  return (
    <>
      <AvoidKeyboard>
        <ScrollView keyboardDismissMode={'interactive'} keyboardShouldPersistTaps={'handled'}>
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.GREEN}
            iconElement={
              <CustomImage
                imageProps={{ source: require('@/assets/images/transactions.png'), contentFit: 'cover' }}
                className="w-80 h-80"
              />
            }
            pageTitleTop={
              <BaseText fontSize={22} classes="mt-10 leading-[30px]" type="heading">
                {isEdit ? 'Edit Invoice' : 'Create Invoice'}
              </BaseText>
            }
          />
          <Container className={'mt-20'}>
            <BasicInformationSection
              form={form}
              accordionRef={basicInfoAccordionRef}
              onPressSave={() => handleOnPressSave(CREATE_INVOICE_STEPS.BASIC_INFO)}
              isComplete={stepsStatus.BASIC_INFO}
              hasErrors={!stepsStatus.BASIC_INFO && stepIndex > CREATE_INVOICE_STEPS.BASIC_INFO}
            />
            {form.values?.showOtherSteps && (
              <>
                <Separator className="mx-0 mb-0" />
                <InvoiceItemsSection
                  form={form}
                  getProductsRequest={getAllProductsRequest as any}
                  accordionRef={invoiceItemAccordionRef}
                  onPressSave={() => handleOnPressSave(CREATE_INVOICE_STEPS.INVOICE_ITEMS)}
                  isComplete={stepsStatus.ITEMS}
                  hasErrors={!stepsStatus.ITEMS && stepIndex > CREATE_INVOICE_STEPS.INVOICE_FEES}
                />
                <Separator className="mx-0 mb-0" />
                <InvoiceFeesSection
                  form={form}
                  accordionRef={feesAccordionRef}
                  onPressSave={() => handleOnPressSave(CREATE_INVOICE_STEPS.INVOICE_FEES)}
                  isComplete={stepsStatus.FEES}
                  hasErrors={false}
                />
                <Separator className="mx-0 mb-0" />
                <ExtraInformationSection
                  form={form}
                  accordionRef={extraInfoAccordionRef}
                  onPressSave={() => handleOnPressSave(CREATE_INVOICE_STEPS.EXTRA_INFO)}
                  isComplete={stepsStatus.EXTRA_INFO}
                  hasErrors={false}
                />
              </>
            )}
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save as Draft',
            onPress: () => handleSubmit(true),
            isLoading: draftInvoiceRequest.isLoading || draftingInvoice,
            variant: ButtonVariant.LIGHT,
          },
          {
            text: isEdit ? 'Update Invoice' : 'Create Invoice',
            onPress: () => handleSubmit(false),
            isLoading: createInvoiceRequest.isLoading || updateInvoiceRequest.isLoading,
          },
        ]}
      />
    </>
  );
};

const validationSchema = (stepIndex: number) => {
  return Yup.object().shape({
    title: Yup.string().required('Invoice title is required'),
    customer: Yup.string().required('Customer is required'),
    date_created: Yup.date()
      .typeError('Please provide a valid invoice created date')
      .required('Invoice date is required'),
    date_due: Yup.date().typeError('Please provide a valid due date').required('Invoice due date is required'),
    currency: Yup.string()
      .required('Please select a currency')
      .oneOf(
        [CURRENCIES.EUR, CURRENCIES.USD, CURRENCIES.GBP, CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.ZAR],
        'Please select a valid currency',
      ),
    items: stepIndex > 1 || stepIndex < 1 ? ItemsValidationSchema : (undefined as any),
    fees: stepIndex > 2 || stepIndex < 1 ? FeesValidationSchema : (undefined as any),
    store_logo:
      stepIndex > 3 || stepIndex < 1
        ? Yup.string().typeError('Store logo should be a string').nullable().notRequired()
        : (undefined as any),
    store_address:
      stepIndex > 3 || stepIndex < 1
        ? Yup.string().typeError('Address should be a string').nullable().notRequired()
        : (undefined as any),
  });
};

const ItemsValidationSchema = Yup.array()
  .of(
    Yup.object().shape({
      name: Yup.string().required('Item name is required'),
      price: Yup.number().required('Item price is required').min(1, 'Item price must be greater than 0'),
      quantity: Yup.number().required('Item quantity is required').min(1, 'Item quantity must be greater than 0'),
    }),
  )
  .min(1, 'Please add at least one item');

const FeesValidationSchema = Yup.array().of(
  Yup.object().shape({
    type: Yup.string().required('Fee type is required'),
    amount: Yup.number().required('Fee amount is required').min(1, 'Amount should be greater than 0'),
  }),
);

const checkErrors = async (form: FormikProps<InvoiceFormParams>) => {
  const { values, initialValues, errors: initialErrors } = form;
  let errors: FormikErrors<InvoiceFormParams> = {};

  try {
    await validationSchema(4).validate(form.values, { abortEarly: false });
  } catch (err: any) {
    errors = transformYupErrorsIntoObject(err);
  }

  const statuses = {
    BASIC_INFO: Boolean(errors?.customer || errors?.date_due || errors?.date_created || errors?.title),
    ITEMS: values.items.length > 0 ? Boolean(errors?.items) || errors?.items?.length! > 0 : true,
    FEES: values.fees.length > 0 ? Boolean(errors?.fees) || errors?.fees?.length! > 0 : true,
    EXTRA_INFO:
      initialValues.store_address === values.store_address && initialValues.store_logo === values.store_logo
        ? true
        : Boolean(errors?.store_address || errors?.store_logo),
  };

  return statuses;
};

const transformYupErrorsIntoObject = (errors: Yup.ValidationError): Record<string, string> => {
  const validationErrors: Record<string, string> = {};

  errors?.inner?.forEach((error: any) => {
    if (error.path !== undefined) {
      validationErrors[error.path] = error.errors[0];
    }
  });

  return validationErrors;
};

export default CreateInvoice;
