import React from 'react';
import { View, Linking, Platform } from 'react-native';
import { BaseText } from '@/components/ui';
import * as Animatable from 'react-native-animatable';
import { ArrowUp, RefreshCircle } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import CircledIcon from '@/components/ui/circled-icon';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { StatusBar } from 'expo-status-bar';

const STORE_URLS = {
  ios: 'https://apps.apple.com/ng/app/catlog-create-online-store/id6741193741',
  android: 'https://play.google.com/store/apps/details?id=shop.catlog.app',
};

const ForceUpdate = () => {
  const handleUpdate = async () => {
    const storeUrl = Platform.select({
      ios: STORE_URLS.ios,
      android: STORE_URLS.android,
    });

    try {
      await Linking.openURL(storeUrl);
    } catch (error) {
      console.error('Failed to open store URL:', error);
    }
  };

  return (
    <SafeAreaProvider className="flex-1 bg-white">
      <StatusBar style="dark" />
      <View className="flex-1 bg-white">
        <View className="flex-1 mx-20 items-center justify-center bg-white">
          <Animatable.View animation="zoomIn" delay={75} duration={200}>
            <CircledIcon className="p-20 bg-primary-pastel">
              <Animatable.View animation="pulse" duration={1000} iterationCount="infinite" delay={150}>
                <ArrowUp variant="Bold" color={colors.primary.main} size={wp(40)} />
              </Animatable.View>
            </CircledIcon>
          </Animatable.View>
          <BaseText fontSize={20} classes="mt-16 text-center" type="heading">
            Update Required
          </BaseText>
          <BaseText fontSize={15} lineHeight={23} classes="text-black-placeholder mt-10 text-center">
            A new version of Catlog is available with{'\n'}important updates. Please update to continue.
          </BaseText>
        </View>
        <FixedBtnFooter
          buttons={[
            {
              text: 'Update Now',
              onPress: handleUpdate,
              leftAddOn: (
                <RefreshCircle size={wp(16)} color={colors.white} style={{ marginRight: 8 }} />
              )
            },
          ]}
        />
      </View>
    </SafeAreaProvider>
  );
};

export default ForceUpdate;
