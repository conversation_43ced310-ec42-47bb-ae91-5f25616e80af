import { <PERSON>ing, ScrollView, Text, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import {
  DirectboxDefault,
  Document,
  EmptyWallet,
  Global,
  Instagram,
  Message,
  Messages3,
  Note1,
  People,
  ShieldTick,
  Sms,
  Whatsapp,
} from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import VideoSection from 'src/components/get-started/video-section';
import FaqSection from 'src/components/get-started/faq-section';
import { BaseText, CircledIcon } from 'src/components/ui';
import SectionContainer from 'src/components/ui/section-container';
import { ListCard } from 'src/components/ui/cards/list-item-card';
import { openLinkInBrowser, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { ArrowRight, CatlogLogo, Tiktok, X } from 'src/components/ui/icons';
import { CONTACT_SUPPORT_WA_LINK, PRIVACY_POLICY_LINK } from 'src/assets/utils/js/constants';
import SelectDropdown, { DropDownItem, DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { useRef } from 'react';
import { COMMUNITY_LINK } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import SubscriptionInfoModal from 'src/components/get-help/subscription-info-modal';
import * as Application from 'expo-application';
import { useFeatureFlags } from '@/contexts/feature-flags/use-feature-flags';
import useFeatureFlagsStore from 'src/contexts/feature-flags/store';
import AboutCatlogModal from 'src/components/get-help/about-catlog-modal';
import Intercom from '@intercom/intercom-react-native';

function GetHelp() {
  const { store } = useAuthContext();
  const { modals, toggleModal } = useModals(['subscriptionInfo', 'catlogModal']);
  const { isFeatureEnabled } = useFeatureFlags();

  const dropDownRef = useRef<DropDownMethods>(null);

  const applicationId = Application.applicationId;
  const applicationName = Application.applicationName;
  const appVersion = Application.nativeApplicationVersion;
  const buildVersion = Application.nativeBuildVersion;

  const getAboutActions = () => {
    let actions = [
      {
        title: 'About Catlog',
        leftElement: (
          <CircledIcon className="bg-primary-pastel p-8">
            <CatlogLogo size={wp(14)} color={colors.primary.main} />
          </CircledIcon>
        ),
        onPress: () => toggleModal('catlogModal', true),
      },
      {
        title: 'Privacy Policy',
        leftElement: (
          <CircledIcon className="bg-accentGreen-pastel2 p-8">
            <ShieldTick size={wp(14)} color={colors.accentGreen.main} variant="Bold" />
          </CircledIcon>
        ),
        onPress: () => openLinkInBrowser(PRIVACY_POLICY_LINK),
      },
      {
        title: 'Terms and Condition',
        leftElement: (
          <CircledIcon className="bg-accentRed-pastel p-8">
            <Note1 size={wp(14)} color={colors.accentRed.main} variant="Bold" />
          </CircledIcon>
        ),
        onPress: () => openLinkInBrowser(PRIVACY_POLICY_LINK),
      },
    ];

    // Only show subscription info if the feature is enabled
    if (isFeatureEnabled('subscriptions')) {
      actions.push({
        title: 'How Catlog Subscription Work',
        leftElement: (
          <CircledIcon className="bg-accentYellow-pastel p-8">
            <Global size={wp(14)} color={colors.accentYellow.main} variant="Bold" />
          </CircledIcon>
        ),
        onPress: () => toggleModal('subscriptionInfo'),
      });
    }

    return actions;
  };

  const sections = [
    {
      title: 'Contact & Support',
      actions: [
        {
          title: 'Live Chat',
          leftElement: (
            <CircledIcon className="bg-primary-whiteTransparent p-8">
              <Messages3 size={wp(14)} color={colors.primary.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => Intercom.present(),
        },
        {
          title: 'Contact us via whatsapp',
          leftElement: (
            <CircledIcon className="bg-accentGreen-whiteTransparent p-8">
              <Whatsapp size={wp(14)} color={colors.accentGreen.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => Linking.openURL(CONTACT_SUPPORT_WA_LINK),
        },
        {
          title: 'Send us an Email',
          leftElement: (
            <CircledIcon className="bg-accentOrange-whiteTransparent p-8">
              <Sms size={wp(14)} color={colors.accentOrange.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
        {
          title: 'Join whatsapp Community',
          leftElement: (
            <CircledIcon className="bg-accentYellow-whiteTransparent p-8">
              <People size={wp(14)} color={colors.accentYellow.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => Linking.openURL(COMMUNITY_LINK[store.country?.code]),
        },
      ],
    },
    { title: 'About & Policies', actions: getAboutActions() },
    {
      title: 'Stay Connected',
      actions: [
        {
          title: 'Find help articles',
          leftElement: (
            <CircledIcon className="bg-accentOrange-pastel p-8">
              <Document size={wp(14)} color={colors.accentOrange.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => openLinkInBrowser('https://www.notion.so/49b13cc6f3904725b8f29aa337f7d73b?pvs=4'),
        },
        {
          title: 'Follow our socials for updates',
          leftElement: (
            <CircledIcon className="bg-accentRed-pastel p-8">
              <Instagram size={wp(14)} color={colors.accentRed.main} variant="Bold" />
            </CircledIcon>
          ),
          onPress: () => dropDownRef.current.open(),
        },
      ],
    },
  ];

  return (
    <DashboardLayout
      headerProps={{ headerBg: 'bg-primary-pastel', pageTitle: 'Get help', variant: HeaderVariants.SUB_LEVEL }}>
      <ScrollView className="flex-1">
        <View className="pb-45">
          <View className="px-20">
            {sections.splice(0, 1).map((h, idx) => (
              <View key={idx} className="mt-24">
                <BaseText fontSize={14} type="heading">
                  {h.title}
                </BaseText>
                <ListCard
                  titleClasses={'text-black-secondary'}
                  titleProps={{ weight: 'medium' }}
                  staticRightElement={
                    <CircledIcon className="bg-white p-6">
                      <ArrowRight size={wp(16)} currentColor={colors.primary.main} strokeWidth={2} />
                    </CircledIcon>
                  }
                  items={h.actions}
                />
                {/* <SectionContainer></SectionContainer> */}
              </View>
            ))}
          </View>
          <View>
            {/* <VideoSection hideIcon /> */}
            <FaqSection hideIcon hideOthers />
          </View>
          <View className="px-20">
            {sections
              .splice(0)
              .reverse()
              .map((h, idx) => (
                <View key={idx} className="mt-24">
                  <BaseText fontSize={14} type="heading">
                    {h.title}
                  </BaseText>
                  <ListCard
                    titleClasses={'text-black-secondary'}
                    titleProps={{ weight: 'medium' }}
                    staticRightElement={
                      <CircledIcon className="bg-white p-6">
                        <ArrowRight size={wp(16)} currentColor={colors.primary.main} strokeWidth={2} />
                      </CircledIcon>
                    }
                    items={h.actions}
                  />
                  {/* <SectionContainer></SectionContainer> */}
                </View>
              ))}
          </View>
          <View className="mt-15 items-center justify-center">
            <BaseText fontSize={11} classes="text-black-placeholder text-center" lineHeight={18}>
              {applicationName} - ({applicationId})
            </BaseText>
            <BaseText fontSize={11} classes="text-black-placeholder text-center" lineHeight={18}>
              v{appVersion} (Build: {buildVersion})
            </BaseText>
          </View>
        </View>
      </ScrollView>
      <SelectDropdown
        showAnchor={false}
        showRadio={false}
        ref={dropDownRef}
        selectedItem=""
        onPressItem={handleOnPressAction}
        items={socialMediaItems}
        containerClasses="mb-15"
        label="Follow Our Socials"
        genItemKeysFun={value => value.label}
        // showButton
        showLabel
      />
      <SubscriptionInfoModal
        isVisible={modals.subscriptionInfo}
        closeModal={() => toggleModal('subscriptionInfo', false)}
      />
      <AboutCatlogModal isVisible={modals.catlogModal} closeModal={() => toggleModal('catlogModal', false)} />
    </DashboardLayout>
  );
}

export default GetHelp;

const socialMediaItems: DropDownItem[] = [
  {
    label: 'Instagram',
    value: 'Instagram',
    // onPress: () => Linking.openURL('instagram://user?username=catlogshop'),
    onPress: () => Linking.openURL('https://www.instagram.com/catlogshop'),
    actionDelay: 600,
    leftElement: (
      <CircledIcon className="p-8 bg-accentOrange-pastel">
        <Instagram variant="Bold" size={wp(15)} color={colors.accentOrange.main} strokeWidth={2} />
      </CircledIcon>
    ),
  },
  {
    label: 'Tiktok',
    value: 'Tiktok',
    onPress: () => Linking.openURL('https://www.tiktok.com/@catlogshop'),
    actionDelay: 600,
    leftElement: (
      <CircledIcon className="p-8 bg-accentRed-pastel">
        <Tiktok size={wp(15)} primaryColor={colors.accentRed.main} />
      </CircledIcon>
    ),
  },
  {
    label: 'X (Twitter)',
    value: 'X (Twitter)',
    onPress: () => Linking.openURL('https://x.com/CatlogShop'),
    actionDelay: 600,
    leftElement: (
      <CircledIcon className="p-8 bg-accentYellow-pastel">
        <X size={wp(15)} primaryColor={colors.accentYellow.main} />
      </CircledIcon>
    ),
  },
];

const handleOnPressAction = (v: string) => {
  const vIndex = socialMediaItems.findIndex(d => d.value === v);
  if (vIndex !== -1) {
    const actionDelay = socialMediaItems[vIndex]?.actionDelay;

    setTimeout(() => {
      socialMediaItems[vIndex].onPress?.();
    }, actionDelay);
  }
};
