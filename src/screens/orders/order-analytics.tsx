import { GET_STORE_ORDER_STATISTICS, GetOrderStatisticsParams, paramsFromObject } from 'catlog-shared';
import { useMemo, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { ChevronDown } from 'src/components/ui/icons';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useFileDownload } from 'src/hooks/use-file-download';
import useModals from 'src/hooks/use-modals';

import { enumToHumanFriendly, generateColorSet, wp } from '@/assets/utils/js';
import OrderAnalyticsCard from '@/components/orders/order-analytics-cards';
import { AreaGraph, BaseText, Row } from '@/components/ui';
import { getFilter } from '@/components/ui/graph/area-graph';
import DoughnutChart from '@/components/ui/graph/doughnut-chart';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import DateRangeModal from '@/components/ui/modals/date-range-modal';
import MoreOptions from '@/components/ui/more-options';
import InfoRow from '@/components/ui/others/info-row';
import SectionContainer from '@/components/ui/section-container';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import colors from '@/theme/colors';
import { TimeRange } from 'src/@types/utils';
import CalendarModal from 'src/components/ui/modals/calendar-modal';
import useAnalyticsRange from 'src/hooks/use-analytics-range';

export interface DateRangeString {
  from: string | null;
  to: string | null;
}

export interface OrderStatistics {
  currencies: string[];
  grouped_data: GroupedData;
  channels: OrderChannels;
}

export interface OrderChannels {
  [key: string]: ChannelData;
}

export interface ChannelData {
  count: number;
  percentage: number;
}

export interface GroupedData {
  [key: string]: StoreSummary;
}

export interface StoreSummary {
  total_orders: number;
  total_customers: number;
  total_orders_amount: number;
  total_completed_orders: number;
  orders: OrdersGraphData;
  channels: OrderChannels;
}

export interface OrdersGraphData {
  volumes: Volume[];
  units: Unit[];
}

export interface Unit {
  time: Date;
  value: string;
}

export interface Volume {
  time: Date;
  value: number;
}

const OrderAnalytics = () => {
  const { store } = useAuthContext();
  const [currency, setCurrency] = useState(store?.currencies?.default as string);
  const { modals, toggleModal } = useModals(['dateRange', 'calendar']);

  //prettier-ignore
  const { analyticsRange, customRange, range, handleSelectRange, setCustomRange, showCalendar, toggleCalendar } = useAnalyticsRange();
  const getStatistics = useApi<GetOrderStatisticsParams, ResponseWithPagination<OrderStatistics>>(
    {
      key: 'get-order-statistics',
      apiFunction: GET_STORE_ORDER_STATISTICS,
      method: 'GET',
    },
    {
      filter: analyticsRange,
    },
  );

  const { downloadFile, isLoading } = useFileDownload();

  const stats = getStatistics?.response?.data;

  const channels: OrderChannels = stats?.grouped_data?.[currency]?.channels ?? {};
  const colorSet = generateColorSet(
    colors.accentRed.main,
    Object.keys(channels).map(key => key),
  );

  const calculatePercentage = (count: number, dataSet: OrderChannels) => {
    const total = Object.values(dataSet).reduce((acc, curr) => acc + curr.count, 0);
    return (count / total) * 100;
  };

  const channelPieData = useMemo(() => {
    return Object.keys(channels ?? {})
      .map((key, index) => ({
        value: channels?.[key]?.count,
        name: enumToHumanFriendly(key),
        color: colorSet[index],
        percentage: calculatePercentage(channels?.[key]?.count, channels),
      }))
      .sort((a, b) => {
        return b.value - a.value;
      });
  }, [channels]);

  const summaryData = stats?.grouped_data?.[currency] ?? ({} as StoreSummary);

  const graphRawData = stats?.grouped_data?.[currency]?.orders?.units ?? [];

  const handleExportOrders = async (dateRange: DateRangeString) => {
    try {
      const reqData = {
        filter: {
          from: dateRange.from,
          to: dateRange.to,
        },
      };
      if (dateRange.from === null || dateRange.to === null) return;

      const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      const fileName = `orders-${new Date(dateRange.from).toDateString()}-${new Date(dateRange.to).toDateString()}.xlsx`;
      const downloadResult = await downloadFile(subUrl, fileName);
    } catch (error) {
      Toast.show({ text1: error?.message ?? 'Error processing your request', type: 'error' });
    }
  };
  const optionElement = stats?.currencies?.map(c => ({ title: c, onPress: () => setCurrency(c) }));

  // console.log(JSON.stringify(stats?.grouped_data))

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Order Analytics',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView className="flex-1" scrollIndicatorInsets={{ right: 1 }}>
        <View className="pt-20 mx-20">
          <Row className="mb-15">
            <Row>
              <BaseText fontSize={15} weight="bold" type="heading">
                Performance Overview
              </BaseText>
              <ChevronDown size={15} primaryColor={colors.grey.muted} />
            </Row>
            <MoreOptions
              options={optionElement ?? []}
              customMenuElement={
                <Row className="items-center bg-grey-bgOne py-7 px-10 rounded-full">
                  <BaseText fontSize={12} classes="mr-4" style={{ color: colors.black.muted }}>
                    {currency}
                  </BaseText>
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
                </Row>
              }
            />
          </Row>
          <OrderAnalyticsCard currency={currency} analytics={summaryData} loading={getStatistics.isLoading} />
        </View>
        {/* <ScrollableActionPills pills={actionPills} title="Quick action" /> */}
        <View className="mx-20 mt-15 pb-80">
          <View className="mt-20">
            {/* {series?.length > 0 && (
              <AreaGraph title={'New Customers'} range={range} setRange={setRange} graphData={series ?? []} />
            )} */}
            <AreaGraph
              title="Total Orders"
              range={range}
              setCustomRange={setCustomRange}
              toggleCalendar={toggleCalendar}
              customRange={customRange}
              showCalendar={showCalendar}
              isLoadingData={getStatistics.isLoading}
              rawData={[graphRawData]}
              setRange={handleSelectRange}
              className="mt-5"
              labels={{
                topPrefix: 'Total Orders:',
              }}
            />
          </View>
          <View className="mt-20">
            <DoughnutChart title="Order Channels" pieData={channelPieData} />
          </View>
          {channelPieData?.length > 0 && (
            <SectionContainer>
              <Row className="py-10 border-b border-b-grey-border">
                <View className="flex-1">
                  <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                    Channel
                  </BaseText>
                </View>
                <View className="flex-1 items-end">
                  <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                    No. Orders
                  </BaseText>
                </View>
                <View className="flex-1 items-end">
                  <BaseText fontSize={12} lineHeight={20} classes="text-black-placeholder">
                    %
                  </BaseText>
                </View>
              </Row>
              <View className="gap-5 mt-5">
                {channelPieData.map((item, index) => (
                  <InfoRow
                    key={item.name + index}
                    iconElement={
                      <Row className="flex-1 justify-start">
                        <View className="w-12 h-12 rounded-full mr-12" style={{ backgroundColor: item.color }} />
                        <BaseText lineHeight={20}>{item?.name}</BaseText>
                      </Row>
                    }
                    title={String(item.value)}
                    titleClasses="flex-1 self-end mx-0 text-right"
                    valueElement={
                      <View className="flex-1 items-end">
                        <BaseText lineHeight={20}>{item?.percentage?.toFixed(1)}%</BaseText>
                      </View>
                    }
                  />
                ))}
              </View>
            </SectionContainer>
          )}
        </View>
      </ScrollView>
      <DateRangeModal
        isVisible={modals.dateRange}
        closeModal={() => toggleModal('dateRange', false)}
        onPressProceed={handleExportOrders}
      />
    </DashboardLayout>
  );
};

export default OrderAnalytics;
