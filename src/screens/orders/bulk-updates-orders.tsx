import {
  alertPromise,
  enumTo<PERSON><PERSON><PERSON>riendly,
  hide<PERSON>oa<PERSON>,
  showError,
  show<PERSON>oader,
  showSuc<PERSON>,
  wp,
} from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useRouteParams from '@/hooks/use-route-params';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { BULK_MARK_AS_PAID, BULK_ORDER_UPDATE, ORDER_STATUSES, capitalizeFirstLetter } from 'catlog-shared';
import { Add, Bag, CloseCircle, MoreCircle, Status, StatusUp, TickCircle, Trash } from 'iconsax-react-native/src';
import { useMemo, useRef, useState } from 'react';
import { Image, Platform, View } from 'react-native';
import Animated from 'react-native-reanimated';
import OrderItemCard from 'src/components/orders/list/order-item-card';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import SelectDropdown, { DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useOrdersApi from 'src/hooks/use-orders-api';
import { OrderListSkeletonLoader } from './list';
import { useApi } from 'src/hooks/use-api';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import CheckBox from 'src/components/ui/buttons/check-box';
import Pressable from 'src/components/ui/base/pressable';

// const BATCH_SIZE = 10;
const BulkUpdateOrders = () => {
  const { payment_status, status = ORDER_STATUSES.PENDING, pageCount = 10 } = useRouteParams<'BulkUpdateOrders'>();
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const navigation = useNavigation();
  const dropDownRef = useRef<DropDownMethods>(null);

  const computedStatusText = capitalizeFirstLetter(
    payment_status ? payment_status.toLowerCase() : status.toLowerCase(),
  );

  const { orders, getOrdersRequest, handleOnEndReach } = useOrdersApi(
    { status, payment_status: payment_status },
    pageCount,
  );

  // const orders = inputOrders ?? orders;

  const bulkUpdateOrdersReq = useApi({ apiFunction: BULK_ORDER_UPDATE, key: BULK_ORDER_UPDATE.name, method: 'PUT' });
  const bulkMarkasPaidReq = useApi({ apiFunction: BULK_MARK_AS_PAID, key: BULK_MARK_AS_PAID.name, method: 'PUT' });

  const completeUpdate = () => {
    eventEmitter.emit('PENDING_ACTIONS_UPDATED', { state: true });
    navigation.goBack();
  };

  const handleBulkMarkAsPaid = async () => {
    const alertResponse = await alertPromise(
      'Update Payment Status',
      `Clicking "Yes, Update" will update the order payment status of the selected orders to paid`,
      'Yes, Update',
      'Cancel',
      false,
    );
    if (alertResponse === false) {
      return;
    }
    dropDownRef.current.close();

    const reqData = { order_ids: selectedOrders };

    showLoader('Updating Status', false, true);
    const [response, error] = await bulkMarkasPaidReq.makeRequest(reqData);
    hideLoader();
    if (response) {
      showSuccess('Selected order status updated successfully');
      setSelectedOrders([]);
      completeUpdate();
    }
    if (error) {
      showError(error?.body.body.message);
    }
  };

  const handleBulkOrderUpdate = async (status: ORDER_STATUSES) => {
    const alertResponse = await alertPromise(
      'Update Order Status',
      `Clicking "Yes, Update" will update the order status of the selected orders to ${status}`,
      'Yes, Update',
      'Cancel',
      false,
    );
    if (alertResponse === false) {
      return;
    }
    dropDownRef.current.close();

    const reqData = { orders: selectedOrders.map(id => ({ id, status })) };

    showLoader('Updating Status', false, true);
    const [response, error] = await bulkUpdateOrdersReq.makeRequest(reqData);
    hideLoader();
    if (response) {
      showSuccess('Selected order status updated successfully');
      setSelectedOrders([]);
      completeUpdate();
    }

    if (error) {
      showError(error?.body.body.message);
    }
  };

  const selectOrders = (orderId: string) => {
    const orderCopy = [...selectedOrders];
    const indexSearch = orderCopy.findIndex(d => d === orderId);

    if (indexSearch === -1) {
      setSelectedOrders(prev => [...prev, orderId]);
      return;
    }

    orderCopy.splice(indexSearch, 1);
    setSelectedOrders(orderCopy);
  };

  const actionCallBack = () => {};

  const statusUpdateDropdownItems = useMemo(() => {
    const items = [
      ORDER_STATUSES.ABANDONED,
      ORDER_STATUSES.CANCELLED,
      ORDER_STATUSES.FULFILLED,
      ORDER_STATUSES.PROCESSING,
    ]
      .filter(s => s !== status)
      .map(value => ({
        label: enumToHumanFriendly(value),
        value,
        onPress: () => {
          handleBulkOrderUpdate(value);
        },
        leftElement: <CircledIcon className="bg-grey-bgOne">{actionIcons[value]}</CircledIcon>,
      }));

    /*  items.push({
       label: 'Mark as Paid',
       value: 'mark_as_paid' as any,
       onPress: () => { handleBulkMarkAsPaid() },
       leftElement: <CircledIcon className="bg-white"><TickCircle size={wp(16)} color={colors.accentGreen.main} /></CircledIcon>,
     })
  */
    return items;
  }, [selectedOrders]);
  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: `Update ${computedStatusText} Orders`,
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <Animated.FlatList
        data={orders}
        removeClippedSubviews={Platform.OS === 'android'}
        windowSize={Platform.OS === 'android' ? 10 : 21}
        maxToRenderPerBatch={Platform.OS === 'android' ? 15 : 30}
        updateCellsBatchingPeriod={Platform.OS === 'android' ? 50 : 100}
        scrollEventThrottle={16}
        onEndReachedThreshold={0.3}
        // refreshControl={<RefreshControl refreshing={false} onRefresh={() => handlePullToRefresh()} />}
        renderItem={({ item, index }) => (
          <View key={index} className="px-10">
            <OrderItemCard
              order={item}
              actionCallBack={actionCallBack}
              disabled={selectedOrders.length > 0}
              onPress={() => navigation.navigate('OrderInfo', { id: item.id })}
              orderStatus={ORDER_STATUSES.PENDING}
              handleSelection={() => selectOrders(item.id)}
              toggleMarkAsPaid={() => {}}
              isSelectionActive={selectedOrders.includes(item.id)}
            />
          </View>
        )}
        ListHeaderComponent={() => (
          <View>
            <ScreenInfoHeader
              colorPalette={ColorPaletteType.RED}
              isTextFollowPalette={false}
              iconElement={
                <Image
                  source={require('@/assets/images/cart.png')}
                  resizeMode={'contain'}
                  className="w-[80px] h-[80px]"
                />
              }
              pageTitleTop={'Update'}
              pageTitleBottom={`${computedStatusText} Orders`}
            />
            <Row className="px-20 pt-20 pb-10">
              <Pressable
                onPress={() => {
                  if (selectedOrders.length === orders.length) {
                    setSelectedOrders([]);
                    return;
                  }
                  setSelectedOrders(orders.map(d => d.id));
                }}>
                <Row>
                  <CheckBox checked={selectedOrders.length === orders.length} />
                  <BaseText classes="ml-10 text-grey-mutedDark" fontSize={14} type="body">
                    Select All
                  </BaseText>
                </Row>
              </Pressable>
              <BaseText fontSize={13} type="body" weight="medium" classes="text-black">
                {orders?.length} Orders
              </BaseText>
            </Row>
          </View>
        )}
        ListFooterComponent={() => (
          <View style={{ paddingBottom: 120 }} className="px-10">
            {orders?.length > 0 && getOrdersRequest.isLoading && (
              <View>
                <OrderListSkeletonLoader />
              </View>
            )}
          </View>
        )}
        ListEmptyComponent={() =>
          getOrdersRequest.isLoading ? (
            <OrderListSkeletonLoader />
          ) : (
            <EmptyState
              icon={<Bag variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
              btnText="Record an Order"
              onPressBtn={() => navigation.navigate('RecordOrder')}
              text={'No orders found'}
            />
          )
        }
        onEndReached={handleOnEndReach}
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
      />
      {/* <ScrollView keyboardShouldPersistTaps={'handled'}>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.YELLOW}
          isTextFollowPalette={false}
          iconElement={
            <CircledIcon className="bg-accentYellow-main p-15">
              <Add variant={'Bold'} color={colors.white} size={wp(30)} />
            </CircledIcon>
          }
          pageTitleTop={'Update'}
          pageTitleBottom={'Pending Orders'}
        />
        <Container className={'mt-20'}>
        </Container>
      </ScrollView> */}
      <FixedBtnFooter
        buttons={[
          {
            disabled: selectedOrders.length === 0,
            text: payment_status ? 'Mark as Paid' : 'Select Status',
            onPress: () => (payment_status ? handleBulkMarkAsPaid() : dropDownRef.current?.open()),
          },
        ]}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem=""
        // onPressItem={value => {console.log(value) }}
        items={statusUpdateDropdownItems ?? []}
        containerClasses="mb-15"
        label="Select Order Status"
        closeAfterSelection={false}
        genItemKeysFun={value => value.label}
        showButton
        // buttons={[{ text: 'Proceed', onPress: () => dropDownRef.current?.close() }]}
        headerComponent={
          <View className="px-20 pb-10">
            <BaseText classes="text-black-muted">
              Order status would be updated to desired status you select for the selected orders.
            </BaseText>
          </View>
        }
        showLabel
      />
    </DashboardLayout>
  );
};

export default BulkUpdateOrders;

const actionIcons = {
  [ORDER_STATUSES.ABANDONED]: <Trash size={wp(16)} color={colors.accentOrange.main} strokeWidth={2} />,
  [ORDER_STATUSES.PROCESSING]: <MoreCircle size={wp(16)} color={colors.accentYellow.main} strokeWidth={2} />,
  [ORDER_STATUSES.FULFILLED]: <TickCircle size={wp(16)} color={colors.accentGreen.main} strokeWidth={2} />,
  [ORDER_STATUSES.CANCELLED]: <CloseCircle size={wp(16)} color={colors.accentRed.main} strokeWidth={2} />,
  // [ORDER_STATUSES.PENDING]: <Status size={wp(16)} color={colors.accentOrange.main} />,
};
