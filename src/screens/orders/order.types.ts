import { CartI<PERSON>, Create<PERSON>rder<PERSON>ara<PERSON>, ORDER_CHANNELS, ProductItemInterface, StoreInterface } from "catlog-shared";
import { ResponseWithPagination } from "src/hooks/use-api";

export enum RECORD_ORDER_STEPS {
  ORDER_ITEMS,
  CUSTOMER_INFORMATION,
  DELIVERY_INFORMATION,
  DISCOUNT_AND_FEES,
}

export interface CreateOrderFormParams extends Omit<CreateOrderParams, 'items'> {
  showOtherSteps?: boolean;
  selectedCustomerId?: string;
  useSameInfoAsCustomer: boolean;
  tempSelectedFeeType: string;
  tempFeeAmount: string;
  delivery_phone: { code: string; digits: string };
  channel?: ORDER_CHANNELS;
  is_paid: boolean;
  items: CartItem[];
}
export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}