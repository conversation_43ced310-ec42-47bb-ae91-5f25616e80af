import { ScrollView, Text, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useNavigation } from '@react-navigation/native';
import { Container } from '@/components/ui';
import useModals from 'src/hooks/use-modals';
import DeliveryAnalyticsCards from '@/components/deliveries/delivery-analytics-cards';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { GET_DELIVERIES_STATISTICS } from 'catlog-shared';

export interface DeliveryStat {
  cancelled_shipments: number;
  confirmed_shipments: number;
  draft_shipments: number;
  ongoing_shipments: number;
  total_shipments: number;
}

function DeliveryAnalytics() {
  const getDeliveriesStatRequest = useApi<void, ResponseWithoutPagination<DeliveryStat>>({
    apiFunction: GET_DELIVERIES_STATISTICS,
    method: 'GET',
    key: 'get-order',
  });

  const stats = getDeliveriesStatRequest?.response?.data ?? ({} as DeliveryStat);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'Delivery Analytics',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView>
        <Container className={'mt-20 z-50 overflow-visible pb-100'}>
          <DeliveryAnalyticsCards statistics={stats} isLoading={getDeliveriesStatRequest?.isLoading} />
        </Container>
      </ScrollView>
    </DashboardLayout>
  );
}

export default DeliveryAnalytics;
