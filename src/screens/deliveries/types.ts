import { Yup } from 'src/assets/utils/js';
import { phoneValidation } from 'src/assets/utils/js/common-validations';

export enum DELIVERY_STEPS {
  EMPTY = 'EMPTY',
  ITEMS = 'ITEMS',
  PICKUP = 'PICKUP',
  DROP_OFF = 'DROP_OFF',
  EXTRA_INFO = 'EXTRA_INFO',
  COURIERS = 'COURIERS',
  SUMMARY = 'SUMMARY',
  PAYMENT = 'PAYMENT',
  SUCCESS = 'SUCCESS',
}

export const ExtraInfoValidationSchema = {
  package_category: Yup.number().required('Package category is required'),
  package_dimensions: Yup.object().shape({
    height: Yup.number().typeError('Package dimension is required').required('Package dimension is required'),
    width: Yup.number().typeError('Package dimension is required').required('Package dimension is required'),
    length: Yup.number().typeError('Package dimension is required').required('Package dimension is required'),
  }),
  pickup_date: Yup.date().required('Pickup date is required'),
};

export const ItemsValidationShema = Yup.array()
  .of(
    Yup.object().shape({
      unit_weight: Yup.number()
        .typeError('Item weight is required')
        .required('Item weight is required')
        .min(0.01, 'Item weight must be at least 0.01kG'),
      quantity: Yup.number()
        .typeError('Item weight is required')
        .required('Item quantity is required')
        .min(1, 'Item quantity must be greater than 0'),
    }),
  )
  .min(1, 'Please add at least one item');

export const AddressValidationShema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  address: Yup.string().required('Please select an address from the dropdown'),
  email: Yup.string().email('Invalid email address'),
});
