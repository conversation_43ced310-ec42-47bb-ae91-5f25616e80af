import { useNavigation } from '@react-navigation/native';
import {
  ProductItemInterface,
  StoreInterface,
  CREATE_ORDER,
  CREATE_DELIVERY_DRAFT,
  CreateDeliveryDraftParams,
  GET_SHIPPING_RATES,
  GetShippingRatesParams,
  UPDATE_DELIVERY_DRAFT,
  UpdateDeliveryDraftParams,
  DELIVERY_PROVIDERS,
  IDeliveryAddress,
  IDeliveryCourier,
  DeliveryForm,
  IDelivery,
  phoneObjectFromString,
  PAYMENT_TYPES,
} from 'catlog-shared';
import dayjs from 'dayjs';
import { FormikProps, useFormik } from 'formik';
import { TickCircle } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import useAuthContext from 'src/contexts/auth/auth-context';
import * as Yup from 'yup';

import { wp } from '@/assets/utils/js';
import DeliverySummary from '@/components/deliveries/initiate-delivery/delivery-summary';
import InitiateDeliveryForm from '@/components/deliveries/initiate-delivery/initiate-delivery-form';
import MakePayment from '@/components/deliveries/initiate-delivery/make-payment';
import ShippingOptions from '@/components/deliveries/initiate-delivery/shipping-options';
import { BaseText, CircledIcon } from '@/components/ui';
import { ButtonVariant } from '@/components/ui/buttons/button';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { ApiData, ResponseWithoutPagination, ResponseWithPagination, useApi } from '@/hooks/use-api';
import useFluxState from '@/hooks/use-flux-state';
import useRouteParams from '@/hooks/use-route-params';
import useSteps, { UseStepsData } from '@/hooks/use-steps';
import colors from '@/theme/colors';
import PaymentsWidget, { PAYMENT_WIDGET_ADDONS } from 'src/components/payments/payments-widget';
import { AddressValidationShema, DELIVERY_STEPS, ExtraInfoValidationSchema, ItemsValidationShema } from './types';
import useModals from 'src/hooks/use-modals';

// export enum DELIVERY_STEPS {
//   EMPTY = 'EMPTY',
//   ITEMS = 'ITEMS',
//   PICKUP = 'PICKUP',
//   DROP_OFF = 'DROP_OFF',
//   EXTRA_INFO = 'EXTRA_INFO',
//   COURIERS = 'COURIERS',
//   SUMMARY = 'SUMMARY',
//   PAYMENT = 'PAYMENT',
//   SUCCESS = 'SUCCESS',
// }

export interface SharedStepProps {
  form: FormikProps<CreateDeliveryFormParams>;
  formSteps: UseStepsData;
  isLoading?: boolean;
}

export interface CreateDeliveryFormParams extends DeliveryForm {
  stepIndex: number;
}
export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

export interface ShippingOptionsResponse
  extends ResponseWithoutPagination<{ request_token: string; couriers: IDeliveryCourier[] }> {}

const InitiateDelivery = () => {
  const [deliveryCreated, setDeliveryRecorded] = useState(false);
  const [firstStepIsComplete, setFirstStepIsComplete] = useState(false);
  const [deliveryData, setDeliveryData] = useFluxState({}, []);
  const { modals, toggleModal } = useModals(['payments']);

  const [isInitiateFormValid, setIsInitiateFormValid] = useState(false);
  const { store, user } = useAuthContext();

  const params = useRouteParams<'InitiateDelivery'>();

  const initialData = params?.deliveryData ?? undefined;

  const formSteps = useSteps(Object.values(DELIVERY_STEPS), 1);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;

  const navigation = useNavigation();

  const createOrderRequest = useApi({ apiFunction: CREATE_ORDER, method: 'POST', key: 'create-order' });

  const createDeliveryDraftRequest = useApi<CreateDeliveryDraftParams>({
    apiFunction: CREATE_DELIVERY_DRAFT,
    method: 'POST',
    key: 'create-delivery-draft',
  });

  const updateDeliveryDraftRequest = useApi<UpdateDeliveryDraftParams>({
    apiFunction: UPDATE_DELIVERY_DRAFT,
    method: 'PUT',
    key: 'update-delivery-draft',
  });

  const getShippingRateRequest = useApi<GetShippingRatesParams, ShippingOptionsResponse>({
    apiFunction: GET_SHIPPING_RATES,
    method: 'GET',
    autoRequest: false,
    key: 'get-shipping-draft',
  });

  useEffect(() => {
    const initialValues = getFormFields(
      { ...initialData, sender_address: initialData?.sender_address ?? store?.pickup_address },
      user,
      store?.delivery_providers ?? [],
    );
    form.setValues({ ...initialValues, stepIndex: form.values.stepIndex });
  }, []);

  const initialValues = getFormFields(
    { ...initialData, sender_address: initialData?.sender_address ?? store?.pickup_address },
    user,
    store?.delivery_providers ?? [],
  );

  const form = useFormik<CreateDeliveryFormParams>({
    initialValues: {
      ...initialValues,
      stepIndex,
    },
    validationSchema,
    onSubmit: async values => {
      try {
        switch (stepIndex) {
          case 1:
            await saveDraft();
            next();
            break;
          case 2:
            await saveDraft();
            next();
            break;
          case 3:
            await saveDraft();
            next();
            break;
          case 4:
            handleNext();
            //DONT GO NEXT BUT INITIATE FETCHING RATES
            break;
        }
      } catch (e) {}
    },
  });

  const createDeliveryDraft = async (
    form: FormikProps<CreateDeliveryFormParams>,
    req: ApiData<CreateDeliveryDraftParams, ResponseWithoutPagination<IDelivery>>,
  ) => {
    const items = form.values.items;
    const [res, err] = await req.makeRequest({ items, provider: form?.values?.provider, order: form?.values?.order });

    if (err) {
      Toast.show({ text1: err?.message ?? "Couldn't save info, please retry", type: 'error' });
      return Promise.reject(err);
    }

    form.setFieldValue('id', res.data.id);
    return Promise.resolve(res);
  };

  //move to app common
  //move to app common
  //move to app common
  //move to app common
  function normalizeDateToIso(date: Date) {
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString();
  }

  const updateDeliveryDraft = async (
    form: FormikProps<CreateDeliveryFormParams>,
    req: ApiData<UpdateDeliveryDraftParams, ResponseWithoutPagination<IDelivery>>,
  ) => {
    const formValues = form.values;

    if (!formValues.id) return;

    const [res, err] = await req.makeRequest({
      provider: formValues.provider ?? DELIVERY_PROVIDERS.SHIPBUBBLE,
      id: formValues.id,
      items: formValues.items,
      sender_address: formValues.pickup_address?.id,
      receiver_address: formValues.dropoff_address?.id,
      delivery_notes: formValues.delivery_notes,
      package_category: Number(formValues.package_category),
      package_dimensions: formValues.package_dimensions,
      pickup_date: formValues.pickup_date && normalizeDateToIso(formValues.pickup_date),
      courier: formValues?.courier
        ? {
            service_code: formValues.courier?.service_code ?? '',
            courier_id: formValues.courier?.courier_id ?? '',
            delivery_date: new Date(formValues?.courier?.delivery_eta_time).toISOString() ?? '',
            courier_image: formValues.courier?.courier_image ?? '',
            courier_name: formValues.courier?.courier_name ?? '',
          }
        : undefined,
    });

    if (err) {
      return Promise.reject(err);
    }

    return Promise.resolve(res);
  };

  const saveDraft = async () => {
    const shouldUpdate = Boolean(form?.values?.pickup_address?.id); //check if anything other than items exist;
    const shouldCreate = !form?.values?.id;

    if (shouldCreate) {
      const res = await createDeliveryDraft(form, createDeliveryDraftRequest);
      form.setFieldValue('id', res?.data?.id);
      // form.values.id = res?.data?.id;
    }

    if (shouldUpdate) {
      await updateDeliveryDraft(form, updateDeliveryDraftRequest);
    }

    setFirstStepIsComplete(true);
  };

  const stepIsPreShippingRates = (step: DELIVERY_STEPS) => {
    return [
      DELIVERY_STEPS.ITEMS,
      DELIVERY_STEPS.DROP_OFF,
      DELIVERY_STEPS.PICKUP,
      DELIVERY_STEPS.EXTRA_INFO,
      DELIVERY_STEPS.EMPTY,
    ].includes(step);
  };

  const handleNext = async () => {
    setFirstStepIsComplete(true);

    if (stepIsPreShippingRates(step as DELIVERY_STEPS)) {
      if (!form?.values?.id) {
        const res = await createDeliveryDraft(form, createDeliveryDraftRequest);
        form.values.id = res?.data?.id;
      }

      const data = await updateDeliveryDraft(form, updateDeliveryDraftRequest);
      setDeliveryData(data?.data!);

      const [res, error] = await getShippingRateRequest.makeRequest({ id: form?.values?.id! });

      if (res) {
        changeStep(DELIVERY_STEPS.COURIERS);
        return;
      }

      Toast.show({ text1: error?.error ?? error?.message, type: 'error' });
      return;
    }

    if (step === DELIVERY_STEPS.SUMMARY) {
      const data = await updateDeliveryDraft(form, updateDeliveryDraftRequest);

      setDeliveryData(data?.data!);
      // next();
      toggleModal('payments');
      return;
    }

    next();
  };

  const loading =
    createOrderRequest.isLoading || updateDeliveryDraftRequest.isLoading || getShippingRateRequest.isLoading;

  return (
    <DashboardLayout
      headerProps={{
        headerBg: headerData(stepIndex)?.headerBg ?? 'bg-accentOrange-pastel',
        pageTitle: headerData(stepIndex).title ?? 'Create Delivery',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      {deliveryCreated === false && (
        <>
          {stepIndex <= 4 && (
            <InitiateDeliveryForm
              form={form}
              formSteps={formSteps}
              setIsInitiateFormValid={setIsInitiateFormValid}
              firstStepIsComplete={firstStepIsComplete}
              isLoading={createDeliveryDraftRequest.isLoading || updateDeliveryDraftRequest.isLoading}
            />
          )}
          {stepIndex === 5 && (
            <ShippingOptions form={form} formSteps={formSteps} getShippingRatesRequest={getShippingRateRequest} />
          )}
          {(stepIndex === 6 || stepIndex === 7) && <DeliverySummary form={form} formSteps={formSteps} />}
        </>
      )}
      {/* {stepIndex === 7 && <MakePayment form={form} formSteps={formSteps} />} */}
      {deliveryCreated === false && (
        <FixedBtnFooter
          buttons={[
            ...(stepIsPreShippingRates(step as DELIVERY_STEPS)
              ? [
                  {
                    text: 'Get Shipping Option',
                    onPress: handleNext,
                    disabled: !canNext || !isInitiateFormValid || getShippingRateRequest.isLoading,
                    isLoading:
                      createOrderRequest.isLoading ||
                      updateDeliveryDraftRequest.isLoading ||
                      getShippingRateRequest.isLoading,
                  },
                ]
              : []),
            ...(stepIndex > 4
              ? [
                  {
                    text: 'Go Back',
                    onPress: previous,
                    disabled: getShippingRateRequest.isLoading,
                    variant: ButtonVariant.LIGHT,
                  },
                ]
              : []),
            ...(!stepIsPreShippingRates(step as DELIVERY_STEPS)
              ? [
                  {
                    text: 'Proceed',
                    onPress: handleNext,
                    disabled: !canNext || !form.values.courier,
                    isLoading:
                      createOrderRequest.isLoading ||
                      updateDeliveryDraftRequest.isLoading ||
                      getShippingRateRequest.isLoading,
                  },
                ]
              : []),
          ]}
        />
      )}
      {deliveryCreated && (
        <View className="flex-1 justify-center">
          <ScrollView className="flex-1">
            <View className="items-center justify-center mt-30">
              <CircledIcon className="bg-accentGreen-pastel p-15">
                <CircledIcon className="bg-accentGreen-main p-25">
                  <TickCircle variant="Bold" size={wp(50)} color={colors.white} />
                </CircledIcon>
              </CircledIcon>
              <BaseText fontSize={22} type="heading" classes="text-center mt-10 max-w-[325px]">
                Your delivery has{'\n'}been booked
              </BaseText>
            </View>
          </ScrollView>
          <FixedBtnFooter
            buttons={[
              {
                text: 'See all Deliveries',
                onPress: () => navigation.goBack(),
              },
            ]}
          />
        </View>
      )}

      {/* {stepIndex === 7 && ( */}
      <PaymentsWidget
        onComplete={d => {
          setDeliveryRecorded(true);
        }}
        data={{
          delivery: form.values as unknown as IDelivery,
          addon: PAYMENT_WIDGET_ADDONS.DELIVERY,
          paymentType: PAYMENT_TYPES.DELIVERY,
          successMessage: 'Your payment for this delivery was successful. Click continue to get delivery info',
        }}
        show={modals.payments}
        toggle={() => toggleModal('payments')}
      />
      {/* )} */}
    </DashboardLayout>
  );
};

const getEmptyDefaultAddress = (isPickup: boolean) => {
  const address = {
    id: '',
    address: '',
    name: '',
    phone: {
      code: '+234',
      digits: '',
    },
    email: '',
    customer: '',
    // place_id: "",
  };

  if (isPickup) return address;

  return { ...address, save_as_customer: false };
};

const getFormFields = (initialData?: IDelivery, user?: any, providers?: DELIVERY_PROVIDERS[]): DeliveryForm => {
  const extractAddressForm = (address: IDeliveryAddress) => {
    return {
      name: address?.name,
      phone: phoneObjectFromString(address?.phone),
      id: address?.id,
      email: address?.email,
      address: address?.formatted_address,
    };
  };

  if (initialData) {
    const { receiver_address, sender_address, pickup_date, meta, package_dimensions } = initialData;

    return {
      ...initialData,
      items: initialData?.items ?? [],
      courier: undefined,
      pickup_date: dayjs().startOf('day').isAfter(dayjs(pickup_date)) ? null : pickup_date && new Date(pickup_date),
      dropoff_address: receiver_address ? extractAddressForm(receiver_address) : getEmptyDefaultAddress(false),
      pickup_address: sender_address
        ? extractAddressForm(sender_address)
        : {
            name: user?.name,
            phone: phoneObjectFromString(user?.phone),
            id: null,
            email: user?.email,
            address: null,
          },
      package_category: meta?.shipbubble_category_id,
      package_dimensions: package_dimensions ?? { height: null, width: null, length: null },
      provider: initialData?.provider ?? (providers.length > 1 ? null : DELIVERY_PROVIDERS.SHIPBUBBLE),
    };
  }

  return {
    courier: null,
    pickup_date: null,
    dropoff_address: getEmptyDefaultAddress(false),
    pickup_address: {
      name: user?.name,
      phone: phoneObjectFromString(user?.phone),
      id: null,
      email: user?.email,
      address: null,
    },
    package_category: null,
    package_dimensions: { height: null, width: null, length: null },
    delivery_notes: '',
    items: [],
    order: null,
    provider: initialData?.provider ?? (providers.length > 1 ? null : DELIVERY_PROVIDERS.SHIPBUBBLE),
  };
};

const validationSchema = (stepIndex: number) => {
  return Yup.object().shape({
    items: ItemsValidationShema,
    pickup_address: Yup.object().when('stepIndex', ([stepIndex], schema) => {
      if (stepIndex > 1 || stepIndex < 1) return AddressValidationShema.required('Pickup Address in required');
      return schema;
    }),
    dropoff_address: Yup.object().when('stepIndex', ([stepIndex], schema) => {
      if (stepIndex > 2 || stepIndex < 1) return AddressValidationShema.required('Pickup Address in required');
      return schema;
    }),
    // pickup_address:
    //   stepIndex > 1 || stepIndex < 1 ? AddressValidationShema.required('Pickup Address in required') : undefined,
    // dropoff_address:
    //   stepIndex > 2 || stepIndex < 1 ? AddressValidationShema.required('Drop Address in required') : undefined,
    ...{ ...(stepIndex > 3 || stepIndex < 1 ? ExtraInfoValidationSchema : undefined) },
  });
};

const headerData = (step: number) => ({
  ...(step < 6 && {
    title: 'Create Delivery',
    headerBg: 'bg-accentOrange-pastel',
  }),
  ...(step === 5 && {
    title: 'Shipping Option',
  }),
  ...(step === 6 && {
    title: 'Delivery Summary',
  }),
  ...(step === 7 && {
    title: 'Make Payment',
    headerBg: 'bg-accentOrange-pastel2',
  }),
});

export default InitiateDelivery;
