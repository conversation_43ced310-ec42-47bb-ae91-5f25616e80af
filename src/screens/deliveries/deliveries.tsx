import { wp } from '@/assets/utils/js';
import DeliveriesList from '@/components/deliveries/deliveries-list';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import FAB from '@/components/ui/buttons/fab';
import { ArrowUpRight } from '@/components/ui/icons';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import TopTabs from '@/components/ui/others/top-tabs';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { DELIVERY_STATUSES, GET_DELIVERIES_STATISTICS } from 'catlog-shared';
import { useState } from 'react';
import DeliveriesAnalyticsModal from 'src/components/deliveries/deliveries-analytics-modal';
import DeliveriesPlaceholder from 'src/components/deliveries/deliveries-placeholder';
import SearchContainer, { SEARCH_BG_VARIANT } from 'src/components/ui/others/search-container';
import useModals from 'src/hooks/use-modals';
import useScrollHandler from 'src/hooks/use-scroll-handler';

enum DeliveriesType {
  DRAFT_DELIVERIES = 'Draft',
  PENDING_DELIVERIES = 'Pending',
  CONFIRMED_ORDERS = 'Confirmed',
  PICKED_UP_DELIVERIES = 'Picked-Up',
  IN_TRANSIT_DELIVERIES = 'In-Transit',
  COMPLETED_DELIVERIES = 'Completed',
  CANCELLED_DELIVERIES = 'Cancelled',
}

interface DeliveryStat {
  cancelled_shipments: number;
  confirmed_shipments: number;
  draft_shipments: number;
  ongoing_shipments: number;
  total_shipments: number;
}

const Deliveries = () => {
  const navigation = useNavigation();
  const [analyticsHeight, setAnalyticsHeight] = useState(0);
  const [tabIndex, setTabIndex] = useState(0);

  const { modals, toggleModal } = useModals(['deliveriesModal']);

  const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100, analyticsHeight);

  const getDeliveriesStatRequest = useApi<void, ResponseWithoutPagination<DeliveryStat>>({
    apiFunction: GET_DELIVERIES_STATISTICS,
    method: 'GET',
    key: 'get-order',
  });

  const stats = getDeliveriesStatRequest?.response?.data ?? ({} as DeliveryStat);

  if (false) {
    return (
      <DashboardLayout
        headerProps={{
          headerBg: 'bg-accentOrange-pastel',
          pageTitle: 'Deliveries',
          variant: HeaderVariants.SUB_LEVEL,
        }}>
        <DeliveriesPlaceholder />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'Deliveries',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchContainer
        {...{ headerStyle, inputStyle }}
        color={SEARCH_BG_VARIANT.ORANGE}
        onPressSearch={() => navigation.navigate('SearchOrders')}
        placeholder="Search Deliveries"
      />
      {/* <Animated.View
        style={analyticsHeaderStyle}
        onLayout={e => setAnalyticsHeight(e.nativeEvent.layout.height)}
        className={'overflow-visible'}>
        <View className="bg-accentOrange-pastel px-20 py-25">
          <View>
            <Input
              containerClasses="bg-white"
              editable={false}
              placeholder={'Search Deliveries'}
              hasBorder={false}
              rightAccessory={<Search size={wp(18)} primaryColor={colors?.black.muted} />}
            />
          </View>
        </View>
        <Row className="my-15 mx-20 ">
          <Row>
            <BaseText fontSize={15} weight="bold" type="heading">
              Performance Overview
            </BaseText>
          </Row>
        </Row>
        <View className='mx-20 '>
        <DeliveryAnalyticsCards statistics={stats} isLoading={getDeliveriesStatRequest?.isLoading} />
        </View>
      </Animated.View> */}
      <Pressable
        // onPress={() => navigation.navigate('DeliveriesAnalytics')}
        onPress={() => toggleModal('deliveriesModal')}
        className="px-20 py-15"
        style={{ backgroundColor: '#fff0eb60' }}>
        <Row>
          <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
            See Analytics
          </BaseText>
          <CircledIcon className="bg-white">
            <ArrowUpRight size={wp(13)} strokeWidth={2} currentColor={colors.primary.main} />
          </CircledIcon>
        </Row>
      </Pressable>
      <TopTabs
        setIndex={setTabIndex}
        currentIndex={tabIndex}
        tabItems={[
          {
            key: DeliveriesType.DRAFT_DELIVERIES,
            title: DeliveriesType.DRAFT_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.DRAFT} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.PENDING_DELIVERIES,
            title: DeliveriesType.PENDING_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.PENDING} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.CONFIRMED_ORDERS,
            title: DeliveriesType.CONFIRMED_ORDERS,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.CONFIRMED} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.PICKED_UP_DELIVERIES,
            title: DeliveriesType.PICKED_UP_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.PICKED_UP} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.IN_TRANSIT_DELIVERIES,
            title: DeliveriesType.IN_TRANSIT_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.IN_TRANSIT} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.COMPLETED_DELIVERIES,
            title: DeliveriesType.COMPLETED_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.COMPLETED} scrollHandler={scrollHandler} />,
            func: () => {},
          },
          {
            key: DeliveriesType.CANCELLED_DELIVERIES,
            title: DeliveriesType.CANCELLED_DELIVERIES,
            component: <DeliveriesList deliveryStatus={DELIVERY_STATUSES.CANCELLED} scrollHandler={scrollHandler} />,
            func: () => {},
          },
        ]}
      />
      <FAB onPress={() => navigation.navigate('InitiateDelivery')} />
      <DeliveriesAnalyticsModal
        isVisible={modals.deliveriesModal}
        stats={stats}
        isLoading={getDeliveriesStatRequest.isLoading}
        closeModal={() => toggleModal('deliveriesModal', false)}
      />
    </DashboardLayout>
  );
};

export default Deliveries;
