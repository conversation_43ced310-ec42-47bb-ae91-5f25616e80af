import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import SearchHeader from '@/components/search/search-header';
import useModals from 'src/hooks/use-modals';
import { delay, ensureUniqueItems, getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import {
  AffiliateInterface,
  GET_AFFILIATES,
  GetItemsParams,
  PaginateSearchParams,
} from 'catlog-shared';
import AffiliateListSection from 'src/components/affiliates/affiliate-list-section';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import usePagination from 'src/hooks/use-pagination';
import { View } from 'react-native';

interface AffiliateResponseWithPagination extends ResponseWithPagination<AffiliateInterface[]> {}
interface AffiliateResponse {
  data: AffiliateResponseWithPagination;
}

const PER_PAGE = 10;

const SearchAffiliates = () => {
  const [filters, setFilters] = useState<PaginateSearchParams['filter']>({});
  const validFilters = removeEmptyAndUndefined(filters);
  const [affiliates, setAffiliates] = useState<AffiliateInterface[]>([]);

  const { currentPage, setPage, goNext } = usePagination();

  const getAffiliatesRequest = useApi<PaginateSearchParams, AffiliateResponse>(
    {
      key: 'get-affiliates',
      apiFunction: GET_AFFILIATES,
      method: 'GET',
      onSuccess: response => {
        setAffiliates(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      filter: validFilters,
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const form = useFormik<PaginateSearchParams['filter']>({
    initialValues: {
      search: '',
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  useEffect(() => {
    const fn = async () => {
      await delay(1000);
      setAffiliates([]);
      setPage(1);
      getAffiliatesRequest.refetch();
    };
    fn();
  }, [validFilters]);

  const handlePullToRefresh = () => {
    setAffiliates([]);

    if (currentPage === 1) {
      getAffiliatesRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        // sort: 'desc',
      });
      return;
    }

    setPage(1);
  };

  const handleClearSearch = () => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    form.setFieldValue('search', '');
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Search Affiliates',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
          placeholder: 'Search Affiliates',
        }}
        showFilter={false}
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressFilterButton={() => {}}
        onPressClear={handleClearSearch}
        showClear={form.values?.search?.length > 0}
      />
      <View className="pt-10 flex-1">
        <AffiliateListSection
          isSearch={true}
          {...{ affiliates, setAffiliates, currentPage, goNext, setPage }}
          pullToRefreshFunc={handlePullToRefresh}
          isReLoading={getAffiliatesRequest.isReLoading}
          isLoading={getAffiliatesRequest.isLoading}
          total_pages={getAffiliatesRequest?.response?.data?.total_pages}
          error={getAffiliatesRequest.error}
        />
      </View>
    </DashboardLayout>
  );
};

export default SearchAffiliates;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};
