import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, View, ViewStyle } from 'react-native';
import { Add, Co<PERSON>, FlashCircle } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Row from '@/components/ui/row';
import colors from '@/theme/colors';
import useStatusbar from '@/hooks/use-statusbar';
import { useNavigation } from '@react-navigation/native';
import { ArrowRight } from '@/components/ui/icons';
import { BaseText, CircledIcon } from '@/components/ui';
import { cx, hp, wp } from '@/assets/utils/js';
import { useEffect, useMemo, useState } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import VideoSection from '@/components/get-started/video-section';
import FaqSection from '@/components/get-started/faq-section';
import GetStartedTasks from '@/components/get-started/tasks';
import useAuthContext from '@/contexts/auth/auth-context';
import { StoreInterface, User, Wallet } from 'catlog-shared';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { ONBOARDING_CREDITS, SETUP_TYPE } from 'src/@types/utils';
import UploadLogoModal from 'src/components/get-started/upload-logo-modal';
import useModals from 'src/hooks/use-modals';
import RewardInfoModal from './reward-info-modal';

interface StepStatusBarProps {
  progressStyle: ViewStyle;
  completedCount: number;
  stepCount: number;
}

const StepStatusBar = ({ progressStyle, completedCount, stepCount }: StepStatusBarProps) => (
  <View className={cx(' bg-white', { 'border-b border-b-grey-border': true })}>
    <Row className="py-15 bg-white px-20">
      <CircledIcon iconBg="bg-accentGreen-pastel">
        <FlashCircle variant={'Bulk'} size={wp(20)} color={colors?.accentGreen.main} />
      </CircledIcon>
      <BaseText fontSize={14} type="heading" classes="flex-1 mx-10 text-black-secondary">
        Finish setting up
      </BaseText>
      <BaseText fontSize={12} classes="text-black-secondary">
        {completedCount}/{stepCount} complete
      </BaseText>
    </Row>
    <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-r-full w-2/5" />
  </View>
);

const GetStarted = () => {
  const { setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);
  const navigation = useNavigation();
  const progressWidth = useSharedValue(0);

  const { modals, toggleModal } = useModals(['rewardInfo']);

  const { store, user } = useAuthContext();

  const { wallets } = useWalletContext();

  const progressStyle = useAnimatedStyle(() => {
    return { width: `${progressWidth.value}%` };
  });

  const accountSetupSteps = useMemo(() => getAccountSetupSteps(store!, user!, wallets), [store, user]);
  const allTasks = accountSetupSteps.flatMap(step => step.tasks);
  const completedTaskCount = allTasks.filter(task => task.isCompleted).length;
  const length = getTotalTasksCount(accountSetupSteps);

  useEffect(() => {
    const percentage = Math.ceil((completedTaskCount * 100) / length);
    progressWidth.value = withDelay(300, withSpring(percentage));
  }, [completedTaskCount]);

  return (
    <DashboardLayout
      headerProps={{ headerBg: 'bg-primary-pastel', pageTitle: 'Get Started', variant: HeaderVariants.SUB_LEVEL }}>
      <ScrollView className="flex-1" stickyHeaderIndices={[0]} scrollIndicatorInsets={{ right: 1 }}>
        <StepStatusBar completedCount={completedTaskCount} stepCount={length} progressStyle={progressStyle} />
        <GetStartedTasks
          tasks={accountSetupSteps}
          storeId={store?.id!}
          openRewardModal={() => toggleModal('rewardInfo', true)}
          onboardingSteps={store?.onboarding_steps!}
          storeCountry={store?.country!}
        />
        <View className="pb-45">
          {/* <VideoSection /> */}
          <FaqSection hideFaqList />
        </View>
      </ScrollView>
      <RewardInfoModal isVisible={modals.rewardInfo} closeModal={() => toggleModal('rewardInfo', false)} />
    </DashboardLayout>
  );
};

export interface SetupTask {
  title: string;
  tasks: {
    rightIcon: React.ReactNode;
    title: string;
    isCompleted?: boolean;
    hasReward?: boolean;
    reward?: number;
    onPress?: VoidFunction;
    type: SETUP_TYPE;
  }[];
}

export const getAccountSetupSteps = (store: StoreInterface, user: User, wallets: Wallet[]) => {
  return [
    {
      title: 'Getting Started',
      tasks: [
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Book Onboarding Call',
          type: SETUP_TYPE.BOOK_ONBOARDING_CALL,
          isCompleted: user?.onboarding_steps?.onboarding_call_booked,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Join our community on whatsapp',
          type: SETUP_TYPE.JOIN_COMMUNITY,
          isCompleted: user?.onboarding_steps?.community_joined,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Follow our Socials',
          type: SETUP_TYPE.FOLLOW_OUR_SOCIALS,
          isCompleted: user?.onboarding_steps?.has_followed_socials,
        },
      ].sort((a, b) => {
        if (a.isCompleted == b.isCompleted) return 1;
        return a.isCompleted ? 1 : -1;
      }),
    },
    {
      title: 'Start Taking Orders',
      tasks: [
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: `Verify ${!user?.email_verified ? 'Email & Phone Number' : 'Phone Number'} ${user?.email_verified && user?.phone_verified ? 'Email & Phone Number' : ''}`,
          type: SETUP_TYPE.GET_VERIFIED,
          isCompleted: user?.email_verified && user?.phone_verified,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <Add size={wp(16)} color={colors?.primary.main} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Upload 10 Products',
          type: SETUP_TYPE.UPLOAD_10_PRODUCT,
          isCompleted: store?.item_count >= 10,
          hasReward: true,
          reward: ONBOARDING_CREDITS[SETUP_TYPE.UPLOAD_10_PRODUCT][store?.country?.currency ?? 'NGN'],
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <Copy size={wp(16)} color={colors?.primary.main} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Store Link to your Socials',
          type: SETUP_TYPE.ADD_LINK_TO_SOCIAL,
          isCompleted: store?.onboarding_steps?.link_added,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Take Order With Payment',
          type: SETUP_TYPE.TAKE_ORDER_WITH_PAYMENT,
          isCompleted: store?.onboarding_steps?.has_taken_first_order_with_payment,
          hasReward: true,
          reward: ONBOARDING_CREDITS[SETUP_TYPE.TAKE_ORDER_WITH_PAYMENT][store?.country?.currency ?? 'NGN'],
        },
      ].sort((a, b) => {
        if (a.isCompleted == b.isCompleted) return 1;
        return a.isCompleted ? 1 : -1;
      }),
    },
    {
      title: 'Setting Up Payments',
      tasks: [
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Verify Identity',
          type: SETUP_TYPE.VERIFY_IDENTITY,
          isCompleted: store?.payments_enabled && store?.kyc_approved,
          hasReward: true,
          reward: ONBOARDING_CREDITS[SETUP_TYPE.VERIFY_IDENTITY][store?.country?.currency ?? 'NGN'],
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Security Pin',
          type: SETUP_TYPE.ADD_SECURITY_PIN,
          isCompleted: store?.onboarding_steps?.security_pin_added,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add withdrawal account',
          type: SETUP_TYPE.ADD_WITHDRAWAL_ACCOUNT,
          isCompleted: Boolean(wallets?.[0]?.accounts?.length > 0),
        },
      ].sort((a, b) => {
        if (a.isCompleted == b.isCompleted) return 1;
        return a.isCompleted ? 1 : -1;
      }),
    },
    {
      title: 'Customizing your store',
      tasks: [
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Logo & Cover Image',
          type: SETUP_TYPE.ADD_LOGO_AND_COVER_IMAGE,
          isCompleted: !!store?.logo && !!store?.hero_image,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Customize Color',
          type: SETUP_TYPE.CUSTOMIZE_COLOR,
          isCompleted: !!store?.configuration?.color,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Delivery Area',
          type: SETUP_TYPE.ADD_DELIVERY_AREA,
          isCompleted: store?.delivery_areas?.length > 0,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Delivery Timeline',
          type: SETUP_TYPE.ADD_DELIVERY_TIMELINE,
          isCompleted: !!store?.extra_info?.delivery_timeline,
        },
        {
          rightIcon: (
            <CircledIcon iconBg="bg-grey-bgOne">
              <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
            </CircledIcon>
          ),
          title: 'Add Store Location',
          type: SETUP_TYPE.ADD_STORE_LOCATION,
          isCompleted: Boolean(store?.address),
        },
      ].sort((a, b) => {
        if (a.isCompleted == b.isCompleted) return 1;
        return a.isCompleted ? 1 : -1;
      }),
    },
  ];
};

const getTotalTasksCount = steps => {
  let totalTasks = 0;

  steps.forEach(step => {
    totalTasks += step.tasks.length;
  });

  return totalTasks;
};

// .sort((a, b) => {
//   if (a.isCompleted == b.isCompleted) return 1;
//   return a.isCompleted ? 1 : -1;
// });

export default GetStarted;
