import { ScrollView, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Container from '@/components/ui/container';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Row, SelectionPill } from '@/components/ui';
import { useEffect, useRef, useState } from 'react';
import Toast from 'react-native-toast-message';
import SelectCategorizationForm, {
  SelectCategorizationFormMethod,
} from '@/components/get-started/select-categorization-form';

const SelectCategorization = () => {
  const [loading, setLoading] = useState(false);

  const formRef = useRef<SelectCategorizationFormMethod>(null);
  return (
    <DashboardLayout
      isLoading={loading}
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: 'Select Categorization',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView>
        <ScreenInfoHeader
          iconElement={<CircledIcon />}
          colorPalette={ColorPaletteType.PRIMARY}
          pageTitleTop={'Tell us more,'}
          pageTitleBottom={'About your business'}
        />
        <Container className={'mt-15'}>
          <SelectCategorizationForm ref={formRef} loadingCallBack={status => setLoading(status)} />
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: formRef?.current?.submitFormLoading ? 'Loading...' : 'Proceed',
            onPress: () => formRef?.current?.submitForm(),
            disabled: formRef?.current?.submitFormLoading,
          },
        ]}
      />
    </DashboardLayout>
  );
};

export default SelectCategorization;
