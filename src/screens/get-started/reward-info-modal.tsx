import { ButtonVariant } from 'src/components/ui/buttons/button';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { Dimensions, View } from 'react-native';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { Gift, TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { CountryInterface, GET_PLANS, Plan, PLAN_TYPE, PlanOption } from 'catlog-shared';
import { useMemo } from 'react';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import * as Animatable from 'react-native-animatable';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const width = Dimensions.get('window').width;

const RewardInfoModal = ({ closeModal, ...props }: Props) => {
  return (
    <BottomModal {...props} enableDynamicSizing closeModal={closeModal} buttons={[{ text: 'Finish setting up', onPress: closeModal }]}>
      <Container className="pb-50">
        <Animatable.View
          className={`bg-accentYellow-pastel2 self-center rounded-full p-10 mt-20`}
          animation={'zoomIn'}
          duration={300}>
          <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
            <CircledIcon className={`bg-accentYellow-main`}>
              <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                <Gift variant="Bold" color={colors.white} size={wp(40)} />
              </Animatable.View>
            </CircledIcon>
          </Animatable.View>
        </Animatable.View>
        <BaseText fontSize={20} type="heading" classes="text-center mt-10">
          Earn Rewards
        </BaseText>
        <View>
          <BaseText fontSize={14} classes="text-black-secondary mt-10 text-center" lineHeight={22}>
            Complete tasks marked with a gift icon within{'\n'}specified time and earn cash rewards in{'\n'}Catlog
            credits!
          </BaseText>
        </View>
      </Container>
    </BottomModal>
  );
};

export default RewardInfoModal;
