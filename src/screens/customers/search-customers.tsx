import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import SearchHeader from '@/components/search/search-header';
import useModals from 'src/hooks/use-modals';
import { delay, ensureUniqueItems, getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import {
  CustomerInterface,
  DELIVERY_METHODS,
  GET_CUSTOMERS,
  GetItemsParams,
  GetOrdersParams,
  ORDER_CHANNELS,
  ORDER_PAID_TAG,
  PaginateSearchParams,
} from 'catlog-shared';
import CustomersListSection from 'src/components/customer/customers-list-section';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import usePagination from 'src/hooks/use-pagination';
import { View } from 'react-native';

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const PER_PAGE = 10;

const SearchCustomers = () => {
  const [filters, setFilters] = useState<PaginateSearchParams['filter']>({});
  const validFilters = removeEmptyAndUndefined(filters);
  const [customers, setCustomers] = useState<CustomerInterface[]>([]);

  const { currentPage, setPage, goNext } = usePagination();

  const getCustomersRequest = useApi<PaginateSearchParams, CustomerResponse>(
    {
      key: 'get-customers',
      apiFunction: GET_CUSTOMERS,
      method: 'GET',
      onSuccess: response => {
        setCustomers(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      filter: validFilters,
      page: currentPage,
      per_page: PER_PAGE,
    },
  );

  const form = useFormik<PaginateSearchParams['filter']>({
    initialValues: {
      search: '',
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  useEffect(() => {
    const fn = async () => {
      await delay(1000);
      setCustomers([]);
      setPage(1);
      getCustomersRequest.refetch();
    };
    fn();
  }, [validFilters]);

  const handlePullToRefresh = () => {
    setCustomers([]);

    if (currentPage === 1) {
      getCustomersRequest.makeRequest({
        filter: { search: '' },
        page: currentPage,
        per_page: PER_PAGE,
        // sort: 'desc',
      });
      return;
    }

    setPage(1);
  };

  const handleClearSearch = () => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    form.setFieldValue('search', '');
  };

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'Search Customers',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <SearchHeader
        inputProps={{
          ...getFieldvalues('search', form),
          onSubmitEditing: () => form.submitForm(),
          placeholder: 'Search Customers',
        }}
        showFilter={false}
        numberOfActiveFilters={Object.keys(filtersWithoutSearch(validFilters)).length}
        onPressFilterButton={() => {}}
        onPressClear={handleClearSearch}
        showClear={form.values?.search?.length > 0}
      />
      <View className="pt-10 flex-1">
        <CustomersListSection
          isSearch={true}
          {...{ customers, setCustomers, currentPage, goNext, setPage }}
          pullToRefreshFunc={handlePullToRefresh}
          isReLoading={getCustomersRequest.isReLoading}
          isLoading={getCustomersRequest.isLoading}
          total_pages={getCustomersRequest?.response?.data?.total_pages}
          error={getCustomersRequest.error}
        />
      </View>
    </DashboardLayout>
  );
};

export default SearchCustomers;

const filtersWithoutSearch = (filters: GetItemsParams['filter']) => {
  const { search, ...rest } = filters;
  return rest;
};
