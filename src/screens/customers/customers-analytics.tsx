import { ScrollView, Text, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Bag, Call, Check, Profile2User, ShoppingBag } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import Input from '@/components/ui/inputs/input';
import { ArrowUpRight, Search, Whatsapp } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { AreaGraph, BaseText, CircledIcon, Row, SelectionPill } from '@/components/ui';
import { wp } from '@/assets/utils/js';
import Animated, { useSharedValue } from 'react-native-reanimated';
import { useState } from 'react';
import { useVector } from 'react-native-redash';
import { useNavigation } from '@react-navigation/native';
import { MoreOptionElementProps } from '@/components/ui/more-options';
import { mockCustomer, mockProducts } from '@/constant/mock-data';
import CustomerCard from '@/components/customer/customer-card';
import SectionContainer from '@/components/ui/section-container';
import { OrderInterface } from 'catlog-shared';


enum OrdersType {
  PENDING_ORDERS = 'Pending Orders',
  PROCESSING_ORDERS = 'Processing Orders',
  FULFILLED_ORDERS = 'Fulfilled Orders',
  CANCELLED_ORDERS = 'Cancelled Orders',
}

const CustomersAnalytics = () => {
  const [selectedPill, setSelectedPill] = useState<OrdersType>(OrdersType.PENDING_ORDERS);
  const [isEdit, setIsEdit] = useState(false);

  const isDiscount = selectedPill === OrdersType.FULFILLED_ORDERS;

  const moreOptions: MoreOptionElementProps[] = [
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Check size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            {isDiscount ? 'Edit Discount' : 'Edit Coupons'}
          </BaseText>
        </Row>
      ),
      title: isDiscount ? 'Edit Discount' : 'Edit Coupons',
      onPress: () => {},
    },
  ];

  const data = [
    { value: 60, label: 'Jan' },
    { value: 80, label: 'Feb' },
    { value: 90, label: 'Mar' },
    { value: 70, label: 'Apr' },
    { value: 30, label: 'May' },
    { value: 40, label: 'Jun' },
    { value: 75, label: 'Jul' },
    { value: 55, label: 'Aug' },
    { value: 35, label: 'Sep' },
    { value: 75, label: 'Oct' },
    { value: 65, label: 'Nov' },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentYellow-pastel',
        pageTitle: 'Customers Analytics',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView className={'flex-1'}>
        <View className="px-20 pb-40">
          <Row className=" py-15 px-10 border rounded-[10px] border-grey-border mt-15">
            <CircledIcon iconBg="bg-accentRed-main" className="p-10">
              <ShoppingBag size={wp(18)} variant={'Bold'} color={colors.white} />
            </CircledIcon>
            <View className={'flex-1 mx-10 item-stretch'}>
              <BaseText classes="text-black-muted">Total Customers</BaseText>
              <BaseText fontSize={15} weight={'bold'} type={'heading'}>
                209
              </BaseText>
            </View>
          </Row>
          {/* <View className="mt-20">
            <AreaGraph title={'New Customers'} graphData={data} />
          </View> */}
          <Row className={'justify-start pt-15 bg-white mt-15'}>
            <SelectionPill title={'Highest Orders'} selected />
            <SelectionPill title={'Top Volume'} selected={false} />
          </Row>
          <SectionContainer>
            {mockCustomer.map((item, index) => (
              <CustomerCard
                key={item?.name}
                title={item?.name}
                bottomComponent={
                  <View className="mt-5">
                    <BaseText fontSize={10} weight={'semiBold'} classes="text-accentYellow-main">
                      100 ORDERS
                    </BaseText>
                  </View>
                }
                showBorder={index === mockCustomer?.length - 1 ? false : true}
                onPress={() => {}}
                moreOptions={[]}
                index={index}
              />
            ))}
            {/* <FlatList
              data={mockCustomer}
              scrollEventThrottle={16}
              ListEmptyComponent={() => (
                <EmptyState
                  icon={
                    <CircledIcon
                      className="mb-20 p-15 bg-white"
                      style={{
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 10,
                        },
                        shadowOpacity: 0.05,
                        shadowRadius: 6.84,

                        elevation: 5,
                      }}>
                      <Bag variant={'Bulk'} size={wp(40)} color={colors.grey.muted} />
                    </CircledIcon>
                  }
                  btnText={'Record an Order'}
                  text={'Orders customers place \n place will appear here'}
                />
              )}
              className="flex-1 px-20"
              contentContainerStyle={{ flexGrow: 1 }}
              renderItem={({ item }) => (
                <CustomerCard
                  title={item?.name}
                  description={item?.contact}
                  onPress={() => setCustomerInfoVisible(true)}
                  moreOptions={[]}
                />
              )}
            /> */}
          </SectionContainer>
        </View>
      </ScrollView>
    </DashboardLayout>
  );
};

export default CustomersAnalytics;
