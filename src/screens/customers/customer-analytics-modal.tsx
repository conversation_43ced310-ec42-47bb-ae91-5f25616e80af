import * as Yup from 'yup';
import { View } from 'react-native';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { DeliveryStat } from 'src/screens/deliveries/deliveries-analytics';
import { InvoiceStatsResponse } from 'src/screens/invoices/dashboard';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import CustomerAnalyticsCards from 'src/components/customer/customer-analytics-cards';
import { CustomerStatisticsInterface, GET_CUSTOMERS_STATISTICS } from 'catlog-shared';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { useMemo, useState } from 'react';
import { TimeRange } from 'src/@types/utils';
import useModals from 'src/hooks/use-modals';
import { getFilter } from 'src/components/ui/graph/area-graph';

interface CustomerAnalyticsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  stats?: CustomerStatisticsInterface;
  isLoading?: boolean;
}

const CustomerAnalyticsModal = ({ closeModal, stats, isLoading, ...props }: CustomerAnalyticsModalProps) => {
  const [range, setRange] = useState(TimeRange.THIS_YEAR);

  const { modals, toggleModal } = useModals(['dateRange']);

  const analyticsRange = useMemo(() => {
    return getFilter(range);
  }, [range]);

  const getPaymentAnalytics = useApi<
    {
      from: string;
      to: string;
    },
    ResponseWithoutPagination<CustomerStatisticsInterface>
  >(
    {
      key: 'get-order-statistics',
      apiFunction: GET_CUSTOMERS_STATISTICS,
      method: 'GET',
    },
    { ...analyticsRange },
  );

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      showButton={false}
      buttons={[
        {
          text: 'Close',
          onPress: () => closeModal,
        },
      ]}>
      <View className="mx-20 pb-10">
        <CustomerAnalyticsCards
          range={range}
          setRange={setRange}
          analytics={getPaymentAnalytics?.response?.data ?? []}
          isLoading={getPaymentAnalytics.isLoading}
        />
      </View>
    </BottomModal>
  );
};

export const addAddressValidationSchema = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default CustomerAnalyticsModal;
