import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import { BaseText, CircledIcon } from '@/components/ui';
import TopTabs from '@/components/ui/others/top-tabs';
import { useMemo, useState } from 'react';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import Customization from '@/components/store-settings/store-configuration/customization';
import OpeningClosingHours from '@/components/store-settings/store-configuration/opening-closing-hours';
import BasicDetails from '@/components/user-profile/basic-details';
import UpdatePassword from '@/components/user-profile/update-password';
import { Profile } from 'iconsax-react-native/src';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import CustomImage from 'src/components/ui/others/custom-image';
import Preference from 'src/components/user-profile/preference';
import useTabs from 'src/hooks/use-tabs';

enum SelectionPillType {
  BASIC_DETAILS = 'Basic Details',
  UPDATE_PASSWORD = 'Update Password',
  PREFERENCES = 'Preferences',
}

function UserProfile() {
  const { tabs, active, switchTab } = useTabs({
    tabs: Object.values(SelectionPillType),
    initialActiveTab: 0,
  });

  const tabItems = useMemo(() => {
    return [
      {
        key: SelectionPillType.BASIC_DETAILS,
        title: SelectionPillType.BASIC_DETAILS,
        component: <BasicDetails />,
      },
      {
        key: SelectionPillType.UPDATE_PASSWORD,
        title: SelectionPillType.UPDATE_PASSWORD,
        component: <UpdatePassword />,
      },
      {
        key: SelectionPillType.PREFERENCES,
        title: SelectionPillType.PREFERENCES,
        component: <Preference />,
      },
    ];
  }, []);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: 'My Profile',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.ORANGE}
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/user-profile-2.png') }}
            className="w-[80px] h-[80px]"
          />
        }
        customElements={
          <BaseText fontSize={20} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
            My Profile
          </BaseText>
        }
      />
      <TopTabs setIndex={switchTab} currentIndex={active} tabItems={tabItems} />
    </DashboardLayout>
  );
}

export default UserProfile;
