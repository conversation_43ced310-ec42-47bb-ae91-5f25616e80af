import { useEffect, useMemo, useState } from 'react';
import { Linking, RefreshControl, ScrollView, View } from 'react-native';
import Container from '@/components/ui/container';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import useAuthContext from '@/contexts/auth/auth-context';
import useStatusbar from '@/hooks/use-statusbar';
import useModals from 'src/hooks/use-modals';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { Danger } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Button from 'src/components/ui/buttons/button';
import { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { CONTACT_SUPPORT_WA_LINK } from 'src/assets/utils/js/constants';
import { useNavigation } from '@react-navigation/native';
import AuthQuestion from 'src/components/auth/auth-question';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const AccountDeactivated = () => {
  const { store } = useAuthContext();
  const { setStatusBar } = useStatusbar();
  const { modals, toggleModal } = useModals(['inviteModal']);
  setStatusBar('dark', 'transparent', true);

  const navigation = useNavigation();

  const handleAccountDeactivatedPullToRefresh = async () => {};

  const insets = useSafeAreaInsets();

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: store?.name ?? '_',
        variant: HeaderVariants.ROOT_LEVEL,
        showNotifications: false,
      }}>
      <View className="flex-1 px-20" style={{ paddingBottom: insets.bottom + 12 }}>
        <View className={`flex-1 justify-center`}>
          <CircledIcon className="bg-accentYellow-pastel2 p-16 self-center mb-10">
            <Danger variant={'Bold'} color={colors.accentYellow.main} size={wp(32)} />
          </CircledIcon>
          <BaseText type={'heading'} fontSize={18} classes="text-center mb-5">
            Account Access Restricted
          </BaseText>
          <View>
            <BaseText fontSize={14} lineHeight={20} classes="text-black-placeholder text-center mt-5">
              Your subscription has expired, and your account is now restricted. To continue, you can either reactivate
              your account or go to your wallet to access funds.
            </BaseText>
          </View>
          <View className="mt-20">
            <Button
              text={'Reactivate Account'}
              onPress={() => navigation.navigate('ChangePlan')}
              variant={ButtonVariant.PRIMARY}
            />
            <Button
              text={'Go to your Wallet'}
              onPress={() => navigation.navigate('DeactivatedPayments')}
              btnStyle="mt-15"
              variant={ButtonVariant.LIGHT}
            />
          </View>
        </View>
        <AuthQuestion
          question={'Not sure what to do?'}
          actionText={'Contact Support'}
          onPress={() => Linking.openURL(CONTACT_SUPPORT_WA_LINK)}
        />
      </View>
    </DashboardLayout>
  );
};

export default AccountDeactivated;
