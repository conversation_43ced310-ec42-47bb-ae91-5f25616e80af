import {
  GET_STORE_ANALYTICS,
  GetStoreAnalyticsParams,
  GET_STORE_CREDITS,
  GET_ORDERS,
  GET_STORE_TOP_PRODUCTS,
  GetStoreTopProductsParams,
  GetOrdersParams,
  GET_PENDING_ACTIONS,
  StorePendingActions,
} from 'catlog-shared'; //todo: @silas
import { Copy, Gift, Hashtag, HashtagSquare, Instagram, Profile2User, Shop } from 'iconsax-react-native/src';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Alert, Linking, RefreshControl, ScrollView, View } from 'react-native';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import ViewShot from 'react-native-view-shot';
import { OrdersResponse } from '../orders/list';

import { copyToClipboard, delay, getCommunityLink, openLinkInBrowser, wp } from '@/assets/utils/js';
import HomeAnalyicsCards from '@/components/home/<USER>';
import HomeNotificationCard from '@/components/home/<USER>';
import DashboardOrdersAndTopProducts, { TopProductResponse } from '@/components/home/<USER>';
import { AreaGraph, CircledIcon, Row } from '@/components/ui';
import ScrollableActionPills from '@/components/ui/buttons/scrollable-action-pills';
import Container from '@/components/ui/container';
import { getFilter } from '@/components/ui/graph/area-graph';
import { ArrowUpRight, MoreHorizontal } from '@/components/ui/icons';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import useAuthContext from '@/contexts/auth/auth-context';
import useStatusbar from '@/hooks/use-statusbar';
import colors from '@/theme/colors';
import useNotificationSetup from 'src/hooks/use-notification-setup';
import useRouteParams from 'src/hooks/use-route-params';
import AcceptInviteModal from 'src/components/auth/accept-invite-modal';
import useModals from 'src/hooks/use-modals';
import { TimeRange } from 'src/@types/utils';
import PendingActionSection from 'src/components/home/<USER>';
import CalendarModal from 'src/components/ui/modals/calendar-modal';
import useEventListener from 'src/hooks/use-event-emitter';
import useAsyncEffect from 'src/hooks/use-async-effect';
import { shareAsync } from 'expo-sharing';
import useAnalyticsRange from 'src/hooks/use-analytics-range';
import DownloadCardModal from 'src/components/home/<USER>';
import BaseText from 'src/components/ui/base/base-text';
import Pressable from 'src/components/ui/base/pressable';
import { appStorage } from 'src/assets/utils/js/zustand-mmkv-storage';
import UpdateAppModal from 'src/components/ui/others/update-app-modal';
import useFeatureFlagsStore from 'src/contexts/feature-flags/store';

// export enum TimeRange {
//   TODAY = 'Today',
//   THIS_WEEK = 'This Week',
//   LAST_WEEK = 'Last Week',
//   LAST_30_DAYS = 'Last 30 Days',
//   THIS_YEAR = 'This Year',
//   ALL_TIME = 'All Time',
//   CUSTOM = 'Custom',
// }

const Home = () => {
  const { analyticsRange, customRange, range, handleSelectRange, setCustomRange, showCalendar, toggleCalendar } =
    useAnalyticsRange();
  const { store, storeLink, storeId, user, refetchSession } = useAuthContext();
  const { setStatusBar } = useStatusbar();
  const { modals, toggleModal } = useModals(['inviteModal', 'calender', 'signin_counter']);
  const { isAppOutdated } = useFeatureFlagsStore();
  setStatusBar('dark', 'transparent', true);

  const param = useRouteParams<'Home'>();

  const hasInvite = Boolean(param?.id);

  useEffect(() => {
    const hasShownSigninCounter = appStorage.getBoolean('hasShownSigninCounter');
    if (
      user.meta?.is_first_mobile_app_signin == true &&
      user.meta?.mobile_app_signin_position !== undefined &&
      !hasShownSigninCounter
    ) {
      appStorage.set('hasShownSigninCounter', true);
      toggleModal('signin_counter');
    }
  }, [user]);

  useEffect(() => {
    (async () => {
      if (hasInvite) {
        await delay(600);
        toggleModal('inviteModal');
      }
    })();
  }, [param]);

  useAsyncEffect(async () => {
    console.log({isAppOutdated})
    if (isAppOutdated) {
      await delay(600);
      setVisible(true);
    }
  }, [isAppOutdated]);

  useNotificationSetup();

  const storeAnalyticsRequest = useApi<GetStoreAnalyticsParams>(
    { apiFunction: GET_STORE_ANALYTICS, method: 'GET', key: 'get-store-analytics' },
    { id: store?.id, filter: { ...analyticsRange }, is_mobile: true },
  );

  const pendingActionRequest = useApi<void, ResponseWithoutPagination<StorePendingActions>>({
    key: GET_PENDING_ACTIONS.name,
    apiFunction: GET_PENDING_ACTIONS,
    method: 'GET',
    autoRequest: false,
  });

  const catlogCreditRequest = useApi({ key: 'get-store-credit', apiFunction: GET_STORE_CREDITS, method: 'GET' });

  const getLatestOrdersReq = useApi<GetOrdersParams, OrdersResponse>(
    { apiFunction: GET_ORDERS, method: 'GET', key: `'get-latest-orders'${store?.id}` },
    { page: 1, per_page: 5, sort: 'DESC', filter: {} },
  );

  const getTopProductReq = useApi<GetStoreTopProductsParams, TopProductResponse>(
    { apiFunction: GET_STORE_TOP_PRODUCTS, method: 'GET', key: 'get-top-product' },
    { id: storeId! },
  );

  const statisticData = {
    total_orders: storeAnalyticsRequest?.response?.data?.total_orders ?? 0,
    total_payments: storeAnalyticsRequest?.response?.data?.total_payments ?? 0,
    total_visits: storeAnalyticsRequest?.response?.data?.total_visits ?? 0,
  };

  const trends = storeAnalyticsRequest?.response?.data?.trends ?? {
    total_orders: 0,
    total_payments: 0,
    total_visits: 0,
  };

  const graphRawData = storeAnalyticsRequest?.response?.data?.visits ?? [];

  const actionPills = [
    {
      LeftIcon: ({ ...props }) => <Shop {...props} />,
      title: 'Visit Store',
      showArrow: true,
      onPress: () => openLinkInBrowser(storeLink),
      addon: <ArrowUpRight currentColor={colors.black.secondary} strokeWidth={1.5} size={wp(18)} />,
    },
    {
      LeftIcon: ({ ...props }) => <Copy {...props} />,
      title: 'Copy Store Link',
      onPress: () => copyToClipboard(storeLink),
    },
    {
      LeftIcon: ({ ...props }) => <Profile2User {...props} />,
      title: 'Join Community',
      onPress: () => Linking.openURL(getCommunityLink(store?.country)),
    },
  ];

  useEventListener('PENDING_ACTIONS_UPDATED', () => {
    pendingActionRequest.refetch();
  });

  const handleHomePullToRefresh = async () => {
    storeAnalyticsRequest.refetch();
    catlogCreditRequest.refetch();
    getLatestOrdersReq.refetch();
    getTopProductReq.refetch();
    pendingActionRequest.refetch();
  };

  // Check if all critical requests failed
  const criticalError = storeAnalyticsRequest.error && getLatestOrdersReq.error && getTopProductReq.error;

  // Function to retry all requests
  const retryAllRequests = () => {
    storeAnalyticsRequest.refetch();
    catlogCreditRequest.refetch();
    getLatestOrdersReq.refetch();
    getTopProductReq.refetch();
  };
  const [visible, setVisible] = useState(false);

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-primary-pastel',
        pageTitle: store?.name ?? '_____',
        variant: HeaderVariants.ROOT_LEVEL,
      }}>
      <ScrollView
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            onRefresh={handleHomePullToRefresh}
            // refreshing={catlogCreditRequest.isReLoading || storeAnalyticsRequest.isReLoading}
            refreshing={false}
          />
        }>
        <HomeNotificationCard store={store} user={user} refetchSession={refetchSession} />

        <View className="flex-1 pt-20">
          <Pressable onPress={() => toggleModal('signin_counter')}>
            <Row className="bg-grey-bgOne rounded-10 p-10 mx-20 mb-15">
              <Row>
                <CircledIcon className="p-7 mr-5">
                  <Gift size={16} color={colors.accentGreen.main} variant="Bold" />
                </CircledIcon>
                <BaseText fontSize={12} classes="text-black-muted" weight="medium">
                  Join Giveaway on Instagram
                </BaseText>
              </Row>
              <Row>
                <BaseText fontSize={12} classes="text-primary-main" weight="semiBold">
                  Share Card
                </BaseText>
                <ArrowUpRight currentColor={colors.primary.main} strokeWidth={2} size={wp(18)} />
              </Row>
            </Row>
          </Pressable>
          <ScrollableActionPills pills={actionPills} title="Overview" />
          <Container className="mt-20 z-50 overflow-visible pb-45">
            <QueryErrorBoundary
              error={storeAnalyticsRequest.error}
              isLoading={storeAnalyticsRequest.isLoading}
              refetch={storeAnalyticsRequest.refetch}
              variant="section"
              errorTitle="Failed to load analytics">
              <HomeAnalyicsCards
                isLoading={storeAnalyticsRequest?.isLoading}
                statistics={statisticData}
                trends={trends}
                catlogCreditRequest={catlogCreditRequest}
              />
            </QueryErrorBoundary>

            <AreaGraph
              title="Total Store Visits"
              error={storeAnalyticsRequest.error}
              onPressRetry={storeAnalyticsRequest.refetch}
              rawData={[graphRawData]}
              customRange={customRange}
              setCustomRange={setCustomRange}
              toggleCalendar={toggleCalendar}
              showCalendar={showCalendar}
              range={range}
              setRange={handleSelectRange}
              className="mt-20 mb-20"
              isLoadingData={storeAnalyticsRequest?.isLoading}
              labels={{ topPrefix: 'Total Store Visits' }}
            />

            <PendingActionSection pendingActionRequest={pendingActionRequest} />
            <QueryErrorBoundary
              error={getLatestOrdersReq.error || getTopProductReq.error}
              isLoading={getLatestOrdersReq.isLoading || getTopProductReq.isLoading}
              refetch={() => {
                getLatestOrdersReq.refetch();
                getTopProductReq.refetch();
              }}
              variant="section"
              errorTitle="Failed to load products & orders">
              <DashboardOrdersAndTopProducts
                getLatestOrdersReq={getLatestOrdersReq}
                getTopProductReq={getTopProductReq}
              />
            </QueryErrorBoundary>
          </Container>
        </View>
      </ScrollView>
      <DownloadCardModal
        count={user.meta?.mobile_app_signin_position ?? 0}
        closeModal={() => toggleModal('signin_counter')}
        isVisible={modals.signin_counter}
      />
      {hasInvite && (
        <AcceptInviteModal
          isVisible={modals.inviteModal}
          closeModal={() => toggleModal('inviteModal', false)}
          inviteDetail={param}
        />
      )}
      <UpdateAppModal isVisible={visible} closeModal={() => setVisible(false)} />
    </DashboardLayout>
  );
};

export default Home;
