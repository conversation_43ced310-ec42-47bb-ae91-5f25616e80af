import { GET_PLANS } from 'catlog-shared';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { PickPlanMain } from 'src/screens/setup/pick-plan';

const ChangePlan = () => {
  const { store, userAccountDeactivated } = useAuthContext();
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
    },
    {
      country: typeof store?.country === 'string' ? store?.country : store?.country?.code,
    },
  );

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentOrange-pastel',
        pageTitle: userAccountDeactivated ? 'Reactivate Account' : 'Change Plan',
        variant: HeaderVariants.SUB_LEVEL,
      }}
      isLoading={getPlansReq.isLoading}>
      <PickPlanMain  isSetup={false} getPlansReq={getPlansReq} />
    </DashboardLayout>
  );
};
export default ChangePlan;
