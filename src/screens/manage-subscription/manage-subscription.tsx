import { Image, ScrollView, Text, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Calendar1, Calendar2, CardTick, Clock, EmptyWallet, Gift, Status, Wallet2 } from 'iconsax-react-native/src';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import useAuthContext from 'src/contexts/auth/auth-context';
import ProductInfoRow from 'src/components/products/product-info-row';
import Separator from 'src/components/ui/others/separator';
import {
  alertPromise,
  cx,
  formatDate,
  getColorAlternates,
  showLoader,
  showSuccess,
  toCurrency,
  toNaira,
  wp,
} from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import Container from 'src/components/ui/container';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import { CANCEL_SUBSCRIPTION_TOGGLE, GET_SUBSCRIPTION_PAYMENTS, SubscriptionInterface } from 'catlog-shared';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant, TextColor } from 'src/components/ui/buttons/button';
import { useNavigation } from '@react-navigation/native';
import { useApi } from 'src/hooks/use-api';
import InfoBadge from 'src/components/store-settings/info-badge';
import { Fragment, useMemo } from 'react';
import TopTabs from 'src/components/ui/others/top-tabs';
import useTabs from 'src/hooks/use-tabs';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import { TransactionCard } from 'src/components/payments/transaction-history';
import Column from 'src/components/ui/column';
import { colorAlternates } from 'src/constant/static-data';
import TransactionSkeletonLoader from '../../components/payments/transaction-skeleton-loader';

enum ManageSubscriptionTabs {
  Subscription_Details = 'SubscriptionDetails',
  Payment_History = 'PaymentHistory',
}

function ManageSubscription() {
  const { subscription, updateStore } = useAuthContext();
  const { tabs, active, switchTab } = useTabs({
    tabs: Object.values(ManageSubscriptionTabs),
  });

  const isActive = subscription?.status == 'ACTIVE';
  const navigation = useNavigation();

  const toggleReq = useApi({
    apiFunction: CANCEL_SUBSCRIPTION_TOGGLE,
    key: CANCEL_SUBSCRIPTION_TOGGLE.name,
    method: 'POST',
  });

  const toggleSubscription = async () => {
    if (subscription.cancel_at_period_end == false) {
      const res = await alertPromise(
        'Cancel Subscription',
        'Are you sure you want to cancel your subscription?',
        'Yes Cancel',
        'No',
        true,
      );

      if (res == false) return;
    }
    showLoader('Updating subscription...');
    const [res, err] = await toggleReq.makeRequest({ cancel_at_period_end: !subscription.cancel_at_period_end });
    if (res) {
      showSuccess('Subscription updated successfully');
      updateStore({
        subscription: {
          ...subscription,
          cancel_at_period_end: !subscription.cancel_at_period_end,
        },
      });
    }
  };

  const tabItems = [
    {
      key: ManageSubscriptionTabs.Subscription_Details,
      title: 'Subscription Details',
      component: <SubscriptionDetailsScreen subscription={subscription} updateStore={updateStore} />,
    },
    {
      key: ManageSubscriptionTabs.Payment_History,
      title: 'Payment History',
      component: <PaymentHistory />,
    },
  ];

  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentRed-pastel',
        pageTitle: 'Manage Subscription',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.RED}
        iconElement={
          <Image
            source={require('@/assets/images/subscription.png')}
            resizeMode={'contain'}
            className="w-[80px] h-[80px]"
          />
        }
        customElements={
          <BaseText fontSize={24} weight={'bold'} classes={`text-black-primary mt-10`} type="heading">
            {subscription.plan?.name ?? '-'} Plan
          </BaseText>
        }
      />

      <TopTabs setIndex={switchTab} currentIndex={active} tabItems={tabItems} />
    </DashboardLayout>
  );
}

export default ManageSubscription;

const SubscriptionDetailsScreen = ({
  subscription,
  updateStore,
}: {
  subscription: SubscriptionInterface;
  updateStore: any;
}) => {
  const isActive = subscription?.status == 'ACTIVE';
  const navigation = useNavigation();

  const toggleReq = useApi({
    apiFunction: CANCEL_SUBSCRIPTION_TOGGLE,
    key: CANCEL_SUBSCRIPTION_TOGGLE.name,
    method: 'POST',
  });

  const toggleSubscription = async () => {
    if (subscription.cancel_at_period_end == false) {
      const res = await alertPromise(
        'Cancel Subscription',
        'Are you sure you want to cancel your subscription?',
        'Yes Cancel',
        'No',
        true,
      );

      if (res == false) return;
    }
    showLoader('Updating subscription...');
    const [res, err] = await toggleReq.makeRequest({ cancel_at_period_end: !subscription.cancel_at_period_end });
    if (res) {
      showSuccess('Subscription updated successfully');
      updateStore({
        subscription: {
          ...subscription,
          cancel_at_period_end: !subscription.cancel_at_period_end,
        },
      });
    }
  };

  const subscriptionDetails = [
    {
      icon: Clock,
      title: 'Interval',
      value: subscription.plan?.interval_text ?? '-',
    },
    {
      icon: Calendar1,
      title: 'Last Payment Date',
      value: formatDate(subscription?.last_payment_date, 'DD MMM YYYY') ?? '-',
    },
    {
      icon: Calendar2,
      title: 'Next Payment Date',
      value: formatDate(subscription?.next_payment_date, 'DD MMM YYYY') ?? '-',
    },
    {
      icon: Wallet2,
      title: 'Payment Method',
      value: 'PAYSTACK',
    },
    {
      icon: Status,
      title: 'Subscription Status',
      value: subscription.status ?? '-',
      customValue: (
        <StatusPill
          statusType={isActive ? StatusType.SUCCESS : StatusType.PROCESSING}
          className="bg-grey-bgOne"
          title={subscription.status ?? '-'}
        />
      ),
    },
  ];

  return (
    <View className="flex-1">
      <ScrollView>
        <Container>
          <View>
            {subscriptionDetails.map((detail, index) => (
              <Fragment key={detail.title}>
                <Row>
                  <Row disableSpread>
                    <CircledIcon className="bg-grey-bgOne p-6">
                      <detail.icon
                        variant={'Outline'}
                        size={wp(15)}
                        color={detail.title === 'Subscription Status' ? colors.black.main : colors.black.muted}
                      />
                    </CircledIcon>
                    <BaseText fontSize={12} className="ml-8 text-black-muted">
                      {detail.title}
                    </BaseText>
                  </Row>
                  {detail.customValue || (
                    <BaseText weight="medium" fontSize={12} classes="text-black-secondary">
                      {detail.value}
                    </BaseText>
                  )}
                </Row>
                {index < subscriptionDetails.length - 1 && <Separator className="mx-0 my-12" />}
              </Fragment>
            ))}
          </View>
          {subscription?.discount && (
            <Row className="p-10 bg-grey-bgOne rounded-15 mt-10" alignCenter={false}>
              <View className="h-[35px] w-[35px] bg-white rounded-full flex items-center justify-center">
                <Gift color={colors.accentRed.main} size={wp(17)} variant="Bold" />
              </View>
              <View className="flex-1 ml-8">
                <BaseText fontSize={14} type="heading" classes="mb-5 text-black-secondary">
                  {subscription?.discount?.percentage}% Discount off your next payment
                </BaseText>
                <BaseText fontSize={12} type="body" weight="regular" classes="text-black-muted">
                  Reason: {subscription?.discount?.narration}
                </BaseText>
                <BaseText fontSize={12} type="body" weight="regular" classes="text-black-muted">
                  Expires: {formatDate(subscription?.discount?.expiry_date, 'DD MMM YYYY')}
                </BaseText>
              </View>
            </Row>
          )}
        </Container>
      </ScrollView>

      <FixedBtnFooter
        buttons={[
          {
            text: subscription.cancel_at_period_end ? 'Resume Plan' : 'Cancel Plan',
            onPress: () => toggleSubscription(),
            isLoading: toggleReq.isLoading,
            variant: ButtonVariant.LIGHT,
            textColor: subscription.cancel_at_period_end ? TextColor.PRIMARY : TextColor.NEGATIVE,
          },
          {
            isLoading: toggleReq.isLoading,
            text: 'Change Plan',
            onPress: () => navigation.navigate('ChangePlan'),
          },
        ]}
      />
    </View>
  );
};

interface PaymentHistoryData {
  amount: number;
  amount_with_charge: number;
  created_at: string;
  currency: string;
  formerly_succeeded: boolean;
  id: string;
  merchant_fees: number;
  meta: PaymentHistoryMeta;
  owner: string;
  payment_method: string;
  payment_method_type: null;
  plan: null;
  reference: string;
  status: string;
  subscription: null;
  success: Date;
  type: string;
  updated_at: Date;
}

interface PaymentHistoryMeta {
  plan: string;
  subscription: string;
}

const PaymentHistory = () => {
  const paymentReq = useApi({
    apiFunction: GET_SUBSCRIPTION_PAYMENTS,
    key: CANCEL_SUBSCRIPTION_TOGGLE.name,
    method: 'GET',
  });

  const paymentHistoryData: PaymentHistoryData[] = paymentReq.response?.data ?? [];

  return (
    <View className="flex-1">
      <ScrollView>
        <Container>
          {paymentReq.isLoading && (
            <View className="mt-15 -mx-20">
              <TransactionSkeletonLoader showTitle={false} />
            </View>
          )}
          {!paymentReq.isLoading && (
            <View>
              {paymentHistoryData.map((transaction, idx) => {
                const isLast = idx === paymentHistoryData.length - 1;
                const color = getColorAlternates(idx);
                return (
                  <View key={idx}>
                    <Row
                      className={cx('w-full space-x-2.5 py-15 border-b border-grey-border', {
                        'border-b-0': isLast,
                      })}>
                      <CircledIcon className="p-10" style={{ backgroundColor: color.bgColor }}>
                        <CardTick color={color.iconColor} size={wp(20)} variant="Bold" />
                      </CircledIcon>
                      <Column className="flex-1">
                        <Row className="w-full space-x-20 mb-5" spread>
                          <View className="flex-1 ">
                            <BaseText
                              numberOfLines={1}
                              type="body"
                              weight="regular"
                              fontSize={12}
                              classes="text-black-muted">
                              {transaction.payment_method} ({transaction.reference})
                            </BaseText>
                          </View>
                          <StatusPill statusType={statusMap[transaction.status]} title={transaction.status} />
                        </Row>
                        <Row className="">
                          <BaseText
                            type="heading"
                            fontSize={12}
                            classes="text-black-muted "
                            style={{ letterSpacing: 0.5 }}>
                            {toCurrency(toNaira(transaction.amount), transaction.currency)}
                          </BaseText>
                          <BaseText fontSize={10} classes="ml-15 text-black-muted ">
                            {formatDate(transaction.created_at, 'DD MMM YYYY, hh:mm A')}
                          </BaseText>
                        </Row>
                      </Column>
                    </Row>
                  </View>
                );
              })}
            </View>
          )}
        </Container>
      </ScrollView>
    </View>
  );
};

const statusMap = {
  PENDING: StatusType.WARN,
  FAILED: StatusType.DANGER,
  SUCCESS: StatusType.SUCCESS,
};
