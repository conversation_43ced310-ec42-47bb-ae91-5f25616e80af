import React from 'react';
import { ActivityIndicator, View } from 'react-native';
import colors from '@/theme/colors';
import CustomImage from 'src/components/ui/others/custom-image';

const LoadingScreen = () => {
  return (
    <View className="flex-1 bg-white flex justify-center items-center">
      {/* <ActivityIndicator size="large" color={colors.primary.main} /> */}
      <CustomImage imageProps={{ source: require('@/assets/gif/loader.gif') }} className="w-40 h-40 rounded-8" />
    </View>
  );
};

export default LoadingScreen;
