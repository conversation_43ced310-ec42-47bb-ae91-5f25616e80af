import { useReducer, useState } from 'react';

function usePagination() {
  const [currentPage, setCurrentPage] = useReducer((_: any, a: number) => a + 0, 1);

  function goNext(totalPages?: number) {
    if (totalPages && currentPage >= totalPages) return;
    setCurrentPage(currentPage + 1);
  }

  function goPrevious() {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  }

  return {
    currentPage,
    goNext,
    goPrevious,
    setPage: setCurrentPage,
  };
}

export default usePagination;
