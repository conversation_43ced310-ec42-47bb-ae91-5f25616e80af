import { useState, useEffect } from 'react';
import * as FileSystem from 'expo-file-system';
import { FFprobeKit } from 'ffmpeg-kit-react-native';

// export const useVideoMeta = (videoUri) => {
//   const [metadata, setMetadata] = useState({
//     size: null,
//     type: null,
//     extension: null,
//     duration: null,
//     loading: true,
//     error: null
//   });

//   useEffect(() => {
//     if (!videoUri) {
//       setMetadata(prev => ({ ...prev, loading: false }));
//       return;
//     }

//     const getVideoMetadata = async () => {
//       try {
//         setMetadata(prev => ({ ...prev, loading: true, error: null }));

//         // Get file info using Expo FileSystem
//         const fileInfo = await FileSystem.getInfoAsync(videoUri);
        
//         if (!fileInfo.exists) {
//           throw new Error('Video file does not exist');
//         }

//         // Extract file extension and type
//         const extension = videoUri.split('.').pop()?.toLowerCase();
//         const mimeType = getMimeType(extension);

//         // Use FFprobeKit to get video duration and additional metadata
//         const session = await FFprobeKit.execute(`-v quiet -show_entries format=duration -of csv=p=0 "${videoUri}"`);
//         const returnCode = await session.getReturnCode();
        
//         if (!returnCode.isValueSuccess()) {
//           throw new Error('Failed to get video duration');
//         }
        
//         const output = await session.getOutput();
//         const duration = parseFloat(output.trim());

//         setMetadata({
//           size: fileInfo.size,
//           type: mimeType,
//           extension: extension,
//           duration: duration,
//           loading: false,
//           error: null
//         });

//       } catch (error) {
//         setMetadata(prev => ({
//           ...prev,
//           loading: false,
//           error: error.message
//         }));
//       }
//     };

//     getVideoMetadata();
//   }, [videoUri]);

//   return metadata;
// };

// Helper function to get MIME type from extension
const getMimeType = (extension: string) => {
  const mimeTypes = {
    'mp4': 'video/mp4',
    'mov': 'video/quicktime',
    'avi': 'video/x-msvideo',
    'mkv': 'video/x-matroska',
    'webm': 'video/webm',
    'm4v': 'video/x-m4v',
    '3gp': 'video/3gpp',
    'flv': 'video/x-flv',
    'wmv': 'video/x-ms-wmv'
  };
  
  return mimeTypes[extension] || 'video/unknown';
};

// Enhanced version that gets additional video metadata using FFprobeKit
export const useVideoMeta = (videoUri: string) => {
  const [metadata, setMetadata] = useState({
    size: null,
    type: null,
    extension: null,
    duration: null,
    width: null,
    height: null,
    bitrate: null,
    codec: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    if (!videoUri) {
      setMetadata(prev => ({ ...prev, loading: false }));
      return;
    }

    const getDetailedVideoMetadata = async () => {
      try {
        setMetadata(prev => ({ ...prev, loading: true, error: null }));

        // Get file info using Expo FileSystem
        const fileInfo = await FileSystem.getInfoAsync(videoUri);
        
        if (!fileInfo.exists) {
          throw new Error('Video file does not exist');
        }

        // Extract file extension and type
        const extension = videoUri.split('.').pop()?.toLowerCase();
        const mimeType = getMimeType(extension);

        // Use FFprobeKit to get comprehensive video metadata
        const command = `-v quiet -print_format json -show_format -show_streams "${videoUri}"`;
        const session = await FFprobeKit.execute(command);
        const returnCode = await session.getReturnCode();
        
        if (!returnCode.isValueSuccess()) {
          throw new Error('Failed to get video metadata');
        }
        
        const output = await session.getOutput();
        const probeData = JSON.parse(output);
        
        // Find video stream
        const videoStream = probeData.streams.find(stream => stream.codec_type === 'video');
        
        setMetadata({
          size: fileInfo.size,
          type: mimeType,
          extension: extension,
          duration: parseFloat(probeData.format.duration),
          width: videoStream?.width || null,
          height: videoStream?.height || null,
          bitrate: parseInt(probeData.format.bit_rate) || null,
          codec: videoStream?.codec_name || null,
          loading: false,
          error: null
        });

      } catch (error) {
        setMetadata(prev => ({
          ...prev,
          loading: false,
          error: error.message
        }));
      }
    };

    getDetailedVideoMetadata();
  }, [videoUri]);

  return metadata;
};
// Usage examples:
/*
// Basic usage
import { useVideoMeta } from './hooks/useVideoMeta';

const VideoComponent = ({ videoUri }) => {
  const { size, type, extension, duration, loading, error } = useVideoMeta(videoUri);

  if (loading) return <Text>Loading video metadata...</Text>;
  if (error) return <Text>Error: {error}</Text>;

  return (
    <View>
      <Text>File Size: {size ? `${(size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</Text>
      <Text>File Type: {type}</Text>
      <Text>Extension: {extension}</Text>
      <Text>Duration: {duration ? `${duration.toFixed(2)} seconds` : 'Unknown'}</Text>
    </View>
  );
};

// Detailed usage with additional metadata
import { useVideoMetaDetailed } from './hooks/useVideoMeta';

const DetailedVideoComponent = ({ videoUri }) => {
  const { 
    size, type, extension, duration, width, height, 
    bitrate, codec, loading, error 
  } = useVideoMetaDetailed(videoUri);

  if (loading) return <Text>Loading video metadata...</Text>;
  if (error) return <Text>Error: {error}</Text>;

  return (
    <View>
      <Text>File Size: {size ? `${(size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</Text>
      <Text>Resolution: {width && height ? `${width}x${height}` : 'Unknown'}</Text>
      <Text>Duration: {duration ? `${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}` : 'Unknown'}</Text>
      <Text>Codec: {codec || 'Unknown'}</Text>
      <Text>Bitrate: {bitrate ? `${Math.round(bitrate / 1000)} kbps` : 'Unknown'}</Text>
    </View>
  );
};
*/