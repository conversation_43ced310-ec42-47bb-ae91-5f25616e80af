import React, { useEffect, useState } from 'react';
import messaging, { getMessaging } from '@react-native-firebase/messaging';
import * as Notifications from 'expo-notifications';
import { SAVE_USER_NOTIFICATION_TOKEN } from 'node_modules/catlog-shared/dist';
import { useApi } from './use-api';
import useNotificationHandler from './use-notification-handler';

// Define types for better type safety
interface NotificationSetupHookResult {
  fcmToken: string | null;
  permissionStatus: 'granted' | 'denied' | 'unsupported' | 'pending';
}

const useNotificationSetup = (): NotificationSetupHookResult => {

  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<NotificationSetupHookResult['permissionStatus']>('pending');

  const saveTokenRequest = useApi({
    apiFunction: SAVE_USER_NOTIFICATION_TOKEN,
    method: 'POST',
    key: SAVE_USER_NOTIFICATION_TOKEN.name,
  });

  // Request Notification Permissions
  const requestNotificationPermissions = async (): Promise<boolean> => {
    try {
      // Request Firebase messaging permissions
      const authStatus = await getMessaging().requestPermission();
      const firebaseEnabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      // Request Expo notification permissions
      const { status: expoStatus } = await Notifications.requestPermissionsAsync();
      const expoEnabled = expoStatus === 'granted';
      // Update permission status
      if (firebaseEnabled && expoEnabled) {
        setPermissionStatus('granted');
        return true;
      } else {
        setPermissionStatus('denied');
        return false;
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      setPermissionStatus('denied');
      return false;
    }
  };

  // Fetch and send FCM Token
  const fetchAndSendToken = async () => {
    try {
      // Request permissions first
      const permissionsGranted = await requestNotificationPermissions();
      if (!permissionsGranted) {
        return;
      }

      // Get FCM token
      const token = await getMessaging().getToken();
      setFcmToken(token);

      await sendTokenToServer(token);

      // Handle token refresh
      return getMessaging().onTokenRefresh(async newToken => {
        setFcmToken(newToken);
        await sendTokenToServer(newToken);
      });
      return null;
    } catch (error) {
      console.error('Token fetching error:', error);
    }
  };

  const sendTokenToServer = async (token: string) => {
    try {
      const [res, err] = await saveTokenRequest.makeRequest({
        fcm_token: token,
        country: 'NG',
      });
      console.log('Sending token to server:', token);
      if (res) {
      }
    } catch (error) {
      console.error('Error sending token to server:', error);
    }
  };

  // Setup effect
  useEffect(() => {
    const setupNotifications = async () => {
      // Fetch and send token
      const tokenRefreshUnsubscribe = await fetchAndSendToken();

      // Cleanup
      return () => {
        tokenRefreshUnsubscribe?.();
      };
    };

    setupNotifications();
  }, []);

  // useEffect(() => {
  //   // Handle foreground notifications
  //   const unsubscribeForeground = getMessaging().onMessage(handleForegroundNotification);

  //   // Handle background notifications when app is closed
  //   getMessaging().setBackgroundMessageHandler(handleBackgroundNotification);

  //   // Handle notification click when app is in background
  //   getMessaging().onNotificationOpenedApp(remoteMessage => {
  //     handleBackgroundNotification(remoteMessage);
  //   });

  //   // Handle initial notification when app is opened from a closed state
  //   getMessaging().getInitialNotification().then(handleBackgroundNotification);

  //   // Cleanup
  //   return () => {
  //     unsubscribeForeground();
  //   };
  // }, [handleForegroundNotification, handleBackgroundNotification]);

  return {
    fcmToken,
    permissionStatus,
  };
};

export default useNotificationSetup;
