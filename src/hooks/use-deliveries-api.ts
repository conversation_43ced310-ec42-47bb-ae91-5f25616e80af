import { useState, useEffect } from 'react';
import { Keyboard, LayoutAnimation } from 'react-native';
import usePagination from './use-pagination';
import { ResponseWithPagination, useApi } from './use-api';
import { delay, ensureUniqueItems } from 'src/assets/utils/js';
import { DELIVERY_STATUSES, IDelivery, OrderInterface } from 'catlog-shared';
import { GET_ORDERS } from 'catlog-shared';
import { GET_DELIVERIES, GetDeliveriesParams } from 'catlog-shared';

interface DeliveriesResponseWithPagination extends ResponseWithPagination<OrderInterface[]> {}
export interface DeliveriesResponse {
  data: DeliveriesResponseWithPagination;
}
const useDeliveriesApi = (status: DELIVERY_STATUSES, perPage = 10) => {
  const [deliveries, setDeliveries] = useState<IDelivery[]>([]);

  const { currentPage, goNext, setPage } = usePagination();

  const changeStatusRequest = useApi<GetDeliveriesParams, ResponseWithPagination<{}>>({
    apiFunction: GET_DELIVERIES,
    method: 'PUT',
    key: 'update-order',
  });

  const getDeliveriesRequest = useApi(
    {
      apiFunction: GET_DELIVERIES,
      method: 'GET',
      key: 'get-deliveries',
      onSuccess: response => {
        setDeliveries(prev => ensureUniqueItems([...prev, ...response?.data?.data]));
      },
    },
    {
      filter: { status: status },
      page: currentPage,
      per_page: perPage,
      sort: 'DESC',
    },
  );

  const handlePullToRefresh = async () => {
    if (currentPage === 1) {
      getDeliveriesRequest.makeRequest({
        filter: { status: status },
        page: currentPage,
        per_page: perPage,
        sort: 'DESC',
      });
      await delay(1000);
      setDeliveries([]);
      return;
    }
    setPage(1);
    await delay(1000);
    setDeliveries([]);
  };

  const handleOnEndReach = () => {
    if (
      !getDeliveriesRequest?.isLoading &&
      deliveries?.length > 0 &&
      currentPage < getDeliveriesRequest?.response?.data?.total_pages
    ) {
      goNext(getDeliveriesRequest?.response?.data?.total_pages);
    }
  };

  const handleUpdateStatus = async () => {};

  return {
    getDeliveriesRequest,
    deliveries,
    goNext,
    setPage,
    handlePullToRefresh,
    handleOnEndReach,
    setDeliveries,
  };
};

export default useDeliveriesApi;
