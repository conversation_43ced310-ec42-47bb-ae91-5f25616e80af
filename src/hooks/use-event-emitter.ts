// React Native hook example
import { useEffect } from 'react';
import eventEmitter from 'src/assets/utils/js/event-emitter';

function useEventListener(event: string, callback: (...args: any[]) => void) {
  useEffect(() => {
    // Subscribe to the event
    const unsubscribe = eventEmitter.on(event, callback);
    
    // Cleanup on unmount
    return () => unsubscribe();
  }, [event, callback]);
}

export default useEventListener;
