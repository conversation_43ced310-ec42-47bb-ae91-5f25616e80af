// types.ts
export enum ProcessingStep {
  TRIM_VIDEO = 'trim_video',
  UPLOAD_VIDEO = 'upload_video',
  UPDATE_VIDEO_URL = 'update_video_url',
  UPLOAD_THUMBNAIL = 'upload_thumbnail',
  UPDATE_THUMBNAIL_URL = 'update_thumbnail_url',
}

export interface QueueItem {
  type: MediaType;
  src: string;
  name: string;
  lastModified: number;
  fileSize?: number;
  file: File | Blob;
  isUploading?: boolean;
  uploadProgress?: number;
  url?: string;
  thumbnail?: string;
  error?: boolean;
  meta?: {
    id: string;
    thumbnail: {
      src: string;
      name: string;
      lastModified: number;
      file: string | null;
      isUploading: boolean;
      uploadProgress: number;
      url: string;
      error: boolean;
      key: string;
    };
  };
  key?: string;
  isPlaceholder?: boolean;

  // Queue-specific properties
  queueId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  currentStep: ProcessingStep;
  stepProgress: Record<ProcessingStep, number>;
  overallProgress: number;
  errorMessage?: string;
  retryCount: number;
  processedVideoUrl?: string;
  processedThumbnailUrl?: string;

  // Trimming parameters
  startTimeMs: number;
  endTimeMs: number;
  outputPath?: string;
}

// useVideoProcessingQueue.ts
import { useState, useEffect, useCallback, useRef } from 'react';
import { MediaType } from 'catlog-shared';
import { uploadImage } from 'src/assets/utils/js/upload-files';
import { useVideoTrimmer } from './use-video-trimmer';

// Progress weights for each step (should sum to 100)
const STEP_WEIGHTS = {
  [ProcessingStep.TRIM_VIDEO]: 30,
  [ProcessingStep.UPLOAD_VIDEO]: 40,
  [ProcessingStep.UPDATE_VIDEO_URL]: 5,
  [ProcessingStep.UPLOAD_THUMBNAIL]: 20,
  [ProcessingStep.UPDATE_THUMBNAIL_URL]: 5,
};

const PROCESSING_STEPS = [
  ProcessingStep.TRIM_VIDEO,
  ProcessingStep.UPLOAD_VIDEO,
  ProcessingStep.UPDATE_VIDEO_URL,
  ProcessingStep.UPLOAD_THUMBNAIL,
  ProcessingStep.UPDATE_THUMBNAIL_URL,
];

function calculateOverallProgress(stepProgress: Record<ProcessingStep, number>): number {
  let totalProgress = 0;

  for (const [step, progress] of Object.entries(stepProgress)) {
    const weight = STEP_WEIGHTS[step as ProcessingStep];
    totalProgress += (progress / 100) * weight;
  }

  return Math.round(totalProgress);
}

function createQueueItem(
  item: Omit<QueueItem, 'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'>,
): QueueItem {
  return {
    ...item,
    queueId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    status: 'pending',
    currentStep: ProcessingStep.TRIM_VIDEO,
    stepProgress: PROCESSING_STEPS.reduce(
      (acc, step) => ({
        ...acc,
        [step]: 0,
      }),
      {} as Record<ProcessingStep, number>,
    ),
    overallProgress: 0,
    retryCount: 0,
    outputPath: item.outputPath || null,
  };
}

export const useVideoProcessingQueue = (updateVideoInQueue: (queueItem: QueueItem) => void) => {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [completedItems, setCompletedItems] = useState<QueueItem[]>([]);
  const [failedItems, setFailedItems] = useState<QueueItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentItem, setCurrentItem] = useState<QueueItem | null>(null);
  const [isPaused, setIsPaused] = useState(false);

  const { trimVideo } = useVideoTrimmer();
  const processingRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateProgress = useCallback((queueId: string, step: ProcessingStep, progress: number) => {
    setQueue(prevQueue =>
      prevQueue.map(item => {
        if (item.queueId === queueId) {
          const newStepProgress = {
            ...item.stepProgress,
            [step]: progress,
          };
          const updatedItem = {
            ...item,
            stepProgress: newStepProgress,
            overallProgress: calculateOverallProgress(newStepProgress),
          };

          // Update current item if it's the one being processed
          setCurrentItem(prev => (prev?.queueId === queueId ? updatedItem : prev));

          return updatedItem;
        }
        return item;
      }),
    );
  }, []);

  const processQueueItem = useCallback(
    async (item: QueueItem): Promise<void> => {
      const { queueId } = item;
      let trimmedVideo: any = null;
      let videoUrl: string | null = null;
      let thumbnailUrl: string | null = null;

      try {
        // Step 1: Trim Video (30%)
        updateProgress(queueId, ProcessingStep.TRIM_VIDEO, 0);
        updateVideoInQueue({
          ...item,
          isUploading: true,
          currentStep: ProcessingStep.TRIM_VIDEO,
          uploadProgress: 10,
        });

        trimmedVideo = await trimVideo(item.src, item.startTimeMs, item.endTimeMs, item.outputPath);

        updateProgress(queueId, ProcessingStep.TRIM_VIDEO, 100);
        updateVideoInQueue({
          ...item,
          isUploading: true,
          uploadProgress: 30,
          currentStep: ProcessingStep.TRIM_VIDEO,
        });

        // Step 2: Upload Video (40%)
        updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, 0);
        updateVideoInQueue({
          ...item,
          isUploading: true,
          uploadProgress: 70,
          src: trimmedVideo.outputPath,
          fileSize: trimmedVideo.size,
          currentStep: ProcessingStep.UPLOAD_VIDEO,
        });

        videoUrl = await uploadImage<string>(
          trimmedVideo.outputPath,
          'file',
          (progress: number) => {
            updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, progress);
          },
          undefined,
          undefined,
          undefined,
          MediaType.VIDEO,
        );

        updateProgress(queueId, ProcessingStep.UPLOAD_VIDEO, 100);

        // Step 3: Update Video URL locally (5%)
        updateProgress(queueId, ProcessingStep.UPDATE_VIDEO_URL, 0);

        // Update local state with new video URL
        updateVideoInQueue({
          ...item,
          isUploading: true,
          uploadProgress: 75,
          src: trimmedVideo.outputPath,
          fileSize: trimmedVideo.size,
          url: videoUrl,
          currentStep: ProcessingStep.UPDATE_VIDEO_URL,
        });

        // Simulate brief processing time
        await new Promise(resolve => setTimeout(resolve, 200));
        updateProgress(queueId, ProcessingStep.UPDATE_VIDEO_URL, 100);

        // Step 4: Upload Thumbnail (20%)
        updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, 0);

        updateVideoInQueue({
          ...item,
          isUploading: true,
          uploadProgress: 95,
          src: trimmedVideo.outputPath,
          fileSize: trimmedVideo.size,
          url: videoUrl,
          currentStep: ProcessingStep.UPLOAD_THUMBNAIL,
        });
        const thumbnailFile = item.meta?.thumbnail?.src;
        if (thumbnailFile) {
          thumbnailUrl = await uploadImage<string>(thumbnailFile, 'file', (progress: number) => {
            updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, progress);
          });
        } else {
          // If no thumbnail file, skip with 100% progress
          thumbnailUrl = item.thumbnail || '';
        }

        updateProgress(queueId, ProcessingStep.UPLOAD_THUMBNAIL, 100);

        // Step 5: Update Thumbnail URL locally (5%)
        updateProgress(queueId, ProcessingStep.UPDATE_THUMBNAIL_URL, 0);

        // Update local state with new thumbnail URL
        updateVideoInQueue({
          ...item,
          url: videoUrl,
          processedVideoUrl: videoUrl,
          processedThumbnailUrl: thumbnailUrl,
          src: trimmedVideo.outputPath,
          fileSize: trimmedVideo.size,
          isUploading: true,
          uploadProgress: 100,
          currentStep: ProcessingStep.UPDATE_THUMBNAIL_URL,
        });

        // Simulate brief processing time
        await new Promise(resolve => setTimeout(resolve, 200));
        updateProgress(queueId, ProcessingStep.UPDATE_THUMBNAIL_URL, 100);

        // Create completed item
        const completedItem = {
          ...item,
          status: 'completed' as const,
          overallProgress: 100,
          isUploading: false,
          processedVideoUrl: videoUrl,
          processedThumbnailUrl: thumbnailUrl,
          src: trimmedVideo.outputPath,
          fileSize: trimmedVideo.size,
        };

        // Mark as completed - use functional updates to avoid stale closures
        setQueue(prevQueue => prevQueue.filter(q => q.queueId !== queueId));
        setCompletedItems(prev => [...prev, completedItem]);

        // Call the update callback
        updateVideoInQueue(completedItem);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        // Create failed item
        const failedItem = {
          ...item,
          status: 'failed' as const,
          errorMessage,
        };

        // Move to failed items
        setQueue(prevQueue => prevQueue.filter(q => q.queueId !== queueId));
        setFailedItems(prev => [...prev, failedItem]);

        // Call the update callback
        updateVideoInQueue(failedItem);

        throw error;
      }
    },
    [trimVideo, updateProgress, updateVideoInQueue],
  );

  const processNextItem = useCallback(async () => {
    // Prevent multiple concurrent executions
    if (processingRef.current || isPaused) {
      return;
    }

    // Find next pending item
    const nextItem = queue.find(item => item.status === 'pending');
    if (!nextItem) {
      setIsProcessing(false);
      setCurrentItem(null);
      return;
    }

    // Set processing flags
    processingRef.current = true;
    setIsProcessing(true);
    setCurrentItem(nextItem);

    // Update item status to processing
    setQueue(prevQueue =>
      prevQueue.map(item => (item.queueId === nextItem.queueId ? { ...item, status: 'processing' as const } : item)),
    );

    try {
      await processQueueItem(nextItem);
    } catch (error) {
    } finally {
      // Always reset processing flags
      processingRef.current = false;
      setIsProcessing(false);
      setCurrentItem(null);
    }
  }, [queue, isPaused, processQueueItem]);

  // Auto-process queue when items are added - FIXED: Better dependency management
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Only process if we have pending items and we're not already processing
    const hasPendingItems = queue.some(item => item.status === 'pending');

    if (hasPendingItems && !processingRef.current && !isPaused) {
      // Use timeout to prevent rapid re-execution
      timeoutRef.current = setTimeout(() => {
        processNextItem();
      }, 100);
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [queue.length, isPaused]); // FIXED: Only depend on queue length, not the entire queue array

  const addToQueue = useCallback(
    (
      item: Omit<QueueItem, 'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'>,
    ) => {
      const queueItem = createQueueItem(item);
      setQueue(prevQueue => [...prevQueue, queueItem]);
    },
    [],
  );

  const removeFromQueue = useCallback((queueId: string) => {
    setQueue(prevQueue => prevQueue.filter(item => item.queueId !== queueId));
  }, []);

  const retryItem = useCallback(
    (queueId: string) => {
      const failedItem = failedItems.find(item => item.queueId === queueId);
      if (failedItem) {
        const retryItem = createQueueItem({
          ...failedItem,
          retryCount: failedItem.retryCount + 1,
        });

        setFailedItems(prev => prev.filter(item => item.queueId !== queueId));
        setQueue(prevQueue => [...prevQueue, retryItem]);
      }
    },
    [failedItems],
  );

  const clearCompleted = useCallback(() => {
    setCompletedItems([]);
  }, []);

  const clearFailed = useCallback(() => {
    setFailedItems([]);
  }, []);

  const pauseQueue = useCallback(() => {
    setIsPaused(true);
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const resumeQueue = useCallback(() => {
    setIsPaused(false);
  }, []);

  const clearAll = useCallback(() => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setQueue([]);
    setCompletedItems([]);
    setFailedItems([]);
    setCurrentItem(null);
    setIsProcessing(false);
    processingRef.current = false;
  }, []);

  // Get current step name for display
  const getCurrentStepName = useCallback((step: ProcessingStep): string => {
    const stepNames = {
      [ProcessingStep.TRIM_VIDEO]: 'Trimming Video',
      [ProcessingStep.UPLOAD_VIDEO]: 'Uploading Video',
      [ProcessingStep.UPDATE_VIDEO_URL]: 'Updating Video',
      [ProcessingStep.UPLOAD_THUMBNAIL]: 'Uploading Thumbnail',
      [ProcessingStep.UPDATE_THUMBNAIL_URL]: 'Updating Thumbnail',
    };
    return stepNames[step] || step;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    queue,
    completedItems,
    failedItems,
    isProcessing,
    currentItem,
    isPaused,

    // Actions
    addToQueue,
    removeFromQueue,
    retryItem,
    clearCompleted,
    clearFailed,
    pauseQueue,
    resumeQueue,
    clearAll,

    // Utilities
    getCurrentStepName,

    // Computed values
    totalItems: queue.length + completedItems.length + failedItems.length,
    pendingCount: queue.filter(item => item.status === 'pending').length,
    processingCount: queue.filter(item => item.status === 'processing').length,
    completedCount: completedItems.length,
    failedCount: failedItems.length,
  };
};
