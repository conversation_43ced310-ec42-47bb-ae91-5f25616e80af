import { CURRENCIES } from 'node_modules/catlog-shared/dist';
import { useEffect, useMemo, useState } from 'react';
import { getExchangeRates } from 'src/assets/utils/js';
import useAuthContext from 'src/contexts/auth/auth-context';

type CurrencyRates = Record<string, number>;
type CurrencyMarkups = Record<string, number>;

interface UseCurrencyFormatterOptions {
  markups?: CurrencyMarkups;
  defaultDecimals?: number;
}

interface CurrencyData {
  active: CURRENCIES;
  markups: { [key: string]: number };
  rates?: { [key: string]: number };
  options: CURRENCIES[];
  // defaultCurrency: CURRENCIES;
  allRates: { [key: string]: { [key: string]: number } };
  rateId: string;
}

/**
 * A hook for currency formatting with conversion and markup support
 */
export const useCurrencyConverter = (
  defaultCurrency?: CURRENCIES,
  { defaultDecimals = 2 }: UseCurrencyFormatterOptions = {},
) => {
  const [currencies, setCurrencies] = useState<CurrencyData>({
    active: CURRENCIES.NGN,
    markups: {},
    options: [],
    allRates: {},
    rateId: '',
  });

  const { store, currentRates } = useAuthContext();

  useEffect(() => {
    const rates = getExchangeRates(currentRates, store.currencies?.products, store.currencies?.storefront);

    setCurrencies(prev => ({
      ...prev,
      markups: store.currencies.rates,
      rates,
    }));
  }, [JSON.stringify(currentRates.rates)]);

  // Create the formatter function memoized with the provided options
  const formatCurrency = (
    amount: number,
    currency?: string,
    options?: { convert?: boolean; decimals?: number },
  ): string => {
    const { convert = true, decimals = defaultDecimals } = options || {};

    // Use provided currency or fall back to default
    const activeCurrency = currency || defaultCurrency;

    // Parse amount to number if it's a string
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    // Safety check for NaN
    if (isNaN(numericAmount)) {
      return `${activeCurrency} 0${'.' + '0'.repeat(decimals)}`;
    }

    // Get conversion rate with proper fallback
    const conversionRate =
      convert && activeCurrency && currencies?.rates?.[activeCurrency] !== undefined
        ? currencies?.rates?.[activeCurrency]
        : 1;

    // Get markup with proper fallback
    const markup =
      activeCurrency !== null && currencies?.markups?.[activeCurrency] !== undefined ? currencies?.markups[activeCurrency] : 0;

    // Scale numbers to avoid floating-point issues
    const scale = Math.pow(10, decimals);

    // Apply markup first
    const amountWithMarkup = Math.round(numericAmount * (1 + markup / 100) * scale) / scale;

    // Then apply conversion
    const convertedAmount = Math.round(amountWithMarkup * conversionRate * scale) / scale;

    // Format the number with proper decimal places
    const formattedAmount = convertedAmount.toLocaleString('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });

    // Add space between currency code and amount
    return `${activeCurrency} ${formattedAmount}`;
  };

  return formatCurrency;
};

// // Example usage:
// /*
// import { useCurrencyFormatter } from './hooks/useCurrencyFormatter';

// const MyComponent = () => {
//   const rates = {"GBP": 5, "GHS": 3, "KES": 2.5, "USD": 20, "ZAR": 15};
//   const markups = {"GBP": 2, "USD": 3.5};

//   const formatCurrency = useCurrencyFormatter({
//     rates,
//     defaultCurrency: 'USD',
//     markups,
//   });

//   return (
//     <View>
//       <Text>{formatCurrency(450000, 'GHS')}</Text>
//       {/* Outputs: "GHS 1,350,000.00" */}
//     </View>
//   );
// };
// */
