import { useEffect, useRef, useState } from "react";

function useUncontrolledValue(input: string, onChange?: (text: string) => void, disabled = false) {
    const [syncedValue, setSyncedValue] = useState<string>();
    const syncedValueRef = useRef('');
    const timeoutRef = useRef<NodeJS.Timeout>()

    const handleChange = (value: string) => {
        if (!disabled)
            syncedValueRef.current = value
        onChange?.(value);
    }

    useEffect(() => {
        if (disabled) return;
        if (input === syncedValueRef.current) return;
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        
        const timeout = setTimeout(() => {
            const valueChanged = input !== syncedValueRef.current
            if (valueChanged) {
                setSyncedValue(input);
            }
        }, 2000);

        timeoutRef.current = timeout
        return () => {
            clearInterval(timeout);
        };

    }, [input, disabled])

    return {
        handleChange,
        syncedValue: disabled ? input : syncedValue ?? input,
    }
}

export default useUncontrolledValue