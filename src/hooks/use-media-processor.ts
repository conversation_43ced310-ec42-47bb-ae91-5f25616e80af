import { Media, MediaType } from "@/assets/interfaces";
import { generateSimpleUUID, getFileType } from "@/assets/js/utils/functions";
import { convertHeicToJpeg, generateKey, handleImageResizeOnCanvas } from "@/assets/js/utils/image-selection";
import { toast } from "@/components/ui/toast";

function useMediaProcessor(
  onComplete: (m: Media[]) => void,
  currentMedias: Media[],
  max?: number,
  includeCurrentMedias = true
) {
  const processFiles = async (files: FileList, excludedTypes?: MediaType[]) => {
    const maxImageSize = 1500;
    const maxFileSize = 100;

    if (files) {
      let medias: Media[] = [];

      for (let i = 0; i < files.length; i++) {
        let file = files[i];
        const fileType = getFileType(file);

        if (excludedTypes && excludedTypes?.includes(fileType)) {
          toast.error({
            message: `Some of the files are not supported on this device. They would be removed from the selection`,
            title: "Error!",
          });
          continue;
        }

        if (file.size > 1024 * 1024 * maxFileSize) {
          toast.error({
            message: `Some of the files are too large. They would be removed from the selection`,
            title: "Error!",
          });
          continue;
        }

        if (currentMedias.some(({ key }) => generateKey(file) === key)) {
          toast.error({
            message: `${file.name} has been selected previously. It would be removed from the selection.`,
            title: "Error!",
          });
          continue;
        }

        if (fileType === undefined) {
          toast.error({
            message: `Some of the files have unknown types. They would be removed from the selection`,
            title: "Error!",
          });
          continue;
        }

        if (fileType === MediaType.IMAGE) {
          const fileIsHeic = file.name.toLowerCase().includes(".heic");

          if (fileIsHeic) {
            try {
              file = await convertHeicToJpeg(file);
            } catch (e) {
              //do  nothing
            }
          }

          try {
            const resizedImg: {
              blob: Blob;
              url: string;
              dataUrl: string;
            } = await new Promise((res, rej) => {
              const blobURL = URL.createObjectURL(file);

              const img = new Image();
              img.src = blobURL;

              img.addEventListener("error", (e) => {
                URL.revokeObjectURL(blobURL);
                rej(e);
              });

              img.addEventListener("load", async () => {
                const resizedImage = await handleImageResizeOnCanvas(img, maxImageSize, blobURL);
                res(resizedImage);
              });
            });

            const m: Media = {
              src: resizedImg.url,
              name: file.name,
              lastModified: file.lastModified,
              file: resizedImg.blob as File,
              isUploading: false,
              uploadProgress: 0,
              url: "",
              type: fileType,
              key: generateKey(file),
              meta: {
                id: generateSimpleUUID(),
              },
            };
            medias.push(m);
            continue;
          } catch (error) {
            toast.error({ title: "Error processing media files!", message: "Error occurred" });
          }
        }

        const m: Media = {
          name: file.name,
          lastModified: file.lastModified,
          file: file,
          src: URL.createObjectURL(file),
          thumbnail: "",
          type: getFileType(file),
          key: generateKey(file),
          meta: {
            id: generateSimpleUUID(),
          },
        };

        medias.push(m);
      }

      if (Boolean(max) && medias.length > max) {
        const toPick = max - medias.length;
        medias = medias.slice(0, toPick);
        toast.error({ message: `You can only add ${max} items`, title: "Error!" });
      }
      onComplete([...(includeCurrentMedias ? currentMedias : []), ...medias]);
    }
  };
  return { processFiles };
}
export default useMediaProcessor;
