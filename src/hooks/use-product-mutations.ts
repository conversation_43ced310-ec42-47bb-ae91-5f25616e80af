import { use<PERSON>out<PERSON>, RouteProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '@/@types/navigation';
import { useApi } from './use-api';
import { Alert, Share } from 'react-native';
import { delay, getProductLink, hideLoader, showError, showLoader, toCurrency } from '@/assets/utils/js';
import Toast from 'react-native-toast-message';
import useAuthContext from 'src/contexts/auth/auth-context';
import {
  CONVERT_IMAGES_TO_STRINGS,
  DELETE_ITEM,
  EDIT_ITEM,
  UPDATE_FEATURED_ITEM,
  ProductItemInterface,
  EditItemParams,
  toAppUrl,
} from 'catlog-shared';

//TODO: Success response and error response feedback
//TODO: Loading State for feedback
//TODO: Invalidate data after success mutation to successfully update front end data

function useProductMutations() {
  const updateFeatureMutation = useApi({
    apiFunction: UPDATE_FEATURED_ITEM,
    method: 'PUT',
    key: 'update-featured-item',
  });

  const imageToStringMutation = useApi({
    apiFunction: CONVERT_IMAGES_TO_STRINGS,
    method: 'POST',
    key: 'convert-images-to-strings',
  });
  const deleteSingleProductMutation = useApi({ apiFunction: DELETE_ITEM, method: 'DELETE', key: 'delete-item' });
  const updateProductMutation = useApi<EditItemParams>({ apiFunction: EDIT_ITEM, method: 'PUT', key: 'edit-item' });

  const handleOnPressDelete = async (product: Partial<ProductItemInterface>, callback?: VoidFunction) => {
    Alert.alert(
      'Delete Product',
      "Customers won't see this product again, set it to unavailable instead if it is out of stock.",
      [
        { text: 'Delete', onPress: () => deleteFunction(), style: 'destructive' },
        { text: 'Cancel', onPress: () => {} },
      ],
    );

    const deleteFunction = async () => {
      const [response, error] = await deleteSingleProductMutation.makeRequest({
        id: product?.id!,
      });

      if (response) {
        if (callback) callback();
      }

      if (error) {
        showError(error);
      }
    };
  };

  const handleProductShare = async (product: Partial<ProductItemInterface>, callback?: VoidFunction) => {
    const productLink = getProductLink(product);
    let pictureBase64 = '';
    if (product?.images?.length) {
      showLoader('Loading...');
      const [response, error] = await imageToStringMutation.makeRequest({
        images: [product?.images![product?.thumbnail!]],
      });
      hideLoader();
      pictureBase64 = response?.data[0];
    }

    const message = `${product?.name}\nPrice: ${toCurrency(
      product?.discount_price ? product?.discount_price : product?.price,
    )}\nClick here to buy: ${productLink}`;
    Share.share({
      title: product.name,
      message: message,
      url: pictureBase64,
    });
  };

  const handleOnPressFeature = async (product: Partial<ProductItemInterface>, callback?: VoidFunction) => {
    showLoader('Updating featured Items');
    const [response, error] = await updateFeatureMutation.makeRequest({
      id: product?.id!,
      item: {
        //TODO: add 'is_featured' to EditItemParams
        is_featured: !product?.is_featured, //todo: @silas take a look at this
      },
    });
    hideLoader();
    await delay(400);

    if (response) {
      Toast.show({ type: 'success', text1: 'Item featured status updated' });
      if (callback) callback();
      return;
    }

    if (error) {
      showError(error);
    }
  };

  const handleEditProduct = async (
    product: Partial<ProductItemInterface>,
    editData: EditItemParams['item'],
    callback?: VoidFunction,
  ) => {
    const [response, error] = await updateProductMutation.makeRequest({
      id: product.id!,
      item: editData,
    });

    if (response) {
      if (callback) callback();
    }

    if (error) {
      showError(error);
      // console.log(error);
    }
  };

  const navigation = useNavigation();

  const duplicateProduct = (product: ProductItemInterface) => {
    const productData = {
      ...product,
      name: `${product?.name} (Duplicate)`,
      images: [],
      quantity: product?.quantity ? product?.quantity : 0,
      discount_price: Boolean(product.discount_price) ? Number(product.discount_price) : 0,
      ...(product.expiry_date && { expiry_date: product.expiry_date }),
    };

    navigation.navigate('EditProduct', {
      product: productData,
      isDuplicate: true,
    });
  };

  return {
    handleOnPressFeature,
    handleProductShare,
    handleOnPressDelete,
    handleEditProduct,
    duplicateProduct,
    updateLoading: updateProductMutation.isLoading,
    deleteLoading: deleteSingleProductMutation.isLoading,
  };
}

export default useProductMutations;
