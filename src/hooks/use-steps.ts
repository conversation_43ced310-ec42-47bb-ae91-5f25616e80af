import { useState } from 'react';

type StepKeys = string;

export type StepState<T extends StepKeys> = {
  [key in T]: boolean;
};

export interface UseStepsData<T extends StepKeys> {
  step: T;
  changeStep: (step: T) => void;
  stepIndex: number;
  isActive: (s: T) => boolean;
  steps: T[];
  next: () => void;
  previous: () => void;
  canNext: boolean;
  canPrevious: boolean;
}

const useSteps = <T extends StepKeys>(steps: T[], active: number = 0): UseStepsData<T> => {
  const [step, setStep] = useState<T>(steps[active]);
  const canNext = steps.indexOf(step) < steps.length - 1;
  const canPrevious = steps.indexOf(step) > 0;

  const isActive = (s: T) => {
    return step === s;
  };

  const handleNext = () => {
    if (canNext) {
      const index = steps.indexOf(step);
      setStep(steps[index + 1]);
    }
  };

  const handlePrevious = () => {
    if (canPrevious) {
      const index = steps.indexOf(step);
      setStep(steps[index - 1]);
    }
  };

  return {
    step,
    changeStep: setStep,
    stepIndex: steps.indexOf(step),
    isActive,
    steps,
    next: handleNext,
    previous: handlePrevious,
    canNext,
    canPrevious,
  };
};

export default useSteps;
