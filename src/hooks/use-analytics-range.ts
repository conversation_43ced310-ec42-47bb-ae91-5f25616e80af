import { useMemo, useState } from "react";
import { TimeRange } from "src/@types/utils";
import { CustomRange, getFilter } from "src/components/ui/graph/area-graph";
import useModals from "src/hooks/use-modals";

function useAnalyticsRange() {
    const [range, setRange] = useState(TimeRange.THIS_YEAR);
    const [customRange, setCustomRange] = useState<CustomRange>({ startDate: null, endDate: null });
    const { modals, toggleModal } = useModals(['calendar'])

    const handleSelectRange = (selectedRange: TimeRange) => {
        if (selectedRange === TimeRange.CUSTOM) {
            toggleModal("calendar", true)
            // return;
        }
        setRange(selectedRange);
    };

    const analyticsRange = useMemo(() => {
        return getFilter(range, customRange);
    }, [range, customRange]);

    return {
        showCalendar: modals.calendar,
        handleSelectRange,
        analyticsRange,
        customRange,
        setCustomRange,
        range,
        setRange,
        toggleCalendar: (s?: boolean) => toggleModal("calendar", s),
    }
}


export default useAnalyticsRange;