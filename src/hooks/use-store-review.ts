import { useState, useEffect, useMemo, useCallback } from 'react';
import * as StoreReview from 'expo-store-review';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';

interface ReviewState {
  hasReviewed: boolean;
  lastDeclined: number | null;
  neverAskAgain: boolean;
}

const STORAGE_KEY = 'store_review_state';
const DECLINE_COOLDOWN_DAYS = 3;
const DECLINE_COOLDOWN_MS = DECLINE_COOLDOWN_DAYS * 24 * 60 * 60 * 1000;

export const useStoreReview = (requestOnMount = false) => {
  const [isLoading, setIsLoading] = useState(true);
  const [canRequestReview, setCanRequestReview] = useState(false);

  const { isFeatureEnabled } = useFeatureFlags();

  const storeReviewFlagEnabled = useMemo(() => isFeatureEnabled('store_reviews'), []);

  // Load review state from storage
  useEffect(() => {
    loadReviewState();
  }, []);

  useEffect(() => {
    if (storeReviewFlagEnabled && canRequestReview && requestOnMount) {
      requestReview();
    }
  }, [canRequestReview, storeReviewFlagEnabled, requestOnMount]);

  const loadReviewState = async () => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      const reviewState: ReviewState = stored
        ? JSON.parse(stored)
        : { hasReviewed: false, lastDeclined: null, neverAskAgain: false };

      const canRequest = await determineCanRequestReview(reviewState);
      setCanRequestReview(canRequest);
    } catch (error) {
      console.error('Error loading review state:', error);
      // Default to allowing review request if there's an error
      setCanRequestReview(true);
    } finally {
      setIsLoading(false);
    }
  };

  const determineCanRequestReview = async (reviewState: ReviewState): Promise<boolean> => {
    // Check if store review is available on this device
    const isAvailable = await StoreReview.isAvailableAsync();
    if (!isAvailable) return false;

    // Never ask again if user already reviewed
    if (reviewState.hasReviewed) return false;

    // Never ask again if user explicitly opted out
    if (reviewState.neverAskAgain) return false;

    // Check cooldown period after decline
    if (reviewState.lastDeclined) {
      const timeSinceDecline = Date.now() - reviewState.lastDeclined;
      if (timeSinceDecline < DECLINE_COOLDOWN_MS) return false;
    }

    return true;
  };

  const saveReviewState = async (newState: Partial<ReviewState>) => {
    try {
      const current = await AsyncStorage.getItem(STORAGE_KEY);
      const currentState: ReviewState = current
        ? JSON.parse(current)
        : { hasReviewed: false, lastDeclined: null, neverAskAgain: false };

      const updatedState = { ...currentState, ...newState };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
    } catch (error) {
    }
  };

  const requestReview = async (): Promise<boolean> => {
    if (!canRequestReview) {
      return false;
    }

    try {
      // Request the review
      await StoreReview.requestReview();

      // Mark as reviewed (we assume the user completed the review)
      await saveReviewState({ hasReviewed: true });
      setCanRequestReview(false);

      return true;
    } catch (error) {
      console.error('Error requesting review:', error);
      markDeclined();
      return false;
    }
  };

  const markDeclined = async () => {
    await saveReviewState({ lastDeclined: Date.now() });
    setCanRequestReview(false);
  };

  const markNeverAskAgain = async () => {
    await saveReviewState({ neverAskAgain: true });
    setCanRequestReview(false);
  };

  const resetReviewState = async () => {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      setCanRequestReview(true);
    } catch (error) {
      console.error('Error resetting review state:', error);
    }
  };

  const getReviewState = async (): Promise<ReviewState | null> => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error getting review state:', error);
      return null;
    }
  };

  const getDaysUntilNextRequest = async (): Promise<number | null> => {
    const state = await getReviewState();
    if (!state?.lastDeclined) return null;

    const timeSinceDecline = Date.now() - state.lastDeclined;
    const timeUntilNext = DECLINE_COOLDOWN_MS - timeSinceDecline;

    if (timeUntilNext <= 0) return 0;

    return Math.ceil(timeUntilNext / (24 * 60 * 60 * 1000));
  };

  const handleReviewRequest = useCallback(() => {
    if (storeReviewFlagEnabled && canRequestReview) {
      requestReview();
    }
  }, [storeReviewFlagEnabled, canRequestReview]);


  return {
    isLoading,
    storeReviewFlagEnabled,
    canRequestReview,
    requestReview,
    markDeclined,
    markNeverAskAgain,
    resetReviewState, // Useful for testing
    getReviewState, // Useful for debugging
    getDaysUntilNextRequest,
    handleReviewRequest,
  };
};
