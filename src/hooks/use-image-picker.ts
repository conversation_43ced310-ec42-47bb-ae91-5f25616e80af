import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';

interface ImageInfo {
  uri: string;
  width: number;
  height: number;
  type?: string;
}

interface ImagePickerOptions {
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  mediaTypes?: ImagePicker.MediaTypeOptions;
  base64?: boolean;
  exif?: boolean;
}

interface UseImagePickerReturn {
  selectedImage: ImageInfo[] | null;
  error: string | null;
  loading: boolean;
  pickImage: () => Promise<ImagePicker.ImagePickerAsset[] | undefined>;
  takePhoto: () => Promise<ImagePicker.ImagePickerAsset[] | undefined>;
  reset: () => void;
}

const useImagePicker = (options: ImagePickerOptions = {}): UseImagePickerReturn => {
  const [selectedImage, setSelectedImage] = useState<ImageInfo[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const requestMediaLibraryPermissions = async (): Promise<boolean> => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      setError('Permission to access media library was denied');
      return false;
    }
    return true;
  };

  const requestCameraPermissions = async (): Promise<boolean> => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      setError('Permission to access camera was denied');
      return false;
    }
    return true;
  };

  const pickImage = async (): Promise<ImagePicker.ImagePickerAsset[] | undefined> => {
    try {
      setLoading(true);
      setError(null);

      const hasPermission = await requestMediaLibraryPermissions();
      if (!hasPermission) return;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: options.mediaTypes ?? ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 0.1,
        aspect: options.aspect,
        base64: options.base64,
        exif: options.exif,
      });

      if (!result.canceled && result.assets.length > 0) {
        const selectedAsset = result.assets;
        setSelectedImage(selectedAsset);
        return selectedAsset;
      }
    } catch (err) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const takePhoto = async (): Promise<ImagePicker.ImagePickerAsset[] | undefined> => {
    try {
      setLoading(true);
      setError(null);

      const hasPermission = await requestCameraPermissions();
      if (!hasPermission) return;

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: options.mediaTypes ?? ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 1,
        aspect: options.aspect,
        base64: options.base64,
        exif: options.exif,
      });

      if (!result.canceled && result.assets.length > 0) {
        const selectedAsset = result.assets;
        setSelectedImage(selectedAsset);
        return selectedAsset;
      }
    } catch (err) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const reset = (): void => {
    setSelectedImage(null);
    setError(null);
  };

  return {
    selectedImage,
    error,
    loading,
    pickImage,
    takePhoto,
    reset,
  };
};

export default useImagePicker;