import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useState, useCallback } from 'react';
import { Platform } from 'react-native';
import { EXPO_PUBLIC_API_URL } from 'src/configs-files/@env';
import useAuthStore from 'src/contexts/auth/store';

const BASE_URL = EXPO_PUBLIC_API_URL;

interface ProcessResult {
  type: 'pdf' | 'xlsx';
  uri: string;
  mimeType: string;
}

interface FileTypes {
  extension: string;
  mimeType: string;
  uti: string;
}

interface UseFileDownloadResult {
  downloadFile: (url: string, filename: string) => Promise<ProcessResult>;
  processBlob: (response: Response, filename: string) => Promise<ProcessResult>;
  isLoading: boolean;
  error: Error | null;
}

const FILE_TYPES: Record<'pdf' | 'xlsx', FileTypes> = {
  pdf: {
    extension: '.pdf',
    mimeType: 'application/pdf',
    uti: 'com.adobe.pdf',
  },
  xlsx: {
    extension: '.xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    uti: 'org.openxmlformats.spreadsheetml.sheet',
  },
};

/**
 * This exposes downloadFile, processBlob, isLoading, and error
 * downloadFile takes url and fileName make sure the file name has the extension of the file eiteher pdf or xlsx
 *
 */
export const useFileDownload = (): UseFileDownloadResult => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const reqPermission = async () => {
    try {
      const permissions = await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();
      return permissions;
    } catch (error) {}
  };

  const processBlob = useCallback(async (response: Response, filename: string): Promise<ProcessResult> => {
    try {
      // Check if response is valid
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      if (!filename) {
        throw new Error('Filename is required');
      }

      // Get file type from filename
      const isExcel = filename.toLowerCase().endsWith('.xlsx');
      const fileType = isExcel ? 'xlsx' : 'pdf';
      const { extension, mimeType, uti } = FILE_TYPES[fileType];

      // Ensure filename has correct extension
      const sanitizedFilename = filename.endsWith(extension) ? filename : `${filename}${extension}`;

      // Create a temporary file URI in the cache directory
      const fileUri = `${FileSystem.cacheDirectory}${sanitizedFilename}`;

      // Get the blob data
      const blobData = await response.blob();
      const blob = new Blob([blobData]);

      // Convert blob to base64
      const reader = new FileReader();

      const base64Promise = new Promise<string>((resolve, reject) => {
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result.split(',')[1]);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      const base64Data = await base64Promise;
      // Write the file
      await FileSystem.writeAsStringAsync(fileUri, base64Data, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Share the file if sharing is available
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Open file',
          UTI: uti,
        });
      }

      return {
        type: fileType,
        uri: fileUri,
        mimeType,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Error processing file: ${errorMessage}`);
    }
  }, []);

  const downloadFile = useCallback(
    async (url: string, filename: string): Promise<ProcessResult> => {
      setError(null);
      setIsLoading(true);

      const token = useAuthStore.getState().token;

      const fileUrl = `${BASE_URL}/${url}`;

      const headers = {
        ...(token && { Authorization: `Bearer ${token}` }),
      };

      const config = {
        method: 'GET',
        headers,
      };

      try {
        if (Platform.OS === 'android') {
          const permissionResponse = await reqPermission();
          if (!permissionResponse?.granted) throw error;
        }

        const response = await fetch(fileUrl, config);
        const result = await processBlob(response, filename);
        return result;
      } catch (error) {
        const newError = error instanceof Error ? error : new Error('Unknown error occurred');
        setError(newError);
        throw newError;
      } finally {
        setIsLoading(false);
      }
    },
    [processBlob],
  );

  return {
    downloadFile,
    processBlob,
    isLoading,
    error,
  };
};
