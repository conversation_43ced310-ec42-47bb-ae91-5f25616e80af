import { useState, useCallback } from 'react';
import { FFmpegKit, FFmpegKitConfig, ReturnCode } from 'ffmpeg-kit-react-native';
import * as FileSystem from 'expo-file-system';

interface TrimResult {
  success: boolean;
  outputPath?: string;
  size?: number;
  message?: string;
  error?: string;
}

export const useVideoTrimmer = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  const trimVideo = useCallback(
    async (inputPath: string, startTimeMs: number, endTimeMs: number, outputPath = null): Promise<TrimResult> => {
      try {
        setIsProcessing(true);
        setProgress(0);
        setError(null);

        // Validate inputs
        if (!inputPath || startTimeMs == null || endTimeMs == null) {
          throw new Error('Input path, start time, and end time are required');
        }

        if (startTimeMs >= endTimeMs) {
          throw new Error('Start time must be less than end time');
        }

        // Generate output path if not provided
        if (!outputPath) {
          const timestamp = Date.now();
          const fileName = `trimmed_video_${timestamp}.mp4`;
          outputPath = `${FileSystem.documentDirectory}${fileName}`;
        }

        // Check if input file exists
        const inputExists = await FileSystem.getInfoAsync(inputPath);
        if (!inputExists.exists) {
          throw new Error('Input video file does not exist');
        }

        // Convert milliseconds to seconds for FFmpeg
        const startTime = Math.ceil(startTimeMs / 1000);
        const endTime = Math.ceil(endTimeMs / 1000);
        const duration = endTime - startTime;

        // Build FFmpeg command
        const command = [
          '-i',
          inputPath,
          '-ss',
          startTime.toString(),
          '-t',
          duration.toString(),
          '-c:v',
          'libx264',
          '-crf',
          '28',
          '-preset',
          'fast',
          '-movflags',
          '+faststart',
          '-avoid_negative_ts',
          'make_zero',
          '-y', // Overwrite output file if it exists
          outputPath,
        ].join(' ');

        // Wait for session to complete
        const session = await FFmpegKit.execute(command);

        /* const session = await new Promise<any>((resolve, reject) => {

         const statisticsCallback = stats => {
           if (!stats) return;
           const timeMs = stats.getTime();
           if (timeMs >= 0 && durationMs > 0) {
             const pct = Math.min(100, (timeMs / durationMs) * 100);
             setProgress(Math.floor(pct));
           }
         };

         // Optional: capture logs if you want
         const logCallback = log => {
           // console.log(log.getMessage());
         };

           // Completion callback
         FFmpegKit.executeAsync(
           command,
           async completedSession => {
             // Clean up callback
             FFmpegKitConfig.enableStatisticsCallback(null);
             resolve(completedSession);
           },
           logCallback,
           statisticsCallback,
         ).catch(err => {
           FFmpegKitConfig.enableStatisticsCallback(null);
           reject(err);
         });
       }); */


        const returnCode = await session.getReturnCode();

        // Disable statistics callback
        FFmpegKitConfig.enableStatisticsCallback(null);

        if (ReturnCode.isSuccess(returnCode)) {
          // Verify output file was created
          const outputInfo = await FileSystem.getInfoAsync(outputPath);
          if (!outputInfo.exists) {
            throw new Error('Output file was not created');
          }

          setProgress(100);
          setIsProcessing(false);

          return {
            success: true,
            outputPath,
            size: outputInfo.size,
            message: 'Video trimmed successfully',
          };
        } else {
          const logs = await session.getLogs();
          const errorMessage = logs.map(log => log.getMessage()).join('\n');
          throw new Error(`FFmpeg execution failed: ${errorMessage}`);
        }
      } catch (err) {
        console.error('Video trimming error:', err);
        setError(err.message);
        setIsProcessing(false);
        setProgress(0);

        return {
          success: false,
          error: err.message,
        };
      }
    },
    [],
  );

  const cancelProcessing = useCallback(async () => {
    try {
      await FFmpegKit.cancel();
      setIsProcessing(false);
      setProgress(0);
      setError('Processing cancelled by user');
    } catch (err) {
      console.error('Error cancelling FFmpeg session:', err);
    }
  }, []);

  const resetState = useCallback(() => {
    setIsProcessing(false);
    setProgress(0);
    setError(null);
  }, []);

  return {
    // State
    isProcessing,
    progress,
    error,

    // Methods
    trimVideo,
    cancelProcessing,
    resetState,

    // Utilities
    // formatTime,
  };
};
