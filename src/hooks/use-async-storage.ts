import AsyncStorage from '@react-native-async-storage/async-storage';
import { useState, useCallback } from 'react';

/**
 * A hook that provides a wrapper around AsyncStorage with error handling and loading states
 */
export const useAsyncStorage = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Store a string value in AsyncStorage
   */
  const setItem = useCallback(async (key: string, value: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await AsyncStorage.setItem(key, value);
      return true;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to store data');
      setError(err);
      console.error('AsyncStorage setItem error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Store an object value in AsyncStorage (serialized as JSO<PERSON>)
   */
  const setObject = useCallback(async <T>(key: string, value: T): Promise<boolean> => {
    try {
      const jsonValue = JSON.stringify(value);
      return await setItem(key, jsonValue);
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to serialize and store data');
      setError(err);
      console.error('AsyncStorage setObject error:', err);
      return false;
    }
  }, [setItem]);

  /**
   * Retrieve a string value from AsyncStorage
   */
  const getItem = useCallback(async (key: string): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const value = await AsyncStorage.getItem(key);
      return value;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to retrieve data');
      setError(err);
      console.error('AsyncStorage getItem error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Retrieve an object value from AsyncStorage (parsed from JSON)
   */
  const getObject = useCallback(async <T>(key: string, defaultValue?: T): Promise<T | null> => {
    try {
      const jsonValue = await getItem(key);
      if (jsonValue === null) return defaultValue ?? null;
      return JSON.parse(jsonValue) as T;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to parse retrieved data');
      setError(err);
      console.error('AsyncStorage getObject error:', err);
      return defaultValue ?? null;
    }
  }, [getItem]);

  /**
   * Remove an item from AsyncStorage
   */
  const removeItem = useCallback(async (key: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to remove data');
      setError(err);
      console.error('AsyncStorage removeItem error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Clear all data from AsyncStorage
   */
  const clear = useCallback(async (): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await AsyncStorage.clear();
      return true;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to clear storage');
      setError(err);
      console.error('AsyncStorage clear error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get all keys from AsyncStorage
   */
  const getAllKeys = useCallback(async (): Promise<string[] | null> => {
    setLoading(true);
    setError(null);
    try {
      const keys = await AsyncStorage.getAllKeys();
      return [...keys];
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to get all keys');
      setError(err);
      console.error('AsyncStorage getAllKeys error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get multiple items from AsyncStorage
   */
  const multiGet = useCallback(async (keys: string[]): Promise<[string, string | null][] | null> => {
    setLoading(true);
    setError(null);
    try {
      const items = await AsyncStorage.multiGet(keys);
      return [...items];
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to get multiple items');
      setError(err);
      console.error('AsyncStorage multiGet error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Set multiple items in AsyncStorage
   */
  const multiSet = useCallback(async (keyValuePairs: [string, string][]): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await AsyncStorage.multiSet(keyValuePairs);
      return true;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to set multiple items');
      setError(err);
      console.error('AsyncStorage multiSet error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Remove multiple items from AsyncStorage
   */
  const multiRemove = useCallback(async (keys: string[]): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await AsyncStorage.multiRemove(keys);
      return true;
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to remove multiple items');
      setError(err);
      console.error('AsyncStorage multiRemove error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Log all AsyncStorage contents (for debugging)
   */
  const logAllStorage = useCallback(async (): Promise<[string, string | null][] | null> => {
    setLoading(true);
    setError(null);
    try {
      const keys = await AsyncStorage.getAllKeys();
      console.log('All AsyncStorage keys:', keys);

      const items = await AsyncStorage.multiGet(keys);
      console.log('All AsyncStorage contents:');
      items.forEach(([key, value]) => {
        console.log(`${key}: ${value}`);
      });

      return [...items];
    } catch (e) {
      const err = e instanceof Error ? e : new Error('Failed to log storage');
      setError(err);
      console.error('AsyncStorage logAllStorage error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    // Basic operations
    setItem,
    getItem,
    removeItem,
    clear,
    
    // Object operations (with JSON serialization)
    setObject,
    getObject,
    
    // Batch operations
    getAllKeys,
    multiGet,
    multiSet,
    multiRemove,
    
    // Utility
    logAllStorage,
    
    // State
    loading,
    error,
  };
};

export default useAsyncStorage;
