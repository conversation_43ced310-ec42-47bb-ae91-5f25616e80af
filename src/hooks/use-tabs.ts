import { useEffect, useState, useCallback, useMemo } from 'react';

interface UseTabs {
  tabs: string[];
  initialActiveTab?: number | string;
}

const useTabs = ({ tabs, initialActiveTab = 0 }: UseTabs) => {
  // Memoize keys to prevent unnecessary recalculations
  const keys = useMemo(() => tabs.map(tab => tab.toLocaleLowerCase().replace(/\s+/g, '_')), [tabs]);

  // Determine initial active index
  const getInitialActiveIndex = useCallback(() => {
    // If initialActiveTab is a number and within range, use it
    if (typeof initialActiveTab === 'number') {
      return initialActiveTab >= 0 && initialActiveTab < tabs.length ? initialActiveTab : 0;
    }

    // If initialActiveTab is a string, find matching index
    if (typeof initialActiveTab === 'string') {
      const normalizedInput = initialActiveTab.toLocaleLowerCase().replace(/\s+/g, '_');
      const matchedIndex = keys.findIndex(key => key === normalizedInput);
      return matchedIndex !== -1 ? matchedIndex : 0;
    }

    // Fallback to 0 if no valid input
    return 0;
  }, [initialActiveTab, tabs, keys]);

  const [active, setActive] = useState(getInitialActiveIndex());

  // Optimize tab switching with useCallback
  const switchByKey = useCallback(
    (key: string) => {
      const keyId = keys.findIndex(k => k === key);
      setActive(keyId !== -1 ? keyId : 0);
    },
    [keys],
  );

  // Memoized active state checks
  const isActive = useCallback(
    (key: string) => {
      return keys[active] === key;
    },
    [active, keys],
  );

  return {
    tabs,
    active,
    isActive,
    switchTab: setActive,
    switchByKey,
    activeValue: tabs[active],
    activeKey: keys[active],
  };
};

export default useTabs;
