import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { useState, useCallback } from 'react';
import { Platform } from 'react-native';
import { EXPO_PUBLIC_API_URL } from 'src/configs-files/@env';
import useAuthStore from 'src/contexts/auth/store';

const BASE_URL = EXPO_PUBLIC_API_URL;

interface ProcessResult {
  type: 'mp4' | 'mov' | 'avi' | 'mkv';
  uri: string;
  size: number;
  mimeType: string;
}

interface FileTypes {
  extension: string;
  mimeType: string;
  uti: string;
}

interface UseVideoDownloadResult {
  downloadVideo: (url: string, filename: string) => Promise<ProcessResult>;
  processBlob: (response: Response, filename: string) => Promise<ProcessResult>;
  // isLoading: boolean;
  // downloadProgress: number;
  // error: Error | null;
}

const VIDEO_TYPES: Record<'mp4' | 'mov' | 'avi' | 'mkv', FileTypes> = {
  mp4: { extension: '.mp4', mimeType: 'video/mp4', uti: 'public.mpeg-4' },
  mov: { extension: '.mov', mimeType: 'video/quicktime', uti: 'com.apple.quicktime-movie' },
  avi: { extension: '.avi', mimeType: 'video/x-msvideo', uti: 'public.avi' },
  mkv: { extension: '.mkv', mimeType: 'video/x-matroska', uti: 'org.matroska.mkv' },
};

/**
 * This exposes downloadVideo, processBlob, isLoading, downloadProgress, and error
 * downloadVideo takes url and fileName - make sure the file name has the extension of the video file (mp4, mov, avi, mkv)
 * Videos are saved to cache directory for fast access
 */
export const useVideoDownload = (): UseVideoDownloadResult => {
  // const [isLoading, setIsLoading] = useState(false);
  // const [downloadProgress, setDownloadProgress] = useState(0);
  // const [error, setError] = useState<Error | null>(null);

  const reqPermission = async () => {
    try {
      const permissions = await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();
      return permissions;
    } catch (error) {
      return null;
    }
  };

  const getVideoType = (filename: string): 'mp4' | 'mov' | 'avi' | 'mkv' => {
    const lower = filename.toLowerCase();
    if (lower.endsWith('.mov')) return 'mov';
    if (lower.endsWith('.avi')) return 'avi';
    if (lower.endsWith('.mkv')) return 'mkv';
    return 'mp4'; // default to mp4
  };

  const processBlob = useCallback(async (response: Response, filename: string): Promise<ProcessResult> => {
    try {
      // Check if response is valid
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      if (!filename) {
        throw new Error('Filename is required');
      }

      // Get video type from filename
      const videoType = getVideoType(filename);
      const { extension, mimeType, uti } = VIDEO_TYPES[videoType];

      // Ensure filename has correct extension
      const sanitizedFilename = filename.endsWith(extension) ? filename : `${filename}${extension}`;

      // Create a file URI in the cache directory
      const fileUri = `${FileSystem.cacheDirectory}${sanitizedFilename}`;

      // Get content length for progress tracking
      const contentLength = response.headers.get('content-length');
      const total = contentLength ? parseInt(contentLength, 10) : 0;

      // Get the response as array buffer for better performance with large files

      const blobData = await response.blob();
      const blob = new Blob([blobData]);

      // Convert blob to base64

      const base64String = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          // Remove data URL prefix (data:video/mp4;base64,)
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      // Write the file
      await FileSystem.writeAsStringAsync(fileUri, base64String, { encoding: FileSystem.EncodingType.Base64 });

      return { type: videoType, uri: fileUri, size: blobData.size, mimeType };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Error processing video: ${errorMessage}`);
    }
  }, []);

  const downloadVideo = useCallback(
    async (url: string, filename: string, callback?: ({ progress, error }) => void): Promise<ProcessResult> => {
      // setError(null);
      // setIsLoading(true);
      callback?.({ progress: 0, error: null });

      const token = useAuthStore.getState().token;

      const videoUrl = `${BASE_URL}/utils/download-file?url=${btoa(unescape(encodeURIComponent(url)))}`;

      const headers = { ...(token && { Authorization: `Bearer ${token}` }) };

      const config = { method: 'GET', headers };

      try {
        if (Platform.OS === 'android') {
          const permissionResponse = await reqPermission();
          if (!permissionResponse?.granted) {
            throw new Error('Storage permission not granted');
          }
        }

        const response = await fetch(videoUrl, config);
        const result = await processBlob(response, filename);
        callback?.({ progress: 100, error: null });
        return result;
      } catch (error) {
        const newError = error instanceof Error ? error : new Error('Unknown error occurred');
        callback?.({ progress: 0, error: newError });
        throw newError;
      }
    },
    [processBlob],
  );

  return {
    downloadVideo,
    processBlob,
    // isLoading,
    // downloadProgress,
    // error,
  };
};
