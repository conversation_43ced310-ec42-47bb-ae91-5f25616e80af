import AsyncStorage from '@react-native-async-storage/async-storage';
import { DependencyList, useEffect, useState } from 'react';

export function useLocalObject<T extends Object | any[]>(
  key: string,
  defaultValue?: T,
  deps?: DependencyList,
): [T, (value?: T) => Promise<void>] {
  const [state, set] = useState<T>(defaultValue!);

  useEffect(() => {
    const fn = async () => {
      const currentState = await AsyncStorage.getItem(key);
      if (currentState) {
        const value = JSON.parse(currentState) as T;
        set(value);
      }
    };
    fn();
  }, deps ?? []);

  const setState = async (value?: Partial<T>) => {
    const isArray = Array.isArray(value);
    const finalValue = isArray ? (value as T) : ({ ...state, ...value } as T); //add new keys to objects

    if (value !== undefined) {
      const json = JSON.stringify(finalValue);
      await AsyncStorage.setItem(key, json);
      set(finalValue);
    } else {
      await AsyncStorage.removeItem(key);
      set(undefined!);
    }
  };

  return [state, setState];
}
