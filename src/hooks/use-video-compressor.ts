import { useState, useCallback } from 'react';
import { FFmpegKit, ReturnCode, FFmpegKitConfig } from 'ffmpeg-kit-react-native';
import * as FileSystem from 'expo-file-system';

const useVideoCompression = () => {
  const [isCompressing, setIsCompressing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  const compressVideo = useCallback(async (inputUri, outputFileName = null) => {
    try {
      setIsCompressing(true);
      setProgress(0);
      setError(null);

      // Generate output filename if not provided
      const timestamp = Date.now();
      const finalOutputFileName = outputFileName || `compressed_video_${timestamp}.mp4`;
      const outputPath = `${FileSystem.documentDirectory}${finalOutputFileName}`;

      // Get video duration for progress calculation
      let videoDuration = 0;
      const durationCommand = `-i "${inputUri}" -f null -`;
      
      try {
        const durationSession = await FFmpegKit.execute(durationCommand);
        const durationLogs = await durationSession.getLogs();
        
        // Parse duration from logs
        for (const log of durationLogs) {
          const logMessage = log.getMessage();
          const durationMatch = logMessage.match(/Duration: (\d{2}):(\d{2}):(\d{2})/);
          if (durationMatch) {
            const hours = parseInt(durationMatch[1], 10);
            const minutes = parseInt(durationMatch[2], 10);
            const seconds = parseInt(durationMatch[3], 10);
            videoDuration = hours * 3600 + minutes * 60 + seconds;
            break;
          }
        }
      } catch (durationError) {
        console.warn('Could not parse video duration:', durationError);
      }

      // Fallback duration if parsing fails
      if (videoDuration === 0) {
        videoDuration = 60; // Default to 60 seconds
      }

      // Optimized compression command with proper scaling and quality settings
      const compressionCommand = [
        `-i "${inputUri}"`,
        `-vf "scale='if(gt(iw,1280),1280,iw)':'if(gt(ih,720),720,ih)':force_original_aspect_ratio=decrease:force_divisible_by=2"`,
        `-c:v libx264`,
        `-crf 30`,
        `-preset fast`,
        `-movflags +faststart`,
        `-c:a aac`,
        `-b:a 64k`,
        `"${outputPath}"`
      ].join(' ');

      // Enable statistics callback for progress tracking
      FFmpegKitConfig.enableStatisticsCallback((statistics) => {
        const timeInMilliseconds = statistics.getTime();
        const currentTime = timeInMilliseconds / 1000; // Convert to seconds
        
        if (videoDuration > 0) {
          const progressPercentage = Math.min((currentTime / videoDuration) * 100, 100);
          setProgress(Math.round(progressPercentage));
        }
      });

      // Execute compression
      const session = await FFmpegKit.execute(compressionCommand);
      const returnCode = await session.getReturnCode();

      // Disable statistics callback
      FFmpegKitConfig.enableStatisticsCallback(null);

      if (ReturnCode.isSuccess(returnCode)) {
        // Get file info
        const fileInfo = await FileSystem.getInfoAsync(outputPath);
        
        setProgress(100);
        setIsCompressing(false);
        
        return {
          success: true,
          outputPath,
          outputUri: outputPath,
          fileSize: fileInfo.size,
          fileName: finalOutputFileName
        };
      } else {
        const logs = await session.getLogs();
        const errorMessage = logs.map(log => log.getMessage()).join('\n');
        
        setError('Video compression failed');
        setIsCompressing(false);
        
        return {
          success: false,
          error: 'Video compression failed',
          details: errorMessage
        };
      }

    } catch (err) {
      console.error('Video compression error:', err);
      
      // Cleanup: Disable statistics callback in case of error
      FFmpegKitConfig.enableStatisticsCallback(null);
      
      setError(err.message);
      setIsCompressing(false);
      
      return {
        success: false,
        error: err.message
      };
    }
  }, []);

  const cancelCompression = useCallback(async () => {
    try {
      await FFmpegKit.cancel();
      FFmpegKitConfig.enableStatisticsCallback(null);
      setIsCompressing(false);
      setProgress(0);
      setError('Compression cancelled');
    } catch (err) {
      console.error('Error cancelling compression:', err);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setIsCompressing(false);
    setProgress(0);
    setError(null);
  }, []);

  return {
    compressVideo,
    cancelCompression,
    clearError,
    reset,
    isCompressing,
    progress,
    error
  };
};

export default useVideoCompression;