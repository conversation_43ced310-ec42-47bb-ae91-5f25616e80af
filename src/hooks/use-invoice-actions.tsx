import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { useNavigation } from '@react-navigation/native';
import {
  OrderInterface,
  InvoiceInterface,
  InvoiceParams,
  DELETE_INVOICE,
  UPDATE_INVOICE_TO_PAID,
  toAppUrl,
  INVOICE_STATUSES,
} from 'catlog-shared';
import { useState } from 'react';
import { Alert } from 'react-native';
import Toast from 'react-native-toast-message';
import { copyToClipboard, delay, hideLoader, showLoader } from 'src/assets/utils/js';

import { ResponseWithPagination, useApi } from './use-api';
import useModals from './use-modals';
import { useFileDownload } from './use-file-download';

interface OrdersResponseWithPagination extends ResponseWithPagination<OrderInterface[]> {}
export interface OrdersResponse {
  data: OrdersResponseWithPagination;
}
const useInvoiceActions = (
  invoices: InvoiceInterface[],
  setInvoices: React.Dispatch<React.SetStateAction<InvoiceInterface[]>>,
) => {
  const [currentInvoice, setCurrentInvoice] = useState<InvoiceInterface>();
  const { modals, toggleModal, switchModals } = useModals([
    'info',
    'search',
    'options',
    'preview',
    'send',
    'previewReceipt',
  ]);

  const navigation = useNavigation();

  const deleteInvoiceReq = useApi<InvoiceParams>({
    apiFunction: DELETE_INVOICE,
    key: DELETE_INVOICE.name,
    method: 'DELETE',
    autoRequest: false,
  });

  const markInvoiceReq = useApi<InvoiceParams>({
    apiFunction: UPDATE_INVOICE_TO_PAID,
    key: UPDATE_INVOICE_TO_PAID.name,
    method: 'PUT',
    autoRequest: false,
  });

  const { downloadFile, isLoading } = useFileDownload();

  const handleDownloadReceipt = async (invoiceId: string) => {
    try {
      if (invoiceId === null) return;

      // const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      await delay(600);
      showLoader('Downloading Invoice', true);

      const subUrl = `invoices/pdf/${invoiceId}`;
      const fileName = `invoice-${invoiceId}.pdf`;
      const downloadResult = await downloadFile(subUrl, fileName);

      hideLoader();
    } catch (error) {
      hideLoader();
      Toast.show({ text1: 'Error processing your request', type: 'error' });
    }
  };

  const openInvoice = (invoice: InvoiceInterface) => {
    setCurrentInvoice(invoice);
    toggleModal('info', true);
  };

  const deleteInvoice = async (id: string) => {
    showLoader('Deleting Invoice...', true);
    const [res, error] = await deleteInvoiceReq.makeRequest({ invoiceId: id });
    if (res) {
      if (invoices.length > 0) {
        const invoiceIndex = invoices.findIndex(i => i.id === id);
        if (invoiceIndex > -1) {
          const newInvoices = [...invoices];
          newInvoices.splice(invoiceIndex, 1);
          setInvoices(newInvoices);
        }
      }
      Toast.show({ type: 'success', text1: 'Invoice deleted successfully' });
    }
    if (error) {
      Toast.show({ type: 'error', text1: error?.message });
    }
  };

  const markInvoiceAsPaid = async (invoice: InvoiceInterface) => {
    showLoader('Marking invoice as paid...', true);
    const [_, err] = await markInvoiceReq.makeRequest({ invoiceId: invoice.id });

    if (err) {
      Toast.show({ type: 'error', text1: err?.message });
    } else {
      Toast.show({ type: 'success', text1: 'Invoice marked as paid successfully' });
      if (invoices.length > 0) {
        const invoiceIndex = invoices.findIndex(i => i.id === invoice.id);
        if (invoiceIndex > -1) {
          const newInvoices = [...invoices];
          newInvoices[invoiceIndex].status = INVOICE_STATUSES.PAID;
          setInvoices(newInvoices);
        }
      }
    }
  };

  const handleInvoiceItemAction = (
    action: 'edit' | 'delete' | 'options' | 'download' | 'copy' | 'preview' | 'send' | 'mark',
    invoice = currentInvoice,
  ) => {
    setCurrentInvoice(invoice);
    // toggleModal('info', false);
    // toggleModal('options', false);

    switch (action) {
      case 'delete': {
        Alert.alert(
          'Delete Invoice',
          'This Invoice will be deleted permanently, if this invoice has been paid - Mark it as paid.',
          [
            {
              text: 'Delete',
              onPress: () => {
                toggleModal('info', false);
                toggleModal('options', false);
                deleteInvoice(invoice?.id ?? '');
              },
              style: 'destructive',
            },
            { text: 'Cancel', onPress: () => {} },
          ],
        );

        break;
      }
      case 'copy': {
        copyToClipboard(toAppUrl(`invoices/${invoice?.invoice_id}`, true, EXPO_PUBLIC_PUBLIC_URL));
        break;
      }
      case 'edit': {
        toggleModal('info', false);
        toggleModal('options', false);
        navigation.navigate('EditInvoice', { invoice });
        break;
      }
      case 'options': {
        switchModals('info', 'options', 700);
        break;
      }
      case 'send': {
        switchModals(modals.options ? 'options' : 'preview', 'send', 600);
        break;
      }
      case 'preview': {
        // switchModals('preview', 'options', 700);
        toggleModal('preview');
        break;
      }
      case 'download': {
        // switchModals(modals.options ? 'options' : 'preview', 'send', 700);
        toggleModal('options', false);
        handleDownloadReceipt(invoice.invoice_id);
        break;
      }
      case 'mark': {
        Alert.alert(
          'Mark as Paid',
          'Do you want to mark as paid? A receipt will be generated and this action is irreversible.',
          [
            {
              text: 'Yes, mark as paid',
              onPress: () => {
                toggleModal('options', false);
                markInvoiceAsPaid(invoice);
              },
              style: 'destructive',
            },
            { text: 'Cancel', onPress: () => {}, style: 'cancel' },
          ],
        );
        break;
      }
    }
  };

  return { openInvoice, handleInvoiceItemAction, currentInvoice, setCurrentInvoice, modals, toggleModal, switchModals };
};

export default useInvoiceActions;
