import { useEffect } from 'react';
import {
  Easing,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

function useScrollHandler(
  height: number,
  analyticsHeight?: number,
  options?: {
    animationDuration?: number;
    hideOnScrollDown?: boolean;
    showOnScrollUp?: boolean;
    scrollThreshold?: number;
    velocityThreshold?: number;
  },
) {
  // Default options
  const {
    animationDuration = 100,
    hideOnScrollDown = true,
    showOnScrollUp = true,
    scrollThreshold = 100,
    velocityThreshold = 0,
  } = options || {};

  const headerHeight = useSharedValue(height);
  const analyticsHeaderHeight = useSharedValue(analyticsHeight ?? 0);
  const scrollY = useSharedValue(0);
  const lastScrollY = useSharedValue(0);
  const scrollDelta = useSharedValue(0);
  const isHeaderVisible = useSharedValue(true);
  const isScrollingDown = useSharedValue(false);
  const lastScrollTime = useSharedValue(0);

  useEffect(() => {
    if (analyticsHeight !== undefined) {
      analyticsHeaderHeight.value = analyticsHeight;
    }
  }, [analyticsHeight]);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      const currentScrollY = event.contentOffset.y;
      const now = Date.now();
      const timeDelta = now - lastScrollTime.value;

      // Calculate velocity (pixels/ms)
      const velocity = timeDelta > 0 ? Math.abs(currentScrollY - lastScrollY.value) / timeDelta : 0;

      // Update scroll delta (accumulated difference)
      scrollDelta.value += currentScrollY - lastScrollY.value;

      // Determine if we've exceeded threshold (either by distance or velocity)
      const exceededDistanceThreshold = Math.abs(scrollDelta.value) >= scrollThreshold;
      const exceededVelocityThreshold = velocityThreshold > 0 && velocity >= velocityThreshold;

      // Only evaluate direction if threshold is exceeded
      if (exceededDistanceThreshold || exceededVelocityThreshold) {
        const currentDirection = currentScrollY > lastScrollY.value;

        // Update header visibility based on scroll direction
        if (currentDirection && hideOnScrollDown) {
          isHeaderVisible.value = false;
        } else if (!currentDirection && showOnScrollUp) {
          isHeaderVisible.value = true;
        }

        // Reset delta when we've acted on it
        scrollDelta.value = 0;
        isScrollingDown.value = currentDirection;
      }

      // Update tracking values
      scrollY.value = currentScrollY;
      lastScrollY.value = currentScrollY;
      lastScrollTime.value = now;
    },
  });

  const headerStyle = useAnimatedStyle(() => {
    return {
      // transform: [{translateY: withTiming(
      //   isHeaderVisible.value ? 0 : -headerHeight.value,
      //   { duration: animationDuration }
      // )}],
      marginTop: withTiming(isHeaderVisible.value ? 0 : -headerHeight.value, {
        duration: animationDuration,
        easing: Easing.ease,
      }),
      paddingHorizontal: 20,
      paddingVertical: 25,
    };
  });

  const analyticsHeaderStyle = useAnimatedStyle(() => {
    return {
      marginTop: withTiming(isHeaderVisible.value ? 0 : -analyticsHeaderHeight.value, {
        duration: animationDuration,
        easing: Easing.ease,
      }),
    };
  });

  const inputStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isHeaderVisible.value ? 1 : 0, { duration: animationDuration, easing: Easing.ease }),
    };
  });

  return { scrollHandler, headerStyle, analyticsHeaderStyle, inputStyle };
}

export default useScrollHandler;

// import { useEffect } from 'react';
// import {
//   interpolate,
//   useAnimatedScrollHandler,
//   useAnimatedStyle,
//   useSharedValue,
//   useDerivedValue,
// } from 'react-native-reanimated';

// function useScrollHandler(height: number, analyticsHeight?: number) {
//   const headerHeight = useSharedValue(height);
//   const analyticsHeaderHeight = useSharedValue(analyticsHeight ?? 0);
//   const scrollY = useSharedValue(0);

//   useEffect(() => {
//     if (analyticsHeight !== undefined) {
//       analyticsHeaderHeight.value = analyticsHeight;
//     }
//   }, [analyticsHeight]);

//   const scrollHandler = useAnimatedScrollHandler({
//     onScroll: event => {
//       scrollY.value = event.contentOffset.y;
//     },
//   });

//   // Derived value for smooth animation (prevents re-renders causing glitches)
//   const interpolatedScroll = useDerivedValue(() => scrollY.value, [scrollY]);

//   const headerStyle = useAnimatedStyle(() => {
//     return {
//       height: interpolate(interpolatedScroll.value, [0, 500], [headerHeight.value, 0], 'clamp'),
//       marginTop: interpolate(interpolatedScroll.value, [0, 500], [0, -headerHeight.value], 'clamp'),
//       paddingHorizontal: 20,
//       paddingVertical: 25,
//     };
//   });

//   const analyticsHeaderStyle = useAnimatedStyle(() => {
//     return {
//       marginTop: interpolate(
//         interpolatedScroll.value,
//         [0, analyticsHeaderHeight.value],
//         [0, -analyticsHeaderHeight.value],
//         'clamp',
//       ),
//     };
//   });

//   const inputStyle = useAnimatedStyle(() => {
//     return {
//       opacity: interpolate(interpolatedScroll.value, [0, 150], [1, 0], 'clamp'),
//     };
//   });

//   return { scrollHandler, headerStyle, analyticsHeaderStyle, inputStyle };
// }

// export default useScrollHandler;
