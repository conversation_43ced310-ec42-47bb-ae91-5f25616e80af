import { useCallback } from 'react';
import * as Clarity from '@microsoft/react-native-clarity';

type ActionProperties = Record<string, any>;

export type TrackingProvider = {
  logEvent: (eventName: string, properties?: ActionProperties) => void;
};

const provider: TrackingProvider = {
  logEvent: (eventName: string, properties?: ActionProperties) => {
    Clarity.sendCustomEvent(eventName);
  },
};

export const useTrackAction = () => {
  const trackAction = (actionName: string, properties?: ActionProperties) => {
    provider.logEvent(actionName, {
      action_type: 'user_action',
      action_name: actionName,
      ...properties,
    });
  };

  const trackButtonClick = (buttonName: string, properties?: ActionProperties) => {
    provider.logEvent('button_click', {
      button_name: buttonName,
      ...properties,
    });
  };

  return {
    trackAction,
    trackButtonClick,
  };
};
