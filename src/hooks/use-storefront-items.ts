import { useEffect, useState } from 'react';
// import { ProductItemInterface, StrippedItem } from "../../assets/interfaces";
import { useApi } from './use-api';
import { GET_ITEMS, GetItemsParams, ProductItemInterface } from 'catlog-shared';
import { ProductsResponse } from 'src/screens/products/storefront';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ensureUniqueItems } from '@/assets/utils/js';

 const useStorefrontItems = (
  storeId?: string,
  filter?: GetItemsParams['filter'],
  preloadedItems?: ProductItemInterface[],
) => {
  const [items, setItems] = useState<ProductItemInterface[]>([]);

    const {store} = useAuthContext()

    const theStoreId = storeId ?? store?.id;

    const getItemRequest = useApi<GetItemsParams, ProductsResponse>(
      {
        key: 'fetch-products',
        apiFunction: GET_ITEMS,
        method: 'GET',
      },
      {
        filter: { store: theStoreId, ...(filter ?? {}) },
        page: 1,
        per_page: Number.MAX_SAFE_INTEGER,
        sort: 'asc',
      },
    );

  const { response } = getItemRequest;

  useEffect(() => {
    if (response) {
      setItems(ensureUniqueItems([...response?.data?.items]));
    }
  }, [response]);

  const getItem = (id: string) => {
    //uses items from preloaded data if available
    if (items.length === 0 && preloadedItems && preloadedItems.length > 0) {
      return preloadedItems.find(item => item.id === id);
    }

    return items.find(item => item.id === id);
  };

  return {
    items,
    fetchItemsReq: getItemRequest,
    getItem,
  };
};

export default useStorefrontItems;

// items?filter%5Bstore%5D=61ba34bec3808b7b5b6b12eb&page=1&per_page=10&sort=asc

// items?filter%5Bstore%5D=61ba34bec3808b7b5b6b12eb&page=1&per_page=9007199254740991&sort=asc