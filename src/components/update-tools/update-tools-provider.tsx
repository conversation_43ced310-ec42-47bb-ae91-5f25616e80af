import React, { ReactNode, useEffect } from 'react';
import * as Updates from 'expo-updates';
import Modal, { ModalProps } from 'react-native-modal';
import { BaseText, CircledIcon } from '../ui';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useModals from 'src/hooks/use-modals';
import Button, { ButtonVariant } from '../ui/buttons/button';
import { hp, wp } from 'src/assets/utils/js';
import FixedBtnFooter from '../ui/buttons/fixed-btn-footer';
import * as Animatable from 'react-native-animatable';
import colors from 'src/theme/colors';
import { RefreshCircle } from 'node_modules/iconsax-react-native/src';

interface UpdateToolsProviderProps {
  children: ReactNode;
}

const UpdateToolsProvider = ({ children }: UpdateToolsProviderProps) => {
  const { modals, toggleModal } = useModals(['updatePrompt']);
  useEffect(() => {
    checkForUpdates();
  }, []);

  const checkForUpdates = async () => {
    // Implement update check logic here
    const updates = await Updates.checkForUpdateAsync();

    console.log(updates);

    if (updates.isAvailable) {
      // Implement update flow here
      toggleModal('updatePrompt', true);
      // await Updates.fetchUpdateAsync();
      // await Updates.reloadAsync();
    }
  };

  const downloadUpdate = async () => {
    try {
      await Updates.fetchUpdateAsync();
      await Updates.reloadAsync();
    } catch (error) {
      console.error('Error downloading update: ', error);
    }
  };  

  return (
    <>
      {children}
      <UpdatePromptModal
        isVisible={true}
        closeModal={() => toggleModal('updatePrompt', false)}
        downloadUpdate={downloadUpdate}
      />
    </>
  );
};

export default UpdateToolsProvider;

interface UpdatePromptModalProps extends Partial<ModalProps> {
  downloadUpdate: VoidFunction;
  closeModal: VoidFunction;
}

const UpdatePromptModal = ({ downloadUpdate, closeModal, ...props }: UpdatePromptModalProps) => {
  const insets = useSafeAreaInsets();
  const insertBottom = insets.bottom + hp(12);

  return (
    <Modal
      avoidKeyboard={true}
      isVisible={props.isVisible}
      onBackdropPress={closeModal}
      backdropColor={'#1E1E1E80'}
      useNativeDriverForBackdrop={true}
      onBackButtonPress={closeModal}
      style={[{ flex: 1, justifyContent: 'flex-end', margin: 0 }]}
      {...props}>
      <View style={[{ justifyContent: 'flex-end' }]} className="bg-white rounded-[20px]">
        <View>
          <View className='bg-grey-muted h-5 w-50 self-center rounded-full mt-10' />
        </View>
        <View className="px-20 pt-20">
          <Animatable.View
            className={`bg-accentGreen-pastel2 self-center rounded-full p-20`}
            animation={'zoomIn'}
            duration={300}>
            <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
              <CircledIcon className={`bg-accentGreen-main`}>
                <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                  <RefreshCircle variant="Bold" color={colors.white} size={wp(40)} />
                </Animatable.View>
              </CircledIcon>
            </Animatable.View>
          </Animatable.View>
          <View className="pt-20 pb-10">
            <BaseText fontSize={20} type="heading" classes="text-center">
              Update Available
            </BaseText>
          </View>
          <View className="pb-40">
            <BaseText fontSize={14} classes="text-black-muted text-center">
              There is an update available please{'\n'} download to keep enjoying.
            </BaseText>
          </View>
        </View>
        <FixedBtnFooter
          buttons={[
            {
              text: 'Later',
              variant: ButtonVariant.LIGHT,
              onPress: closeModal,
            },
            {
              text: 'Update Now',
              onPress: downloadUpdate,
            },
          ]}
        />
      </View>
    </Modal>
  );
};

interface DownloadProgressModalProps extends Partial<ModalProps> {
  downloadUpdate: VoidFunction;
  closeModal: VoidFunction;
}

const DownloadProgressModal = ({ downloadUpdate, closeModal, ...props }: DownloadProgressModalProps) => {
  const insets = useSafeAreaInsets();
  const insertBottom = insets.bottom + hp(12);

  return (
    <Modal
      avoidKeyboard={true}
      isVisible={props.isVisible}
      onBackdropPress={closeModal}
      backdropColor={'#1E1E1E80'}
      useNativeDriverForBackdrop={true}
      onBackButtonPress={closeModal}
      style={[{ flex: 1, justifyContent: 'flex-end', margin: 0 }]}
      {...props}>
      <View style={[{ justifyContent: 'flex-end' }]} className="bg-white rounded-[20px]">
        <View>
          <View className='bg-grey-muted h-5 w-50 self-center rounded-full mt-10' />
        </View>
        <View className="px-20 pt-20">
          <Animatable.View
            className={`bg-accentGreen-pastel2 self-center rounded-full p-20`}
            animation={'zoomIn'}
            duration={300}>
            <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
              <CircledIcon className={`bg-accentGreen-main`}>
                <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                  <RefreshCircle variant="Bold" color={colors.white} size={wp(40)} />
                </Animatable.View>
              </CircledIcon>
            </Animatable.View>
          </Animatable.View>
          <View className="pt-20 pb-10">
            <BaseText fontSize={20} type="heading" classes="text-center">
              Downloading Update
            </BaseText>
          </View>
          <View className="pb-40">
            <BaseText fontSize={14} classes="text-black-muted text-center">
              There is an update available please{'\n'} download to keep enjoying.
            </BaseText>
          </View>
        </View>
        <FixedBtnFooter
          buttons={[
            {
              text: 'Later',
              variant: ButtonVariant.LIGHT,
              onPress: closeModal,
            },
            {
              text: 'Update Now',
              onPress: downloadUpdate,
            },
          ]}
        />
      </View>
    </Modal>
  );
};
