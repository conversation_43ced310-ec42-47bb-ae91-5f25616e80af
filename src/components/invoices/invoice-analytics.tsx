import { useNavigation } from '@react-navigation/native';
import { Money, MoneyRemove, MoneyTick, Receipt1 } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo, useState } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import HomeAnalyticsSkeletonLoader from '../home/<USER>';
import AnalyticsCard from '../ui/cards/analytics-card';
import MoreOptions from '../ui/more-options';
import { BaseText, Row } from '../ui';
import { ChevronDown } from '../ui/icons';
import { InvoiceStatsResponse } from 'src/screens/invoices/dashboard';
import useAuthContext from 'src/contexts/auth/auth-context';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';

export enum AnalyticsVariants {
  'AMOUNT_RECEIVED' = 'amount_received',
  'INVOICE_CREATED' = 'invoice_created',
  'INVOICES_PAID' = 'invoice_paid',
  'INVOICES_UNPAID' = 'invoices_unpaid',
}

interface InvoiceAnalyticsCardsProps {
  data: InvoiceStatsResponse['data'];
  isLoading: boolean;
}

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

const InvoiceAnalyticsCards = ({ isLoading = false, data }: InvoiceAnalyticsCardsProps) => {
  const { store } = useAuthContext();
  const [currentCurrency, setCurrentCurrency] = useState<string>(store?.currencies?.default!);

  const options = useMemo(() => {
    if (!data) return [];
    return (
      data?.currencies?.map(currency => ({
        title: currency,
        onPress: () => setCurrentCurrency(currency),
      })) ?? []
    );
  }, [data]);

  const analyticsCardsInfo: AnalyticsCardInfo[] = useMemo(
    () => [
      {
        title: 'Amount Received',
        cardBg: 'bg-accentRed-pastel',
        icon: <Money variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentRed-main',
        key: AnalyticsVariants.AMOUNT_RECEIVED,
        value: millify(data?.grouped_data?.[currentCurrency]?.total_amount_received ?? 0, 2, currentCurrency),
        change: 0,
      },
      {
        title: 'Unpaid Invoices',
        icon: <MoneyTick variant="Bold" size={wp(18)} color={colors?.white} />,
        cardBg: 'bg-accentOrange-pastel',
        iconBg: 'bg-accentOrange-main',
        key: AnalyticsVariants.INVOICES_PAID,
        value: data?.grouped_data?.[currentCurrency]?.invoices_unpaid ?? 0,
        change: 0,
      },
      {
        title: 'Invoices Created',
        cardBg: 'bg-accentYellow-pastel',
        icon: <Receipt1 variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentYellow-main',
        key: AnalyticsVariants.INVOICE_CREATED,
        value: data?.grouped_data?.[currentCurrency]?.total_invoices ?? 0,
        change: 0,
      },
      {
        title: 'Paid Invoices',
        cardBg: 'bg-accentGreen-pastel',
        iconBg: 'bg-accentGreen-main',
        icon: <MoneyRemove variant="Bold" size={wp(18)} color={colors?.white} />,
        key: AnalyticsVariants.INVOICES_UNPAID,
        value: data?.grouped_data?.[currentCurrency]?.total_paid_invoices ?? 0,
        change: 0,
      },
    ],
    [currentCurrency, data],
  );

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  return (
    <View className="mt-20">
      <Row className="mb-15">
        <Row>
          <BaseText fontSize={15} weight="bold" type="heading">
            Performance Overview
          </BaseText>
          <ChevronDown size={15} primaryColor={colors.grey.muted} />
        </Row>
        <MoreOptions
          options={options ?? []}
          customMenuElement={
            <Row className="items-center bg-grey-bgOne py-7 px-10 rounded-full">
              <BaseText fontSize={12} classes="mr-4" style={{ color: colors.black.muted }}>
                {currentCurrency}
              </BaseText>
              <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
            </Row>
          }
        />
      </Row>
      <View className="border border-grey-border rounded-[15px] overflow-hidden">
        {isLoading && <AnalyticsSkeletonLoader />}
        {!isLoading && (
          <>
            {splitCards.map((group, index) => (
              <Fragment key={index}>
                <View className="flex-row last:mb-0" key={index}>
                  {group.map((info, index) => (
                    <Fragment key={info.key}>
                      <AnalyticsCard
                        key={info.key}
                        // className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                        title={info.title}
                        value={info.value}
                        iconBg={info.iconBg}
                        change={info.change}
                        showChange={false}
                        icon={info.icon}
                        addon={info.addon}
                        theme="white"
                      />
                      {index === 0 && <View className="w-1 bg-grey-border" />}
                    </Fragment>
                  ))}
                </View>
                {index === 0 && <View className="h-1 bg-grey-border" />}
              </Fragment>
            ))}
          </>
        )}
      </View>
    </View>
  );
};

export default InvoiceAnalyticsCards;
