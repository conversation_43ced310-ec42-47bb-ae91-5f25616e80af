import * as Yup from 'yup';
import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { DeliveryStat } from 'src/screens/deliveries/deliveries-analytics';
import InvoiceAnalyticsCards from './invoice-analytics';
import { InvoiceStatsResponse } from 'src/screens/invoices/dashboard';

interface InvoiceAnalyticsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  stats?: InvoiceStatsResponse['data'];
  isLoading?: boolean;
}

const InvoiceAnalyticsModal = ({ closeModal, stats, isLoading, ...props }: InvoiceAnalyticsModalProps) => {
  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      showButton={false}
      buttons={[
        {
          text: 'Close',
          onPress: () => closeModal,
        },
      ]}>
      <View className="mx-20 pb-10 -mt-20">
        <InvoiceAnalyticsCards data={stats} isLoading={isLoading} />
      </View>
    </BottomModal>
  );
};

export const addAddressValidationSchema = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default InvoiceAnalyticsModal;
