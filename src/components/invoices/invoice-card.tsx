import { Copy, Edit, Framer, Import, ImportCurve, Receipt21, Trash } from 'iconsax-react-native/src';
import { styled } from 'nativewind';
import { View } from 'react-native';
import CustomerInitial from '../customer/customer-initial';
import { BaseText, CircledIcon, Row } from '../ui';
import Pressable, { PressableProps } from '../ui/base/pressable';
import ListItemCard from '../ui/cards/list-item-card';
import MoreOptions, { MoreOptionElementProps, OptionWithIcon } from '../ui/more-options';
import Separator from '../ui/others/separator';
import StatusPill, { StatusType } from '../ui/others/status-pill';
import colors from 'src/theme/colors';
import { copyToClipboard, getColorAlternates, toCurrency, wp } from 'src/assets/utils/js';
import { INVOICE_STATUSES, InvoiceInterface, toAppUrl } from 'catlog-shared';

interface InvoiceCardProps extends PressableProps {
  item: InvoiceInterface;
  index: number;
  onAction: (action: 'edit' | 'delete' | 'download' | 'copy') => void;
}

const InvoiceCard = ({ item, index = 0, onAction, ...props }: InvoiceCardProps) => {
  const colorAlternate = getColorAlternates(index);

  const LeftElement = () => {
    return (
      <CircledIcon className="rounded-8 p-10" style={{ backgroundColor: colorAlternate.bgColor }}>
        <Receipt21 variant="Bold" color={colorAlternate.iconColor} />
      </CircledIcon>
    );
  };

  const statusMap = {
    [INVOICE_STATUSES.PENDING]: StatusType.WARN,
    [INVOICE_STATUSES.EXPIRED]: StatusType.DANGER,
    [INVOICE_STATUSES.PAID]: StatusType.SUCCESS,
  };
  //Todo: @kayode Invoices can also be "draft", it's not a status but looks like we've missed it

  const options: MoreOptionElementProps[] = [
    {
      onPress: () => onAction('copy'),
      title: 'Copy Link',
      optionElement: (
        <OptionWithIcon icon={<Copy size={wp(15)} color={colors.black.placeholder} />} label="Copy Link" />
      ),
    },
    {
      onPress: () => onAction('edit'),
      title: 'Edit Invoice',
      optionElement: (
        <OptionWithIcon icon={<Edit size={wp(15)} color={colors.black.placeholder} />} label="Edit Invoice" />
      ),
    },
    {
      onPress: () => onAction('download'),
      title: 'Download Invoice',
      optionElement: (
        <OptionWithIcon icon={<ImportCurve size={wp(15)} color={colors.black.placeholder} />} label="Download Invoice" />
      ),
    },
    {
      onPress: () => onAction('delete'),
      title: 'Delete Invoice',
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label="Delete Invoice" />
      ),
    },
  ];

  return (
    <Pressable className="p-15 mb-15 bg-white rounded-12 border border-grey-border" {...props}>
      <ListItemCard
        onPress={() => props.onPress?.()}
        leftElement={<LeftElement />}
        title={item?.title}
        className="py-0"
        titleProps={{ classes: 'text-black-muted', fontSize: 12, weight: 'medium' }}
        descriptionProps={{ classes: 'text-black-main', fontSize: 12, weight: 'medium' }}
        description={toCurrency(item?.total_amount, item?.currency)}
        rightElement={
          <StatusPill
            className="self-start"
            statusType={statusMap[item?.status] ?? StatusType.DEFAULT}
            title={item?.status}
            greyBg
          />
        }
      />
      <Separator className="mx-0" />
      <Row>
        <CustomerInitial initial={item?.receiver?.name?.[0] ?? ''} textProps={{ fontSize: 12 }} classes="px-8" greyBg />
        <View className="flex-1 mx-5">
          <BaseText classes="text-black-secondary" weight="medium">
            {item?.receiver?.name}
          </BaseText>
        </View>
        <MoreOptions options={options} />
      </Row>
    </Pressable>
  );
};

export default InvoiceCard;
