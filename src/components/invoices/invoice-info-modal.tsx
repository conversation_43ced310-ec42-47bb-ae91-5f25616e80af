import { toCurrency, wp } from '@/assets/utils/js';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import useModals, { ModalsState } from '@/hooks/use-modals';
import colors from '@/theme/colors';
import dayjs from 'dayjs';
import {
  Calendar,
  CalendarTick,
  Copy,
  DollarCircle,
  Eye,
  Link2,
  PercentageCircle,
  Sms,
} from 'iconsax-react-native/src';
import { View } from 'react-native';
import CustomerInitial from '../customer/customer-initial';
import ProductInfoRow from '../products/product-info-row';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../ui';
import { ButtonVariant, TextColor } from '../ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import InfoRow from '../ui/others/info-row';
import { INVOICE_STATUSES, InvoiceInterface, removeCountryCode } from 'catlog-shared';
import Pressable from '../ui/base/pressable';
import MoreOptionsModal from './more-options-modal';
import PreviewInvoiceModal from './preview-modal';
import SendInvoiceModal from './send-invoice-modal';

interface InvoiceInfoModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  editInvoice: VoidFunction;
  openOptions: VoidFunction;
  copyInvoiceLink: VoidFunction;
  modals: ModalsState<'options' | 'preview' | 'send' | 'search' | 'info' | 'previewReceipt'>;
  toggleModal: (key: 'options' | 'preview' | 'send' | 'search' | 'info' | 'previewReceipt', value?: boolean) => void;
  handleInvoiceItemAction: (
    action: 'edit' | 'delete' | 'options' | 'download' | 'copy' | 'preview' | 'send' | 'mark',
    invoice?: InvoiceInterface,
  ) => void;
  activeInvoice?: InvoiceInterface;
}

const InvoiceInfoModal = ({
  handleInvoiceItemAction,
  activeInvoice,
  modals,
  toggleModal,
  openOptions,
  closeModal,
  editInvoice,
  copyInvoiceLink,
  ...props
}: InvoiceInfoModalProps) => {
  const statusMap = {
    [INVOICE_STATUSES.PENDING]: StatusType.WARN,
    [INVOICE_STATUSES.EXPIRED]: StatusType.DANGER,
    [INVOICE_STATUSES.PAID]: StatusType.SUCCESS,
  };

  // const { modals, toggleModal } = useModals(['options', 'preview']);

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'More Options',
          variant: ButtonVariant.LIGHT,
          textColor: TextColor.PRIMARY,
          // onPress: () => openOptions(),
          onPress: () => toggleModal('options'),
        },
        { text: 'Edit Invoice', onPress: () => editInvoice() },
      ]}
      title={activeInvoice?.title}>
      <View className="px-20">
        <Row className="py-15">
          <CustomerInitial initial={activeInvoice?.receiver?.name?.[0] ?? ''} classes={'w-36 h-36'} />
          <View className={'flex-1 mx-10'}>
            <BaseText fontSize={15} weight={'bold'} type={'heading'}>
              {activeInvoice?.receiver?.name}
            </BaseText>
            <BaseText classes="text-black-muted">{removeCountryCode(activeInvoice?.receiver?.phone)}</BaseText>
          </View>
          <StatusPill
            statusType={statusMap[activeInvoice!.status] ?? StatusType.DEFAULT}
            title={activeInvoice!.status}
          />
        </Row>
        <ProductInfoRow
          className="border-y border-grey-border py-15"
          leftItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                <PercentageCircle variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
              </CircledIcon>
            ),
            value: activeInvoice?.invoice_id ?? '-',
            title: 'Invoice ID',
          }}
          rightItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentOrange-pastel'}>
                <DollarCircle variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
              </CircledIcon>
            ),
            value: activeInvoice?.total_amount ? `${toCurrency(activeInvoice?.total_amount)}` : '-',
            title: 'Total Amount',
          }}
        />
        <View className="mt-15">
          <InfoRow
            title={'Invoice Link'}
            icon={<Link2 size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <WhiteCardBtn
                onPress={() => copyInvoiceLink()}
                className="py-0 px-0 self-center"
                icon={
                  <CircledIcon className="rounded-[5px] bg-grey-bgOne p-5">
                    <Copy color={colors.primary.main} size={wp(10)} />
                  </CircledIcon>
                }>
                Copy Link
              </WhiteCardBtn>
            }
          />
          <InfoRow
            title={'Customer Email'}
            icon={<Sms size={wp(15)} color={colors.black.placeholder} />}
            value={activeInvoice?.receiver?.email}
          />
          <InfoRow
            title={'Date Created'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={dayjs(activeInvoice?.date_created).format('MMMM D, YYYY')}
          />
          <InfoRow
            title={'Due Date'}
            icon={<CalendarTick size={wp(15)} color={colors.black.placeholder} />}
            value={dayjs(activeInvoice?.date_due).format('MMMM D, YYYY')}
          />
        </View>
        <View className="border-t border-t-grey-border">
          <Pressable className="py-15" onPress={() => toggleModal('preview')}>
            <Row spread={false}>
              <CircledIcon className="bg-accentGreen-pastel3">
                <Eye color={colors.accentGreen.main} size={wp(15)} />
              </CircledIcon>
              <BaseText className="text-black-secondary ml-10">Preview Invoice</BaseText>
            </Row>
          </Pressable>
        </View>
      </View>
      <MoreOptionsModal
        activeInvoice={activeInvoice}
        isVisible={modals.options}
        closeModal={() => toggleModal('options', false)}
        onAction={(action: any) => handleInvoiceItemAction(action, activeInvoice)}
      />
      <PreviewInvoiceModal
        activeInvoice={activeInvoice}
        isVisible={modals.preview}
        closeModal={() => toggleModal('preview', false)}
        onAction={(action: any) => handleInvoiceItemAction(action, activeInvoice)}
      />

      <SendInvoiceModal
        activeInvoice={activeInvoice}
        isVisible={modals.send}
        closeModal={() => toggleModal('send', false)}
      />
    </BottomModal>
  );
};

export default InvoiceInfoModal;
