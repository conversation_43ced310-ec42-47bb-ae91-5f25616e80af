import { hp, wp } from '@/assets/utils/js';
import React from 'react';
import Column from '../ui/column';
import Row from '../ui/row';
import Shimmer from '../ui/shimmer';
import { View } from 'react-native';

const dummyRow = new Array(1).fill(0);

interface StorefrontSkeletonLoaderProps {}

const InvoicesSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return dummyRow.map((_, index) => (
    <View key={index} className="p-15 mb-15 bg-white rounded-12 border border-grey-border">
      <View className="flex-row justify-between pb-20 border-grey-border border-b">
        <View className="flex-row justify-between items-center">
          <Shimmer classes="mr-10" borderRadius={wp(10)} height={hp(40)} width={wp(40)} />
          <Column alignCenter={false}>
            <Shimmer borderRadius={wp(10)} height={hp(15)} width={wp(100)} />
            <Shimmer borderRadius={wp(10)} height={hp(10)} width={wp(50)} marginTop={wp(10)} />
          </Column>
        </View>
        <Shimmer borderRadius={wp(10)} height={hp(20)} width={wp(60)} />
      </View>
      <Row classes="pt-10">
        <Row>
          <Shimmer borderRadius={wp(999)} height={hp(25)} width={wp(25)} />
          <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(15)} width={wp(70)} />
        </Row>
      </Row>
    </View>
  ));
};

export default InvoicesSkeletonLoader;
