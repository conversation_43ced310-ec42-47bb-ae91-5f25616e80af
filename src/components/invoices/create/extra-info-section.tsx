import { getFieldvalues, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon } from '@/components/ui';
import { ArrowRight } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { FormikProps } from 'formik';
import { useState } from 'react';
import { View } from 'react-native';
import { Image } from 'src/@types/utils';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import Pressable from '@/components/ui/base/pressable';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import Input from '@/components/ui/inputs/input';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import CustomImage from '@/components/ui/others/custom-image';
import useAuthContext from '@/contexts/auth/auth-context';
import { ResponseWithPagination } from '@/hooks/use-api';
import { InvoiceFormParams } from '@/screens/invoices/create-invoice';
import { CustomerInterface } from 'catlog-shared';


interface ExtraInformationSectionProps {
  form: FormikProps<InvoiceFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  isComplete: boolean;
  hasErrors: boolean;
}

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const ExtraInformationSection = ({
  form,
  accordionRef,
  hasErrors,
  isComplete,
  onPressSave,
}: ExtraInformationSectionProps) => {
  const { store } = useAuthContext();
  const [images, setImages] = useState<Image[]>([]);
  const image = images[0];

  return (
    <View>
      <Accordion
        anchorElement={status => (
          <AccordionAnchor title="Extra Info" isOpened={status} isSaved={isComplete} isError={hasErrors} isOptional />
        )}
        ref={accordionRef}>
        <View>
          <Pressable
            onPress={() => pickMultipleImages(images, setImages, true)}
            className="flex flex-row mt-15 items-center p-10 rounded-12 bg-grey-bgTwo border border-grey-border border-dashed">
            <CustomImage
              className="w-28 h-28 rounded-full"
              imageProps={{ source: { uri: image?.src ?? store?.logo }, contentFit: 'cover' }}
            />
            <View className="flex-1 mx-10">
              <BaseText>Click here to change the logo (Optional)</BaseText>
            </View>
            <CircledIcon className="bg-white">
              <ArrowRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />
            </CircledIcon>
          </Pressable>
          <Input label={'Address'} {...getFieldvalues('store_address', form)} containerClasses="mt-15" />
        </View>
        <Button
          className="self-end py-10"
          btnStyle="px-30"
          style={{ width: 'auto' }}
          text={'Save'}
          size={ButtonSize.MEDIUM}
          onPress={onPressSave}
          disabled={false}
        />
      </Accordion>
    </View>
  );
};

export default ExtraInformationSection;
