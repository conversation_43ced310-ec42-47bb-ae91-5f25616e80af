import { getFieldvalues, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, Row } from '@/components/ui';
import colors from '@/theme/colors';
import { FormikProps } from 'formik';
import { Add } from 'iconsax-react-native/src';
import { useRef, useState } from 'react';
import { View } from 'react-native';
import FeesCard from '@/components/orders/create/fees-card';
import Pressable from '@/components/ui/base/pressable';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import LeftLabelledInput from '@/components/ui/inputs/left-labelled-input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import { InvoiceFormParams } from 'src/screens/invoices/create-invoice';
import {
  CouponItemInterface,
  GET_COUPONS,
  PaginateSearchParams,
  ORDER_FEE_TYPES,
  INVOICE_FEE_TYPES,
} from 'catlog-shared';

interface InvoiceFeesSectionProps {
  form: FormikProps<InvoiceFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  isComplete: boolean;
  hasErrors: boolean;
}

interface CouponResponse extends ResponseWithPagination<{ items: CouponItemInterface[] }> {}

const InvoiceFeesSection = ({ form, accordionRef, onPressSave, isComplete, hasErrors }: InvoiceFeesSectionProps) => {
  const [showFeeInput, setShowFeeInput] = useState(false);
  const [tempFee, setTempFee] = useState<{ amount: string; type: string } | undefined>();
  const couponRef = useRef<DropDownMethods>(null);

  const getCouponsRequest = useApi<PaginateSearchParams, CouponResponse>(
    {
      key: 'get-coupons',
      apiFunction: GET_COUPONS,
      method: 'GET',
      onSuccess: response => {},
    },
    {
      filter: {},
      per_page: 9007199254740991,
      sort: 'desc',
    },
  );

  const [feeTypes, setFeeTypes] = useState(
    Object.entries(INVOICE_FEE_TYPES).map(data => ({
      value: data[1],
      label: data[1],
    })),
  );

  const coupons = getCouponsRequest?.response?.data?.items?.map(coupon => ({
    value: coupon.coupon_code,
    label: coupon.coupon_code,
  }));

  const addedFeeTypes = form.values.fees?.map(({ type }) => type) ?? [];

  const handleAddFee = () => {
    if (tempFee?.amount === '' || tempFee?.type === '' || !tempFee?.amount || !tempFee?.type) return;
    const newFees = [...form?.values?.fees!, tempFee];

    form.setFieldValue('fees', newFees);

    setTempFee({ amount: '', type: '' });
    setShowFeeInput(false);
  };

  const updateOption = (
    option: {
      type: string;
      amount: number;
    },
    index: number,
  ) => {
    const feesCopy = [...feeTypes];
    feesCopy[index] = {
      value: option.type as any,
      label: option.type as any,
    };

    setFeeTypes(feesCopy);
  };

  const handleAddCoupon = (couponCode: string) => {
    form.setFieldValue('coupon', couponCode);
  };

  const handleSelectFeeType = (feeType: string) => {
    setTempFee({ amount: '', type: feeType });
  };

  const handleFeeDelete = (index: number) => {
    const feesCopy = form.values.fees;
    feesCopy?.splice(index, 1);
    form.setFieldValue('fees', feesCopy);
  };

  const handleFeeEdit = (index: number) => {
    const feesCopy = form.values.fees;
    form.setFieldValue('tempSelectedFeeType', form.values.fees?.[index]?.type);
    form.setFieldValue('tempFeeAmount', form.values.fees?.[index]?.amount);
    feesCopy?.splice(index, 1);
    form.setFieldValue('fees', feesCopy);
    setShowFeeInput(true);
  };

  return (
    <View>
      <Accordion
        anchorElement={status => (
          <AccordionAnchor title="Invoice Fees" isOpened={status} isSaved={isComplete} isError={hasErrors} isOptional />
        )}
        ref={accordionRef}>
        <View className="mt-15 mb-20">
          {form.values.fees?.map((item, index) => (
            <FeesCard
              key={item.type}
              feeData={item}
              availableFeeTypes={feeTypes}
              form={form}
              onPressDelete={() => handleFeeDelete(index)}
              updateOption={updateOption}
            />
          ))}
          <Row className="gap-x-10 mb-15">
            <View className="flex-1">
              <SelectDropdown
                items={feeTypes.filter(item => !addedFeeTypes.includes(item.value))}
                onPressItem={handleSelectFeeType}
                selectedItem={tempFee?.type}
                label={'Select Type'}
                isMultiSelect
                selectedItems={tempFee?.type ? [tempFee?.type] : []}
              />
            </View>
            <View className="flex-1">
              <LeftLabelledInput
                leftText="NGN"
                label={'Fee'}
                keyboardType={'number-pad'}
                value={tempFee?.amount}
                onChangeText={text => setTempFee({ amount: text, type: tempFee?.type! })}
                // onSubmitEditing={handleAddFee}
              />
            </View>
          </Row>
          <Pressable className="self-start py-5" onPress={handleAddFee}>
            <Row className=" justify-start">
              <Add size={wp(14)} color={colors.primary.main} />
              <BaseText fontSize={12} weight={'medium'} classes="text-primary-main ml-2">
                Add Fee
              </BaseText>
            </Row>
          </Pressable>
          <Button
            className="self-end py-10"
            btnStyle="px-30"
            style={{ width: 'auto' }}
            text={'Save'}
            size={ButtonSize.MEDIUM}
            onPress={onPressSave}
            disabled={false}
          />
        </View>
      </Accordion>

      <SelectDropdown
        items={coupons ?? []}
        ref={couponRef}
        showAnchor={false}
        onPressItem={value => handleAddCoupon(value)}
        label={'Select Coupon'}
        containerClasses="my-15"
      />
    </View>
  );
};

export default InvoiceFeesSection;
