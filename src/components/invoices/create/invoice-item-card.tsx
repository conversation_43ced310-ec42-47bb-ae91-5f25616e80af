import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Add, Minus, Trash } from 'iconsax-react-native/src';
import { styled } from 'nativewind';
import { Pressable, View, ViewProps } from 'react-native';
import ProductCard from '@/components/products/storefront-products/product-card';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Separator from '@/components/ui/others/separator';
import { InvoiceFormParams } from 'src/screens/invoices/create-invoice';

interface OrderItemCardProps extends ViewProps {
  item: InvoiceFormParams['items'][0];
  onPressDelete?: VoidFunction;
  onChangeItem: VoidFunction;
  onPressToggleQuality?: (action: 'decrease' | 'increase') => void;
}

const OrderItemCard = ({ item, onPressDelete, onPressToggleQuality, onChangeItem, ...props }: OrderItemCardProps) => {
  return (
    <View className="p-15 mb-15 bg-grey-bgOne rounded-12" {...props}>
      <ProductCard
        listView
        product={{
          id: item.id,
          available: true,
          description: '',
          images: [item.image ?? ''],
          name: item.name,
          price: item.price,
          thumbnail: 0,
          store: '',
        }}
        rightElement={
          <Pressable onPress={onPressDelete}>
            <CircledIcon className="bg-white p-5">
              <Trash size={wp(18)} color={colors.accentRed.main} />
            </CircledIcon>
          </Pressable>
        }
      />
      <Separator className="mx-0" />
      <Row>
        <WhiteCardBtn className="rounded-full">
          <BaseText onPress={onChangeItem} fontSize={12} weight={'medium'}>
            Change Item
          </BaseText>
        </WhiteCardBtn>
        <Row className="justify-start gap-x-10">
          <Pressable
            className="p-5 border-grey-border border rounded-full bg-white"
            onPress={() => onPressToggleQuality?.('decrease')}>
            {item?.quantity === 1 ? (
              <Trash size={wp(16)} color={colors.accentRed.main} />
            ) : (
              <Minus size={wp(16)} color={colors.black.placeholder} />
            )}
          </Pressable>
          <View className="py-5 px-14 border-grey-border border rounded-[6px] bg-white">
            <BaseText fontSize={12} weight={'semiBold'}>
              {item?.quantity}
            </BaseText>
          </View>
          <Pressable
            className="p-5 border-grey-border border rounded-full bg-white"
            onPress={() => onPressToggleQuality?.('increase')}>
            <Add size={wp(16)} color={colors.black.placeholder} />
          </Pressable>
        </Row>
      </Row>
    </View>
  );
};

export default styled(OrderItemCard);
