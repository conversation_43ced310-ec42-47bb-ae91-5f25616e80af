import React from 'react';
import useKeyboard from '@/hooks/use-keyboard';
import { CloseCircle, TickCircle } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { Linking, Pressable, ScrollView, View } from 'react-native';
import { getFieldvalues, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { BaseText, CircledIcon, Row } from '../ui';
import Input from '../ui/inputs/input';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Radio from '../ui/buttons/radio';
import { useFormik } from 'formik';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import * as Yup from 'yup';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import { useApi } from 'src/hooks/use-api';
import { set } from 'date-fns';
import {
  InvoiceInterface,
  phoneObjectFromString,
  phoneObjectToString,
  SEND_INVOICE_VIA_MAIL,
  SendInvoiceViaMailParams,
} from 'catlog-shared';

interface SendInvoiceModalProps extends Partial<BottomModalProps> {
  activeInvoice: InvoiceInterface;
  closeModal: () => void;
  onPressButton?: () => void;
}

const SendInvoiceModal = ({ closeModal, activeInvoice, ...props }: SendInvoiceModalProps) => {
  const [state, setState] = useState<'form' | 'error' | 'success'>('form');
  const keyboardIsVisible = useKeyboard();
  const invoiceLink = `${process.env.EXPO_PUBLIC_PUBLIC_URL}/invoices/${activeInvoice.invoice_id}`;

  useEffect(() => {
    if (activeInvoice) {
      form.setValues({
        email: activeInvoice?.receiver?.email,
        phone: phoneObjectFromString(activeInvoice?.receiver?.phone),
        body: `Here's your invoice for the payment of ${toCurrency(
          activeInvoice.total_amount,
        )}, you can use this link to view it and make payment: ${invoiceLink}`,
        method: 'email',
      }),
        setState('form');
    }
  }, [activeInvoice]);

  const sendInvoiceReq = useApi<SendInvoiceViaMailParams>({
    apiFunction: SEND_INVOICE_VIA_MAIL,
    key: SEND_INVOICE_VIA_MAIL.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      email: activeInvoice?.receiver?.email,
      phone: phoneObjectFromString(activeInvoice?.receiver?.phone),

      body: `Here's your invoice for the payment of ${toCurrency(
        activeInvoice.total_amount,
      )}, you can use this link to view it and make payment: ${invoiceLink}`,

      method: 'email',
    },
    validationSchema,
    onSubmit: async values => {
      const { email, phone, method, body } = values;
      const isEmail = method === 'email';

      if (isEmail) {
        const [res, error] = await sendInvoiceReq.makeRequest({
          id: activeInvoice.id,
          data: { email, message: body },
        });
        if (res) setState('success');
        if (error) {
          setState('error');
        }
        return;
      }
      Linking.openURL(getWhatsappLink(phoneObjectToString(phone), body));
      setState('success');
    },
  });

  const setInviteMode = (mode: string) => {
    form.setFieldValue('method', mode);
  };

  const sendMethod = form.values.method;

  const buttonStates = {
    form: {
      text: 'Send Invoice',
      onPress: () => form.submitForm(),
      isLoading: sendInvoiceReq.isLoading,
    },
    success: {
      text: 'Continue',
      onPress: () => {
        closeModal();
        setState('form');
      },
    },
    error: {
      text: 'Try Again',
      onPress: () => setState('form'),
    },
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      containerStyle={keyboardIsVisible ? { flex: 1 } : undefined}
      contentContainerClass={keyboardIsVisible ? 'flex-1' : undefined}
      buttons={[buttonStates[state]]}
      showButton={!keyboardIsVisible}
      title="Send Invoice">
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <View className="p-20">
          {state === 'form' && (
            <View>
              <Row className="justify-start mb-20">
                <Pressable className="flex-1 flex-row items-center" onPress={() => setInviteMode('email')}>
                  <Radio active={sendMethod === 'email'} onClick={() => setInviteMode('email')} />
                  <BaseText classes="ml-10">Email Address</BaseText>
                </Pressable>
                <Pressable className="flex-1 flex-row items-center" onPress={() => setInviteMode('phone')}>
                  <Radio active={sendMethod === 'phone'} onClick={() => setInviteMode('phone')} />
                  <BaseText classes="ml-10">Phone</BaseText>
                </Pressable>
              </Row>
              {sendMethod === 'email' && (
                <>
                  <Input {...getFieldvalues('email', form)} label={'Customer Email'} containerClasses="" />
                  {/* <Input {...getFieldvalues('subject', form)} label={'Email Subject'} containerClasses="mt-15" /> */}
                </>
              )}
              {sendMethod === 'phone' && (
                <>
                  <PhoneNumberInput
                    {...getFieldvalues('phone', form)}
                    onChange={value => form.setFieldValue('phone', value)}
                  />
                </>
              )}
              <Input
                {...getFieldvalues('body', form)}
                label={'Message body'}
                containerClasses="mt-15"
                className="h-[120px]"
                multiline
              />
            </View>
          )}
          {state === 'success' && (
            <View className="px-40 py-30">
              <CircledIcon className="bg-accentGreen-main p-16 self-center">
                <TickCircle variant={'Bold'} color={colors.white} size={wp(32)} />
              </CircledIcon>
              <BaseText type={'heading'} fontSize={20} classes="text-center mt-10">
                Invoice Sent
              </BaseText>
              <BaseText fontSize={14} classes="text-center mt-10 text-black-secondary">
                Your invoice has been emailed to {form.values.email} successfully!
              </BaseText>
            </View>
          )}
          {state === 'error' && (
            <View className="px-40 py-30">
              <CircledIcon className="bg-accentRed-main p-16 self-center">
                <CloseCircle variant={'Bold'} color={colors.white} size={wp(32)} />
              </CircledIcon>
              <BaseText type={'heading'} fontSize={20} classes="text-center mt-10">
                Sending Failed
              </BaseText>
              <BaseText fontSize={14} classes="text-center mt-10 text-black-secondary">
                An error occurred while sending this invoice. Please try again.
              </BaseText>
            </View>
          )}
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default SendInvoiceModal;

const validationSchema = Yup.object({
  method: Yup.string(),
  email: Yup.string().when('method', {
    is: 'email',
    then: () => Yup.string().email('Please enter a valid email').required('Email is required'),
    otherwise: null,
  }),
  phone: Yup.object().when('method', {
    is: 'phone',
    then: () => phoneValidation(),
    otherwise: null,
  }),
  body: Yup.string().required('Body is required'),
  // attach: Yup.boolean(),
});

const getWhatsappLink = (phone?: string, message: string = '') =>
  `https://api.whatsapp.com/send/?phone=${resolvePhone(phone)}&text=${message}`;

const resolvePhone = (phone: string) => {
  if (phone?.startsWith('0')) {
    return phone?.replace('0', '234');
  }

  return phone?.replace('-', '');
};
