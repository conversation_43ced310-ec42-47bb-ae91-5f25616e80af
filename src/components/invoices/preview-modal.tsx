import { InvoiceInterface } from 'catlog-shared';
import React, { useState } from 'react';
import { Dimensions, ScrollView, View } from 'react-native';
import WebView from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import useModals from 'src/hooks/use-modals';

import MoreOptionsModal from './more-options-modal';
import { ContainerLoader } from '../products/add-product-controller/instagram-import/instagram-webview';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface Props extends Partial<BottomModalProps> {
  activeInvoice: InvoiceInterface;
  onAction: (action: string) => void;
  isInPreview?: boolean;
}
const PreviewInvoiceModal: React.FC<Props> = ({ onAction, isInPreview, activeInvoice, ...props }) => {
  const { modals, toggleModal } = useModals(['options']);
  const [siteLoaded, setSiteLoaded] = useState(false);

  const handleClose = () => {
    props.closeModal && props.closeModal();
    setSiteLoaded(false);
  };

  return (
    <>
      <BottomModal
        wrapChildren
        title="Preview Invoice"
        useChildrenAsDirectChild
        customSnapPoints={[90]}
        childrenWrapperStyle={{ padding: 20 }}
        {...props}
        closeModal={() => handleClose()}
        buttons={[
          {
            text: 'Manage Invoice',
            onPress: () => toggleModal('options', true),
          },
        ]}>
        <BottomSheetView style={{ flex: 1 }} className="flex-1" enableFooterMarginAdjustment>
          <WebView
            onLoad={() => setSiteLoaded(true)}
            className={cx('w-full flex-1', { 'opacity-0': !siteLoaded })}
            onError={e => console.log(e)}
            source={{ uri: `${process.env.EXPO_PUBLIC_PUBLIC_URL}/invoices/pdf/${activeInvoice?.invoice_id}` }}
          />
          {!siteLoaded && <ContainerLoader message="Loading Preview..." />}
        </BottomSheetView>
        <MoreOptionsModal
          isInPreview
          activeInvoice={activeInvoice}
          isVisible={modals.options}
          closeModal={() => toggleModal('options', false)}
          onAction={action => onAction(action)}
        />
      </BottomModal>
    </>
  );
};
export default PreviewInvoiceModal;
