import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { Edit2, Eye, ReceiveSquare, Send2, TickCircle, Trash } from 'iconsax-react-native/src';
import { cx, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { BaseText, Row } from '../ui';
import CircledIcon from '../ui/circled-icon';
import Pressable from '../ui/base/pressable';
import React from 'react';
import { INVOICE_STATUSES, InvoiceInterface } from 'catlog-shared';

interface Props extends Partial<BottomModalProps> {
  activeInvoice: InvoiceInterface;
  onAction: (action: string) => void;
  isInPreview?: boolean;
}
const MoreOptionsModal: React.FC<Props> = ({ onAction, isInPreview, activeInvoice, ...props }) => {
  const options = [
    ...(isInPreview
      ? []
      : [
          {
            text: 'Preview Invoice',
            onPress: () => onAction('preview'),
            icon: <Eye size={wp(15)} color={colors.primary.light} />,
          },
        ]),
    {
      text: 'Edit Invoice',
      onPress: () => onAction('edit'),
      icon: <Edit2 size={wp(15)} color={colors.black.secondary} />,
    },
    {
      text: 'Download Invoice',
      onPress: () => onAction('download'),
      icon: <ReceiveSquare size={wp(15)} color={colors.accentOrange.main} />,
    },

    {
      text: 'Send Invoice',
      onPress: () => onAction('send'),
      icon: <Send2 size={wp(15)} color={colors.primary.main} />,
    },
    ...(activeInvoice.status === INVOICE_STATUSES.PAID
      ? []
      : [
          {
            text: 'Mark Invoice as Paid',
            onPress: () => onAction('mark'),
            icon: <TickCircle size={wp(15)} color={colors.accentGreen.main} />,
          },
          {
            text: 'Delete Invoice',
            onPress: () => onAction('delete'),
            icon: <Trash size={wp(15)} color={colors.accentRed.main} />,
          },
        ]),
  ];

  return (
    <BottomModal
      wrapChildren
      // childrenWrapperStyle={{ padding: 20 }}
      {...props}
      closeModal={props.closeModal}
      showButton={false}>
        <View className='mx-20 pb-10'>
        <View className="px-15 py-12 bg-grey-bgOne rounded-[20px]">
          {options.map((item, index) => (
            <Pressable key={index} onPress={item.onPress}>
              <Row classes={cx("py-12", {'border-b border-b-grey-border': index !== options.length - 1})} spread={false} key={index}>
                <CircledIcon classes="bg-white">{item.icon}</CircledIcon>
                <BaseText classes="ml-2.5">{item.text}</BaseText>
              </Row>
            </Pressable>
          ))}
        </View>
        </View>
    </BottomModal>
  );
};
export default MoreOptionsModal;
