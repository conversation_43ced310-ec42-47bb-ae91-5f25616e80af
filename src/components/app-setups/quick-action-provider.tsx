import { useQuickActionCallback } from 'node_modules/expo-quick-actions/hooks';
import * as QuickActions from 'expo-quick-actions';
import { useEffect } from 'react';
import { Platform, View } from 'react-native';
import { navigate } from 'src/navigation';

const QuickActionProvider = () => {
  useQuickActionCallback(action => {
    actionEvent?.[action?.id]?.onPress?.();
  });

  useEffect(() => {
    QuickActions.setItems([
      {
        icon: Platform.OS === 'ios' ? 'symbol:plus.circle' : undefined,
        title: 'Add Products',
        description: 'Upload store products',
        id: '0',
      },
      {
        icon: Platform.OS === 'ios' ? 'symbol:bag' : undefined,
        title: 'Record Order',
        description: 'Record orders you received',
        id: '1',
      },
      {
        icon: Platform.OS === 'ios' ? 'symbol:ticket' : undefined,
        title: 'Create coupons',
        description: 'Manage coupons for products',
        id: '2',
      },
      {
        icon: Platform.OS === 'ios' ? 'symbol:percent' : undefined,
        title: 'Create Discounts',
        description: 'Mange discounts for products',
        id: '3',
      },
    ]);
  }, []);

  return <View />;
};

export default QuickActionProvider;

const actionEvent = {
  [0]: {onPress: () => navigate('CreateProducts')},
  [1]: {onPress: () => navigate('RecordOrder')},
  [2]: {onPress: () => navigate('CreateCoupon')},
  [3]: {onPress: () => navigate('CreateDiscount')},
};
