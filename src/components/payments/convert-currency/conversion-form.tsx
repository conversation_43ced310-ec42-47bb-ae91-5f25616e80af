import { useEffect, useMemo, useRef, useState } from 'react';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { BaseText, CircledIcon } from '@/components/ui';
import CurrencyInput from '../../ui/inputs/currency-input';
import SectionContainer from '../../ui/section-container';
import Separator from '../../ui/others/separator';
import Row from '../../ui/row';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import { FormikProps } from 'node_modules/formik/dist';
import {
  ConversionForm as ConversionFormData,
  ConversionQuote,
  CONVERT_CURRENCY_STEPS,
  isPairSupported,
} from './convert-currency-modal';
import { CURRENCY_FLAG_MAP, paymentsEnabledCurrencies } from 'catlog-shared';
import { currencyDetails } from '../wallet-cards';
import { amountFormat, getFieldvalues, toCurrency, toNaira } from 'src/assets/utils/js';
import dayjs from 'node_modules/dayjs';
import colors from 'src/theme/colors';

interface ConversionFormProps {
  form: FormikProps<ConversionFormData>;
  quoteData: ConversionQuote;
  isLoadingRate: boolean;
  step: CONVERT_CURRENCY_STEPS;
  fetchQuote: () => void;
}

const ConversionForm = ({ form, quoteData, isLoadingRate, step, fetchQuote }: ConversionFormProps) => {
  const { wallets } = useWalletContext();

  const [timeLeft, setTimeLeft] = useState(0);
  const countdownTimerRef = useRef(null);
  const storeCurrencies = useMemo(() => wallets.map(w => w?.currency), [wallets]);
  const storeCurrencyOptions = useMemo(
    () =>
      storeCurrencies.map(currency => ({
        label: currency,
        value: currency,
        leftElement: <View className="w-20 h-20">{currencyDetails[currency].icon({})}</View>,
      })),
    [storeCurrencies],
  );

  const destinationCurrencyOptions = useMemo(() => {
    const newOptions = paymentsEnabledCurrencies
      .filter(quoteCurrency => isPairSupported(form.values.source_currency, quoteCurrency))
      .map(currency => ({
        label: currency,
        value: currency,
        leftElement: <View className="w-20 h-20">{currencyDetails[currency].icon({})}</View>,
      }));

    return newOptions;
  }, [form?.values]);

  useEffect(() => {
    if (!destinationCurrencyOptions.find(option => option.value === form.values.destination_currency)) {
      form.setFieldValue(
        'destination_currency',
        destinationCurrencyOptions.length > 0 ? destinationCurrencyOptions[0].value : '',
      );
    }
  }, [destinationCurrencyOptions, form.values.destination_currency]);

  const currentWallet = wallets.find(wallet => wallet?.currency === form.values.source_currency);
  const isValidQuote = quoteData && quoteData.rate > 0 && quoteData.reference;

  useEffect(() => {
    if (countdownTimerRef.current) clearInterval(countdownTimerRef.current);

    if (!quoteData.expire_at) return;
    const timeLeftInSeconds = dayjs(quoteData.expire_at).diff(dayjs(), 'second');
    setTimeLeft(timeLeftInSeconds);

    countdownTimerRef.current = setInterval(() => {
      setTimeLeft(prevTimeLeft => {
        const timeLeft = prevTimeLeft - 1;
        if (timeLeft === 0) {
          clearInterval(countdownTimerRef.current);
          fetchQuote();

          return 0;
        }

        return timeLeft < 1 ? 0 : timeLeft;
      });
    }, 1000);

    return () => {
      if (countdownTimerRef.current) clearInterval(countdownTimerRef.current);
    };
  }, [quoteData.reference, step]);

  return (
    <View className="mx-20 mb-20">
      <CurrencyInput
        label={"Amount you're converting"}
        currencyOptions={storeCurrencyOptions}
        selectedCurrency={form.values.source_currency}
        onChangeCurrency={currency => form.setFieldValue('source_currency', currency)}
        {...getFieldvalues('amount', form)}
      />
      <Row className="my-5 justify-end w-full">
        <Row className="bg-grey-bgOne rounded-[20px] py-8 px-12 self-end">
          <View>
            <BaseText fontSize={11} classes="text-black-muted">
              Current Balance:{' '}
            </BaseText>
          </View>
          <BaseText fontSize={11} weight="semiBold" classes="text-black-secondary">
            {toCurrency(toNaira(currentWallet?.balance || 0), currentWallet?.currency)}
          </BaseText>
        </Row>
      </Row>
      {isLoadingRate && (
        <SectionContainer className="px-0">
          <Row className="p-16">
            <BaseText classes="text-black-muted" fontSize={13}>
              Loading Rate..
            </BaseText>
            <ActivityIndicator size="small" color={colors.primary.main} />
          </Row>
        </SectionContainer>
      )}
      {isValidQuote && (
        <SectionContainer className="px-0 my-10">
          <Row className="p-16">
            <BaseText classes="text-black-muted" fontSize={13}>
              Conversion Rate:
            </BaseText>
            <BaseText weight="medium" classes="text-black-secondary" fontSize={13}>
              1.00 {form.values.source_currency} = {amountFormat(quoteData.rate, 5)} {form.values.destination_currency}
            </BaseText>
          </Row>
          <Separator className="mx-0 my-0" />
          <Row className="p-16">
            <BaseText classes="text-black-muted" fontSize={13}>
              Conversion Fee:
            </BaseText>
            <BaseText weight="medium" classes="text-black-secondary" fontSize={13}>
              {toCurrency(quoteData.fee, form.values.source_currency)}
            </BaseText>
          </Row>
          <Row className="p-16">
            <BaseText classes="text-black-muted" fontSize={13}>
              Quote Expires In:
            </BaseText>
            <BaseText weight="medium" classes="text-black-secondary" fontSize={13}>
              {isLoadingRate ? <ActivityIndicator size="small" color={colors.primary.main} /> : `${timeLeft} seconds`}
            </BaseText>
          </Row>
        </SectionContainer>
      )}
      <CurrencyInput
        label={"Amount you'd receive"}
        containerClasses="mt-15"
        selectedCurrency={form.values.destination_currency ?? destinationCurrencyOptions[0].value}
        onChangeCurrency={currency => form.setFieldValue('destination_currency', currency)}
        // editable={false}
        value={quoteData.amount_to_receive}
        currencyOptions={destinationCurrencyOptions}
      />
    </View>
  );
};

export default ConversionForm;
