import { ScrollView, View } from 'react-native';
import { useEffect, useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { ListCard } from '@/components/ui/cards/list-item-card';
import { BaseText, CircledIcon } from '@/components/ui';
import { hp, setDisappearingError, toKobo, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import useSteps from 'src/hooks/use-steps';
import { TickCircle } from 'iconsax-react-native/src';
import ConversionForm from './conversion-form';
import ConversionSummary from './conversion-summary';
import EnterSecurityPin from './enter-security-pin';
import { FormikProps, useFormik } from 'node_modules/formik/dist';
import { CURRENCIES, GENERATE_QUOTE, INITIATE_CONVERSION, Wallet } from 'catlog-shared';
import { useApi } from 'src/hooks/use-api';
import useDebounce from 'src/hooks/use-debounce';
import { WalletBalances } from 'src/contexts/wallet/store';
import * as Yup from 'yup';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import ModalErrorLabel from 'src/components/ui/modal-error-label';
import { ButtonProps, ButtonVariant } from 'src/components/ui/buttons/button';
import dayjs from 'node_modules/dayjs';
import { useStoreReview } from 'src/hooks/use-store-review';

export interface ConversionForm {
  source_currency: CURRENCIES;
  destination_currency: CURRENCIES;
  amount: number;
  code: string;
  security_pin: string;
}

export interface ConversionQuote {
  reference: string;
  rate: number;
  fee: number;
  amount_to_receive: number;
  expire_at: string;
}

export enum CONVERT_CURRENCY_STEPS {
  FORM = 'FORM',
  SUMMARY = 'SUMMARY',
  ENTER_SECURITY_PIN = 'ENTER_SECURITY_PIN',
  SUCCESS = 'SUCCESS',
}

interface ConvertCurrencyModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  initialWallet: Wallet;
}

const ConvertCurrencyModal = ({
  children,
  closeModal,
  onPressProceed,
  initialWallet,
  ...props
}: ConvertCurrencyModalProps) => {
  const formSteps = useSteps(Object.values(CONVERT_CURRENCY_STEPS), 0);
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;
  const { wallets, updateBalance } = useWalletContext();

  const { handleReviewRequest } = useStoreReview();

  let form: FormikProps<ConversionForm>;
  const [error, setError] = useState('');
  const balances = wallets.map(w => ({ currency: w?.currency, balance: w.balance, id: w.id }));

  const generateQuoteReq = useApi({
    apiFunction: GENERATE_QUOTE,
    key: GENERATE_QUOTE.name,
    method: 'POST',
  });

  const initiateConversionReq = useApi({
    apiFunction: INITIATE_CONVERSION,
    key: INITIATE_CONVERSION.name,
    method: 'POST',
  });

  const quoteData: ConversionQuote = generateQuoteReq.response?.data ?? {
    reference: '',
    rate: 0,
    fee: 0,
    amount_to_receive: 0,
    expire_at: '',
  };
  const wallet = wallets.find(w => w?.currency === form?.values?.source_currency);

  form = useFormik<ConversionForm>({
    initialValues: {
      source_currency: initialWallet.currency,
      destination_currency: null,
      amount: 0,
      code: '',
      security_pin: '',
    },
    validationSchema: validationSchema(balances, step),
    onSubmit: async values => {
      switch (step) {
        case CONVERT_CURRENCY_STEPS.FORM:
          const insufficientFunds = toKobo(form.values.amount + quoteData.fee) > wallet?.balance;
          if (insufficientFunds) {
            setError('Insufficient funds for fee, please adjust the amount');
            return;
          }

          next();
          break;
        case CONVERT_CURRENCY_STEPS.SUMMARY:
          next();
          break;
        case CONVERT_CURRENCY_STEPS.ENTER_SECURITY_PIN:
          const [res, err] = await initiateConversionReq.makeRequest({
            quote_reference: quoteData.reference,
            amount: Number(values.amount),
            security_pin: values.security_pin,
            from_wallet: wallets.find(w => w?.currency === values.source_currency).id,
            to_wallet: wallets.find(w => w?.currency === values.destination_currency).id,
          });

          if (err) {
            setDisappearingError(err?.message, setError);
            changeStep(CONVERT_CURRENCY_STEPS.FORM);
            return;
          }

          if (!err) {
            updateBalance({
              walletId: wallet?.id,
              type: 'debit',
              amount: toKobo(Number(values.amount) + Number(quoteData.fee)),
            });
            next();
            handleReviewRequest();
          }

          break;
        case CONVERT_CURRENCY_STEPS.SUCCESS:
          closeModal();
          break;
      }
    },
  });

  const debouncedAmount = useDebounce(form?.values?.amount, 500);

  useEffect(() => {
    form.setFieldValue('source_currency', initialWallet?.currency);
  }, [initialWallet]);

  useEffect(() => {
    if (!props.isVisible) {
      form.resetForm();
      changeStep(CONVERT_CURRENCY_STEPS.FORM);
      generateQuoteReq.reset();
      initiateConversionReq.reset();
    }
  }, [props.isVisible]);

  useEffect(() => {
    if (debouncedAmount && !form.errors?.amount) {
      fetchQuote();
    }
  }, [debouncedAmount, form?.errors?.amount]);

  const fetchQuote = async () => {
    // setTimeout(() => {
    if (step !== CONVERT_CURRENCY_STEPS.FORM) {
      return;
    }
    const requestData = {
      source_currency: form?.values?.source_currency,
      destination_currency: form?.values?.destination_currency,
      amount: Number(debouncedAmount),
    };
    const [res, err] = await generateQuoteReq.makeRequest(requestData);

    if (err) {
      setDisappearingError(err?.message, setError);
    }
    // }, 250); //waiting a few milliseconds helps prevent a bug where the setTimeLeft react state update retriggers
  };

  const insufficientFunds = toKobo(form.values.amount + quoteData.fee) > wallet?.balance;
  const stepCTAMap: { [key: string]: ButtonProps[] } = {
    [CONVERT_CURRENCY_STEPS.FORM]: [
      {
        text: 'Continue',
        disabled:
          !canNext ||
          generateQuoteReq.isLoading ||
          !quoteData ||
          !form.values.amount ||
          !form.values.destination_currency ||
          insufficientFunds,
        onPress: () => form.handleSubmit(),
        isLoading: generateQuoteReq.isLoading,
        loadingText: 'Fetching Quote...',
      },
    ],
    [CONVERT_CURRENCY_STEPS.SUMMARY]: [
      {
        variant: ButtonVariant.LIGHT,
        text: 'Back',
        disabled: false,
        onPress: () => changeStep(CONVERT_CURRENCY_STEPS.FORM),
      },
      {
        text: 'Convert',
        disabled: false,
        onPress: () => form.handleSubmit(),
        isLoading: false,
        loadingText: '',
      },
    ],
    [CONVERT_CURRENCY_STEPS.ENTER_SECURITY_PIN]: [
      {
        variant: ButtonVariant.LIGHT,
        text: 'Back',
        disabled: false,
        onPress: () => changeStep(CONVERT_CURRENCY_STEPS.SUMMARY),
      },
      {
        text: 'Confirm',
        disabled: initiateConversionReq.isLoading,
        onPress: () => form.handleSubmit(),
        isLoading: initiateConversionReq.isLoading,
        loadingText: 'Please wait...',
      },
    ],
    [CONVERT_CURRENCY_STEPS.SUCCESS]: [
      {
        text: 'Continue',
        disabled: false,
        onPress: () => closeModal(),
        isLoading: false,
      },
    ],
  };

  return (
    <BottomModal
      closeModal={closeModal}
      avoidKeyboard={false}
      size="md"
      title={modalTitles[step]}
      buttons={stepCTAMap[step]}
      {...props}>
      <ModalErrorLabel error={error} classes="mb-15" />
      <ScrollView contentContainerStyle={{ marginBottom: 20, marginTop: 10 }}>
        {step === CONVERT_CURRENCY_STEPS.FORM && (
          <ConversionForm form={form} isLoadingRate={generateQuoteReq.isLoading} {...{ quoteData, fetchQuote, step }} />
        )}
        {step === CONVERT_CURRENCY_STEPS.SUMMARY && (
          <ConversionSummary quoteData={quoteData} conversionData={form.values} />
        )}
        {step === CONVERT_CURRENCY_STEPS.ENTER_SECURITY_PIN && <EnterSecurityPin form={form} />}
        {step === CONVERT_CURRENCY_STEPS.SUCCESS && (
          <View className="mx-20 mb-[60px]">
            <CircledIcon className="self-center bg-accentGreen-pastel p-8">
              <CircledIcon className="bg-accentGreen-main p-20">
                <TickCircle variant={'Bold'} size={wp(40)} color={colors.white} />
              </CircledIcon>
            </CircledIcon>
            <BaseText fontSize={20} type={'heading'} classes="text-center mt-10">
              Your conversion is processing
            </BaseText>
            <BaseText type={'body'} classes="text-center mt-10 text-black-muted">
              We're processing your conversion now and it should be completed shortly.
            </BaseText>
          </View>
        )}
      </ScrollView>
    </BottomModal>
  );
};

const validationSchema = (balances: WalletBalances['wallets'], step: string) =>
  Yup.object().shape({
    amount: Yup.number()
      .typeError('Amount must be a number')
      .required('Amount is required')
      .min(1, 'Please enter an amount greater than zero')
      .test('is-greater', 'Insufficient funds', function (value) {
        const balance = balances.find(b => b?.currency === this?.parent?.source_currency).balance || 0;
        return balance >= value * 100;
      }),
    source_currency: Yup.string().required('Source currency is required'),
    destination_currency: Yup.string()
      .required('Destination currency is required')
      .test('is_pair_supported', 'This currency pair is not supported', function (value) {
        return isPairSupported(this.parent.source_currency, value);
      }),
    security_pin:
      step === 'SECURITY_PIN'
        ? Yup.string().required('Security PIN is required').min(6, 'Security PIN must be at least 6 digits')
        : Yup.string(),
  });

const pairsToSkip = new Set([
  'NGN/GHS',
  'NGN/ZAR',
  'NGN/KES',
  'GHS/NGN',
  'GHS/KES',
  'GHS/ZAR',
  'KES/ZAR',
  'KES/GHS',
  'ZAR/NGN',
  'ZAR/KES',
  'ZAR/GHS',
]);

const unsupportedCurrencies = [];

export const isPairSupported = (baseCurrency, quoteCurrency) => {
  if (baseCurrency === quoteCurrency) return false;
  if (unsupportedCurrencies.includes(baseCurrency) || unsupportedCurrencies.includes(quoteCurrency)) {
    return false;
  }
  const pair = `${baseCurrency}/${quoteCurrency}`;
  if (pairsToSkip.has(pair)) {
    return false;
  }
  return true;
};

const modalTitles = {
  [CONVERT_CURRENCY_STEPS.FORM]: 'Convert Funds',
  [CONVERT_CURRENCY_STEPS.SUMMARY]: 'Conversion Summary',
  [CONVERT_CURRENCY_STEPS.ENTER_SECURITY_PIN]: 'Enter Security PIN',
  [CONVERT_CURRENCY_STEPS.SUCCESS]: 'Conversion Complete',
};

export default ConvertCurrencyModal;
