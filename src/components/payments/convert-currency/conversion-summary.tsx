import { View } from 'react-native';
import { BaseText, CircledIcon } from '@/components/ui';
import SectionContainer from '../../ui/section-container';
import Separator from '../../ui/others/separator';
import { Coin1 } from 'iconsax-react-native/src';
import { amountFormat, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { ConversionForm, ConversionQuote } from './convert-currency-modal';

interface ConversionSummaryProps {
  quoteData: ConversionQuote;
  conversionData: ConversionForm;
}

const ConversionSummary = ({ quoteData, conversionData }: ConversionSummaryProps) => {
  return (
    <View className="px-20">
      <View className="items-center">
        <CircledIcon className="bg-accentRed-main p-20">
          <Coin1 size={wp(28)} color={colors.white} variant="Bold" />
        </CircledIcon>
        <BaseText fontSize={20} type="heading" classes="text-black-main mt-12">
          Your Conversion
        </BaseText>
      </View>
      <SectionContainer className="px-0">
        <View className="p-16">
          <BaseText classes="text-black-muted">You're Converting:</BaseText>
          <BaseText weight="semiBold" classes="mt-5 text-black-secondary">
            {toCurrency(conversionData.amount, conversionData.source_currency)}
          </BaseText>
        </View>
        <Separator className="mx-0 my-0" />
        <View className="p-16">
          <BaseText classes="text-black-muted">Conversion Rate:</BaseText>
          <BaseText weight="medium" classes="mt-5 text-black-secondary">
            1.00 {conversionData.source_currency} = {amountFormat(quoteData.rate, 5)}{' '}
            {conversionData.destination_currency}
          </BaseText>
        </View>
        <View className="p-16 pt-0">
          <BaseText classes="text-black-muted">Conversion Fee:</BaseText>
          <BaseText weight="medium" classes="mt-5 text-black-secondary">
            {amountFormat(quoteData.fee, 2)} {conversionData.source_currency}
          </BaseText>
        </View>
        <Separator className="mx-0 my-0" />
        <View className="p-16">
          <BaseText classes="text-black-muted">You're Receiving:</BaseText>
          <BaseText weight="semiBold" classes="mt-5 text-black">
            {toCurrency(quoteData.amount_to_receive, conversionData.destination_currency)}
          </BaseText>
        </View>
      </SectionContainer>
    </View>
  );
};

export default ConversionSummary;
