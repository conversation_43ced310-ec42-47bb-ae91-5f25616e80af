import { ScrollView, View } from 'react-native';
import { BaseText } from '@/components/ui';
import PinInput from '@/components/ui/inputs/pin-input';

interface EnterOtpProps {}

const EnterOtp = ({}: EnterOtpProps) => {
  return (
    <View className="mx-20">
      <BaseText fontSize={18} type="heading">
        Confirm Otp
      </BaseText>
      <BaseText className="text-black-muted">Please enter the 6 digit OTP sent to your email</BaseText>
      <PinInput value="" setValue={() => {}} />
    </View>
  );
};

export default EnterOtp;
