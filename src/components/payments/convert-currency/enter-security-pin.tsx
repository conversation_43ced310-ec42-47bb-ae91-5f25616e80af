import { ScrollView, View } from 'react-native';
import { BaseText } from '@/components/ui';
import PinInput from '@/components/ui/inputs/pin-input';
import { FormikProps } from 'node_modules/formik/dist';
import { ConversionForm } from './convert-currency-modal';
import { getFieldvalues } from 'src/assets/utils/js';

interface EnterSecurityPinProps {
  form: FormikProps<ConversionForm>;
}

const EnterSecurityPin = ({ form }: EnterSecurityPinProps) => {
  return (
    <View className="mx-20">
      <BaseText classes="text-black-muted">Please enter your security pin to complete conversion</BaseText>
      <PinInput {...getFieldvalues('security_pin', form)} setValue={pin => form.setFieldValue('security_pin', pin)} />
    </View>
  );
};

export default EnterSecurityPin;
