import React, { useEffect } from 'react';
import { GenericFormRef } from 'src/screens/payments/payment-links/payment-link-form';
import PersonalInfo from './personal-info';
import BvnInfo from './bvn-info';
import PhoneInfo from './phone-info';
import AddressInfo from './address-info';
import IdCardInfo from './id-info';
import { COUNTRIES, KYCInfo } from 'catlog-shared';
import { validateData } from 'src/screens/payments/kyc';
import { KYC_FORM_STEPS, SharedKYCStepProps } from '../types';

interface KYCFormsProps extends SharedKYCStepProps {
  formRefs: {
    basicInfo: React.MutableRefObject<GenericFormRef>;
    bvnPhoneInfo: React.MutableRefObject<GenericFormRef>;
    addressInfo: React.MutableRefObject<GenericFormRef>;
  };
}

const KYCForms = (props: KYCFormsProps) => {
  const { formRefs, ...commonProps } = props;
  const { step } = props.formSteps;
  const isNGKYC = props.country === COUNTRIES.NG;

  useEffect(() => {
    if (commonProps.kycInfo) {
      const step = getStep(commonProps.kycInfo, props.country);
      props.formSteps.changeStep(step);
      return;
    }

    props.formSteps.changeStep(KYC_FORM_STEPS.PERSONAL_INFO);
  }, [commonProps.kycInfo, props.country]);

  return (
    <>
      {step === KYC_FORM_STEPS.PERSONAL_INFO && <PersonalInfo ref={formRefs.basicInfo} {...commonProps} />}
      {step === KYC_FORM_STEPS.BVN_PHONE_INFO &&
        (isNGKYC ? (
          <BvnInfo ref={formRefs.bvnPhoneInfo} {...commonProps} />
        ) : (
          <PhoneInfo ref={formRefs.bvnPhoneInfo} {...commonProps} />
        ))}
      {step === KYC_FORM_STEPS.ID_CARD_INFO && <IdCardInfo {...commonProps} defaultToManual={isNGKYC} />}
      {step === KYC_FORM_STEPS.ADDRESS_INFO && <AddressInfo ref={formRefs.addressInfo} {...commonProps} />}
    </>
  );
};

const getStep = (kycInfo: KYCInfo, country: COUNTRIES) => {
  if (!validateData(kycInfo).BASIC) return KYC_FORM_STEPS.PERSONAL_INFO;
  if (!validateData(kycInfo).BVN && country === COUNTRIES.NG) return KYC_FORM_STEPS.BVN_PHONE_INFO;
  if (!validateData(kycInfo).PHONE && country === COUNTRIES.GH) return KYC_FORM_STEPS.BVN_PHONE_INFO;
  if (!validateData(kycInfo).ID) return KYC_FORM_STEPS.ID_CARD_INFO;
  return KYC_FORM_STEPS.ADDRESS_INFO;
};

export default KYCForms;
