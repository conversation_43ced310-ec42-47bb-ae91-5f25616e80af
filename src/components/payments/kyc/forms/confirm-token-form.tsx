import AsyncStorage from '@react-native-async-storage/async-storage';
import { FormikProps } from 'formik';
import classNames from 'node_modules/classnames';
import { Message } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { getFieldvalues, openLinkInBrowser, wp } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import ResendCodeBtn from 'src/components/ui/buttons/resend-code-btn';
import Input from 'src/components/ui/inputs/input';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import Row from 'src/components/ui/row';
import { ApiData } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import { ArrowUpRight } from 'src/components/ui/icons';
import { CONTACT_SUPPORT_WA_LINK } from 'src/assets/utils/js/constants';

interface TokenFormProps {
  form: FormikProps<any>;
  resendReq: ApiData<any, any>;
  req: ApiData<any, any>;
  fieldKey: string;
  showContactSupport?: boolean;
}
export const ConfirmTokenForm: React.FC<TokenFormProps> = ({
  form,
  fieldKey,
  resendReq,
  req,
  showContactSupport = false,
}) => {
  const RESEND_DELAY_MINS = 5;

  const [resendStatus, setResendStatus] = useState<'success' | 'failed' | 'pending'>();
  const [lastResendTime, setLastResendTime] = useState<number>(null);
  const [nextResendTime, setNextResendTime] = useState<number>(0);
  const canResendToken = nextResendTime <= 0;
  const storageKey = 'last-send-bvn-token';

  useEffect(() => {
    const fn = async () => {
      const storedLastResendTime = AsyncStorage.getItem(storageKey);

      if (storedLastResendTime) {
        setLastResendTime(Number(storedLastResendTime));
      }
    };
    fn();
  }, []);

  useEffect(() => {
    let interval;
    const fn = async () => {
      if (!lastResendTime) {
        return;
      }

      const storedLastResendTime = await AsyncStorage.getItem(storageKey);

      if (storedLastResendTime !== String(lastResendTime)) {
        await AsyncStorage.setItem(storageKey, String(lastResendTime));
      }

      interval = setInterval(async () => {
        const nextResendTime = RESEND_DELAY_MINS * 60000 - (Date.now() - lastResendTime);
        setNextResendTime(nextResendTime);

        if (nextResendTime < 0) {
          clearInterval(interval);
          await AsyncStorage.removeItem(storageKey);
        }
      }, 1000);
    };
    fn();
    return () => {
      clearInterval(interval);
    };
  }, [lastResendTime]);

  const resend = async () => {
    setResendStatus('pending');
    const [res, error] = await resendReq.makeRequest({ [fieldKey]: form.values[fieldKey] });

    if (res) {
      setResendStatus('success');
      setLastResendTime(Date.now());
    } else {
      setResendStatus('failed');
    }

    setTimeout(() => {
      setResendStatus(null);
    }, 2000);
  };

  const statusMap = {
    success: {
      title: 'Token Resent Successfully',
      statusType: StatusType.SUCCESS,
    },
    failed: {
      title: resendReq?.error?.message,
      statusType: StatusType.DANGER,
    },
    pending: {
      title: 'Sending...',
      statusType: StatusType.PROCESSING,
    },
  };

  return (
    <>
      <View className="bg-primary-pastel p-10 rounded-12 items-center mt-15">
        <BaseText fontSize={11} classes="text-center text-primary-main">
          We&apos;ve sent a token to {req?.response?.data?.email ? `${req?.response?.data?.email},` : ''}{' '}
          {req?.response?.data?.email && req?.response?.data?.phone ? 'and' : ''}{' '}
          {req?.response?.data?.phone ? req?.response?.data?.phone : ''}
        </BaseText>
      </View>
      {statusMap[resendStatus] && <StatusPill {...statusMap[resendStatus]} className="mt-10" />}
      <Input
        {...getFieldvalues('token', form)}
        label={'Enter Token'}
        containerClasses="mt-15"
        keyboardType={'number-pad'}
        inputMode="numeric"
      />
      <Row
        className={classNames('mt-10 items-center', {
          'justify-between': showContactSupport,
          'justify-end': !showContactSupport,
        })}>
        {showContactSupport && (
          <Pressable className="flex items-center flex-row" onPress={() => openLinkInBrowser(CONTACT_SUPPORT_WA_LINK)}>
            <BaseText fontSize={11} weight="medium" classes="text-primary-main mr-2">
              Contact Support
            </BaseText>
            <ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />
          </Pressable>
        )}
        <ResendCodeBtn nextResendTime={nextResendTime} disableResend={!canResendToken} onPressResend={resend} />
      </Row>
    </>
  );
};
