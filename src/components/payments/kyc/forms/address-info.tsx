import Container from '@/components/ui/container';
import { SAVE_ADDRESS } from 'catlog-shared';
import { COUNTRIES } from 'catlog-shared';
import { getLgas } from 'catlog-shared';
import { getDistricts } from 'catlog-shared';
import { getSubCounties } from 'catlog-shared';
import { getSuburbsAndTownships } from 'catlog-shared';
import { useFormik } from 'formik';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { getFieldvalues, getStates, wp, Yup } from 'src/assets/utils/js';
import { Row } from 'src/components/ui';
import Input from 'src/components/ui/inputs/input';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { GenericFormRef } from 'src/screens/payments/payment-links/payment-link-form';
import { SharedKYCStepProps } from '../types';
import CustomImage from 'src/components/ui/others/custom-image';

interface AddressInfoProps extends SharedKYCStepProps {}

const AddressInfo = (
  { formSteps, setIsLoading, next, setKycInfo, kycInfo, setCTAEnabled }: AddressInfoProps,
  ref: ForwardedRef<GenericFormRef>,
) => {
  const { store } = useAuthContext();
  const kycCountry = kycInfo?.country ?? store?.country?.code;
  const states = getStates(kycCountry);

  const saveAddressReq = useApi({
    apiFunction: SAVE_ADDRESS,
    key: SAVE_ADDRESS.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      address_line1: kycInfo?.address?.address_line1 ?? '',
      lga: kycInfo?.address?.lga ?? '',
      state: kycInfo?.address?.state ?? '',
      city: kycInfo?.address?.city ?? '',
    },
    onSubmit: async values => {
      setIsLoading(true);
      const [res, err] = await saveAddressReq.makeRequest(values);

      if (res) {
        setKycInfo(res?.data);
        //go to summary page
        next();
      }

      setIsLoading(false);
    },
    validationSchema,
  });

  useImperativeHandle(ref, () => ({ submitForm: () => form.submitForm() }), []);
  const lgas = useMemo(() => lgaCountryMap[kycCountry](form.values.state), [kycInfo, form.values.state]);
  useEffect(() => {
    if (Object.keys(form.errors).length < 1) {
      setCTAEnabled(true);
    } else {
      setCTAEnabled(false);
    }
  }, [form.errors]);

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        pageTitleTop={'Final Step'}
        pageTitleBottom={'Your Address'}
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/address.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
      />
      <Container className="mt-16">
        <Input
          {...getFieldvalues('address_line1', form)}
          label={'Street Address'}
          containerClasses="mt-15"
          autoComplete={'street-address'}
        />
        <SelectDropdown
          items={arrayToInputValues(states)}
          label={`Select ${labelsMap[kycCountry].state}`}
          showLabel
          containerClasses="mt-15"
          {...getFieldvalues('state', form, 'select')}
        />
        <Row style={{ gap: wp(10) }}>
          <View className="flex-1">
            <SelectDropdown
              items={arrayToInputValues(lgas)}
              label={`Search ${labelsMap[kycCountry].lga}`}
              showLabel
              containerClasses="mt-15"
              {...getFieldvalues('lga', form, 'select')}
            />
          </View>
          <View className="flex-1">
            <Input {...getFieldvalues('city', form)} label={'Your City'} containerClasses="mt-15 h-[48px]" />
          </View>
        </Row>
      </Container>
    </Animatable.View>
  );
};

const validationSchema = Yup.object().shape({
  address_line1: Yup.string().required('Street address is required'),
  lga: Yup.string().required('Local government is required'),
  state: Yup.string().required('State is required'),
  city: Yup.string().required('City is required'),
});

const lgaCountryMap = {
  [COUNTRIES.NG]: state => getLgas(state),
  [COUNTRIES.GH]: state => getDistricts(state),
  [COUNTRIES.ZA]: state => getSuburbsAndTownships(state),
  [COUNTRIES.KE]: state => getSubCounties(state),
};

const labelsMap = {
  [COUNTRIES.NG]: {
    state: 'State',
    lga: 'LGA',
  },
  [COUNTRIES.GH]: {
    state: 'Region',
    lga: 'District',
  },
  [COUNTRIES.ZA]: {
    state: 'Province',
    lga: 'Suburb/Township',
  },
  [COUNTRIES.KE]: {
    state: 'County',
    lga: 'Sub County',
  },
};

const arrayToInputValues = (arr: string[]) => arr.map(val => ({ label: val, value: val }));

export default forwardRef(AddressInfo);
