import Container from '@/components/ui/container';
import { LOOKUP_BVN, RESEND_BVN_TOKEN, VERIFY_BVN } from 'catlog-shared';
import { useFormik } from 'formik';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import * as Animatable from 'react-native-animatable';
import Toast from 'react-native-toast-message';
import { dateToDDMMYYYY, DDMMYYYYToDate, getFieldvalues, showError, wp, Yup } from 'src/assets/utils/js';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText } from 'src/components/ui';
import CalenderInput from 'src/components/ui/inputs/calender-input';
import Input from 'src/components/ui/inputs/input';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import { useApi } from 'src/hooks/use-api';
import { GenericFormRef } from 'src/screens/payments/payment-links/payment-link-form';
import { ConfirmTokenForm } from './confirm-token-form';
import Pressable from 'src/components/ui/base/pressable';
import { ArrowUpRight } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import dayjs from 'node_modules/dayjs';
import { SharedKYCStepProps } from '../types';

import customParseFormat from 'dayjs/plugin/customParseFormat';
import CustomImage from 'src/components/ui/others/custom-image';

dayjs.extend(customParseFormat);

interface BvnInfoProps extends SharedKYCStepProps {}

const BvnInfo = (
  { formSteps, kycInfo, setKycInfo, next, setIsLoading, setCTAEnabled }: BvnInfoProps,
  ref: ForwardedRef<GenericFormRef>,
) => {
  // const [isVerified, setIsVerified] = useState(false);
  const [showContactSupport, setShowContactSupport] = useState(false);

  const lookupBVNReq = useApi({
    apiFunction: LOOKUP_BVN,
    key: LOOKUP_BVN.name,
    method: 'POST',
  });

  const verifyBVNReq = useApi({
    apiFunction: VERIFY_BVN,
    key: VERIFY_BVN.name,
    method: 'POST',
  });

  const resendTokenReq = useApi({
    apiFunction: RESEND_BVN_TOKEN,
    key: RESEND_BVN_TOKEN.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      bvn: kycInfo?.bvn,
      dob: kycInfo?.dob ? kycInfo.dob : dayjs().subtract(18, 'year').format('DD-MM-YYYY'),
      token: '',
      isVerified: Boolean(kycInfo?.bvn_verified_at && kycInfo?.bvn),
      hasLookup: false,
    },
    onSubmit: async ({ hasLookup, isVerified, ...values }, { setFieldTouched }) => {
      setIsLoading(true);
      if (!hasLookup && !isVerified) {
        setFieldTouched('token', false);
        const [res, err] = await lookupBVNReq.makeRequest(values);

        if (res) form.setFieldValue('hasLookup', true);
        if (err) {
          if (err.statusCode === 412) setShowContactSupport(true);
          else showError(err);
        }
      }

      if (hasLookup && !isVerified) {
        const [res, err] = await verifyBVNReq.makeRequest(values);

        if (err) {
          if (err.statusCode === 412) setShowContactSupport(true);
          else Toast.show({ type: 'error', text1: err.message });
        }

        if (res) {
          form.setFieldValue('isVerified', true);
          setKycInfo(res?.data);
          setIsLoading(false);
          formSteps.next();
        }
      }

      if (isVerified) formSteps.next();
      setIsLoading(false);
    },
    validationSchema,
  });

  useImperativeHandle(ref, () => ({ submitForm: () => form.submitForm() }), []);
  useEffect(() => {
    if (Object.keys(form.errors).length < 1) {
      setCTAEnabled(true);
    } else {
      setCTAEnabled(false);
    }
  }, [form.errors]);

  const showTokenForm = lookupBVNReq?.response && !form.values.isVerified;
  const isVerified = !!kycInfo?.bvn_verified_at && !!kycInfo?.bvn;

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/id-card.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
        customElements={
          <BaseText fontSize={22} type={'heading'} lineHeight={27}>
            BVN Information
          </BaseText>
        }
      />
      <Container className="mt-16">
        <Animatable.View animation={'fadeIn'}>
          {/* todo: @silas - need bvn explanation modal from @joseph */}
          <InfoBadge
            text="We need your BVN for verifications"
            addon={
              <Pressable className="flex items-center flex-row">
                <BaseText fontSize={11} weight="medium" classes="text-primary-main">
                  Learn More
                </BaseText>
                <ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />
              </Pressable>
            }
          />
        </Animatable.View>
        <Input
          {...getFieldvalues('bvn', form)}
          label={'BVN Number'}
          containerClasses="mt-15"
          keyboardType={'number-pad'}
          editable={!isVerified}
        />
        <CalenderInput
          classes="mt-15"
          inputProps={{
            label: 'Date of birth',
            value: form.values.dob,
          }}
          initialDate={form.values.dob ? dayjs(form.values.dob, 'DD-MM-YYYY').toDate() : new Date()}
          onDateChange={d => form.setFieldValue('dob', dateToDDMMYYYY(d))}
          calenderModalProps={{
            maxDate: dayjs().subtract(18, 'year').toDate(),
            minDate: dayjs().subtract(120, 'year').toDate(),
          }}
          disabled={isVerified}
        />
        {showTokenForm && (
          <ConfirmTokenForm
            form={form}
            resendReq={resendTokenReq}
            fieldKey="bvn"
            req={lookupBVNReq}
            showContactSupport={showContactSupport}
          />
        )}
      </Container>
    </Animatable.View>
  );
};

export default forwardRef(BvnInfo);

const validationSchema = Yup.object().shape({
  bvn: Yup.string()
    .required('BVN is required')
    .test('digits', 'BVN should contain only digits', value => /^\d+$/.test(value || ''))
    .length(11, 'BVN should be 11 digits'),

  token: Yup.string().when('hasLookup', {
    is: true,
    then: schema => schema.required('Token is required').length(6, 'Token must be exactly 6 characters'),
    otherwise: schema => schema.notRequired(),
  }),
});
