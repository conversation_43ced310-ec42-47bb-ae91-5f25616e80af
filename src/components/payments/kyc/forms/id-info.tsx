import Container from '@/components/ui/container';
import { MANUALLY_VERIFY_ID } from 'catlog-shared';
import { ID_TYPES } from 'catlog-shared';
import { KYCInfo } from 'catlog-shared/src/interfaces/stores';
import { useFormik } from 'formik';
import { SecurityUser } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import * as Animatable from 'react-native-animatable';
import { getFieldvalues, wp, Yup } from 'src/assets/utils/js';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText, CircledIcon } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import Input from 'src/components/ui/inputs/input';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import UploadIdModal from '../id-upload';
import { COUNTRIES } from 'catlog-shared/src/interfaces';
import { VERIFICATION_METHODS } from 'src/assets/utils/js/constants';
import { VERIFY_ID } from 'catlog-shared/src/api';
import { SharedKYCStepProps } from '../types';
import CustomImage from 'src/components/ui/others/custom-image';

interface IdCardInfoProps extends SharedKYCStepProps {
  defaultToManual?: boolean;
}

const IdCardInfo = ({ formSteps, kycInfo, setKycInfo, setCTAEnabled, defaultToManual }: IdCardInfoProps) => {
  const { modals, toggleModal } = useModals(['id_upload']);
  const [saveManually, setSaveManually] = useState(defaultToManual);
  const [idIsVerified, setIdIsVerified] = useState(false);

  const verifyIDReq = useApi({
    apiFunction: VERIFY_ID,
    key: VERIFY_ID.name,
    method: 'POST',
  });

  const manualIdVerificationReq = useApi({
    apiFunction: MANUALLY_VERIFY_ID,
    key: MANUALLY_VERIFY_ID.name,
    method: 'POST',
  });

  const verificationReq = saveManually ? manualIdVerificationReq : verifyIDReq;

  const form = useFormik({
    initialValues: {
      id_type: kycInfo?.identity?.type ?? '',
      id_number: kycInfo?.identity?.number ?? '',
      selfie: '',
      photo_id: kycInfo?.identity?.url ?? '',
      filename: kycInfo?.identity?.filename ?? '',
    },
    onSubmit: async values => {
      if (!modals.id_upload) {
        toggleModal('id_upload');
      } else {
        const [res, err] = await verificationReq.makeRequest(values);

        if (res) {
          handleSuccess();
        }
      }
    },
    validationSchema: validationSchema(modals.id_upload),
  });

  const retryManually = async () => {
    setSaveManually(true);
    const [res, err] = await manualIdVerificationReq.makeRequest(form.values);

    if (err) setSaveManually(false);
    if (res) {
      handleSuccess();
    }
  };

  const handleSuccess = () => {
    setIdIsVerified(true);
  };

  useEffect(() => {
    if (Object.keys(form.errors).length > 0 || !idIsVerified) {
      setCTAEnabled(false);
    } else {
      setCTAEnabled(true);
    }
  }, [form.errors]);

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        customElements={
          <BaseText fontSize={22} type={'heading'} lineHeight={27}>
            Identity Card
          </BaseText>
        }
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/id-card.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
      />
      <Container className="mt-16">
        <SelectDropdown
          items={getIdDropdownOptions(kycInfo?.country)}
          label={'Select ID Method'}
          showLabel
          containerClasses="mt-15"
          disabled={idIsVerified}
          {...getFieldvalues('id_type', form, 'select')}
        />
        <Input
          {...getFieldvalues('id_number', form)}
          label={'Enter ID Number'}
          containerClasses="mt-15"
          keyboardType={'number-pad'}
        />
        <Pressable
          onPress={() => toggleModal('id_upload')}
          className="flex-row bg-grey-bgTwo py-10 px-15 rounded-12 mt-15 justify-center items-center">
          <CircledIcon className="p-5 bg-white">
            <SecurityUser variant={'Bulk'} color={colors.accentYellow.main} size={wp(18)} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-center text-primary-main ml-10">
            Submit your ID
          </BaseText>
        </Pressable>
      </Container>
      <UploadIdModal
        form={form}
        handleSuccess={() => null}
        verifyIDReq={verificationReq}
        isVisible={modals.id_upload}
        closeModal={() => {
          if (verificationReq?.response?.data) {
            setKycInfo(verificationReq?.response?.data);
          }
          toggleModal('id_upload', false);
        }}
        idIsVerified={idIsVerified}
        retryManually={retryManually}
      />
    </Animatable.View>
  );
};

export default IdCardInfo;

const validationSchema = (startVerfication: boolean) =>
  Yup.object().shape({
    id_type: Yup.string().required('ID type is required'),
    id_number: Yup.string().when('id_type', {
      is: ID_TYPES.NIN,
      then: () => Yup.string().required('NIN is required').length(11, 'NIN should be 11 characters'),
      otherwise: () => Yup.string().required('ID number is required'),
    }),
    // vnin: Yup.string().when('id_type', {
    //   is: ID_TYPES.NIN,
    //   then: () => Yup.string().required('VNIN is required').length(16, 'VNIN should be 16 characters'),
    //   otherwise: () => undefined,
    // }),
    selfie: startVerfication ? Yup.string().required('Selfie image is required') : undefined,
    photo_id: startVerfication ? Yup.string().required('ID image is required') : undefined,
    filename: startVerfication ? Yup.string().required('ID filename is required') : undefined,
  });

const IDMethodsOptions = [
  {
    value: VERIFICATION_METHODS.NIN,
    label: 'National Identity Card/Slip',
  },
  {
    value: VERIFICATION_METHODS.DRIVERS_LINCENSE,
    label: 'Drivers License',
  },
  {
    value: VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
    label: 'International Passport',
  },
  {
    value: VERIFICATION_METHODS.VOTERS_CARD,
    label: 'Voters ID',
  },
  {
    value: VERIFICATION_METHODS.SSNIT_ID,
    label: 'Social Security National ID',
  },
  {
    value: VERIFICATION_METHODS.NATIONAL_ID,
    label: 'National ID Card',
  },
];

export const CountryIDMethodOptions = {
  [COUNTRIES.NG]: [VERIFICATION_METHODS.NIN],
  [COUNTRIES.GH]: [
    VERIFICATION_METHODS.SSNIT_ID,
    VERIFICATION_METHODS.DRIVERS_LINCENSE,
    VERIFICATION_METHODS.VOTERS_CARD,
    VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
  ],
  [COUNTRIES.KE]: [VERIFICATION_METHODS.NATIONAL_ID, VERIFICATION_METHODS.INTERNATIONAL_PASSPORT],
  [COUNTRIES.ZA]: [VERIFICATION_METHODS.NATIONAL_ID],
};

const getIdDropdownOptions = (country: COUNTRIES) => {
  return CountryIDMethodOptions[country].map(method => {
    return IDMethodsOptions.find(option => option.value === method);
  });
};
