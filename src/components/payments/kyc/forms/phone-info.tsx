import Container from '@/components/ui/container';
import { LOOKUP_PHONE, RESEND_PHONE_TOKEN, UPDATE_DOB, VERIFY_PHONE } from 'catlog-shared';
import { useFormik } from 'formik';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import * as Animatable from 'react-native-animatable';
import Toast from 'react-native-toast-message';
import { dateToDDMMYYYY, DDMMYYYYToDate, getFieldvalues, Yup } from 'src/assets/utils/js';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText } from 'src/components/ui';
import CalenderInput from 'src/components/ui/inputs/calender-input';
import Input from 'src/components/ui/inputs/input';
import ScreenInfoHeader, { ColorPaletteType } from 'src/components/ui/screen-info-header';
import { useApi } from 'src/hooks/use-api';
import { GenericFormRef } from 'src/screens/payments/payment-links/payment-link-form';
import { ConfirmTokenForm } from './confirm-token-form';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import { phoneObjectFromString, phoneObjectToString } from 'catlog-shared';
import PhoneNumberInput from 'src/components/ui/inputs/phone-number-input';
import { SharedKYCStepProps } from '../types';
import CustomImage from 'src/components/ui/others/custom-image';

interface BvnInfoProps extends SharedKYCStepProps {}

const PhoneInfo = (
  { formSteps, kycInfo, setKycInfo, next, setIsLoading, user }: BvnInfoProps,
  ref: ForwardedRef<GenericFormRef>,
) => {
  const [showContactSupport, setShowContactSupport] = useState(false);
  const [hasLookup, setHasLookup] = useState(false);

  const lookupPhoneReq = useApi({
    apiFunction: LOOKUP_PHONE,
    key: LOOKUP_PHONE.name,
    method: 'POST',
  });

  const verifyPhoneReq = useApi({
    apiFunction: VERIFY_PHONE,
    key: VERIFY_PHONE.name,
    method: 'POST',
  });

  const updateDobReq = useApi({
    apiFunction: UPDATE_DOB,
    key: UPDATE_DOB.name,
    method: 'POST',
  });

  const resendTokenReq = useApi({
    apiFunction: RESEND_PHONE_TOKEN,
    key: RESEND_PHONE_TOKEN.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      phone: kycInfo?.phone !== '' ? phoneObjectFromString(kycInfo?.phone) : phoneObjectFromString(user?.phone),
      dob: kycInfo?.dob ? dateToDDMMYYYY(new Date(kycInfo?.dob)) : '',
      token: '',
      isVerified: Boolean(kycInfo?.phone_verified && kycInfo?.phone),
    },
    onSubmit: async ({ isVerified, ...values }) => {
      if (isVerified && kycInfo?.dob !== form.values.dob) {
        const [res, err] = await updateDobReq.makeRequest({
          dob: values.dob,
        });

        if (res) {
          setKycInfo(res?.data);
          setHasLookup(true);
          formSteps.next();
        }
      }

      if (!hasLookup && !isVerified) {
        const [res, err] = await lookupPhoneReq.makeRequest({
          phone: phoneObjectToString(values.phone),
          dob: values.dob,
        });
        if (res) {
          setHasLookup(true);
        }
        if (err && err.statusCode == 412) {
          setShowContactSupport(true);
        }
      }

      if (verifyPhoneReq.response) {
        setKycInfo(verifyPhoneReq.response?.data);
        // next();
      }

      if (isVerified) {
        formSteps.next();
      }
    },
    validationSchema,
  });

  useEffect(() => {
    if (kycInfo?.phone_verified && kycInfo?.phone) {
      form.setFieldValue('isVerified', true);
    }
  }, [kycInfo]);

  useImperativeHandle(ref, () => ({ submitForm: () => form.submitForm() }), []);
  const showTokenForm = false;

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        customElements={
          <BaseText fontSize={22} type={'heading'} lineHeight={27}>
            Verify Phone Number
          </BaseText>
        }
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/id-card.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
      />
      <Container className="pt-16">
        <Animatable.View animation={'fadeIn'} className="mb-15">
          <InfoBadge text={"We'll need your Phone number to enable payments for your business"} />
        </Animatable.View>
        <PhoneNumberInput {...getFieldvalues('phone', form)} onChange={value => form.setFieldValue('phone', value)} />
        <CalenderInput
          classes="mt-15"
          inputProps={{
            label: 'Date of birth',
            value: form.values.dob ? DDMMYYYYToDate(form.values.dob).toDateString() : undefined,
          }}
          onDateChange={d => form.setFieldValue('dob', dateToDDMMYYYY(d))}
        />
        {showTokenForm && (
          <ConfirmTokenForm
            form={form}
            resendReq={resendTokenReq}
            fieldKey="phone"
            req={lookupPhoneReq}
            showContactSupport={showContactSupport}
          />
        )}
      </Container>
    </Animatable.View>
  );
};

export default forwardRef(PhoneInfo);

const validationSchema = Yup.object().shape({
  phone: phoneValidation('phone'),
  dob: Yup.date().required('Date of birth is required'),
});
