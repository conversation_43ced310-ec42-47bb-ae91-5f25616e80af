import { MANUALLY_VERIFY_ID } from 'catlog-shared';
import { KYCInfo } from 'catlog-shared';
import { VerifyIDParams } from 'catlog-shared/src/api/kycs/types';
import { FormikProps } from 'formik';
import { CloseCircle, InfoCircle, Refresh2, TickCircle } from 'iconsax-react-native/src';
import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import { ListCard } from 'src/components/ui/cards/list-item-card';
import { CheckActive } from 'src/components/ui/icons';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import { ApiData, useApi } from 'src/hooks/use-api';
import useSteps from 'src/hooks/use-steps';
import colors from 'src/theme/colors';
import SelfieStep, { GenericStepRef } from './selfie-step';
import UploadIdStep from './upload-id-step';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import classNames from 'node_modules/classnames';

export enum StatusVariantType {
  'SUCCESS',
  'DANGER',
}

export interface CustomErrorType extends Error {
  statusCode?: number;
}

interface Props {
  response: any;
  error: CustomErrorType;
  retryManually?: VoidFunction;
}

const VerificationStatus = ({ response, error, retryManually }: Props) => {
  const errorsToShow = (error as any)?.errors ?? [error?.message] ?? defaultErrors;
  const isManualVerification = (response?.data as KYCInfo)?.verification_method === 'MANUAL';

  const variant = (response && response?.data) || error === null ? StatusVariantType.SUCCESS : StatusVariantType.DANGER;

  const icon = {
    [StatusVariantType.SUCCESS]: <SuccessCheckmark />,
    [StatusVariantType.DANGER]: (
      <CircledIcon className="self-center bg-accentRed-pastel p-15">
        <CircledIcon className="bg-accentRed-main p-20">
          <CloseCircle variant={'Bold'} size={wp(40)} color={colors.white} />
        </CircledIcon>
      </CircledIcon>
    ),
  };

  const title = {
    [StatusVariantType.SUCCESS]: isManualVerification ? 'ID Saved Successfully' : 'Verification Successful',
    [StatusVariantType.DANGER]: 'Automatic Verification Failed',
  };

  const description = {
    [StatusVariantType.SUCCESS]: isManualVerification
      ? 'Your ID has been saved and will be verified manually'
      : 'You have successfully verified your identity',
    [StatusVariantType.DANGER]: 'Your verification could have failed for one of the following reasons:',
  };

  const info = errorsToShow?.map((error: string, index) => {
    return {
      description: error,
      leftElement: <ErrorNoticeIcon />,
    };
  }) ?? [
    {
      description: 'Your selfie was unclear',
      leftElement: <ErrorNoticeIcon />,
    },
    {
      description: 'Your ID image was unclear',
      leftElement: <ErrorNoticeIcon />,
    },
    {
      description: 'You were wearing a pair of glasses',
      leftElement: <ErrorNoticeIcon />,
    },
  ];

  return (
    <View className="mx-20 mb-[60px] py-20 flex flex-col items-center">
      {icon[variant]}
      <BaseText fontSize={22} type={'heading'} classes="text-center mt-10">
        {title[variant]}
      </BaseText>
      <BaseText type={'body'} classes="text-center mt-10 text-black-muted max-w-[250px]">
        {description[variant]}
      </BaseText>
      {variant === StatusVariantType.DANGER && (
        <ListCard items={info} descriptionProps={{ weight: 'regular' }} classes="mb-20" />
      )}
      {variant === StatusVariantType.DANGER && (
        <Pressable
          className={classNames('self-center py-10 px-15 rounded-full bg-grey-bgOne mt-15')}
          onPress={retryManually}>
          <Row classes="justify-start">
            <BaseText fontSize={12} weight={'semiBold'} classes={'text-primary-main mr-4'}>
              Request Manual Verification
            </BaseText>
            <Refresh2 size={wp(13)} strokeWidth={2} color={colors.primary.main} />
          </Row>
        </Pressable>
      )}
    </View>
  );
};

const ErrorNoticeIcon = () => (
  <CircledIcon className="bg-accentOrange-pastel p-5">
    <InfoCircle variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
  </CircledIcon>
);

export default VerificationStatus;

const defaultErrors = ['Something went wrong on our end'];
