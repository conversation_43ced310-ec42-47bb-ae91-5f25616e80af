import { CameraView } from 'expo-camera';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { Image, View } from 'react-native';
import { base64ToUri, getBase64Size, hp, wp } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';
import Camera from 'src/components/ui/others/camera';
import { CommonUploadIdStepProps } from '.';
import { manipulateAsync, SaveFormat, FlipType } from 'expo-image-manipulator';
import Pressable from 'src/components/ui/base/pressable';
import classNames from 'node_modules/classnames';
import Row from 'src/components/ui/row';
import { Refresh2 } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';

interface Props extends CommonUploadIdStepProps {}

const SelfieStep: React.ForwardRefRenderFunction<GenericStepRef, Props> = ({ form, next, setCanNext }, ref) => {
  const cameraRef = useRef<CameraView>(null);

  const takePicture = async () => {
    setCanNext(false);
    try {
      if (cameraRef.current) {
        const photo = await cameraRef.current.takePictureAsync({ base64: true });
        const { base64: base64Resized } = await manipulateAsync(
          base64ToUri(photo.base64),
          [{ resize: { width: 1080 } }, { flip: FlipType.Horizontal }],
          {
            compress: 0.5,
            format: SaveFormat.JPEG,
            base64: true,
          },
        );
        form.setFieldValue('selfie', base64ToUri(base64Resized));
      }
    } finally {
      setCanNext(true);
      // next();
    }
  };

  useImperativeHandle(ref, () => ({ completeStep: () => takePicture() }), []);

  return (
    <View className="mx-20 mb-20 py-20">
      <View className="items-center">
        <BaseText fontSize={18} classes="text-center" type="heading">
          Take A Selfie
        </BaseText>
      </View>
      {form?.values?.selfie ? (
        <View className="mx-80 mt-25 rounded-15 overflow-hidden bg-grey-mutedLight" style={{ height: hp(220) }}>
          <Image source={{ uri: form?.values?.selfie }} style={{ height: hp(220) }} resizeMode="contain" />
        </View>
      ) : (
        <View className="mx-80 mt-25 rounded-15 overflow-hidden bg-grey-mutedLight" style={{ height: hp(220) }}>
          <Camera ref={cameraRef} />
        </View>
      )}

      {form?.values?.selfie ? (
        <Pressable
          className={classNames('self-center py-10 px-15 rounded-full bg-grey-bgOne mt-15')}
          onPress={() => form.setFieldValue('selfie', '')}>
          <Row classes="justify-start">
            <BaseText fontSize={12} weight={'semiBold'} classes={'text-primary-main mr-4'}>
              Retake Selfie
            </BaseText>
            <Refresh2 size={wp(13)} strokeWidth={2} color={colors.primary.main} />
          </Row>
        </Pressable>
      ) : (
        <View className="bg-primary-pastel p-10 rounded-12 items-center mt-15 mx-25">
          <BaseText fontSize={11} classes="text-center text-primary-main">
            Please make sure you're in a well lit environment
          </BaseText>
        </View>
      )}
    </View>
  );
};
export default forwardRef(SelfieStep);

export interface GenericStepRef {
  completeStep: () => Promise<void>;
}
