import { Image } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { Dimensions, View } from 'react-native';
import { base64ToUri, cx, wp } from 'src/assets/utils/js';
import { BaseText, WhiteCardBtn } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import CustomImage from 'src/components/ui/others/custom-image';
import useImagePicker from 'src/hooks/use-image-picker';
import colors from 'src/theme/colors';
import { CommonUploadIdStepProps } from '.';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

const screenWidth = Dimensions.get('window').width;

interface Props extends CommonUploadIdStepProps {}
const UploadIdStep: React.FC<Props> = ({ form, setCanNext }) => {
  const { pickImage } = useImagePicker({ allowsEditing: false });
  const photoUri = form.values.photo_id;

  useEffect(() => {
    setCanNext(Boolean(photoUri));
  }, [photoUri]);

  const snapIdPhoto = async () => {
    try {
      const [image] = await pickImage();

      if (image) {
        const { base64: base64Resized } = await manipulateAsync(image.uri, [{ resize: { width: 1080 } }], {
          compress: 0.5,
          format: SaveFormat.JPEG,
          base64: true,
        });

        form.setFieldValue('photo_id', base64ToUri(base64Resized));
        form.setFieldValue('filename', image.fileName);
      }
    } catch (error) {}
  };

  return (
    <View className="mx-20 mb-20">
      <View className="items-center">
        <BaseText fontSize={18} classes="text-center" type="heading">
          Upload your ID
        </BaseText>
      </View>
      <Pressable
        className={cx('w-full rounded-15 bg-grey-bgOne mt-25', {
          'border border-grey-border p-5 border-dashed': Boolean(photoUri),
        })}
        onPress={snapIdPhoto}
        style={{ height: screenWidth - wp(40) }}>
        {!photoUri && (
          <View className="flex-1 items-center justify-center">
            <Image variant={'Bulk'} size={wp(30)} color={colors.grey.muted} />
            <BaseText fontSize={11} classes="text-black-muted mt-10">
              Click to upload ID Card
            </BaseText>
          </View>
        )}
        {photoUri && (
          <View className="w-full h-full rounded-12 overflow-hidden">
            <CustomImage className="w-full h-full" imageProps={{ source: { uri: photoUri }, contentFit: 'contain' }} />
            <View className="absolute bottom-8 w-full">
              <WhiteCardBtn disabled className="self-center">
                Click to re-upload
              </WhiteCardBtn>
            </View>
          </View>
        )}
      </Pressable>
    </View>
  );
};
export default UploadIdStep;
