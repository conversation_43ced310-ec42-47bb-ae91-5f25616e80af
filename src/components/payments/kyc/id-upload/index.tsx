import { MANUALLY_VERIFY_ID } from 'catlog-shared';
import { KYCInfo } from 'catlog-shared';
import { VerifyIDParams } from 'catlog-shared/src/api/kycs/types';
import { FormikProps } from 'formik';
import { CloseCircle, InfoCircle, Profile, TickCircle } from 'iconsax-react-native/src';
import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import { ListCard } from 'src/components/ui/cards/list-item-card';
import { CheckActive } from 'src/components/ui/icons';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import { ApiData, useApi } from 'src/hooks/use-api';
import useSteps from 'src/hooks/use-steps';
import colors from 'src/theme/colors';
import SelfieStep, { GenericStepRef } from './selfie-step';
import UploadIdStep from './upload-id-step';
import VerificationStatus from './verifcation-step';
import Toast from 'react-native-toast-message';

enum UPLOAD_ID_STEPS {
  GENERAL_INFO = 'GENERAL_INFO',
  TAKE_SELFIE = 'TAKE_SELFIE',
  UPLOAD_ID = 'UPLOAD_ID',
  PROCESSING = 'PROCESSING',
  STATUS = 'STATUS',
}

export interface DateRangeString {
  from: string | null;
  to: string | null;
}

interface UploadIdModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  form: FormikProps<VerifyIDParams>;
  verifyIDReq: ApiData<VerifyIDParams, any>;
  handleSuccess: (kyc: KYCInfo) => void;
  idIsVerified: boolean;
  retryManually: VoidFunction;
}

export interface CommonUploadIdStepProps {
  form: FormikProps<VerifyIDParams>;
  next: VoidFunction;
  setCanNext: (canGoNext: boolean) => void;
}

const UploadIdModal = ({
  children,
  verifyIDReq,
  closeModal,
  handleSuccess,
  onPressProceed,
  form,
  idIsVerified,
  retryManually,
  ...props
}: UploadIdModalProps) => {
  const formSteps = useSteps(Object.values(UPLOAD_ID_STEPS), 0);
  const { step, next, previous, changeStep } = formSteps;

  const [canGoNext, setCanGoNext] = useState(true);
  const selfieStepRef = useRef<GenericStepRef>();

  const { response, error, isLoading } = verifyIDReq;

  useEffect(() => {
    if ((response || error) && !isLoading) {
      changeStep(UPLOAD_ID_STEPS.STATUS);
    }
    if (response && response?.data) {
      handleSuccess(response.data);
    }
  }, [response, error, isLoading]);

  const retry = () => {
    verifyIDReq.makeRequest(form.values);
    changeStep(UPLOAD_ID_STEPS.PROCESSING);
  };

  const manualRetry = async () => {
    changeStep(UPLOAD_ID_STEPS.PROCESSING);
    retryManually();
  };

  const restart = () => {
    form.setFieldValue('photo_id', '');
    form.setFieldValue('selfie', '');

    changeStep(UPLOAD_ID_STEPS.GENERAL_INFO);
  };

  const getButtonText = () => {
    switch (step) {
      case UPLOAD_ID_STEPS.TAKE_SELFIE:
        if (form.values.selfie) return 'Continue';
        return 'Take Selfie';
      case UPLOAD_ID_STEPS.UPLOAD_ID:
        return 'Upload ID';
      case UPLOAD_ID_STEPS.STATUS:
        if (response) return 'Continue';
        if (error?.statusCode > 499) return 'Retry';
        return 'Restart';
      default:
        return 'Proceed';
    }
  };

  const handleStepSubmit = async () => {
    switch (step) {
      case UPLOAD_ID_STEPS.TAKE_SELFIE: {
        if (form.values.selfie) {
          next();
          return;
        }
        selfieStepRef?.current?.completeStep();
        break;
      }
      case UPLOAD_ID_STEPS.UPLOAD_ID: {
        const errors = await form.validateForm();

        if (!(Object.keys(errors).length > 0)) {
          changeStep(UPLOAD_ID_STEPS.PROCESSING);
          verifyIDReq.reset();
          form.submitForm();
        } else {
          const errorString = Object.values(errors)[0];
          Toast.show({ type: 'error', text1: errorString });
        }

        break;
      }
      case UPLOAD_ID_STEPS.STATUS: {
        if (response) return closeModal();
        if (error?.statusCode > 499) {
          retry();
          return;
        }

        restart();
        break;
      }
      default:
        next();
    }
  };

  const handlePrevious = async () => {
    switch (step) {
      case UPLOAD_ID_STEPS.STATUS: {
        changeStep(UPLOAD_ID_STEPS.UPLOAD_ID);
        break;
      }
      default:
        previous();
    }
  };

  const commonProps: CommonUploadIdStepProps = { next, form, setCanNext: setCanGoNext };
  const showButtons = step !== UPLOAD_ID_STEPS.PROCESSING;

  return (
    <BottomModal
      closeModal={closeModal}
      showButton={showButtons}
      size="lg"
      buttons={[
        idIsVerified
          ? null
          : {
              text: 'Go Back',
              variant: ButtonVariant.LIGHT,
              onPress: handlePrevious,
            },
        {
          text: getButtonText(),
          onPress: handleStepSubmit,
          disabled: !canGoNext,
        },
      ].filter(Boolean)}
      {...props}>
      {step === UPLOAD_ID_STEPS.GENERAL_INFO && <IDUploadInstructions />}
      {step === UPLOAD_ID_STEPS.TAKE_SELFIE && <SelfieStep ref={selfieStepRef} {...commonProps} />}
      {step === UPLOAD_ID_STEPS.UPLOAD_ID && <UploadIdStep {...commonProps} />}
      {step === UPLOAD_ID_STEPS.PROCESSING && <ProcessingStatus />}
      {step === UPLOAD_ID_STEPS.STATUS && (
        <VerificationStatus response={response} error={error} retryManually={manualRetry} />
      )}
    </BottomModal>
  );
};

export default UploadIdModal;

export const ProcessingStatus: React.FC<any> = ({}) => {
  return (
    <View className="py-50 flex flex-col items-center">
      <CircledIcon classes="bg-accentYellow-main p-30 items-center justify-center">
        <ActivityIndicator size="large" color={colors.white} />
      </CircledIcon>
      <BaseText fontSize={18} classes="text-center mt-10" type="heading">
        Processing...
      </BaseText>
      <BaseText type={'body'} classes="text-center text-grey-mutedDark mt-10">
        Please wait! We&apos;re processing your Identity
      </BaseText>
    </View>
  );
};

const IDUploadInstructions = () => {
  return (
    <View className="mx-20 py-30">
      <View className="items-center">
        {/* PLACEHOLDER IMAGE */}
        <CircledIcon className="bg-accentOrange-main p-16">
          <Profile size={wp(30)} variant={'Bold'} color={colors.white} />
        </CircledIcon>
        <BaseText fontSize={18} classes="text-center mt-10" type="heading">
          ID Verification
        </BaseText>
      </View>
      <ListCard items={instructions} descriptionProps={{ weight: 'regular' }} classes="mb-20" />
    </View>
  );
};

const instructions = [
  {
    description: 'You should be in a well lit environment to get started',
    leftElement: (
      <CircledIcon className="bg-accentGreen-pastel2">
        <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
      </CircledIcon>
    ),
  },
  {
    description: 'Avoid wearing anything that hides your face, e.g. glasses or caps',
    leftElement: (
      <CircledIcon className="bg-accentGreen-pastel2">
        <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
      </CircledIcon>
    ),
  },
  {
    description: 'All parts of your identity card should show clearly',
    leftElement: (
      <CircledIcon className="bg-accentGreen-pastel2">
        <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
      </CircledIcon>
    ),
  },
];
