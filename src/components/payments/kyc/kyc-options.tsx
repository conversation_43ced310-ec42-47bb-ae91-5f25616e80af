import { KYCInfo } from 'catlog-shared';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText, Container, Row } from 'src/components/ui';
import DashboardLayout from 'src/components/ui/layouts/dashboard-layout';
import { HeaderVariants } from 'src/components/ui/layouts/header';
import * as Animatable from 'react-native-animatable';
import ListItemCard, { ListCard } from 'src/components/ui/cards/list-item-card';
import { ArrowRight, Shield } from 'iconsax-react-native/src';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import { KYC_STATUSES } from 'catlog-shared';
import Button, { ButtonVariant } from 'src/components/ui/buttons/button';
import { alertPromise, cx } from 'src/assets/utils/js';
import { useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { COPY_KYC_INFO } from 'catlog-shared';
import LoadingModal from 'src/components/ui/others/loading-modal';
import Column from 'src/components/ui/column';
import colors from 'src/theme/colors';

interface Props {
  setShowKycOptions: (state: boolean) => void;
  relatedKycData: KYCInfo[];
  kycInfo: KYCInfo;
  onCopySuccess: VoidFunction;
}
const KycOptions: React.FC<Props> = ({ relatedKycData, setShowKycOptions, kycInfo, onCopySuccess }) => {
  const copyKycInfo = useApi({
    apiFunction: COPY_KYC_INFO,
    key: COPY_KYC_INFO.name,
    method: 'POST',
  });

  const handleContinueClick = async (kycId: string) => {
    if (kycInfo?.id === kycId) {
      setShowKycOptions(false);
      return;
    }

    const proceed = await alertPromise(
      'Confirm Update',
      'Are you sure you want to proceed with this store info? This action is not reversible',
      'Continue',
    );

    if (proceed) {
      const [res, err] = await copyKycInfo.makeRequest({ kyc_id: kycId });
      if (res) {
        onCopySuccess();
      }
    }
  };
  return (
    <DashboardLayout
      headerProps={{
        headerBg: 'bg-accentGreen-pastel2',
        pageTitle: 'KYC Options',
        variant: HeaderVariants.SUB_LEVEL,
      }}>
      <ScrollView>
        <Container className="mt-16">
          <Animatable.View animation={'fadeIn'}>
            <InfoBadge
              text={'How would you like to continue. You can import KYC information from other stores you own'}
            />
          </Animatable.View>
          {copyKycInfo.isLoading && (
            <Column classes="pt-40 flex-1 h-full">
              <ActivityIndicator color={colors?.primary.main} size="small" animating />
              <BaseText fontSize={12} weight={'medium'} classes={cx(`text-black-muted mt-10 text-center`)}>
                Please wait while we copy your KYC information...
              </BaseText>
            </Column>
          )}
          {!copyKycInfo.isLoading && (
            <Animatable.View className="mt-15">
              {relatedKycData.map((data, index) => {
                return (
                  <Animatable.View
                    key={index}
                    animation={'fadeInUp'}
                    duration={200}
                    delay={200 + index * 50}
                    className="mt-15">
                    <ListItemCard
                      key={index}
                      onPress={() => handleContinueClick(data.id)}
                      showBorder
                      title={`${data.first_name} ${data.last_name} `}
                      leftElement={<Shield variant="Bulk" className="text-accentOrange-light" />}
                      rightElement={<StatusPill statusType={statusBadgeColorMap[data.status]} title={data.status} />}
                      bottomElement={
                        <View className="w-full">
                          <BaseText fontSize={12} weight={'medium'} classes={cx(`text-black-muted mt-5`)}>
                            {`BVN: ${data.bvn ? `**** ${data.bvn.slice(-4)}` : 'No BVN'} `}
                          </BaseText>
                          <Row spread={false} classes="w-full mt-10">
                            <BaseText fontSize={13} className="text-primary-main">
                              Continue with this info
                            </BaseText>
                            <ArrowRight size={15} className="text-primary-main ml-10" />
                          </Row>
                        </View>
                      }
                    />
                  </Animatable.View>
                );
              })}
              <Animatable.View animation={'fadeInUp'} duration={200} delay={400}>
                <Button
                  className="mt-20"
                  text="Use new BVN info"
                  onPress={() => setShowKycOptions(false)}
                  variant={ButtonVariant.LIGHT}
                />
              </Animatable.View>
            </Animatable.View>
          )}
        </Container>
      </ScrollView>
    </DashboardLayout>
  );
};
export default KycOptions;

export const statusBadgeColorMap: { [key: string]: StatusType } = {
  [KYC_STATUSES.APPROVED]: StatusType.SUCCESS,
  [KYC_STATUSES.PENDING]: StatusType.DEFAULT,
  [KYC_STATUSES.IN_PROGRESS]: StatusType.PROCESSING,
  [KYC_STATUSES.DENIED]: StatusType.DANGER,
};
