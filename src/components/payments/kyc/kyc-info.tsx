import { ScrollView, View } from 'react-native';
import Container from '@/components/ui/container';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import CustomImage from '@/components/ui/others/custom-image';
import { BaseText, CircledIcon } from '@/components/ui';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { CardTick, EmptyWalletTime, ReceiptText, Wallet, WalletAdd, WalletAdd1 } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { hp, wp } from 'src/assets/utils/js';
import Button from '@/components/ui/buttons/button';
import * as Animatable from 'react-native-animatable';
import Separator from 'src/components/ui/others/separator';
import { COUNTRIES } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';

interface KYCInfoProps {
  next: VoidFunction;
}

const KYCInfo = ({ next }: KYCInfoProps) => {
  const { store } = useAuthContext();

  return (
    <Animatable.View className="flex-1">
      <Container>
        <View className="mt-16">
          {/* PLACEHOLDER IMAGE */}
          <CustomImage
            className="w-100 h-100 bg-white"
            imageProps={{ source: require('@/assets/images/coin-stack-gold.png') }}
          />
          <BaseText fontSize={22} type={'heading'} classes="mt-12">
            Verify Identity to Unlock More
          </BaseText>
          <BaseText fontSize={13} classes="text-black-muted mt-5">
            {kycInfoCopies[getCountryCode(store?.country)]?.description}
          </BaseText>
          <SectionContainer className="mt-20">
            {(kycInfoCopies[getCountryCode(store?.country)]?.cards ?? [])?.map((value, index) => (
              <View key={index}>
                <ListItemCard
                  key={value.title}
                  showBorder={false}
                  title={value.title}
                  titleProps={{ type: 'heading', fontSize: 15, lineHeight: hp(22), classes: 'mb-0' }}
                  titleClasses="text-black-main"
                  description={value?.description ?? undefined}
                  descriptionProps={{ lineHeight: hp(15), classes: 'mt-0', weight: 'regular' }}
                  disabled={true}
                  leftElement={<CircledIcon className="bg-white">{value.icon}</CircledIcon>}
                  containerClasses="items-start"
                />
                {index !== kycInfoCopies[getCountryCode(store?.country)].cards.length - 1 && <Separator className="mx-0 my-0" />}
              </View>
            ))}
          </SectionContainer>
          <Button text="Start Identity Verification" className="mt-30" onPress={next} />
        </View>
      </Container>
    </Animatable.View>
  );
};

const getCountryCode = (country: string | { code: string }): string => {
  if (typeof country === 'string') {
    return country;
  }
  return country?.code || '';
};

const kycInfoCopies = {
  [COUNTRIES.NG]: {
    description: 'Get a free bank account, collect payment in multiple ways & create professional invoices',
    cards: [
      {
        title: 'Flexible Payment Options',
        description: 'Give your customers multiple ways to pay you - with tranfers, cards & ussd.',
        icon: <CardTick variant={'Bold'} size={wp(18)} color={colors.accentOrange.main} />,
        color: 'text-accent-yellow-500 bg-accent-yellow-pastel',
      },
      {
        title: 'Dedicated Business Account',
        description: 'You get a dedicated business account number - this helps you separate your business finance.',
        icon: <Wallet variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
        color: 'text-accent-orange-500 bg-accent-orange-pastel',
      },
      {
        title: 'Instant Withdrawals',
        description: 'Withdraw funds anytime you like and get them deposited into your bank accounts instantly.',
        icon: <EmptyWalletTime variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />,
        color: 'text-accent-red-500 bg-accent-red-pastel',
      },
    ],
  },
  [COUNTRIES.GH]: {
    description: 'Accept MoMo and card payments seamlessly on your storefront and via inovice links',
    cards: [
      {
        title: 'Flexible Payment Options',
        description:
          'Provide your customers with multiple payment options - through cards or their mobile money wallets.',
        icon: <CardTick variant={'Bold'} size={wp(18)} color={colors.accentOrange.main} />,
        color: 'text-accent-yellow-500 bg-accent-yellow-pastel',
      },
      {
        title: 'Get Paid with Invoice links',
        description: 'Easily create professional invoices while offering your customers convenient payment options',
        icon: <ReceiptText variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
        color: 'text-accent-orange-500 bg-accent-orange-pastel',
      },
      {
        title: 'Instant Withdrawals',
        description:
          'Withdraw funds anytime and enjoy instant deposits into your mobile money wallets whenever you need them',
        icon: <EmptyWalletTime variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />,
        color: 'text-accent-red-500 bg-accent-red-pastel',
      },
    ],
  },
  [COUNTRIES.KE]: {
    description: 'Accept M-PESA and card payments on your storefront and via invoice links',
    cards: [
      {
        title: 'Flexible Payment Options',
        description: 'Give your customers multiple ways to pay you - with cards or M-PESA.',
        icon: <CardTick variant={'Bold'} size={wp(18)} color={colors.accentOrange.main} />,
        color: 'text-accent-yellow-500 bg-accent-yellow-pastel',
      },
      {
        title: 'Get Paid with Invoice links',
        description: 'Easily create professional invoices while offering your customers convenient payment options',
        icon: <ReceiptText variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
        color: 'text-accent-orange-500 bg-accent-orange-pastel',
      },
      {
        title: 'Instant Withdrawals',
        description:
          'Withdraw funds anytime you like and get them deposited into your bank or M-PESA wallet instantly.',
        icon: <EmptyWalletTime variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />,
        color: 'text-accent-red-500 bg-accent-red-pastel',
      },
    ],
  },
  [COUNTRIES.ZA]: {
    description: 'Accept EFT and card payments on your storefront and via invoice links',
    cards: [
      {
        title: 'Flexible Payment Options',
        description: 'Give your customers multiple ways to pay you - with cards or EFT.',
        icon: <CardTick variant={'Bold'} size={wp(18)} color={colors.accentOrange.main} />,
        color: 'text-accent-yellow-500 bg-accent-yellow-pastel',
      },
      {
        title: 'Get Paid with Invoice links',
        description: 'Easily create professional invoices while offering your customers convenient payment options',
        icon: <ReceiptText variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
        color: 'text-accent-orange-500 bg-accent-orange-pastel',
      },
      {
        title: 'Instant Withdrawals',
        description: 'Withdraw funds anytime you like and get them deposited into your bank account instantly.',
        icon: <EmptyWalletTime variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />,
        color: 'text-accent-red-500 bg-accent-red-pastel',
      },
    ],
  },
};

export default KYCInfo;
