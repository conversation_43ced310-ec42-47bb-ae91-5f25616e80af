import { Dimensions, ScrollView, View } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import Container from '@/components/ui/container';
import { HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import Input from '@/components/ui/inputs/input';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import * as Animatable from 'react-native-animatable';
import InfoBadge from 'src/components/store-settings/info-badge';
import { Call, Framer, Location, Map, Profile, Shield, TagUser } from 'iconsax-react-native/src';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import AccordionGroup from '@/components/ui/others/accordion/accordion-group';
import Accordion from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import dayjs from 'dayjs';
import CustomImage from 'src/components/ui/others/custom-image';
import { KYCInfo } from 'catlog-shared';
import { COUNTRIES } from 'catlog-shared/src/interfaces';
import { SharedKYCStepProps } from './types';

interface KycDataSummaryProps extends SharedKYCStepProps {}

const screenWidth = Dimensions.get('window').width;

const KycDataSummary = ({ formSteps, kycInfo }: KycDataSummaryProps) => {
  const section = [
    {
      title: 'Basic Information',
      iconElement: (
        <CircledIcon className="bg-accentRed-pastel mr-10">
          <Profile variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      content: <NameInformation kycInfo={kycInfo} />,
    },
    {
      title: 'BVN Information',
      iconElement: (
        <CircledIcon className="bg-accentGreen-pastel2 mr-10">
          {kycInfo?.country !== COUNTRIES.NG ? (
            <Call variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
          ) : (
            <Shield variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
          )}
        </CircledIcon>
      ),
      content: <BvnPhoneInformation kycInfo={kycInfo} />,
    },
    {
      title: 'ID Information',
      iconElement: (
        <CircledIcon className="bg-accentYellow-pastel mr-10">
          <TagUser variant={'Bold'} size={wp(20)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      content: <IdInformation kycInfo={kycInfo} />,
    },
    {
      title: 'Address Information',
      iconElement: (
        <CircledIcon className="bg-accentOrange-pastel mr-10">
          <Location variant={'Bold'} size={wp(20)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      content: <AddressInformation kycInfo={kycInfo} />,
    },
  ];

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <ScreenInfoHeader
        colorPalette={ColorPaletteType.GREEN}
        iconElement={
          <CustomImage
            imageProps={{ source: require('@/assets/images/kyc-summary.png'), contentFit: 'cover' }}
            className="w-80 h-80"
          />
        }
        customElements={
          <BaseText fontSize={22} type={'heading'} lineHeight={27}>
            KYC Summary
          </BaseText>
        }
      />
      <Container className="mt-16">
        <Animatable.View animation={'fadeIn'}>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
        </Animatable.View>
        <View>
          <AccordionGroup>
            {section.map(item => (
              <Accordion
                key={item.title}
                anchorElement={status => (
                  <AccordionAnchor iconElement={item.iconElement} title={item.title} isOpened={status} />
                )}
                initiallyOpened>
                {item?.content ?? (
                  <View>
                    <BaseText>Items Information</BaseText>
                  </View>
                )}
              </Accordion>
            ))}
          </AccordionGroup>
        </View>
      </Container>
    </Animatable.View>
  );
};

const NameInformation: React.FC<{ kycInfo?: KYCInfo }> = ({ kycInfo }) => {
  return (
    <View className="my-20 mx-15">
      <Row>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            FIRST NAME
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.first_name}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            LAST NAME
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.last_name}
          </BaseText>
        </View>
      </Row>
    </View>
  );
};

const BvnPhoneInformation: React.FC<{ kycInfo?: KYCInfo }> = ({ kycInfo }) => {
  return (
    <View className="my-20 mx-15">
      <Row>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            {kycInfo?.country === COUNTRIES.NG ? 'BVN Information' : 'Phone Information'}
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.country === COUNTRIES.NG ? kycInfo?.bvn : kycInfo?.phone}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            DOB
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {dayjs(kycInfo?.dob).format('MMM DD, YYYY')}
          </BaseText>
        </View>
      </Row>
    </View>
  );
};

const IdInformation: React.FC<{ kycInfo?: KYCInfo }> = ({ kycInfo }) => {
  return (
    <View className="my-20 mx-15">
      <View className="flex-1">
        <BaseText fontSize={10} classes="text-black-muted">
          ID Image
        </BaseText>
        <View className="p-5 bg-grey-bgOne rounded-12 mt-15 w-full" style={{ height: screenWidth * 0.5 }}>
          <CustomImage
            className="w-full h-full"
            imageProps={{
              source: {
                uri: kycInfo?.identity?.url,
              },
              contentFit: 'contain',
            }}
          />
        </View>
      </View>
      <Row className="mt-15">
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            ID Type
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {idTypes[kycInfo?.identity?.type]}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            ID Number
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.identity?.number}
          </BaseText>
        </View>
      </Row>
    </View>
  );
};

const AddressInformation: React.FC<{ kycInfo?: KYCInfo }> = ({ kycInfo }) => {
  return (
    <View className="my-20 mx-15">
      <Row>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            LGA
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.address?.lga}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            City
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.address?.city}
          </BaseText>
        </View>
      </Row>
      <Row className="mt-15">
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            State
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.address?.state}
          </BaseText>
        </View>
        <View className="flex-1 mt-15">
          <BaseText fontSize={10} classes="text-black-muted">
            STREET ADDRESS
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {kycInfo?.address?.address_line1}
          </BaseText>
        </View>
      </Row>
    </View>
  );
};

export default KycDataSummary;

const idTypes = {
  NIN: 'NIN Slip',
  INTERNATIONAL_PASSPORT: 'Intl. Passport',
  DRIVERS_LICENSE: 'Drivers License',
  VOTERS_CARD: 'Voters ID',
};
