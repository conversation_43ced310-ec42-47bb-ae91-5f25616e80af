import { useEffect, useMemo } from 'react';
import { ScrollView, View } from 'react-native';
import Container from '@/components/ui/container';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import CustomImage from '@/components/ui/others/custom-image';
import { BaseText, CircledIcon } from '@/components/ui';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '@/components/ui/cards/list-item-card';
import {
  CardTick,
  EmptyWalletTime,
  FlashCircle,
  InfoCircle,
  ReceiptText,
  Wallet,
  WalletAdd,
  WalletAdd1,
} from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { hp, wp } from 'src/assets/utils/js';
import { validateData } from 'src/screens/payments/kyc';
import * as Animatable from 'react-native-animatable';
import Separator from 'src/components/ui/others/separator';
import { COUNTRIES, KYCInfo } from 'catlog-shared';
import useAuth<PERSON>ontext from 'src/contexts/auth/auth-context';
import Row from 'src/components/ui/row';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import Pressable from 'src/components/ui/base/pressable';
import { CheckActive } from 'src/components/ui/icons';
import { KYC_MAIN_STEPS } from './types';

interface KYCProgressProps {
  kycInfo: KYCInfo;
  changeStep: (step: KYC_MAIN_STEPS) => void;
}

const KYCProgress = ({ kycInfo, changeStep }: KYCProgressProps) => {
  const { store } = useAuthContext();
  const country = kycInfo.country;
  const progressWidth = useSharedValue(0);

  const setupProgress = useMemo(
    () => [
      {
        title: 'Basic Info',
        status: validateData(kycInfo).BASIC,
        onPress: () => changeStep(KYC_MAIN_STEPS.FORM),
      },
      country === COUNTRIES.NG
        ? {
            title: 'BVN',
            status: validateData(kycInfo).BVN,
            onPress: () => changeStep(KYC_MAIN_STEPS.FORM),
          }
        : {
            title: 'Phone Verification',
            status: validateData(kycInfo).PHONE,
            onPress: () => changeStep(KYC_MAIN_STEPS.FORM),
          },
      {
        title: 'ID Card',
        status: validateData(kycInfo).ID,
        onPress: () => changeStep(KYC_MAIN_STEPS.FORM),
      },
      {
        title: 'Address',
        status: validateData(kycInfo).ADDRESS,
        onPress: () => changeStep(KYC_MAIN_STEPS.FORM),
      },
    ],
    [kycInfo],
  );

  const completedSteps = setupProgress.filter(step => step.status).length ?? 0;
  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  useEffect(() => {
    const percentage = Math.ceil((completedSteps * 100) / setupProgress.length);
    progressWidth.value = withDelay(300, withSpring(percentage));
  }, [completedSteps]);

  return (
    <Animatable.View className="flex-1">
      <ScrollView className="flex-1" stickyHeaderIndices={[0]} scrollIndicatorInsets={{ right: 1 }}>
        <View className=" bg-grey-bgTwo">
          <Row className="py-15 bg-grey-bgTwo px-20">
            <CircledIcon iconBg="bg-accentGreen-pastel">
              <FlashCircle variant={'Bulk'} size={wp(20)} color={colors?.accentGreen.main} />
            </CircledIcon>
            <BaseText fontSize={14} type="heading" classes="flex-1 mx-10 text-black-secondary">
              Your Progress
            </BaseText>
            <BaseText fontSize={12} classes="text-black-secondary">
              {completedSteps}/{setupProgress.length} complete
            </BaseText>
          </Row>
          <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-r-full w-2/5" />
        </View>
        <View className="px-20">
          {setupProgress.map((step, index) => (
            <Pressable key={step.title} onPress={step.onPress} disabled={true}>
              <Row className={`py-20 ${index < setupProgress.length - 1 && 'border-b border-b-grey-border'}`}>
                {step.status ? (
                  <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
                ) : (
                  <View className="rounded-full border-[3px] border-grey-border w-20 h-20" />
                )}
                <BaseText fontSize={13} classes="flex-1 mx-10 text-black-secondary">
                  {step.title}
                </BaseText>
                {step.status ? (
                  <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                    <CheckActive size={wp(16)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
                  </CircledIcon>
                ) : (
                  <CircledIcon iconBg={'bg-primary-pastel'}>
                    <InfoCircle size={wp(16)} color={colors.primary.main} />
                  </CircledIcon>
                )}
              </Row>
            </Pressable>
          ))}
        </View>
      </ScrollView>
    </Animatable.View>
  );
};

export default KYCProgress;
