import React from 'react';
import PaymentActivated from './payment-activated';
import { KYC_STATUSES } from 'catlog-shared';
import ActivationStatus from './activation-status';
import { SharedKYCStepProps } from '../types';

interface KYCStatusProps extends SharedKYCStepProps {
  submitResponse: any;
}

const KycStatus = (props: KYCStatusProps) => {
  const { submitResponse, ...commonProps } = props;

  if (commonProps.kycInfo?.status === KYC_STATUSES.APPROVED) {
    return <PaymentActivated submitResponse={submitResponse} {...commonProps} />;
  }

  return <ActivationStatus {...commonProps} />;
};

export default KycStatus;
