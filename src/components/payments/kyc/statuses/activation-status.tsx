import Container from '@/components/ui/container';
import { BaseText, CircledIcon } from '@/components/ui';
import * as Animatable from 'react-native-animatable';
import { Clock, CloseCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import SectionContainer from '@/components/ui/section-container';
import { KYC_STATUSES } from 'catlog-shared';
import { SharedKYCStepProps } from '../types';
import { View } from 'react-native';

interface ActivationInProgressProps extends SharedKYCStepProps {}

const ActivationStatus = ({ kycInfo }: ActivationInProgressProps) => {
  const status = kycInfo?.status;

  const icon = {
    [KYC_STATUSES.PENDING]: (
      <CircledIcon className="bg-accentYellow-main p-24 self-center">
        <Clock variant={'Bold'} size={wp(35)} color={colors.white} />
      </CircledIcon>
    ),
    [KYC_STATUSES.DENIED]: (
      <CircledIcon className="bg-accentRed-main p-24 self-center">
        <CloseCircle variant={'Bold'} size={wp(35)} color={colors.white} />
      </CircledIcon>
    ),
  };

  const title = {
    [KYC_STATUSES.PENDING]: 'Activation in Progress',
    [KYC_STATUSES.DENIED]: 'Activation Request Declined',
  };

  const description = {
    [KYC_STATUSES.PENDING]:
      "We're currently reviewing your account activation and will notify you via email once it's approved",
    [KYC_STATUSES.DENIED]: "We couldn't verify your Identity. Reason:",
  };

  return (
    <Animatable.View animation={'fadeIn'} duration={100}>
      <Container className="mt-16 px-10">
        <Animatable.View animation={'bounceIn'} duration={500}>
          <SectionContainer className="py-24">
            {icon[status]}
            <BaseText fontSize={22} type={'heading'} classes="text-center mt-10">
              {title[status]}
            </BaseText>
            <BaseText classes="text-center mt-10 text-black-muted max-w-[285px] self-center" lineHeight={18.2}>
              {description[status]}
            </BaseText>

            {kycInfo?.rejection_message && status === KYC_STATUSES.DENIED && (
              <View className="w-full p-10 rounded-10 bg-white mt-10">
                <BaseText classes="text-center text-black-muted" fontSize={12}>
                  {kycInfo?.rejection_message}
                </BaseText>
              </View>
            )}
          </SectionContainer>
        </Animatable.View>
      </Container>
    </Animatable.View>
  );
};

export default ActivationStatus;
