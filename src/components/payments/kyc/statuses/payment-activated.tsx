import Container from '@/components/ui/container';
import { AccountInformation, COUNTRIES, GET_PAYMENTS_SETUP_PROGRESS } from 'catlog-shared';
import { GET_STORE_WALLET } from 'catlog-shared/src/api';
import { Card, Copy, Lock, Map1, TickCircle, Wallet } from 'iconsax-react-native/src';
import { ActivityIndicator, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { copyToClipboard, wp } from 'src/assets/utils/js';
import InfoBadge from 'src/components/store-settings/info-badge';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import SectionContainer from 'src/components/ui/section-container';
import { useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import { getBankImageElement } from '../../withdraw-cash/add-bank-modal';
import { SharedKYCStepProps } from '../types';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import Column from 'src/components/ui/column';
import useAuthContext from 'src/contexts/auth/auth-context';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import { CheckActive, ArrowRight } from 'src/components/ui/icons';
import { useNavigation } from '@react-navigation/native';
import { endOfYear } from 'node_modules/date-fns';
import { PaymentSettingsTabs } from 'src/components/store-settings/types';
import TestPaymentWidget from '../../test-payment-widget';
import useModals from 'src/hooks/use-modals';
import PaymentActivationSteps from '../../activation-steps';

interface PaymentActivatedProps extends SharedKYCStepProps {
  submitResponse: any;
}

const PaymentActivated = ({ submitResponse, kycInfo }: PaymentActivatedProps) => {
  const getStoreWalletReq = useApi({
    apiFunction: GET_STORE_WALLET,
    key: GET_STORE_WALLET.name,
    method: 'GET',
  });

  const account: AccountInformation =
    submitResponse?.data?.account ?? getStoreWalletReq?.response?.data?.accounts?.[0] ?? null;

  if (getStoreWalletReq?.isLoading) {
    return (
      <View className="h-full flex-1 justify-center py-50">
        <ActivityIndicator color={colors?.primary.main} size="small" animating />
        <BaseText fontSize={12} weight={'medium'} classes="text-black-muted mt-10 text-center">
          Please Wait...
        </BaseText>
      </View>
    );
  }

  return (
    <>
      <Animatable.View animation={'fadeIn'} duration={100} className="mt-20">
        <Container classes="mt-16 ">
          <Animatable.View animation={'bounceIn'} duration={500} className='self-center'>
            <SuccessCheckmark />
            <BaseText fontSize={22} type={'heading'} classes="text-center mt-10">
              You've been Verified
            </BaseText>
          </Animatable.View>
          <Animatable.View animation={'fadeInUp'} className="mt-10" duration={200} delay={200}>
            <BaseText fontSize={13} classes="text-black-muted text-center max-w-[320px] self-center">
              Your verification was successful, and you now have full access to payments on Catlog
            </BaseText>
            {/* <InfoBadge text={'Your store has been activated to start collecting payments with invoices'} /> */}
          </Animatable.View>
          <Animatable.View
            animation={'fadeInUp'}
            className="mt-20 bg-grey-bgOne rounded-15 p-12"
            duration={200}
            delay={300}>
            <Row classes="justify-between items-start">
              <Row classes="justify-start items-start">
                {getBankImageElement(account?.image, 'h-[50px] w-[50px]', 50)}
                <Column classes="ml-10">
                  <BaseText fontSize={12} classes="text-black-muted mb-2">
                    {account?.bank_name}
                  </BaseText>
                  <BaseText fontSize={14} type="heading" classes="text-black mb-5">
                    {account?.account_number}
                  </BaseText>
                  <BaseText fontSize={12} classes="text-black-muted mb-2">
                    {account?.account_name}
                  </BaseText>
                </Column>
              </Row>
              <Pressable
                className="p-10 rounded-full bg-white ml-5"
                onPress={() => copyToClipboard(account?.account_number)}>
                <Copy variant="Linear" size={wp(14)} strokeWidth={2} color={colors.primary.main} />
              </Pressable>
            </Row>
            <Button
              text={'Share Details'}
              variant={ButtonVariant.LIGHT}
              size={ButtonSize.MEDIUM}
              className="mt-15"
              btnStyle="bg-white border-0"
            />
          </Animatable.View>
          {/* TODO: Create Other things you can share component that can be used across */}
          <Animatable.View animation={'fadeInUp'} className="mt-20" duration={200} delay={400}>
            <BaseText fontSize={16} type="heading" classes="text-black mb-15">
              Next Steps
            </BaseText>
            <PaymentActivationSteps country={kycInfo?.country} />
          </Animatable.View>
        </Container>
      </Animatable.View>
    </>
  );
};

export default PaymentActivated;
