import { COUNTRIES, KYCInfo, User } from 'catlog-shared';
import { UseStepsData } from 'src/hooks/use-steps';

export enum KYC_FORM_STEPS {
  PERSONAL_INFO = 'PERSONAL_INFO',
  BVN_PHONE_INFO = 'BVN_PHONE_INFO',
  ID_CARD_INFO = 'ID_CARD_INFO',
  ADDRESS_INFO = 'ADDRESS_INFO',
}

export enum KYC_MAIN_STEPS {
  INTRO = 'INTRO',
  FORM = 'FROM',
  SUMMARY = 'SUMMARY',
  STATUS = 'STATUS',
}

export interface SharedKYCStepProps {
  formSteps: UseStepsData<KYC_FORM_STEPS>;
  isLoading?: boolean;
  setIsLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  kycInfo?: KYCInfo;
  setKycInfo?: (kycInfo: KYCInfo) => void;
  next?: VoidFunction;
  user: User;
  country: COUNTRIES;
  CTAEnabled: boolean;
  setCTAEnabled: (state: boolean) => void;
}
