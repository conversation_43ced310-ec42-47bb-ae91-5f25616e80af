import React from 'react';
import { View } from 'react-native';
import { wp, hp, toNaira } from '@/assets/utils/js';
import { BaseText } from '../ui/base';
import TransactionListCard from './transaction-list-card';
import { mockTransaction } from '@/constant/mock-data';
import { CatlogCreditsTransaction, humanFriendlyDate, Transaction } from 'catlog-shared';

interface TransactionsSectionProps {
  content: CatlogCreditsTransaction[];
  title: string;
}

const TransactionsSection = ({ title, content }: TransactionsSectionProps) => {
  return (
    <View className="mt-20 px-20">
      <BaseText fontSize={13} className={'font-interMedium text-black-muted'}>
        {title}
      </BaseText>
      <View className="rounded-[20px] mt-12">
        {content?.map((item, index) => (
          <TransactionListCard
            key={item.id}
            showBorder={index !== content.length - 1}
            description={item.narration}
            date={humanFriendlyDate(item.created_at, true)}
            variant={item.type}
            amount={toNaira(item.amount)}
          />
        ))}
      </View>
    </View>
  );
};

export default TransactionsSection;
