import { View } from 'react-native';
import Row from '../../ui/row';
import Shimmer from '../../ui/shimmer';
import { wp, hp } from 'src/assets/utils/js';
import Column from '../../ui/column';

const dummyRow = new Array(4).fill(0);

interface StorefrontSkeletonLoaderProps {}

const PaymentLinksSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return dummyRow.map((_, index) => (
    <View key={index} className="p-15 mb-5 bg-white rounded-12 border border-grey-border">
      <Row classes="" spread>
        <Row>
          <Shimmer borderRadius={wp(10)} height={hp(40)} width={wp(40)} />
          <Column spread>
          <Shimmer classes="ml-10 mb-10" borderRadius={wp(10)} height={hp(15)} width={wp(70)} />
          <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(10)} width={wp(30)} />
          </Column>
        </Row>
        <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(15)} width={wp(40)} />
      </Row>
    </View>
  ));
};

export default PaymentLinksSkeletonLoader;
