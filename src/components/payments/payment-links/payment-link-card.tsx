import React from 'react';
import { FlatList, FlatListProps, ImageBackground, View, ViewProps } from 'react-native';
import { Edit2, Link21, <PERSON><PERSON><PERSON><PERSON>, Receipt21, Trash } from 'iconsax-react-native/src';
import { colorAlternates } from 'src/constant/static-data';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable, { PressableProps } from '@/components/ui/base/pressable';
import ListItemCard from '@/components/ui/cards/list-item-card';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import MoreOptions, { MoreOptionElementProps } from '@/components/ui/more-options';
import { copyToClipboard, getColorAlternates, toCurrency, wp } from 'src/assets/utils/js';
import { INVOICE_STATUSES, InvoiceInterface } from 'catlog-shared';

interface PaymentLinkCardProps extends PressableProps {
  item: { name: string; amount: number; status: INVOICE_STATUSES };
  index?: number;
  onPressDelete?: VoidFunction;
  moreOptions: MoreOptionElementProps[];
}

const PaymentLinkCard = ({ item, onPressDelete, index = 0, moreOptions, ...props }: PaymentLinkCardProps) => {
  const colorAlternate = getColorAlternates(index);

  return (
    <View className="p-15 mb-15 bg-white rounded-12 border border-grey-border" {...props}>
      <Row>
        <CircledIcon className="rounded-8 p-10" style={{ backgroundColor: colorAlternate.bgColor }}>
          <LinkSquare variant="Bold" color={colorAlternate.iconColor} />
        </CircledIcon>
        <View className="flex-1 mx-5">
          <BaseText fontSize={12} classes="text-black-muted">
            {item?.name}
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-6">
            {toCurrency(item?.amount)}
          </BaseText>
        </View>
        <View className=" items-end justify-between">
          <MoreOptions options={moreOptions} />
          <StatusPill
            statusType={item.status === INVOICE_STATUSES.PAID ? StatusType.SUCCESS : StatusType.DANGER}
            title={item.status}
          />
        </View>
      </Row>
    </View>
  );
};

export default PaymentLinkCard;
