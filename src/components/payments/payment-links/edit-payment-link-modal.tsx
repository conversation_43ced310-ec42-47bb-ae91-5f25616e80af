import { ScrollView, View } from 'react-native';
import { useRef, useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BaseText, Row } from '../../ui';
import { Copy } from 'iconsax-react-native/src';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import Input from '@/components/ui/inputs/input';
import { InvoiceInterface } from 'catlog-shared';
import {
  PaymentLinkForm,
  PaymentLinkFormParams,
  GenericFormRef,
} from 'src/screens/payments/payment-links/payment-link-form';

interface EditPaymentLinkModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  updateInvoices: (invoice: InvoiceInterface) => void;
  selectedPaymentLink: InvoiceInterface;
}

const EditPaymentLinkModal = ({
  children,
  selectedPaymentLink,
  closeModal,
  updateInvoices,
  ...props
}: EditPaymentLinkModalProps) => {
  const formRef = useRef<GenericFormRef>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const initialValues: PaymentLinkFormParams = {
    amount: selectedPaymentLink.total_amount,
    currency: selectedPaymentLink.currency,
    customer: selectedPaymentLink.receiver.id,
    narration: selectedPaymentLink.title,
    id: selectedPaymentLink.id,
  };

  const onComplete = (item: InvoiceInterface) => {
    updateInvoices(item);
    closeModal();
  };

  return (
    <BottomModal
      closeModal={closeModal}
      title={'Edit Payment Link'}
      buttons={[{ text: 'Save Changes', onPress: () => formRef.current?.submitForm(), isLoading: isSubmitting }]}
      {...props}>
      <ScrollView className="mx-20" contentContainerStyle={{ marginBottom: 20 }}>
        <PaymentLinkForm
          setIsSubmitting={setIsSubmitting}
          ref={formRef}
          onComplete={onComplete}
          initialValues={initialValues}
        />
      </ScrollView>
    </BottomModal>
  );
};

export default EditPaymentLinkModal;
