import React from 'react';
import { View } from 'react-native';
import { hp, wp } from '@/assets/utils/js';
import Shimmer from '@/components/ui/shimmer';
import Row from '@/components/ui/row';

interface EnterAmountSkeletonLoaderProps {}

const EnterAmountSkeletonLoader: React.FC<EnterAmountSkeletonLoaderProps> = () => {
  return (
    <View className="bg-grey-bgTwo rounded-8 pt-20 pb-30">
      {/* Currency and Amount Input Section */}
      <View className="">
        <Row className="justify-center items-start">
          <Shimmer 
            width={wp(100)} 
            height={hp(45)} 
            borderRadius={8} 
          />
        </Row>
      </View>

      {/* Account Selection Section */}
      <View className="mt-20">
        {/* Select Account Button Skeleton */}
        <View className="self-center">
          <Shimmer 
            width={wp(180)} 
            height={hp(32)} 
            borderRadius={20} 
          />
        </View>
      </View>
    </View>
  );
};

export default EnterAmountSkeletonLoader;
