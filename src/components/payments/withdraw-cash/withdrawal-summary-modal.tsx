import { Image, View } from 'react-native';
import { BaseText, CircledIcon } from '@/components/ui';
import SectionContainer from '../../ui/section-container';
import Separator from '../../ui/others/separator';
import { Bank, Coin1, Hashtag, Money, User, WalletMinus } from 'iconsax-react-native/src';
import { amountFormat, hp, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { ConversionForm, ConversionQuote } from '../convert-currency/convert-currency-modal';
import { FormikProps } from 'node_modules/formik/dist';
import { WithdrawalForm } from './withdraw-cash-modal';
import { CURRENCIES } from 'node_modules/catlog-shared/dist';
import Row from 'src/components/ui/row';
import BottomModal, { BottomModalProps } from '../../ui/modals/bottom-modal';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import CustomImage from 'src/components/ui/others/custom-image';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface WithdrawalSummaryModalProps extends Partial<BottomModalProps> {
  form: FormikProps<WithdrawalForm>;
  fee: string;
  currency: CURRENCIES;
  closeModal: () => void;
  onPressContinue?: () => void;
  onPressBack?: () => void;
  isLoading?: boolean;
}

const WithdrawalSummaryModal = ({
  form,
  currency,
  fee,
  closeModal,
  onPressContinue,
  onPressBack,
  isLoading,
  ...props
}: WithdrawalSummaryModalProps) => {
  return (
    <BottomModal
      closeModal={closeModal}
      enableDynamicSizing
      useScrollView
      title="Withdrawal Summary"
      buttons={[
        onPressBack
          ? {
              text: 'Back',
              onPress: onPressBack,
              variant: ButtonVariant.LIGHT,
              isLoading,
            }
          : null,
        {
          text: 'Continue',
          onPress: onPressContinue || (() => {}),
          isLoading,
        },
      ]}
      {...props}>
      <BottomSheetView style={{ paddingHorizontal: wp(20), paddingTop: hp(10) }} enableFooterMarginAdjustment>
        <View className="items-center">
          <View className="flex-row items-center w-full justify-center">
            <CustomImage
              className="w-[72px] h-[72px] mr-5"
              imageProps={{
                source: { uri: form.values.account_info.image },
                contentFit: 'cover',
              }}
            />
            <CircledIcon className="bg-white p-5 -m-30 w-[72px] h-[72px]">
              <CircledIcon className="bg-accentGreen-pastel2 w-full h-full">
                <Bank size={wp(32)} color={colors.accentGreen.main} />
              </CircledIcon>
            </CircledIcon>
          </View>
          <BaseText fontSize={20} type="heading" classes="text-black-main mt-20">
            Withdrawing {toCurrency(form.values.amount, currency)}
          </BaseText>
          <BaseText fontSize={14} classes="text-black-placeholder text-center mt-10">
            You're about to make a withdrawal, Please confirm the details below before proceeding.
          </BaseText>
        </View>
        <SectionContainer className="px-0">
          <Row className="p-16 items-center">
            <Row className="items-center">
              <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
                <User size={wp(12)} color={colors.black.muted} />
              </Row>
              <BaseText classes="text-black-muted ml-5">Account Name:</BaseText>
            </Row>
            <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
              {form.values.account_info.account_name}
            </BaseText>
          </Row>
          <Separator className="mx-0 my-0" />
          <Row className="p-16 items-center">
            <Row className="items-center">
              <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
                <Hashtag size={wp(12)} color={colors.black.muted} />
              </Row>
              <BaseText classes="text-black-muted ml-5">Account Number:</BaseText>
            </Row>
            <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
              {form.values.account_info.account_number}
            </BaseText>
          </Row>
          <Separator className="mx-0 my-0" />
          <Row className="p-16 items-center">
            <Row className="items-center">
              <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
                <Bank size={wp(12)} color={colors.black.muted} />
              </Row>
              <BaseText classes="text-black-muted ml-5">Bank:</BaseText>
            </Row>

            <Row className="items-center justify-end max-w-[50%]">
              <CustomImage
                className="w-[24px] h-[24px] mr-5"
                imageProps={{
                  source: { uri: form.values.account_info.image },
                  contentFit: 'cover',
                }}
                // source={{
                //   uri: form.values.account_info.image,
                // }}
                // resizeMode={'cover'}
              />
              <BaseText weight="medium" classes="text-black-secondary mr-5" numberOfLines={1} ellipsizeMode="tail">
                {form.values.account_info.bank_name}
              </BaseText>
            </Row>
          </Row>
          <Separator className="mx-0 my-0" />
          <Row className="p-16 items-center">
            <Row className="items-center">
              <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
                <Money size={wp(12)} color={colors.black.muted} />
              </Row>
              <BaseText classes="text-black-muted ml-5">Amount:</BaseText>
            </Row>
            <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
              {toCurrency(form.values.amount, currency)}
            </BaseText>
          </Row>
          <Separator className="mx-0 my-0" />
          <Row className="p-16 items-center">
            <Row className="items-center">
              <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
                <Hashtag size={wp(12)} color={colors.black.muted} />
              </Row>
              <BaseText classes="text-black-muted ml-5">Fee:</BaseText>
            </Row>
            <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
              {fee}
            </BaseText>
          </Row>
        </SectionContainer>
        <View className="h-50" />
      </BottomSheetView>
    </BottomModal>
  );
};

export default WithdrawalSummaryModal;
