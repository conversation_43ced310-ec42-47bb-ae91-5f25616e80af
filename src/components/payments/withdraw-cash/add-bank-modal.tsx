import React from 'react';
import { useFormik } from 'formik';
import { Bank as BankIcon, InfoCircle, TickCircle } from 'iconsax-react-native/src';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ActivityIndicator, ScrollView, View } from 'react-native';
import { delay, getFieldvalues, wp } from 'src/assets/utils/js';
import { BaseText, Row } from '@/components/ui';
import { Gtbank, Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import * as Yup from 'yup';
import { Image } from 'react-native';
import CircledIcon from '@/components/ui/circled-icon';
import { SvgUri } from 'react-native-svg';
import {
  CREATE_WITHDRAWAL_ACCOUNT,
  GET_BANKS,
  RESOLVE_ACCOUNT_DETAILS,
  Bank,
  WithdrawalAccount,
  CURRENCIES,
} from 'catlog-shared';
import { get } from 'http';
import ModalErrorLabel from 'src/components/ui/modal-error-label';
import InfoBadge from 'src/components/store-settings/info-badge';

interface AddBankAccountModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  onComplete: (account: WithdrawalAccount) => void;
  currency: CURRENCIES;
}

const AddBankAccountModal = ({
  children,
  closeModal,
  onComplete,
  onPressProceed,
  currency,
  ...props
}: AddBankAccountModalProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const [step, setStep] = useState<'resolve_account' | 'save_account' | 'success'>('resolve_account');
  const [banks, setBanks] = useState<Bank[]>([]);
  const [selectedBankImage, setSelectedBankImage] = useState<string>();
  const [createdAccount, setCreatedAccount] = useState<WithdrawalAccount>();

  const canValidate = canValidateAccount(currency);

  const getBanksReq = useApi(
    {
      apiFunction: GET_BANKS,
      key: GET_BANKS.name,
      method: 'GET',
      autoRequest: true,
      onSuccess(response) {
        setBanks(response.data);
      },
    },
    { currency },
  );

  const resolveAccountReq = useApi({
    apiFunction: RESOLVE_ACCOUNT_DETAILS,
    key: RESOLVE_ACCOUNT_DETAILS.name,
    method: 'POST',
  });

  const createWithdrawalAccountReq = useApi({
    apiFunction: CREATE_WITHDRAWAL_ACCOUNT,
    key: CREATE_WITHDRAWAL_ACCOUNT.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      account_name: '',
      account_number: '',
      bank_code: '',
      currency,
    },
    validationSchema,
    onSubmit: async values => {
      if (step === 'resolve_account') {
        const [res, err] = await resolveAccountReq.makeRequest(values);

        if (res) {
          setStep('save_account');
        }
      } else if (step === 'save_account') {
        const [res, err] = await createWithdrawalAccountReq.makeRequest(values);
        if (res) {
          setCreatedAccount(res.data);
          setStep('success');
        }
      } else if (step === 'success') {
        onComplete(createdAccount!);
        closeModal();
      }
    },
  });

  useEffect(() => {
    if (canValidate && (step === 'save_account' || resolveAccountReq.error || createWithdrawalAccountReq.error)) {
      setStep('resolve_account');
      resolveAccountReq.reset();
      createWithdrawalAccountReq.reset();
    }
  }, [form.values]);

  useEffect(() => {
    if (!props.isVisible) {
      form.resetForm();
      setStep('resolve_account');
    }
  }, [props.isVisible]);

  useEffect(() => {
    if (form.values.bank_code) {
      const selectedBank = banks.find(bank => bank.code === form.values.bank_code);
      setSelectedBankImage(selectedBank?.image);
    }
  }, [form.values.bank_code]);

  const banksDropdownItems = useMemo<DropDownItem[]>(() => {
    return (
      banks?.map(bank => ({
        value: bank.code,
        label: bank.name,
        leftElement: getBankImageElement(bank.image, 'h-[25px] w-[25px]', 25),
      })) ?? []
    );
  }, [banks]);

  const resolvedBankAccount = resolveAccountReq.response?.data;

  const getSubmitLabel = () => {
    if (resolveAccountReq?.isLoading) {
      return 'Validating account...';
    }

    if (createWithdrawalAccountReq?.isLoading) {
      return 'Saving account...';
    }

    if (step === 'save_account') {
      return 'Save Account';
    }

    if (step === 'success') {
      return 'Continue';
    }

    return 'Validate Account';
  };

  return (
    <BottomModal
      closeModal={closeModal}
      title={`Add ${labels[currency].type} account`}
      buttons={[
        {
          text: getSubmitLabel(),
          onPress: () => form.submitForm(),
          isLoading: form.isSubmitting,
          loadingText: getSubmitLabel(),
        },
      ]}
      {...props}>
      <ModalErrorLabel error={createWithdrawalAccountReq?.error?.message} />
      <ScrollView className="mx-20 py-5" contentContainerStyle={{ marginBottom: 20 }}>
        {step !== 'success' && (
          <>
            {!canValidate && (
              <InfoBadge
                text={`Please ensure that the ${labels[currency].accountNumber} and the ${labels[currency].bank} are correct, to prevent loss of funds.`}
                classes="mb-15"
              />
            )}
            {!canValidate && <Input label={labels[currency].accountName} {...getFieldvalues('account_name', form)} />}
            <Input
              {...getFieldvalues('account_number', form, 'number')}
              label={labels[currency].accountNumber}
              useBottomSheetInput
              keyboardType="numeric"
            />
            <SelectDropdown
              ref={dropDownRef}
              isLoading={getBanksReq.isLoading}
              selectedItem={form.values.bank_code}
              genItemKeysFun={(item, index) => item.value + index}
              onPressItem={code => form.setFieldValue('bank_code', code)}
              leftAccessory={selectedBankImage && getBankImageElement(selectedBankImage, 'h-[25px] w-[25px] mr-5', 25)}
              label={`Select ${labels[currency].bank}`}
              items={banksDropdownItems}
              containerClasses="mt-15 mb-15"
              hasSearch
              searchLabel={labels[currency].search}
            />
            {resolveAccountReq.isLoading && (
              <Row className="justify-start">
                <ActivityIndicator size={'small'} color={colors.primary.main} />
                <BaseText fontSize={12} classes="text-black-muted ml-10">
                  Resolving account name...
                </BaseText>
              </Row>
            )}
            {resolvedBankAccount && (
              <Row className="justify-start bg-grey-bgOne rounded-12 py-10 px-15">
                <TickCircle variant="Bold" size={wp(15)} color={colors.accentGreen.main} />
                <BaseText fontSize={12} classes="ml-8">
                  {resolvedBankAccount.account_name}
                </BaseText>
              </Row>
            )}
            {resolveAccountReq.error && (
              <Row className="justify-start bg-grey-bgOne rounded-12 py-10 px-15 mt-10">
                <InfoCircle variant="Bold" size={wp(15)} color={colors.accentRed.main} />
                <BaseText fontSize={12} classes="ml-8 text-accentRed-main">
                  Incorrect account details
                </BaseText>
              </Row>
            )}
          </>
        )}

        {step === 'success' && (
          <>
            <View className="mx-20 py-[30px]">
              <CircledIcon className="self-center bg-accentGreen-pastel p-8">
                <CircledIcon className="bg-accentGreen-main p-20">
                  <TickCircle variant={'Bold'} size={wp(40)} color={colors.white} />
                </CircledIcon>
              </CircledIcon>
              <BaseText fontSize={24} type={'heading'} classes="text-center mt-10">
                Account Saved
              </BaseText>
              <BaseText type="body" classes="text-center mt-[5px] text-black-muted">
                Your account has been saved successfully
              </BaseText>
            </View>
          </>
        )}
      </ScrollView>
    </BottomModal>
  );
};

export default AddBankAccountModal;

const canValidateAccount = (currency: CURRENCIES) => [CURRENCIES.NGN, CURRENCIES.GHC].includes(currency);

const validationSchema = Yup.object().shape({
  account_number: Yup.string()
    .required('Account number is required')
    .test('digits', `Account number should contain only digits`, value => /^\d+$/.test(value))
    .length(10, 'Account number should be 10 digits'),
  bank_code: Yup.string().required('Bank code is required'),
});

export const getBankImageElement = (image: string, sizeClasses: string, size: number) =>
  image && !image.includes('.svg') ? (
    <Image
      className={sizeClasses}
      source={{
        uri: image,
      }}
      resizeMode={'cover'}
    />
  ) : (
    <SvgUri width={size} height={size} uri={image} />
  );

export const labels = {
  [CURRENCIES.NGN]: {
    accountNumber: 'Account Number',
    accountName: 'Name on Account',
    bank: 'Bank',
    search: 'Search Banks',
    type: 'Bank',
  },
  [CURRENCIES.GHC]: {
    accountNumber: 'Mobile Number',
    accountName: 'Name on Account',
    bank: 'Network',
    search: 'Search Networks',
    type: 'Mobile Money',
  },
  [CURRENCIES.ZAR]: {
    accountNumber: 'Account Number',
    accountName: 'Name of Receiver',
    bank: 'Bank',
    search: 'Search Banks',
    type: 'Bank',
  },
  [CURRENCIES.KES]: {
    accountNumber: 'Mobile Number',
    accountName: 'Name of Receiver',
    bank: 'Network',
    search: 'Search Networks',
    type: 'Mobile Money',
  },
};
// <CircledIcon classes='w-[30px] h-[30px] bg-accentRed-main border-[5px] border-accentRed-extraLight'>
//   <BankIcon variant='Bold' color={colors.white} size={wp(10)} />
// </CircledIcon>
