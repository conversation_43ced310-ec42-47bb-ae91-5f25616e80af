import { Image, View } from 'react-native';
import { BaseText, CircledIcon } from '@/components/ui';
import SectionContainer from '../../ui/section-container';
import Separator from '../../ui/others/separator';
import { Bank, Coin1, Hashtag, User, WalletMinus } from 'iconsax-react-native/src';
import { amountFormat, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { ConversionForm, ConversionQuote } from '../convert-currency/convert-currency-modal';
import { FormikProps } from 'node_modules/formik/dist';
import { WithdrawalForm } from './withdraw-cash-modal';
import { CURRENCIES } from 'node_modules/catlog-shared/dist';
import Row from 'src/components/ui/row';

interface WithdrawalSummaryProps {
  form: FormikProps<WithdrawalForm>;
  fee: string;
  currency: CURRENCIES;
}

const WithdrawalSummary = ({ form, currency, fee }: WithdrawalSummaryProps) => {
  return (
    <View className="px-20 pt-10">
      <View className="items-center">
        <CircledIcon className="bg-accentRed-main p-20">
          <WalletMinus size={wp(28)} color={colors.white} variant="Bold" />
        </CircledIcon>
        <BaseText fontSize={20} type="heading" classes="text-black-main mt-12">
          Withdrawing {toCurrency(form.values.amount, currency)}
        </BaseText>
      </View>
      <SectionContainer className="px-0">
        <Row className="p-16 items-center">
          <Row className="items-center">
            <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
              <User size={wp(12)} color={colors.black.muted} />
            </Row>
            <BaseText classes="text-black-muted ml-5">Account Name:</BaseText>
          </Row>
          <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
            {form.values.account_info.account_name}
          </BaseText>
        </Row>
        <Separator className="mx-0 my-0" />
        <Row className="p-16 items-center">
          <Row className="items-center">
            <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
              <Hashtag size={wp(12)} color={colors.black.muted} />
            </Row>
            <BaseText classes="text-black-muted ml-5">Account Number:</BaseText>
          </Row>
          <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
            {form.values.account_info.account_number}
          </BaseText>
        </Row>
        <Separator className="mx-0 my-0" />
        <Row className="p-16 items-center">
          <Row className="items-center">
            <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
              <Bank size={wp(12)} color={colors.black.muted} />
            </Row>
            <BaseText classes="text-black-muted ml-5">Bank:</BaseText>
          </Row>

          <Row className="items-center justify-end max-w-[50%]">
            <Image
              className="w-[24px] h-[24px] mr-5"
              source={{
                uri: form.values.account_info.image,
              }}
              resizeMode={'cover'}
            />
            <BaseText weight="medium" classes="text-black-secondary mr-5" numberOfLines={1} ellipsizeMode="tail">
              {form.values.account_info.bank_name}
            </BaseText>
          </Row>
        </Row>
        <Separator className="mx-0 my-0" />
        <Row className="p-16 items-center">
          <Row className="items-center">
            <Row className="bg-white h-[24px] w-[24px] rounded-full items-center justify-center">
              <Hashtag size={wp(12)} color={colors.black.muted} />
            </Row>
            <BaseText classes="text-black-muted ml-5">Fee:</BaseText>
          </Row>
          <BaseText weight="medium" classes="text-black-secondary max-w-[50%]" numberOfLines={1} ellipsizeMode="tail">
            {fee}
          </BaseText>
        </Row>
      </SectionContainer>
    </View>
  );
};

export default WithdrawalSummary;
