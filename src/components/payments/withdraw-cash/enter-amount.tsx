import { AddCircle, Bank } from 'iconsax-react-native/src';
import { useRef, useState } from 'react';
import { View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { ArrowRight, ArrowUpRight, Gtbank } from '@/components/ui/icons';
import SelectDropdown, { DropDownItem, DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import useModals from 'src/hooks/use-modals';
import colors from 'src/theme/colors';
import AddBankAccountModal, { labels } from './add-bank-modal';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { FormikProps } from 'formik';
import { WithdrawalForm } from './withdraw-cash-modal';
import { useApi } from 'src/hooks/use-api';
import { Image, LayoutAnimation, Platform, UIManager } from 'react-native';
import { GET_WITHDRAWAL_ACCOUNTS, WithdrawalAccount, CURRENCIES, Fees } from 'catlog-shared';
import { FakeCurrencyInput } from 'react-native-currency-input';
import InfoBadge from 'src/components/store-settings/info-badge';
import ModalErrorLabel from 'src/components/ui/modal-error-label';
import WithdrawalFeesModal from './withdrawal-fees';
import { Keyboard } from 'react-native';

interface EnterAmountProps {
  form: FormikProps<WithdrawalForm>;
  currency: CURRENCIES;
  error?: string;
  fees: Fees;
}

const MAX_FONT_SIZE = wp(40);
const MIN_FONT_SIZE = wp(30);
const FONT_SIZE_STEP = (MAX_FONT_SIZE - MIN_FONT_SIZE) / 5;
const MAX_WIDTH = wp(275);

if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

const AmountAccountStep: React.FC<EnterAmountProps> = ({ form, currency, error, fees }) => {
  const { modals, toggleModal } = useModals(['addBankModal', 'feesModal']);
  const dropDownRef = useRef<DropDownMethods>(null);

  const [fontSize, setFontSize] = useState<number>(MAX_FONT_SIZE);
  const [accounts, setAccounts] = useState<WithdrawalAccount[]>([]);

  const selectedAccount = form.values.account_info;

  const getWithdrawalAccountsReq = useApi(
    {
      apiFunction: GET_WITHDRAWAL_ACCOUNTS,
      key: GET_WITHDRAWAL_ACCOUNTS.name,
      method: 'GET',
      autoRequest: true,
      onSuccess(response) {
        setAccounts(response?.data ?? []);
      },
    },
    { currency },
  );

  const onTextLayout = event => {
    const { width } = event.nativeEvent.layout;

    if (width > MAX_WIDTH && fontSize > MIN_FONT_SIZE) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setFontSize(Math.max(fontSize - FONT_SIZE_STEP, MIN_FONT_SIZE));
      // setMeasureAttempts(measureAttempts + 1);
    } else if (width <= 0.8 * MAX_WIDTH && fontSize < MAX_FONT_SIZE) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setFontSize(Math.min(fontSize + FONT_SIZE_STEP, MAX_FONT_SIZE));
      // setMeasureAttempts(measureAttempts + 1);
    }
  };

  const openAddBankModal = () => {
    dropDownRef.current?.close();
    setTimeout(() => {
      toggleModal('addBankModal');
    }, 600);
  };

  const handleSelectWithdrawalAccount = async (accountId: string) => {
    const accountDetails = accounts.find(acc => acc.id === accountId);
    await form.setFieldValue('account', accountId, true);
    await form.setFieldValue('account_info', accountDetails, true);
    // await form.validateForm();
  };

  const handleAccountAdded = (account: WithdrawalAccount) => {
    setAccounts([...accounts, account]);
    form.setFieldValue('account', account.id);
    form.setFieldValue('account_info', account);
  };

  return (
    <View className="flex-1">
      <ModalErrorLabel error={form?.errors.amount || error} />
      <View>
        <View className=" bg-grey-bgTwo rounded-8 mt-20 pt-20 pb-30">
          <View className="">
            <Row className="justify-center">
              <BaseText fontSize={15} type="heading" weight="regular" classes="text-black-secondary mr-5">
                {currency}
              </BaseText>
              <View>
                <FakeCurrencyInput
                  value={form.values.amount > 0 ? form.values.amount : null}
                  onChangeValue={amount => form.setFieldValue('amount', amount)}
                  style={{ marginVertical: 0, paddingVertical: 0, paddingLeft: 0, fontSize }}
                  className="font-fhOscarBlack p-10"
                  delimiter=","
                  separator="."
                  autoFocus
                  minValue={0}
                  precision={2}
                  onLayout={onTextLayout}
                  placeholder="0.00"
                  caretHidden={true}
                  placeholderTextColor={colors.black.placeholder}
                />
              </View>
            </Row>
          </View>

          {!selectedAccount && (
            <Pressable
              className="flex-row self-center bg-white rounded-40 px-12 py-8"
              onPress={() => {
                Keyboard.dismiss();
                dropDownRef.current?.open();
              }}>
              <BaseText fontSize={12} weight="medium" classes="text-primary-main mr-4">
                {selectedAccount ? 'Change Withdrawal Account' : 'Select Withdrawal Account'}
              </BaseText>
              <Bank size={wp(14)} strokeWidth={1.5} color={colors.primary.main} />
            </Pressable>
          )}

          {selectedAccount && (
            <Pressable onPress={() => dropDownRef.current?.open()}>
              <Row className="justify-start bg-white rounded-full p-8 self-center">
                <Image
                  className="w-[24px] h-[24px]"
                  source={{
                    uri: selectedAccount.image,
                  }}
                  resizeMode={'cover'}
                />
                <BaseText fontSize={12} classes="ml-5 text-black-placeholder">
                  <BaseText numberOfLines={1} fontSize={12} classes="ml-8 text-black-main">
                    {' '}
                    {selectedAccount.account_name.toUpperCase()} |{' '}
                    <BaseText fontSize={12} weight="medium" classes="text-primary-main">
                      Change
                    </BaseText>
                  </BaseText>
                </BaseText>
              </Row>
            </Pressable>
          )}
        </View>

        <SelectDropdown
          ref={dropDownRef}
          showAnchor={false}
          selectedItem={form.values.account}
          onPressItem={handleSelectWithdrawalAccount}
          label={`Select ${labels[currency].type} account to receive funds`}
          showLabel
          hasSearch
          searchLabel={`Search Accounts`}
          items={accounts.map(account => ({
            leftElement: (
              <Image
                className="w-[30px] h-[30px]"
                source={{
                  uri: account.image,
                }}
                resizeMode={'cover'}
              />
            ),
            label: account.account_name,
            subTitle: `${account.account_number} - ${account.bank_name}`,
            value: account.id,
          }))}
          isLoading={getWithdrawalAccountsReq.isLoading}
          containerClasses="mt-15"
          listAddOns={
            <ListItemCard
              showBorder={false}
              title={'Add New Account'}
              className="border-t border-t-grey-border"
              titleProps={{ weight: 'medium' }}
              onPress={openAddBankModal}
              leftElement={
                <CircledIcon className="p-5 bg-grey-bgOne">
                  <AddCircle variant={'Bold'} size={wp(24)} color={colors.primary.main} />
                </CircledIcon>
              }
              rightElement={
                <CircledIcon className="p-10 bg-grey-bgOne">
                  <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
                </CircledIcon>
              }
            />
          }
        />
        <AddBankAccountModal
          onComplete={handleAccountAdded}
          isVisible={modals.addBankModal}
          closeModal={() => toggleModal('addBankModal', false)}
          currency={currency}
        />
      </View>
    </View>
  );
};

export default AmountAccountStep;
