import { FormikProps } from 'formik';
import { ScrollView, View } from 'react-native';
import { BaseText } from '@/components/ui';
import ResendCodeBtn from '@/components/ui/buttons/resend-code-btn';
import Input from '@/components/ui/inputs/input';
import PasswordInput from '@/components/ui/inputs/password-input';
import PinInput from '@/components/ui/inputs/pin-input';
import { useApi } from 'src/hooks/use-api';
import { WithdrawalForm } from './withdraw-cash-modal';
import React, { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { getFieldvalues } from 'src/assets/utils/js';
import { RESEND_WITHDRAWAL_TOKEN } from 'catlog-shared';
import { TextInput } from 'src/components/ui/icons';

interface Props {
  form: FormikProps<WithdrawalForm>;
}

const RESEND_DELAY_MINS = 5;

const ConfirmWithdrawal: React.FC<Props> = ({ form }) => {
  const [resendStatus, setResendStatus] = useState<'success' | 'failed' | 'pending'>();
  const [lastResendTime, setLastResendTime] = useState<number>(null);
  const [nextResendTime, setNextResendTime] = useState<number>(null);
  const canResendToken = nextResendTime <= 0;

  const resendWithdrawalTokenReq = useApi({
    apiFunction: RESEND_WITHDRAWAL_TOKEN,
    key: RESEND_WITHDRAWAL_TOKEN.name,
    method: 'POST',
  });

  useEffect(() => {
    const fn = async () => {
      const storedLastResendTime = AsyncStorage.getItem(`last-send-withdrawal-token`);

      if (storedLastResendTime) {
        setLastResendTime(Number(storedLastResendTime));
      }
    };
    fn();
  }, []);

  useEffect(() => {
    let interval;
    const fn = async () => {
      if (!lastResendTime) {
        return;
      }

      const storedLastResendTime = await AsyncStorage.getItem(`last-send-withdrawal-token`);

      if (storedLastResendTime !== String(lastResendTime)) {
        await AsyncStorage.setItem(`last-send-withdrawal-token`, String(lastResendTime));
      }

      interval = setInterval(async () => {
        const nextResendTime = RESEND_DELAY_MINS * 60000 - (Date.now() - lastResendTime);
        setNextResendTime(nextResendTime);

        if (nextResendTime < 0) {
          clearInterval(interval);
          await AsyncStorage.removeItem(`last-send-withdrawal-token`);
        }
      }, 1000);
    };
    fn();
    return () => {
      clearInterval(interval);
    };
  }, [lastResendTime]);

  const resend = async () => {
    setResendStatus('pending');
    const [res, error] = await resendWithdrawalTokenReq.makeRequest({ request: form.values.request_id });

    if (res) {
      setResendStatus('success');
      setLastResendTime(Date.now());
    } else {
      setResendStatus('failed');
    }

    setTimeout(() => {
      setResendStatus(null);
    }, 2000);
  };

  const statusMap = {
    success: {
      title: 'Token Resent Successfully',
      statusType: StatusType.SUCCESS,
    },
    failed: {
      title: resendWithdrawalTokenReq?.error?.message,
      statusType: StatusType.DANGER,
    },
    pending: {
      title: 'Sending...',
      statusType: StatusType.PROCESSING,
    },
  };

  return (
    <View className="mx-20">
      {statusMap[resendStatus] && <StatusPill {...statusMap[resendStatus]} className="mt-20 mb-10" />}
      <BaseText classes="text-black-muted" weight="regular">
        Please enter the 6 digit OTP sent to your email
      </BaseText>
      <Input {...getFieldvalues('code', form)} label="OTP Code" containerClasses="mt-15" keyboardType={'numeric'} />
      <ResendCodeBtn
        nextResendTime={nextResendTime}
        disableResend={!canResendToken}
        onPressResend={resend}
        className="mt-15 self-end"
      />
      <BaseText fontSize={14} type="heading">
        Security Pin
      </BaseText>
      <PinInput setValue={v => form.setFieldValue('security_pin', v)} value={form.values.security_pin} />
    </View>
  );
};

export default ConfirmWithdrawal;
