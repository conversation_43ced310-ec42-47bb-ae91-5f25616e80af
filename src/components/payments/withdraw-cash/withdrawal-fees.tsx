import { ScrollView, View } from 'react-native';
import { useMemo, useRef, useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import SectionContainer, { ContainerType } from '../../ui/section-container';
import { BaseText, Row } from '../../ui';
import { toCurrency, toNaira, wp } from 'src/assets/utils/js';
import useAuthContext from 'src/contexts/auth/auth-context';
import { CURRENCIES, Fees, millify } from 'catlog-shared';
import { currencyDetails } from './../wallet-cards';

interface WithdrawalFeesProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  fees: Fees;
  currency: CURRENCIES;
}

const WithdrawalFeesModal = ({ children, closeModal, fees, currency, ...props }: WithdrawalFeesProps) => {
  if (!fees) return null;

  return (
    <BottomModal closeModal={closeModal} title={'Withdrawal Fees'} showButton={false} size="midi" {...props}>
      <ScrollView className="mx-20 mt-10">
        <View className="text-sm text-dark mb-3.5">
          <BaseText fontSize={14} weight="regular" classes="text-black-muted mb-3.5">
            We charge a small processing fee every time you withdraw.
          </BaseText>

          <BaseText fontSize={14} weight="regular" classes="text-black-muted mb-3.5">
            Minimum amount you can withdraw is:{' '}
            <BaseText weight="medium">{toCurrency(toNaira(fees.min_withdrawal_amount), currency)}</BaseText>
          </BaseText>

          <BaseText fontSize={14} weight="regular" classes="text-black-muted mb-3.5">
            Maximum amount you can withdraw is:{' '}
            <BaseText weight="medium">
              {currency} {millify(toNaira(fees.max_withdrawal_amount))}
            </BaseText>
          </BaseText>
        </View>
        <SectionContainer containerType={ContainerType.OUTLINED} className="px-0 mt-4">
          <Row className="px-14 p-16 bg-grey-bgTwo border-b border-b-grey-border">
            <BaseText fontSize={14} classes="text-black-muted">
              Range
            </BaseText>
            <BaseText fontSize={14}>Fee</BaseText>
          </Row>
          {fees.fees.map(f => (
            <Row className="px-14 py-16" key={currency}>
              <View className="flex-1 mx-8">
                <BaseText fontSize={14} weight="regular" classes="text-black-muted">
                  {f.range_desc}
                </BaseText>
              </View>
              <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
                {f.fee_desc}
              </BaseText>
            </Row>
          ))}
        </SectionContainer>
      </ScrollView>
    </BottomModal>
  );
};

export default WithdrawalFeesModal;
