import React, { useState, useEffect, useMemo } from 'react';
import { View, Pressable, Platform } from 'react-native';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BaseText } from '@/components/ui/base';
import { CircledIcon, Row } from '@/components/ui';
import { Bank, Trash } from 'iconsax-react-native/src';
import { alertPromise, delay, showLoader, hideLoader, showSuccess, showError } from '@/assets/utils/js/functions';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import {
  CURRENCIES,
  CURRENCIES_WITH_WITHDRAWALS_ENABLED,
  DELETE_WITHDRAWAL_ACCOUNT,
  GET_WITHDRAWAL_ACCOUNTS,
  WithdrawalAccount,
} from 'catlog-shared';
import colors from '@/theme/colors';
import { wp, hp } from '@/assets/utils/js';
import TopTabs from '@/components/ui/others/top-tabs';
import useWalletContext from '@/contexts/wallet/wallet-context';
import CustomImage from 'src/components/ui/others/custom-image';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import Shimmer from 'src/components/ui/shimmer';
import Column from 'src/components/ui/column';
import { BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import AddBankAccountModal from './add-bank-modal';
import useModals from 'src/hooks/use-modals';

interface WithdrawAccountsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
}

const WithdrawAccountsModal = ({ closeModal, ...props }: WithdrawAccountsModalProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [triggerRefetch, setTriggerRefetch] = useState('');

  const { wallets } = useWalletContext();
  const { modals, toggleModal } = useModals(['addBankModal', 'feesModal']);

  const currencies = wallets
    .map(wallet => wallet.currency)
    .filter(currency => CURRENCIES_WITH_WITHDRAWALS_ENABLED.includes(currency));

  const tabItems = currencies.map(currency => ({
    key: currency,
    title: currency,
    component: <WithdrawalAccountsList currency={currency} triggerRefetch={triggerRefetch} />,
  }));

  const handleAccountAdded = (account: WithdrawalAccount) => {
    // setAccounts([...accounts, account]);
    setTriggerRefetch(Math.random().toString());
  };

  return (
    <BottomModal
      closeModal={closeModal}
      useChildrenAsDirectChild
      size='lg'
      snapPointIndex={1}
      title={'Withdrawal Accounts'}
      buttons={[{ text: 'Add New Account', onPress: () => toggleModal('addBankModal', true) }]}
      {...props}>
      <TopTabs setIndex={setCurrentIndex} currentIndex={currentIndex} tabItems={tabItems} />
      <AddBankAccountModal
        onComplete={handleAccountAdded}
        isVisible={modals.addBankModal}
        closeModal={() => toggleModal('addBankModal', false)}
        currency={currencies[currentIndex]}
      />
    </BottomModal>
  );
};

export default WithdrawAccountsModal;

interface WithdrawalAccountsListProps {
  currency: CURRENCIES;
  triggerRefetch: string;
}

const WithdrawalAccountsList = ({ currency, triggerRefetch }: WithdrawalAccountsListProps) => {
  const [withdrawalAccounts, setWithdrawalAccounts] = useState<WithdrawalAccount[]>([]);

  const deleteAccountRequest = useApi({
    apiFunction: DELETE_WITHDRAWAL_ACCOUNT,
    key: 'delete-withdrawal-account',
    method: 'DELETE',
  });

  const getWithdrawalAccountRequest = useApi<{ currency: CURRENCIES }, ResponseWithoutPagination<WithdrawalAccount[]>>({
    apiFunction: GET_WITHDRAWAL_ACCOUNTS,
    key: GET_WITHDRAWAL_ACCOUNTS.name,
    method: 'GET',
    autoRequest: false,
    onSuccess: response => {
      setWithdrawalAccounts(response.data);
    },
  });

  const handleDeleteAccount = async (account: WithdrawalAccount) => {
    const canDelete = await alertPromise(
      'Delete Account',
      'Are you sure you want to delete this withdrawal account?',
      'Delete',
      'Cancel',
      true,
    );

    if (canDelete) {
      showLoader('Deleting account');
      const [response, error] = await deleteAccountRequest.makeRequest({
        id: account.id,
      });
      hideLoader();
      await delay(700);

      if (response) {
        setWithdrawalAccounts(prev => prev.filter(acc => acc.id !== account.id));
        showSuccess('Withdrawal account deleted successfully');
      }

      if (error) {
        showError(error);
      }
    }
  };

  useEffect(() => {
    getWithdrawalAccountRequest.makeRequest({ currency });
  }, [currency, triggerRefetch]);

  if (getWithdrawalAccountRequest.isLoading) {
    return <WithdrawalAccountsSkeletonLoader />;
  }

  if (withdrawalAccounts.length === 0) {
    return (
      <BottomSheetView style={{flex: 1}} enableFooterMarginAdjustment>
        <EmptyState
          icon={<Bank variant={Platform.OS === 'ios' ? 'Bulk' : "Linear"} size={wp(40)} color={colors.grey.muted} />}
          title="No Withdrawal Accounts"
          text="No withdrawal accounts added yet, click the button below to add a new account"
          btnText="Add New Account"
          showBtn={false}
        />
      </BottomSheetView>
    );
  }

  return (
    <BottomSheetScrollView>
      <View className="px-20">
        {withdrawalAccounts.map((account, index) => (
          <ListItemCard
            key={index}
            leftElement={<CustomImage imageProps={{ source: account.image }} className="w-30 h-30 rounded-full" />}
            title={account.account_name}
            description={`${account.account_number} - ${account.bank_name}`}
            // bottomElement={
            //   <View>
            //     <BaseText fontSize={14} lineHeight={19} weight="semiBold">
            //       {account?.account_number}
            //       {account.account_number} - {account.bank_name}
            //     </BaseText>
            //     <BaseText fontSize={12} classes="text-black-secondary">
            //       {account?.account_name}
            //     </BaseText>
                
            //   </View>
            // }
            rightElement={
              <Pressable onPress={() => handleDeleteAccount(account)}>
                <CircledIcon className="bg-grey-bgOne p-8">
                  <Trash size={wp(14)} color={colors.accentRed.main} />
                </CircledIcon>
              </Pressable>
            }
            titleProps={{ weight: 'medium', classes: 'text-black-secondary', fontSize: 14 }}
          />
        ))}
      </View>
    </BottomSheetScrollView>
  );
};

const dummyRow = new Array(3).fill(0);

const WithdrawalAccountSkeleton = () => {
  return (
    <View className="py-15 mb-5 bg-white rounded-12">
      <Row classes="items-center">
        <Shimmer borderRadius={wp(999)} height={hp(40)} width={wp(40)} />
        <Column className="ml-12 flex-1">
          <Shimmer classes="mb-5" borderRadius={wp(10)} height={hp(12)} width={wp(120)} />
          <Shimmer classes="mb-5" borderRadius={wp(10)} height={hp(10)} width={wp(100)} />
          {/* <Shimmer borderRadius={wp(10)} height={hp(10)} width={wp(80)} /> */}
        </Column>
        <Shimmer borderRadius={wp(999)} height={hp(35)} width={wp(35)} />
      </Row>
    </View>
  );
};

const WithdrawalAccountsSkeletonLoader = () => {
  return (
    <View className="px-20">
      {dummyRow.map((_, index) => (
        <WithdrawalAccountSkeleton key={index} />
      ))}
    </View>
  );
};

// ********** FCMB
// ********** Kuda
