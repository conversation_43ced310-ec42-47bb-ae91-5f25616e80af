import React, { useState } from 'react';
import { Animated, ScrollView, Text, View, FlatList, ImageBackground, Dimensions } from 'react-native';
import { wp, hp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import Row from '../ui/row';
import { BaseText } from '../ui/base';
import cx from 'classnames';

const screenWidth = Dimensions.get('window').width;
const width = wp(305);

export interface BalanceCardItem {
  icon: React.ReactNode;
  iconBg: string;
  // header: React.ReactNode;
  title: string;
  getValue: () => string;
  valueAddon?: React.ReactNode;
  bottomAddon?: React.ReactNode;
  cardBg: any;
  fullWidth?: boolean;
}

interface BalanceCardDotsProps {
  scrollX: Animated.Value;
  colors: { active: string; inactive: string };
  length: number;
}

export const Dots = (props: BalanceCardDotsProps) => {
  const { scrollX, colors, length } = props;
  const stepPosition = Animated.divide(scrollX, width);

  return (
    <View className="flex-row items-center">
      {Array(length)
        .fill(null)
        .map((item, index) => {
          const backgroundColor = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [colors.inactive, colors.active, colors.inactive],
            extrapolate: 'clamp',
          });

          const height = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(3), hp(4), hp(3)],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={`step-${index}`}
              className="w-15 mx-3 rounded-full"
              style={{ backgroundColor, height }}
            />
          );
        })}
    </View>
  );
};

interface RenderCardProps {
  item: BalanceCardItem;
  index: number;
}

const renderCard = ({ item, index }: RenderCardProps) => {
  return (
    <ImageBackground
      className={`px-15 rounded-[15px] overflow-hidden items-start justify-center py-20`}
      source={item?.cardBg}
      style={{ width: item?.fullWidth ? screenWidth - wp(40) : wp(305) }}
      key={index}>
      <Row classes="items-start">
        <Row classes={`p-10 rounded-full items-center justify-center`} style={{ backgroundColor: item.iconBg }}>
          {item?.icon}
        </Row>
        <View className={'flex-1 ml-10'}>
          <BaseText fontSize={12} classes={'text-xs leading-4 text-black-main mb-1'}>
            {item?.title}
          </BaseText>
          {/* {item} */}
          <Row classes={cx('flex-1 justify-start items-center')}>
            <BaseText type="heading" fontSize={22} classes="text-black-main">
              {item.getValue()}
            </BaseText>
            {item.valueAddon ? item.valueAddon : null}
          </Row>
          {item.bottomAddon ? item.bottomAddon : null}
        </View>
      </Row>
    </ImageBackground>
  );
};

interface PaymentBalanceCardsProps {
  cards: BalanceCardItem[];
  dotsColor: string;
}

const PaymentBalanceCards = ({ cards, dotsColor }: PaymentBalanceCardsProps) => {
  const [scrollX] = useState(new Animated.Value(0));

  return (
    <View>
      <FlatList
        horizontal
        scrollEnabled
        showsHorizontalScrollIndicator={false}
        snapToAlignment={'center'}
        pagingEnabled
        contentContainerStyle={{ paddingHorizontal: wp(20) }}
        ItemSeparatorComponent={() => <View className="w-15" />}
        scrollEventThrottle={20}
        data={cards}
        renderItem={props => renderCard({ ...props, item: { ...props.item, fullWidth: cards.length === 1 } })}
        keyExtractor={(item, index) => index.toString()}
        onScroll={Animated.event(
          [
            {
              nativeEvent: { contentOffset: { x: scrollX } },
            },
          ],
          { useNativeDriver: false },
        )}
      />
      <View className="items-center pt-10">
        <Dots scrollX={scrollX} colors={{ active: dotsColor, inactive: colors.grey.border }} length={cards.length} />
      </View>
    </View>
  );
};

export default PaymentBalanceCards;
