import { useEffect, useState } from 'react';
import { Dimensions, ScrollView, View } from 'react-native';
import WebView from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import BottomModal from 'src/components/ui/modals/bottom-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ContainerLoader } from '../products/add-product-controller/instagram-import/instagram-webview';

interface Props {
  show: boolean;
  toggle: VoidFunction;
}
const PreviewStoreFront: React.FC<Props> = ({ show, toggle }) => {
  const [siteLoaded, setSiteLoaded] = useState(false);
  const { storeLink } = useAuthContext();

  useEffect(() => {
    if (show == false) setSiteLoaded(false);
  }, [show]);

  return (
    <BottomModal
      enableDynamicSizing
      showButton={false}
      className="p-0 m-0 justify-end"
      isVisible={show}
      closeModal={toggle}>
      <ScrollView className="w-full p-0 pb-40">
        <WebView
          onLoad={() => setSiteLoaded(true)}
          style={{ height: Dimensions.get('window').height / 1.25 }}
          className={cx('w-full p-0', { 'opacity-0': !siteLoaded })}
          source={{ uri: `${storeLink}?preview_mode=true` }}
        />
      </ScrollView>
      {!siteLoaded && (
        <View className="absolute items-center justify-center w-full h-full flex-1">
          <ContainerLoader message="Loading Webpage..." />
        </View>
      )}
    </BottomModal>
  );
};

export default PreviewStoreFront;
