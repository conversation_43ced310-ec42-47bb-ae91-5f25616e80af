import { COUNTRIES, paramsFromObject } from 'catlog-shared';
import { useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Dimensions, ScrollView, View } from 'react-native';
import WebView, { WebViewMessageEvent, WebViewNavigation } from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';
import BottomModal from 'src/components/ui/modals/bottom-modal';
import { ContainerLoader } from '../products/add-product-controller/instagram-import/instagram-webview';
import { Plan } from 'catlog-shared';
import { IDelivery } from 'catlog-shared';
import { PAYMENT_TYPES } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';
import { EXPO_PUBLIC_DASHBOARD_URL } from '@env';

export enum PAYMENT_WIDGET_ADDONS {
  DELIVERY = 'delivery',
}

enum MESSAGE_TYPE {
  SUCCESS = 'success',
  CLOSE = 'close',
}

interface Props {
  show: boolean;
  toggle: VoidFunction;
  country: COUNTRIES;
}
const TestPaymentWidget: React.FC<Props> = ({ show, toggle, country }) => {
  const [siteLoaded, setSiteLoaded] = useState(false);
  const { getToken } = useAuthContext();

  useEffect(() => {
    if (show == false) setSiteLoaded(false);
  }, [show]);

  const params = useMemo(
    () =>
      paramsFromObject({
        authToken: getToken(),
        country,
      }),
    [country],
  );

  const uri = `${EXPO_PUBLIC_DASHBOARD_URL}/payments/widget/test-payment?${params}`;

  //How do we handle error, I'm not sure if that's what case MESSAGE_TYPE.CLOSE is for
  const handleMessage = (event: WebViewMessageEvent) => {
    const message = event.nativeEvent.data;
    // console.log(message);
    try {
      const data = JSON.parse(message) as { type: MESSAGE_TYPE };
      switch (data.type) {
        case MESSAGE_TYPE.SUCCESS:
          toggle();
          break;
        case MESSAGE_TYPE.CLOSE:
          toggle();
          break;
      }
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <BottomModal showButton={false} isVisible={show} size="lg" closeModal={toggle}>
      <WebView
        onMessage={e => handleMessage(e)}
        onLoad={() => setSiteLoaded(true)}
        style={{ height: Dimensions.get('window').height / 1.25, width: Dimensions.get('window').width }}
        className={cx('w-full p-0')}
        source={{ uri: uri }}
        onError={syntheticEvent => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
        }}
        javaScriptEnabled={true}
        // renderLoading={() => <ContainerLoader message="Loading..." />}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
      />
      {!siteLoaded && (
        <View className="w-full min-h-[150px] bg-white justify-center items-center absolute h-full">
          <ActivityIndicator className="text-black-main" size="small" animating />
          <BaseText classes="mt-10">Loading...</BaseText>
        </View>
      )}
    </BottomModal>
  );
};

export default TestPaymentWidget;
