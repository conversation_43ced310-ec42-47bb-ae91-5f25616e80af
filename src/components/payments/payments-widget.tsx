import { DomainPurchase, paramsFromObject } from 'catlog-shared';
import { useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Dimensions, ScrollView, View } from 'react-native';
import WebView, { WebViewMessageEvent, WebViewNavigation } from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';
import BottomModal, { modalSizes } from 'src/components/ui/modals/bottom-modal';
import { ContainerLoader } from '../products/add-product-controller/instagram-import/instagram-webview';
import { Plan } from 'catlog-shared';
import { IDelivery } from 'catlog-shared';
import { PAYMENT_TYPES } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';
import { EXPO_PUBLIC_DASHBOARD_URL } from '@env';
import * as Sentry from '@sentry/react-native';

export enum PAYMENT_WIDGET_ADDONS {
  DELIVERY = 'delivery',
}

enum MESSAGE_TYPE {
  SUCCESS = 'success',
  CLOSE = 'close',
}

interface Props {
  show: boolean;
  toggle: (status?: boolean) => void;
  onComplete: (reference: string) => void;
  data: {
    plan?: Plan;
    delivery?: IDelivery;
    tokens?: number;
    paymentType?: PAYMENT_TYPES;
    successMessage?: string;
    domain?: DomainPurchase;
    addon?: PAYMENT_WIDGET_ADDONS;
    upfrontSubscription?: boolean;
  };
}

const PaymentsWidget: React.FC<Props> = ({ show, toggle, onComplete, data }) => {
  const [siteLoaded, setSiteLoaded] = useState(false);
  const { getToken } = useAuthContext();

  useEffect(() => {
    if (show == false) setSiteLoaded(false);
  }, [show]);

  const uri = useMemo(() => {
    const token = getToken();
    const payload = { ...data, authToken: token };
    const params = btoa(JSON.stringify(payload));
    const dburl = EXPO_PUBLIC_DASHBOARD_URL;
    return `${dburl}/payments/widget?data=${params}`;
  }, [data]);

  //How do we handle error, I'm not sure if that's what case MESSAGE_TYPE.CLOSE is for
  const handleMessage = (event: WebViewMessageEvent) => {
    const message = event.nativeEvent.data;
    try {
      const data = JSON.parse(message) as { type: MESSAGE_TYPE; reference?: string };
      switch (data.type) {
        case MESSAGE_TYPE.SUCCESS:
          onComplete(data.reference);
          toggle(false);
          break;
        case MESSAGE_TYPE.CLOSE:
          toggle(false);
          break;
      }
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <BottomModal
      size="md"
      useChildrenAsDirectChild
      customSnapPoints={[90]}
      showButton={false}
      className="p-0 m-0 justify-end"
      isVisible={show}
      closeModal={() => toggle(false)}>
      <View className="flex-1  p-0 pb-40">
        {uri && (
          <WebView
            onMessage={e => handleMessage(e)}
            onLoad={() => setSiteLoaded(true)}
            onError={e => {
              console.log(e);
              Sentry.captureException(e);
            }}
            className={cx(
              'w-full flex-1',
              // { 'opacity-0': !siteLoaded }
            )}
            source={{ uri: uri }}
            allowsLinkPreview={false}
          />
        )}
        {(siteLoaded === false || !uri) && (
          <View className="absolute w-full h-full justify-center z-10">
            <ContainerLoader message="Loading Webpage..." />
          </View>
        )}
      </View>
    </BottomModal>
  );
};

export default PaymentsWidget;
