import { Activity, BagTick2, Box, Chart2, Health, Profile2User, ShoppingBag, StatusUp } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify, toCurrency, toN<PERSON>a } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import AnalyticsCard from '../ui/cards/analytics-card';
import { StoreSummary } from '@/screens/orders/order-analytics';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';
import { AnalyticsData } from 'src/screens/payments/payment-analytics';
import { CURRENCIES } from 'catlog-shared';


export enum AnalyticsVariants {
  'TOTAL_VOLUMES' = 'total_volume',
  'TOTAL_PAYMENTS' = 'total_payments',
  'AVERAGE_DAILY_VOLUME' = 'average_daily_volume',
  'AVERAGE_TRANSACTION' = 'average_transaction',
}

interface OrderAnalyticsProps {
  analytics: AnalyticsData;
  loading?: boolean;
}

const PaymentAnalyticsCards = ({ analytics = {} as AnalyticsData, loading }: OrderAnalyticsProps) => {
  const analyticsCardsInfo: AnalyticsCardInfo[] = [
    {
      title: 'Total Volumes',
      iconBg: 'bg-accentRed-main',
      icon: <Chart2 variant="Bold" size={wp(18)} color={colors?.white} />,
      cardBg: 'bg-transparent',
      key: AnalyticsVariants.TOTAL_VOLUMES,
      value: analytics[AnalyticsVariants.TOTAL_VOLUMES]
        ? millify(toNaira(analytics[AnalyticsVariants.TOTAL_VOLUMES]), 2, 'NGN')
        : '-',
      change: 0,
    },
    {
      title: 'Average Daily',
      cardBg: 'bg-transparent',
      icon: <Health variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentGreen-main',
      key: AnalyticsVariants.AVERAGE_DAILY_VOLUME,
      value: analytics[AnalyticsVariants.AVERAGE_DAILY_VOLUME]
        ? millify(toNaira(analytics[AnalyticsVariants.AVERAGE_DAILY_VOLUME]), 2, 'NGN')
        : '-',
      change: 0,
    },
    {
      title: 'No. of payments',
      cardBg: 'bg-transparent',
      icon: <Box variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.TOTAL_PAYMENTS,
      value: analytics[AnalyticsVariants.TOTAL_PAYMENTS] ? analytics[AnalyticsVariants.TOTAL_PAYMENTS] : '-',
      change: 0,
    },
    {
      title: 'Average TX',
      cardBg: 'bg-transparent',
      icon: <StatusUp variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentOrange-main',
      key: AnalyticsVariants.AVERAGE_TRANSACTION,
      value: millify(
        toNaira(analytics[AnalyticsVariants.TOTAL_VOLUMES] / analytics[AnalyticsVariants.TOTAL_PAYMENTS]),
        2,
        'NGN',
      ),
      change: 0,
    },
  ];

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  return (
    <View className="border border-grey-border rounded-[15px]">
      {loading && <AnalyticsSkeletonLoader />}
      {!loading && (
        <Fragment>
          {splitCards.map((group, i) => (
            <Fragment key={i}>
              <View className="flex-row last:mb-0">
                {group.map((info, index) => (
                  <Fragment key={index}>
                    <AnalyticsCard
                      className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                      title={info.title}
                      value={info.value}
                      iconBg={info.iconBg}
                      change={info.change}
                      showChange={false}
                      icon={info.icon}
                      addon={info.addon}
                      theme="white"
                    />
                    {index === 0 && <View className="w-1 bg-grey-border" />}
                  </Fragment>
                ))}
              </View>
              {i === 0 && <View className="h-1 bg-grey-border" />}
            </Fragment>
          ))}
        </Fragment>
      )}
    </View>
  );
};

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

export default PaymentAnalyticsCards;
