import React from 'react';
import SectionContainer from '../ui/section-container';
import ListItemCard from '../ui/cards/list-item-card';
import { useApi } from 'src/hooks/use-api';
import { COUNTRIES, GET_PAYMENTS_SETUP_PROGRESS } from 'node_modules/catlog-shared/dist';
import { useNavigation } from '@react-navigation/core';
import useAuthContext from 'src/contexts/auth/auth-context';
import useModals from 'src/hooks/use-modals';
import TestPaymentWidget from './test-payment-widget';
import { PaymentSettingsTabs } from '../store-settings/types';
import CircledIcon from '../ui/circled-icon';
import { Card, Copy, Lock, Map1, TickCircle, Wallet } from 'iconsax-react-native/src';
import { wp } from 'src/assets/utils/js';
import { ArrowRight, CheckActive } from '../ui/icons';
import colors from 'src/theme/colors';

interface PaymentActivationStepsProp {
  country: COUNTRIES;
}

const PaymentActivationSteps = ({ country }: PaymentActivationStepsProp) => {
  const { store } = useAuthContext();
  const { modals, toggleModal } = useModals(['test_payment']);

  const { response } = useApi({
    apiFunction: GET_PAYMENTS_SETUP_PROGRESS,
    key: GET_PAYMENTS_SETUP_PROGRESS.name,
    method: 'GET',
  });

  const navigation = useNavigation();

  const setupProgressData = { ...response?.data, has_made_test_payment: store?.onboarding_steps?.test_payment_made };
  const steps = (
    setupProgressData ? stepData.map(s => ({ ...s, isCompleted: setupProgressData[s.key] })) : stepData
  ).sort((a, b) => (b.isCompleted ? -1 : 0));

  const handleStepClick = step => {
    switch (step) {
      case 'has_made_test_payment':
        //toggle make test payment widget
        toggleModal('test_payment');
        break;
      case 'has_delivery_areas':
        // route to delivery areas page @feranmi
        // @ts-ignore
        navigation.navigate('StoreSettingsStack', {
          screen: 'DeliveryAreas',
        });
        break;
      case 'has_withdrawal_accounts':
        // route to create withdrawals page
        //@ts-ignore

        //need to implement withdrawal accounts management
        navigation.navigate('StoreSettingsStack', {
          screen: 'PaymentSettings',
        });
        break;
      case 'security_pin_added':
        // route to add security pin page
        //@ts-ignore
        navigation.navigate('StoreSettingsStack', {
          screen: 'PaymentSettings',
          params: { tab: PaymentSettingsTabs.SECURITY_PIN },
        });
        break;
    }
  };

  return (
    <>
      <SectionContainer classes="mt-0">
        {steps.map((item, index) => (
          <ListItemCard
            key={item.key}
            onPress={item?.isCompleted ? undefined : () => handleStepClick(item.key)}
            leftElement={item.leftIcon}
            title={item.title}
            description={item.subtitle}
            descriptionProps={{
              className: 'mt-0 text-black-muted',
              fontSize: wp(11),
            }}
            titleProps={{
              classes: 'mt-0 text-black-main',
              fontSize: wp(12),
              weight: 'bold',
              type: 'heading',
            }}
            showBorder={steps.length - 1 !== index}
            containerClasses="py-15"
            rightElement={
              item.isCompleted ? (
                <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                  <CheckActive size={wp(16)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
                </CircledIcon>
              ) : (
                <CircledIcon iconBg={'bg-white'}>
                  <ArrowRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />
                </CircledIcon>
              )
            }
          />
        ))}
      </SectionContainer>
      <TestPaymentWidget
        country={country}
        show={modals.test_payment}
        toggle={() => toggleModal('test_payment', false)}
      />
    </>
  );
};

export default PaymentActivationSteps;

export const stepData = [
  {
    title: 'Test Payments',
    subtitle: 'Make a test payment',
    leftIcon: (
      <CircledIcon classes="p-10 bg-accentRed-pastel">
        <Card variant="Bold" size={wp(16)} strokeWidth={2} color={colors.accentRed.main} />
      </CircledIcon>
    ),
    isCompleted: false,
    key: 'has_made_test_payment',
  },
  {
    title: 'Delivery Areas',
    subtitle: 'Add delivery areas & Fees',
    leftIcon: (
      <CircledIcon classes="p-10 bg-accentYellow-pastel">
        <Map1 variant="Bold" size={wp(16)} strokeWidth={2} color={colors.accentYellow.main} />
      </CircledIcon>
    ),
    isCompleted: false,
    key: 'has_delivery_areas',
  },
  {
    title: 'Setup Withdrawals',
    subtitle: 'Add withdrawal accounts',
    leftIcon: (
      <CircledIcon classes="p-10 bg-accentOrange-pastel">
        <Wallet variant="Bold" size={wp(16)} strokeWidth={2} color={colors.accentOrange.main} />
      </CircledIcon>
    ),
    isCompleted: false,
    key: 'has_withdrawal_accounts',
  },
  {
    title: 'Security Pin',
    subtitle: 'Set a security pin for withdrawals',
    leftIcon: (
      <CircledIcon classes="p-10 bg-accentGreen-pastel">
        <Lock variant="Bold" size={wp(16)} strokeWidth={2} color={colors.accentGreen.main} />
      </CircledIcon>
    ),
    isCompleted: false,
    key: 'security_pin_added',
  },
];
