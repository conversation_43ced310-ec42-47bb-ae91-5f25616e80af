import { ArrowCircleRight, ArrowCircleRight2, ArrowRight } from 'iconsax-react-native/src';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode } from 'react';
import Row from '../ui/row';
import { StyledProps } from 'nativewind';
import colors from '@/theme/colors';
import { toCurrency, wp } from '@/assets/utils/js';
import { BaseText } from '../ui';
import CircledIcon from '../ui/circled-icon';

export interface TransactionListCardProps {
  showBorder?: boolean;
  date: string;
  amount: number;
  description: string;
  actionScreenName?: string;
  // variant: TransactionListCardVariant;
  variant: 'credit' | 'debit';
}

export enum TransactionListCardVariant {
  'DEBIT' = 'debit',
  'CREDIT' = 'credit',
}

const { width } = Dimensions.get('window');

const TransactionListCard = ({
  showBorder = true,
  date,
  description,
  amount,
  variant = TransactionListCardVariant.CREDIT,
}: TransactionListCardProps) => {
  const navigation = useNavigation();

  const variantProperty: { [key: string]: { icon: ReactNode; iconBg: string } } = {
    [TransactionListCardVariant.DEBIT]: {
      icon: (
        <View style={{ transform: [{ rotate: '-45deg' }] }}>
          <ArrowCircleRight2 variant="Bold" size={wp(24)} color={colors?.accentRed.main} />
        </View>
      ),
      iconBg: 'bg-accentRed-transparent',
    },
    [TransactionListCardVariant.CREDIT]: {
      icon: (
        <View style={{ transform: [{ rotate: '135deg' }] }}>
          <ArrowCircleRight2 variant="Bold" size={wp(24)} color={colors?.accentGreen.main} />
        </View>
      ),
      iconBg: 'bg-accentGreen-transparent',
    },
  };

  return (
    <TouchableOpacity
      className={`flex-row items-center py-15 ${showBorder && 'border-b border-b-grey-border'}`}
      activeOpacity={0.8}>
      <Row className="items-center">
        <CircledIcon classes={`p-8 rounded-full items-center justify-center ${variantProperty[variant].iconBg}`}>
          {variantProperty[variant].icon}
        </CircledIcon>
        <Row className="items-start flex-1 ml-12">
          <View className="mr-12 flex-1">
            <BaseText type="body" fontSize={12} classes={'leading-4 text-black-primary'} numberOfLines={1}>
              {description}
            </BaseText>
            <BaseText type="body" fontSize={11} classes={'leading-4 text-black-muted mt-5'}>
              {date}
            </BaseText>
          </View>
          <BaseText type="heading" fontSize={11} classes={'text-xs leading-4 text-black-muted'}>
            {toCurrency(amount ?? 0)}
          </BaseText>
        </Row>
      </Row>
    </TouchableOpacity>
  );
};

export default TransactionListCard;
