import { View } from 'react-native';
import Row from '../ui/row';
import Shimmer from '../ui/shimmer';
import { wp, hp } from 'src/assets/utils/js';
import Column from '../ui/column';
import Separator from '../ui/others/separator';

const dummyRow = new Array(5).fill(0);

interface StorefrontSkeletonLoaderProps {
  showTitle?: boolean;
}

const TeamsSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = ({ showTitle = true }) => {
  return (
    <View className="mx-20 mb-25">
      {showTitle && <Shimmer classes="mb-20" borderRadius={wp(10)} height={hp(15)} width={wp(70)} />}

      {dummyRow.map((_, index) => (
        <View key={index}>
          <Row className="items-start" spread>
            <Row>
              <Shimmer borderRadius={wp(999)} height={hp(40)} width={wp(40)} />
              <Column className="ml-10" spread>
                <Shimmer classes="mb-10" borderRadius={wp(10)} height={hp(12)} width={wp(80)} />
                <Shimmer classes="" borderRadius={wp(10)} height={hp(10)} width={wp(45)} />
              </Column>
            </Row>
            <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(10)} width={wp(40)} />
          </Row>
          {index !== dummyRow.length && <Separator className="mx-0" />}
        </View>
      ))}
    </View>
  );
};

export default TeamsSkeletonLoader;
