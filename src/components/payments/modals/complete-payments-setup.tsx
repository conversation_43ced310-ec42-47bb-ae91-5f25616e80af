import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { COUNTRIES } from 'catlog-shared';
import PaymentActivationSteps from '../activation-steps';
import { View } from 'react-native';

interface CompletePaymentsSetupModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  country: COUNTRIES;
}

const CompletePaymentsSetupModal = ({ children, country, ...props }: CompletePaymentsSetupModalProps) => {
  return (
    <BottomModal size="midi" title={'Complete Setup'} showButton={false} {...props}>
      <View className="px-20 mt-10">
        <PaymentActivationSteps country={country} />
      </View>
    </BottomModal>
  );
};

export default CompletePaymentsSetupModal;
