import { ScrollView, View } from 'react-native';
import { useMemo, useRef, useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import SectionContainer, { ContainerType } from '@/components/ui/section-container';
import { BaseText, Row } from '@/components/ui';
import { toCurrency, wp } from 'src/assets/utils/js';
import useAuthContext from 'src/contexts/auth/auth-context';
import { CURRENCIES } from 'catlog-shared';
import { currencyDetails } from '../wallet-cards';

interface TodaysRateModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  initialCurrency: CURRENCIES;
}

const TodaysRateModal = ({ children, closeModal, onPressProceed, initialCurrency, ...props }: TodaysRateModalProps) => {
  const { currentRates } = useAuthContext();

  const [baseCurrency, setBaseCurrency] = useState<CURRENCIES>(initialCurrency);
  const currencyRates = currentRates[baseCurrency] ?? {};

  const currencyOptions = useMemo(() => {
    const currencies = Object.keys(currentRates);
    return currencies.map(currency => ({
      value: currency,
      label: `${currencyDetails[currency].fullName} (${currency})`,
    }));
  }, [currentRates]);

  return (
    <BottomModal enableDynamicSizing closeModal={closeModal} title={'Current Rates'} showButton={false} {...props}>
      <View className="px-20 mt-20">
        <SelectDropdown
          label="Base Currency"
          onPressItem={value => setBaseCurrency(value as CURRENCIES)}
          selectedItem={baseCurrency}
          items={currencyOptions}
        />
        <SectionContainer containerType={ContainerType.OUTLINED} className="px-0">
          <Row className="px-14 p-16 bg-grey-bgTwo border-b border-b-grey-border">
            <BaseText fontSize={14} classes="text-black-muted">
              Currency
            </BaseText>
            <BaseText fontSize={14}>Equivalent in {baseCurrency}</BaseText>
          </Row>
          {Object.keys(currencyRates).map((currency, index) => (
            <Row className="px-14 py-16" key={currency}>
              <View className="w-18 h-18 mr-5">{currencyDetails[currency].icon({})}</View>
              <View className="flex-1 mx-8">
                <BaseText fontSize={14} weight="regular" classes="text-black-muted">
                  {currency} 1.0
                </BaseText>
              </View>
              <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
                {toCurrency(1 / currencyRates[currency], baseCurrency, false, 4)}
              </BaseText>
            </Row>
          ))}
        </SectionContainer>
      </View>
      <View className="h-80" />
    </BottomModal>
  );
};

export default TodaysRateModal;
