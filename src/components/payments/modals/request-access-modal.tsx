import { View } from 'react-native';
import { useState } from 'react';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { ListCard } from '@/components/ui/cards/list-item-card';
import { BaseText, CircledIcon } from '@/components/ui';
import { CheckActive } from '@/components/ui/icons';
import { hp, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import useSteps from 'src/hooks/use-steps';
import Camera from '@/components/ui/others/camera';
import { CloseCircle, Image, InfoCircle, TickCircle } from 'iconsax-react-native/src';
import Pressable from '@/components/ui/base/pressable';
import useImagePicker from 'src/hooks/use-image-picker';
import cx from 'classnames';
import CustomImage from '@/components/ui/others/custom-image';
import WhiteCardBtn from '@/components/ui/buttons/white-card-btn';
import { ButtonVariant } from '@/components/ui/buttons/button';
import SelectDropdown from '../../ui/inputs/select-dropdown';
import Input from '../../ui/inputs/input';

export interface DateRangeString {
  from: string | null;
  to: string | null;
}

interface RequestAccessModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
}

const RequestAccessModal = ({ closeModal, onPressProceed, ...props }: RequestAccessModalProps) => {
  const [accessRequested, setAccessRequested] = useState(false);
  return (
    <BottomModal
      closeModal={closeModal}
      title={'Request Access'}
      buttons={[
        {
          text: accessRequested ? 'Done' : 'Request Access',
          onPress: () => (accessRequested ? closeModal() : setAccessRequested(prev => !prev)),
        },
      ]}
      {...props}>
      {!accessRequested && (
        <View className="mx-20 mb-20">
          <SelectDropdown items={[]} label={'Select Currencies'} showLabel onPressItem={v => {}} />
          <Input label="Why do you want to enable international payments?" containerClasses="mt-15" />
          <SelectDropdown
            items={[]}
            label={'Are you currently receiving payments from international customers?'}
            containerClasses="mt-15"
            showLabel
            onPressItem={v => {}}
          />
          <Input label="How do you plan to get paid by your foreign customers?" containerClasses="mt-15" />
        </View>
      )}

      {accessRequested && (
        <View className="mx-20 mb-[60px]">
          <CircledIcon className="self-center bg-accentGreen-pastel p-8">
            <CircledIcon className="bg-accentGreen-main p-14">
              <TickCircle variant={'Bold'} size={wp(32)} color={colors.white} />
            </CircledIcon>
          </CircledIcon>
          <BaseText fontSize={22} type={'heading'} classes="text-center mt-10">
            You request has been{'\n'}successfully sent
          </BaseText>
          <BaseText type={'body'} classes="text-center mt-10">
            Your request to enable international payments on your account has been received and is under review.
          </BaseText>
        </View>
      )}
    </BottomModal>
  );
};

export default RequestAccessModal;
