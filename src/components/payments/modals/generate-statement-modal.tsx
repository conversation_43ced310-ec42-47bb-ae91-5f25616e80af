import { useFormik } from 'formik';
import { useState } from 'react';
import { View } from 'react-native';
import { delay, getFieldvalues, hideLoader, showLoader } from 'src/assets/utils/js/functions';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { useApi } from 'src/hooks/use-api';
import { useFileDownload } from 'src/hooks/use-file-download';
import * as Yup from 'yup';
import CalenderInput from '@/components/ui/inputs/calender-input';
import { CREATE_PAYMENT_STATEMENT, CURRENCIES } from 'catlog-shared';
import { useStoreReview } from 'src/hooks/use-store-review';

interface GenerateStatementModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
  wallets: {
    currency: CURRENCIES;
    id: string;
}[]
}

const GenerateStatementModal = ({ children, wallets, closeModal, onPressProceed, ...props }: GenerateStatementModalProps) => {
  const availableCurrencies = wallets?.map((w) => ({value: w.currency, label: w.currency})) ?? [];

  const [selectedCurrency, setSelectedCurrency] = useState(availableCurrencies?.[0]?.value);

    const { handleReviewRequest } = useStoreReview();

  const { downloadFile } = useFileDownload();
  const [loadingText, setLoadingText] = useState('Generating Statement...');
  const createPaymentStatementReq = useApi<any>({
    key: CREATE_PAYMENT_STATEMENT.name,
    apiFunction: CREATE_PAYMENT_STATEMENT,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      startDate: null,
      endDate: null,
      currency: null,
      format: 'pdf',
    },
    onSubmit: async ({ startDate, endDate, currency }) => {
      showLoader('Generating your statement....');
      const [res, error] = await createPaymentStatementReq.makeRequest({
        start_date: startDate,
        end_date: endDate,
        currency: currency,
      });
      if (res) {
        hideLoader();
        const data = res.data;
        const downloadUrl = `wallets/statements/pdf/${data.id}?pin=${data.pin}`;
        await delay(1000);
        showLoader('Downloading your statement....');
        setLoadingText('Downloading File...');
        await downloadFile(downloadUrl, `Payment_Statement_${data.id}.pdf`);
        hideLoader();
        close();
        handleReviewRequest()
      }
    },
    validationSchema,
  });

  return (
    <BottomModal
      closeModal={closeModal}
      title={'Generate Statements'}
      buttons={[
        { text: 'Generate Statement', onPress: () => form.submitForm(), isLoading: form.isSubmitting, loadingText },
      ]}
      {...props}>
      <View className="mx-20 mb-20 mt-10">
        <CalenderInput
          onDateChange={date => form.setFieldValue('startDate', date)}
          inputProps={{
            label: 'Start Date',
            ...getFieldvalues('startDate', form),
            value: form?.values?.startDate?.toDateString(),
          }}
        />
        <CalenderInput
          onDateChange={date => form.setFieldValue('endDate', date)}
          inputProps={{
            label: 'End Date',
            containerClasses: 'mt-15',
            ...getFieldvalues('endDate', form),
            value: form?.values?.endDate?.toDateString(),
          }}
        />
        <SelectDropdown
          label="Format"
          containerClasses="mt-15"
          items={[{ label: 'PDF', value: 'pdf' }]}
          selectedItem={form.values.format}
          onPressItem={() => {}}
        />
        <SelectDropdown
          label="Select Currency"
          containerClasses="mt-15"
          items={availableCurrencies}
          // selectedItem={form.values.currency}
          {...getFieldvalues('currency', form, 'select')}
        />
      </View>
    </BottomModal>
  );
};

export default GenerateStatementModal;

const validationSchema = Yup.object({
  startDate: Yup.date().required('Start date is required'),
  endDate: Yup.date().when('startDate', {
    is: date => date !== null,
    then: () => Yup.date().min(Yup.ref('startDate'), 'End date must be after start date'),
    otherwise: () => Yup.date().required('End date is required'),
  }),
});
