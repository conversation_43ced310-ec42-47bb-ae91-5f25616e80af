import { LinkSquare, ReceiptText } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import colors from 'src/theme/colors';
import { BaseText, Row } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import CircledIcon from '@/components/ui/circled-icon';
import Pressable from '@/components/ui/base/pressable';
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import classNames from 'node_modules/classnames';

enum CONVERT_CURRENCY_STEPS {
  ENTER_AMOUNT = 'ENTER_AMOUNT',
  ENTER_OTP = 'ENTER_OTP',
  SUCCESS = 'SUCCESS',
}

interface RequestPaymentModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressProceed?: () => void;
}

const RequestPaymentModal = ({ children, closeModal, onPressProceed, ...props }: RequestPaymentModalProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string>();
  const navigation = useNavigation();
  const paymentLinkOption = [
    {
      icon: (
        <CircledIcon className="bg-white p-10">
          <LinkSquare variant="Bold" size={wp(18)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      title: 'Payment Link',
      description: 'Create a link to share with a customer',
      value: 'payment_link',
    },
    {
      icon: (
        <CircledIcon className="bg-white p-10">
          <ReceiptText variant="Bold" size={wp(18)} color={colors.accentGreen.main} />
        </CircledIcon>
      ),
      title: 'Invoice',
      description: 'Generate a professional looking invoice',
      value: 'invoice',
    },
  ];

  const handleContinue = () => {
    if (selectedMethod) {
      closeModal();
      switch (selectedMethod) {
        case 'payment_link':
          navigation.navigate('CreatePaymentLink');
          break;
        case 'invoice':
          navigation.navigate('InvoicesStack', { navigateToCreate: true });
          break;
      }
    }
  };
  return (
    <BottomModal
      closeModal={closeModal}
      buttons={[{ text: 'Continue', onPress: handleContinue, disabled: !selectedMethod }]}
      title="How would you like to get paid?"
      {...props}>
      <View className="mx-20 mb-20">
        <View className="mt-10 bg-grey-bgOne rounded-12">
          {paymentLinkOption.map((item, index) => (
            <Pressable onPress={() => setSelectedMethod(item.value)} key={item.value}>
              <Row className={classNames('py-12 px-15', { 'border-t border-grey-border': index > 0 })}>
                {item.icon}
                <View className="flex-1 mx-8">
                  <BaseText fontSize={12} type="heading" classes="">
                    {item.title}
                  </BaseText>
                  <BaseText fontSize={10} weight="regular" classes="mt-4 text-black-muted">
                    {item.description}
                  </BaseText>
                </View>
                <Radio active={selectedMethod === item.value} />
              </Row>
            </Pressable>
          ))}
        </View>
      </View>
    </BottomModal>
  );
};

export default RequestPaymentModal;
