import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import { FormikProps } from 'formik';
import { getFieldvalues } from 'src/assets/utils/js';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';
import { UpdateStoreConfigParams } from 'catlog-shared';

interface StoreMessagesProps extends SharedStoreConfigProps {}

const StoreMessages = ({ form, isLoading }: StoreMessagesProps) => {
  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Custom store & enquiry messages'} />
          <View className="mt-20 pb-30">
            <Input
              label={'Top Notification Message'}
              multiline
              // placeholder={'My store is keen on making sales of food and clothes an easy one'}
              className="h-[100]"
              containerClasses="mt-15"
              {...getFieldvalues('configuration.custom_message', form)}
              value={form.values.configuration.custom_message}
            />
            <Input
              label={'Custom enquiry message'}
              multiline
              // placeholder={'Describe message for when customers make an enquiry'}
              className="h-[100]"
              containerClasses="mt-15"
              {...getFieldvalues('configuration.enquiry_message', form)}
              value={form.values.configuration.enquiry_message}
            />
            <Input
              label={'Custom order successful message'}
              multiline
              // placeholder={'Describe message for when customers make an enquiry'}
              className="h-[100]"
              containerClasses="mt-15"
              {...getFieldvalues('configuration.custom_order_success_message', form)}
              value={form.values.configuration.custom_order_success_message}
              maxLength={300}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default StoreMessages;
