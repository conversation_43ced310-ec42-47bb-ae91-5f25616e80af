import BottomModal, { BottomModalMethods } from '@/components/ui/modals/bottom-modal';
import {
  CURRENCIES,
  CURRENCY_OPTIONS,
  InternationalPaymentRequestParams,
  paymentsEnabledCurrencies,
  REQUEST_INTERNATIONAL_PAYMENT,
} from 'catlog-shared/dist';
import { useFormik } from 'formik';
import { TickCircle, Warning2 } from 'iconsax-react-native/src';
import { useEffect, useRef, useState } from 'react';
import { Keyboard, TextInput, View } from 'react-native';
import { getFieldvalues, showError, wp, Yup } from 'src/assets/utils/js';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import { BaseText, CircledIcon } from 'src/components/ui';
import Pressable from 'src/components/ui/base/pressable';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import Input from 'src/components/ui/inputs/input';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useKeyboard from 'src/hooks/use-keyboard';
import colors from 'src/theme/colors';

interface Props {
  show: boolean;
  toggle: any;
}
const EnabledCurrenciesModal: React.FC<Props> = ({ show, toggle }) => {
  const { store, subscription } = useAuthContext();
  const [step, setStep] = useState<'form' | 'success' | 'upgrade'>('upgrade');
  const bottomModalRef = useRef<BottomModalMethods>(null);
  const inputRef = useRef<TextInput>(null);

  const existingWallets = store?.wallets.map(wallet => wallet.currency) ?? [];

  let canRequestInternationalPayments = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_REQUEST_INTERNATIONAL_PAYMENTS,
  });

  const currencyOptions = paymentsEnabledCurrencies
    .map(p => CURRENCY_OPTIONS.find(o => o.value === p))
    .filter(currency => !existingWallets.includes(currency.value));

  const isKeyboardActive = useKeyboard();
  
    useEffect(() => {
      if (isKeyboardActive) {
        bottomModalRef.current?.expand();
      } else {
        bottomModalRef.current?.snapToIndex(0);
      }
    }, [isKeyboardActive]);

  useEffect(() => {
    if (!canRequestInternationalPayments) {
      setStep('upgrade');
    } else {
      setStep('form');
    }
  }, [canRequestInternationalPayments, show]);

  const paymentRequest = useApi<InternationalPaymentRequestParams, any>({
    apiFunction: REQUEST_INTERNATIONAL_PAYMENT,
    key: REQUEST_INTERNATIONAL_PAYMENT.name,
    method: 'POST',
  });

  const form = useFormik({
    initialValues: {
      requested_currencies: [],
      reason: '',
      collect_payments_from_abroad: '',
      current_payment_method: '',
      plans_to_get_customers_abroad: '',
    },
    validationSchema: generateValidationSchema(),
    onSubmit: async values => {
      const [res, err] = await paymentRequest.makeRequest({
        collect_payments_from_abroad: values.collect_payments_from_abroad === 'yes',
        current_payment_method: values.current_payment_method,
        plans_to_get_customers_abroad: values.plans_to_get_customers_abroad,
        reason: values.reason,
        requested_currencies: values.requested_currencies,
      });

      if (err) {
        showError(err?.message);
      }

      if (res) {
        setStep('success');
      }
    },
  });

  const handleMultiselectChange = (value: any, field: string) => {
    let values = form.values[field] ?? [];
    if (values) {
      const index = values.indexOf(value);

      if (index > -1) {
        values.splice(index, 1);
      } else {
        values = [...values, value];
      }
      form.setFieldValue(field, values);
    }
  };

  const collect_payments_from_abroad = form.values.collect_payments_from_abroad;

  const close = () => {
    form.resetForm();
    setStep('form');
    toggle();
  };

  return (
    <BottomModal
      size='lg'
      title={'Request Access'}
      buttons={
        step === 'upgrade'
          ? []
          : [{ text: 'Continue', onPress: () => form.submitForm(), isLoading: paymentRequest.isLoading }]
      }
      isVisible={show}
      ref={bottomModalRef}
      customSnapPoints={[50, 90]}
      // enableDynamicSizing
      // enableSnapPoints={false}
      showFooterOnKeyboard
      closeModal={close}>
      <View className="pb-40 px-20 pt-20">
        {step === 'upgrade' && (
          <>
            <EmptyState
              classes="py-20"
              showBtn
              title="Unlock International Payments!"
              icon={<Warning2 variant={'Bold'} color={colors.accentRed.main} size={wp(40)} />}
              btnText="Upgrade to Business+ plan"
              text={`You'll need to be on the Business+ plan to collect payments internationally. Upgrade to collect payments in multiple currencies and expand your reach!`}
              onPressBtn={() => {}}
            />
          </>
        )}
        {step === 'form' && (
          <>
            <SelectDropdown
              items={[...(currencyOptions ?? [])]}
              label={'Select currencies'}
              isMultiSelect
              onPressItem={v => handleMultiselectChange(v, 'requested_currencies')}
              displayText={''}
              closeAfterSelection={false}
              selectedItems={form.values.requested_currencies ?? []}
            />
            <Pressable onPress={() => inputRef.current.focus()}>
              <Input
                useBottomSheetInput
                ref={inputRef}
                label={'Why do you need these currencies?'}
                multiline
                onSubmitEditing={() => Keyboard.dismiss()}
                className="min-h-[80px]"
                containerClasses="mt-15"
                {...getFieldvalues('reason', form)}
              />
            </Pressable>
            <SelectDropdown
              items={paymentOptions}
              label={'Do you currently get paid internationally?'}
              onPressItem={v => form.setFieldValue('collect_payments_from_abroad', v)}
              displayText={''}
              closeAfterSelection
              selectedItem={form.values.collect_payments_from_abroad ?? ''}
              containerClasses="mt-15"
            />
            {collect_payments_from_abroad && collect_payments_from_abroad !== '' && (
              <Input
                useBottomSheetInput
                label={
                  collect_payments_from_abroad == 'no'
                    ? 'How do you intend to get customers abroad?'
                    : 'How do you currently collect those payments?'
                }
                containerClasses="mt-15"
                {...getFieldvalues('current_payment_method', form)}
              />
            )}
          </>
        )}
        {step === 'success' && (
          <>
            <View className={'items-center justify-center pt-10 pb-30'}>
              <CircledIcon className="bg-accentGreen-pastel p-15">
                <CircledIcon className="bg-accentGreen-main p-25">
                  <TickCircle variant={'Bold'} size={wp(40)} color={colors.white} />
                </CircledIcon>
              </CircledIcon>
              <BaseText fontSize={18} type={'heading'} classes="text-center mt-10 max-w-[325px]">
                Your Request has{'\n'} Successfully Sent
              </BaseText>
              <BaseText fontSize={12} type={'body'} classes="text-center text-black-muted mt-10 max-w-[325px]">
                Your request to enable international payments on your account has been received and is under review.
              </BaseText>
            </View>
          </>
        )}
      </View>
    </BottomModal>
  );
};
export default EnabledCurrenciesModal;

const generateValidationSchema = () => {
  return Yup.object().shape({
    requested_currencies: Yup.array()
      .of(
        Yup.string().oneOf([
          CURRENCIES.USD,
          CURRENCIES.GBP,
          CURRENCIES.NGN,
          CURRENCIES.GHC,
          CURRENCIES.ZAR,
          CURRENCIES.KES,
          CURRENCIES.CAD,
        ]),
      )
      .min(1, 'Please select at least one currency'),
    reason: Yup.string().required('Reason is required'),
    collect_payments_from_abroad: Yup.string().required('Please specify if you are currently receiving payments'),
  });
};

const paymentOptions = [
  { value: 'yes', label: 'Yes' },
  { value: 'no', label: 'No' },
];
