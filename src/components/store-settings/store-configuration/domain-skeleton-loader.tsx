import React from 'react';
import { View } from 'react-native';
import { hp, wp } from '@/assets/utils/js';
import { Row } from '@/components/ui';
import Shimmer from '@/components/ui/shimmer';

interface DomainSkeletonLoaderProps {
  count?: number;
}

const DomainCardSkeleton = () => {
  return (
    <View className="p-15 mb-15 bg-white rounded-12 border border-grey-border">
      <Row className="flex-1">
        <View className="flex-1 mx-12">
          <Shimmer borderRadius={wp(10)} height={hp(14)} width={wp(150)} />
          <Row spread={false} style={{ gap: wp(5) }} className="mt-5">
            <Shimmer borderRadius={wp(10)} height={hp(10)} width={wp(60)} />
            <Shimmer borderRadius={wp(4)} height={hp(4)} width={wp(4)} />
            <Shimmer borderRadius={wp(10)} height={hp(10)} width={wp(80)} />
          </Row>
        </View>
        <View>
          <Shimmer borderRadius={wp(20)} height={hp(20)} width={wp(80)} />
        </View>
      </Row>
      <Shimmer 
        borderRadius={wp(20)} 
        height={hp(30)} 
        width={wp(120)} 
        marginTop={hp(10)}
        className="self-start"
      />
    </View>
  );
};

const DomainSkeletonLoader: React.FC<DomainSkeletonLoaderProps> = ({ count = 3 }) => {
  return (
    <View>
      {Array.from({ length: count }, (_, i) => (
        <DomainCardSkeleton key={i} />
      ))}
    </View>
  );
};

export default DomainSkeletonLoader;
