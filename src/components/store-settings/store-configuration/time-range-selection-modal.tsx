import { View } from 'react-native';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { ArrowRight, Clock } from 'iconsax-react-native/src';
import { Row } from '@/components/ui';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import { useEffect, useState } from 'react';
import { generateHoursInterval } from 'catlog-shared';

interface TimeRangeSelectionProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton: (selectedTime: [string, string]) => void;
  value: [string, string];
}

const TimeRangeSelection = ({ closeModal, value, onPressButton, ...props }: TimeRangeSelectionProps) => {
  const [selected, setSelected] = useState<[string, string]>(value ?? ['09:00AM', 'O7:00PM']);

  const options = [...generateHoursInterval(0, 60 * 24, 30), '11:59PM'].map(value => ({ value, label: value }));

  useEffect(() => {
    setSelected(value);
  }, []);

  const selectItem = (value: string, selecting: 'START' | 'END') => {
    const valueIndex = selecting === 'START' ? 0 : 1;
    const selectedCopy: [string, string] = [...selected];

    selectedCopy[valueIndex] = value;
    setSelected(selectedCopy);
  };

  const LeftAccessory = () => (
    <View className="p-3 my-12 bg-grey-bgOne rounded-full">
      <Clock size={wp(16)} color={colors.grey.muted} />
    </View>
  );

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Select time'}
      buttons={[{ text: 'Done', onPress: () => onPressButton(selected) }]}>
      <View className="px-20 mb-20">
        <Row className="mt-20" style={{ gap: wp(10) }}>
          <View className="flex-1">
            <SelectDropdown
              items={options}
              onPressItem={value => selectItem(value, 'START')}
              leftAccessory={<LeftAccessory />}
              rightAccessory={undefined}
              // selectedItem={['Monday']}
              scrollToSelected
              label={'Opening time'}
              customIcon={<Clock size={wp(16)} color={colors.grey.muted} />}
              selectedItem={selected[0]}
            />
          </View>
          <ArrowRight size={wp(18)} color={colors.black.muted} />
          <View className="flex-1">
            <SelectDropdown
              items={options}
              onPressItem={value => selectItem(value, 'END')}
              rightAccessory={undefined}
              label={'Close time'}
              scrollToSelected
              customIcon={<Clock size={wp(16)} color={colors.grey.muted} />}
              leftAccessory={<LeftAccessory />}
              selectedItem={selected[1]}
            />
          </View>
        </Row>
      </View>
    </BottomModal>
  );
};

export default TimeRangeSelection;
