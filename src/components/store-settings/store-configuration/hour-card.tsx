import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import CustomizationCard from '@/components/ui/cards/customization-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import Pressable from '@/components/ui/base/pressable';
import { Add, Calendar, Clock, Edit2, Trash } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';
import { Fragment, useEffect, useMemo, useState } from 'react';
import TimeRangeSelection from './time-range-selection-modal';
import useModals from 'src/hooks/use-modals';
import { Section } from './opening-closing-hours';
import useKeyboard from 'src/hooks/use-keyboard';
import { capitalizeFirstLetter, getRandString } from 'catlog-shared';

interface HourCardProps {
  sections: { [name: string]: Section };
  sectionData: Section;
  sectionKey: string;
  setSections: React.Dispatch<
    React.SetStateAction<{
      [name: string]: Section;
    }>
  >;
  calculateHoursFromSections: VoidFunction;
}

const defaultHours = '08:00AM-08:00PM';
const HourCard = ({ sectionData, setSections, calculateHoursFromSections, sectionKey, sections }: HourCardProps) => {
  const [displayMode, setDisplayMode] = useState<'form' | 'card'>('form');
  const { modals, toggleModal } = useModals(['timeModal']);

  const isKeyboardActive = useKeyboard();

  const getDisplayModeText = (sectionData: Section) => {
    if (sectionData?.days.length < 1) return 'Select Day(s)';
    const firstWeekDay = capitalizeFirstLetter(sectionData?.days?.[0] ?? '');
    const otherDaysCount = sectionData?.days?.length - 1;
    const s = otherDaysCount <= 1 ? '' : 's';
    const allOtherDays = otherDaysCount === 0 ? 'only' : `& ${otherDaysCount} other day${s}`;
    return `${firstWeekDay} ${allOtherDays}`;
  };

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const getSectionDays = (sectionKey: string) => {
    const sectionsArray = Object.keys(sections);
    let selectedDays = sectionsArray.reduce((cumm, key) => {
      if (sectionKey !== key) {
        return [...cumm, ...sections[key].days];
      }
      return cumm;
    }, []);

    return days.filter(({ value }) => !selectedDays.includes(value));
    // Other multiselect dropdowns will not have a day selected in another
  };

  const handleSelectDays = (day: string) => {
    if (sectionData === null) return;
    const sectionDataCopy = { ...sectionData };
    if (sectionDataCopy?.days?.includes(day)) {
      const daysCopy = sectionDataCopy?.days;
      const index = daysCopy.findIndex(item => item === day);
      daysCopy.splice(index, 1);
      sectionDataCopy['days'] === daysCopy;
      setSectionDays(sectionDataCopy.days, sectionDataCopy.hours);
      return;
    }
    sectionDataCopy.days = [...sectionDataCopy.days, day];
    setSectionDays(sectionDataCopy.days, sectionDataCopy.hours);
  };

  const handleSectionDelete = () => {
    const sectionsCopy = { ...sections };
    delete sectionsCopy[sectionKey];
    setSections?.(sectionsCopy);
  };

  const setSectionDays = (days: string[], hours: [string, string]) => {
    const sectionsCopy = { ...sections };
    sectionsCopy[sectionKey] = { days, hours };
    setSections?.(sectionsCopy);
  };

  const toggleDisplayModeToCard = () => {
    if (sectionData.days.length > 0 && sectionData.hours.length === 2) {
      setDisplayMode('card');
      calculateHoursFromSections();
    }
  };

  return (
    <View>
      <Fragment>
        {displayMode === 'form' && (
          <Row className="gap-x-10 mt-10">
            <View className="flex-1">
              <SelectDropdown
                items={getSectionDays(sectionKey)}
                // selectedItem={['Monday']}
                label={'Select days'}
                customIcon={<Calendar size={wp(16)} color={colors.grey.muted} />}
                isMultiSelect
                onPressItem={value => handleSelectDays(value)}
                displayText={getDisplayModeText(sectionData) ?? ''}
                closeAfterSelection={false}
                selectedItems={sectionData.days}
              />
            </View>
            <View className="flex-1">
              <Pressable onPress={() => toggleModal('timeModal', true)}>
                <Input
                  editable={false}
                  value={getStringFromHours(sectionData.hours)}
                  onPressIn={() => toggleModal('timeModal', true)}
                  label={'Select time'}
                  rightAccessory={
                    <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                      <Clock size={wp(16)} color={colors.grey.muted} />
                    </View>
                  }
                  containerClasses={`py-0`}
                />
              </Pressable>
            </View>
          </Row>
        )}
        {displayMode === 'card' && (
          <CustomizationCard
            textTop={`${sectionData?.hours?.[0]} - ${sectionData?.hours?.[1]}`}
            className="mt-10"
            textBottom={getDisplayModeText(sectionData) ?? ''}
            textTopProps={{ weight: 'medium' }}
            textBottomProps={{ weight: 'regular', classes: 'text-black-placeholder' }}
            rightElement={
              <Fragment>
                <Pressable onPress={() => setDisplayMode('form')}>
                  <CircledIcon className="bg-white mr-10">
                    <Edit2 size={wp(18)} color={colors.black.placeholder} />
                  </CircledIcon>
                </Pressable>
                <Pressable onPress={handleSectionDelete}>
                  <CircledIcon className="bg-white">
                    <Trash size={wp(18)} color={colors.black.placeholder} />
                  </CircledIcon>
                </Pressable>
              </Fragment>
            }
          />
        )}
      </Fragment>
      <TimeRangeSelection
        isVisible={modals.timeModal}
        closeModal={() => {
          toggleModal('timeModal', false);
          toggleDisplayModeToCard();
        }}
        onPressButton={selectedTime => {
          setSectionDays(sectionData.days, selectedTime);
          toggleModal('timeModal', false);
          toggleDisplayModeToCard();
        }}
        value={sectionData?.hours ?? ['', '']}
      />
    </View>
  );
};

const getStringFromHours = (hours: [string, string]) => hours.join(' - ');
const days = [
  {
    value: 'sunday',
    label: 'Sunday',
  },
  {
    value: 'monday',
    label: 'Monday',
  },
  {
    value: 'tuesday',
    label: 'Tuesday',
  },
  {
    value: 'wednesday',
    label: 'Wednesday',
  },
  {
    value: 'thursday',
    label: 'Thursday',
  },
  {
    value: 'friday',
    label: 'Friday',
  },
  {
    value: 'saturday',
    label: 'Saturday',
  },
];

export default HourCard;
