import { BaseText, CircledIcon, Container } from '@/components/ui';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { FormikProps } from 'formik';
import { CodeCircle, InfoCircle, Instagram, Message } from 'iconsax-react-native/src';
import { ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { alertPromise, getFieldvalues, wp } from 'src/assets/utils/js';
import Button, { ButtonSize, ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import { useApi } from 'src/hooks/use-api';
import { CustomUpdateStoreInformationParams } from 'src/screens/store-settings/store-information';
import colors from 'src/theme/colors';
import { CHECK_INSTAGRAM_TOKEN, DISCONNECT_INSTAGRAM } from 'catlog-shared';
import React from 'react';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';

interface LocationDetailsProps extends SharedStoreConfigProps {
}

const ThirdPartyIntegrations = ({ form, isLoading }: LocationDetailsProps) => {
  const disconnectInstagramReq = useApi({
    apiFunction: DISCONNECT_INSTAGRAM,
    key: DISCONNECT_INSTAGRAM.name,
    method: 'GET',
    autoRequest: false,
  });

  const checkInstagramTokenReq = useApi({
    apiFunction: CHECK_INSTAGRAM_TOKEN,
    key: CHECK_INSTAGRAM_TOKEN.name,
    method: 'GET',
    autoRequest: true,
  });

  const handleDisconnectInstagram = async () => {
    const response = await alertPromise(
      'Disconnect Instagram',
      `Clicking "Yes, Disconnect" will disconnect Instagram, and you won't be able to import products from Instagram in the future. To reconnect, you'll need to go to the add products page.`,
      'Yes, Disconnect',
      'Cancel',
      true,
    );
    if (response === false) {
      return;
    }
    const [res, err] = await disconnectInstagramReq.makeRequest({ storeId: form.values.id });
    if (res) {
      Toast.show({ type: 'success', text1: 'Instagram disconnected successfully' });
      checkInstagramTokenReq.refetch();
    }
    if (err) {
      Toast.show({ type: 'error', text1: err?.message });
    }
  };

  const showDisconnectInstagramButton = checkInstagramTokenReq.response && !checkInstagramTokenReq.error;

  const settings = [
    {
      leftIcon: (
        <CircledIcon iconBg="bg-accentOrange-pastel p-10">
          <CodeCircle variant={'Bold'} size={wp(16)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      rightIcon: (
        <CustomSwitch
          value={form.values.configuration?.facebook_pixel_enabled}
          onValueChange={value => form.setFieldValue('configuration.facebook_pixel_enabled', value)}
        />
      ),
      title: 'Facebook Pixel',
      description: 'Track conversions and retarget customers',
    },
    ...(showDisconnectInstagramButton
      ? [
          {
            leftIcon: (
              <CircledIcon iconBg="bg-accentRed-pastel p-10">
                <Instagram variant={'Bold'} size={wp(16)} color={colors.accentRed.main} />
              </CircledIcon>
            ),
            rightIcon: (
              <Button
                text="Disconnect"
                variant={ButtonVariant.LIGHT}
                size={ButtonSize.MEDIUM}
                textColor={TextColor.NEGATIVE}
                onPress={handleDisconnectInstagram}
              />
            ),
            title: 'Instagram Import',
            description: 'Import product from instagram post',
          },
        ]
      : []),
  ];

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          {settings?.map((item, idx) => (
            <View key={idx}>
              <ListItemCard
                key={item?.title}
                leftElement={item?.leftIcon}
                rightElement={item?.rightIcon}
                className="py-24"
                titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
                title={item?.title}
                description={item?.description}
                descriptionProps={{ fontSize: 12, weight: 'regular' }}
                disabled={true}
                showBorder={true}
              />
              {idx === 0 && form.values.configuration?.facebook_pixel_enabled && (
                <View className="py-20  border-b border-grey-border">
                  <Input label="Facebook Pixel Code or ID" {...getFieldvalues('configuration.fb_pixel', form)} />
                  <View className="flex-row flex-1 w-full mt-15 p-15 rounded-[10px] bg-grey-bgOne">
                    <CircledIcon className="bg-white mr-10 p-[5px] w-[40px]" style={{ aspectRatio: 1 / 1 }}>
                      <InfoCircle variant="Bold" color={colors.accentOrange.main} />
                    </CircledIcon>
                    <View className="flex-1">
                      <BaseText fontSize={13} classes="text-black-secondary" weight="medium">
                        Catlog will track events automatically
                      </BaseText>
                      <BaseText fontSize={12} classes="text-black-placeholder mt-5">
                        After adding your facebook pixel ID, you don't need to use the facebook events setup tool,
                        Catlog will automatically track cart additions, store visit, purchases and other events.
                      </BaseText>
                    </View>
                  </View>
                </View>
              )}
            </View>
          ))}
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default ThirdPartyIntegrations;
