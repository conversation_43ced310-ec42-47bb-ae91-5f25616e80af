import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { SharedStoreConfigProps } from 'src/screens/store-settings/store-configurations';
import { useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { TOGGLE_LOW_STOCK_NOTIFICATIONS } from 'catlog-shared';
import Input from 'src/components/ui/inputs/input';
import { getFieldvalues, showError, showSuccess } from 'src/assets/utils/js';

interface ProductConfigurationsProps extends SharedStoreConfigProps {}

const ProductConfigurations = ({ form, isLoading }: ProductConfigurationsProps) => {
  const toggleStockNotificationsReq = useApi({
    apiFunction: TOGGLE_LOW_STOCK_NOTIFICATIONS,
    key: TOGGLE_LOW_STOCK_NOTIFICATIONS.name,
    method: 'PUT',
  });

  const [stockConfig] = useState({
    notifications_enabled: form.values.configuration.low_stock_notifications_enabled,
    threshold: form.values.configuration.global_stock_threshold,
  });

  const handleSubmit = async () => {
    const { low_stock_notifications_enabled, global_stock_threshold } = form.values.configuration;
    if (
      stockConfig.notifications_enabled !== low_stock_notifications_enabled ||
      stockConfig.threshold !== global_stock_threshold
    ) {
      const [res, err] = await toggleStockNotificationsReq.makeRequest({
        enabled: Boolean(low_stock_notifications_enabled),
        threshold: Number(global_stock_threshold),
      });

      if (err) {
        showError(err);
      } else if (res) {
        showSuccess('Low stock notifications updated successfully!');
      }
    }
    // return
    // detect change in low stock stuff
    await form.submitForm();
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'How you want your products to appear on your store'} />
          <View className="mt-20 pb-30">
            <Row>
              <BaseText weight={'medium'} fontSize={12} classes="text-black-secondary">
                Show latest products first
              </BaseText>
              <CustomSwitch
                value={form.values.configuration.sort_by_latest_products}
                onValueChange={value => form.setFieldValue('configuration.sort_by_latest_products', value)}
              />
            </Row>
            <Row className="mt-24">
              <BaseText weight={'medium'} fontSize={12} classes="text-black-secondary">
                Show unavailable products
              </BaseText>
              <CustomSwitch
                value={form.values.configuration.show_unavailable_products}
                onValueChange={value => form.setFieldValue('configuration.show_unavailable_products', value)}
              />
            </Row>
            <Row className="mt-24">
              <BaseText weight={'medium'} fontSize={12} classes="text-black-secondary">
                Low Stock Notifications
              </BaseText>
              <CustomSwitch
                value={form.values.configuration.low_stock_notifications_enabled}
                onValueChange={value => form.setFieldValue('configuration.low_stock_notifications_enabled', value)}
              />
            </Row>
            {form.values.configuration.low_stock_notifications_enabled && (
              <Input
                label={'Default Low Stock Threshold'}
                containerClasses="mt-15"
                type="number"
                {...getFieldvalues('configuration.global_stock_threshold', form, 'number')}
                value={form.values.configuration.global_stock_threshold ?? 10}
              />
            )}
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            disabled: toggleStockNotificationsReq.isLoading || isLoading,
            text: toggleStockNotificationsReq.isLoading || isLoading ? 'Updating...' : 'Update Changes',
            onPress: handleSubmit,
            isLoading,
          },
        ]}
      />
    </View>
  );
};

export default ProductConfigurations;
