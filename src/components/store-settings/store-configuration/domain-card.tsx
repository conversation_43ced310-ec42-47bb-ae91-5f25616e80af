import React from 'react';
import { View } from 'react-native';
import { <PERSON><PERSON>quare, Trash } from 'iconsax-react-native/src';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import colors from 'src/theme/colors';
import { alertPromise, cx, formatDate, hideLoader, showError, showLoader, showSuccess, wp } from 'src/assets/utils/js';
import { DomainData, GENERATE_SSL_CERTIFICATE, REMOVE_DOMAIN, VERIFY_DOMAIN } from 'catlog-shared';
import Pressable from 'src/components/ui/base/pressable';
import But<PERSON>, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { useApi } from 'src/hooks/use-api';

interface DomainCardProps {
  domainInfo: DomainData;
  setDomains: React.Dispatch<React.SetStateAction<DomainData[]>>;
}

const DomainCard = ({ domainInfo, setDomains }: DomainCardProps) => {
  const isVerified = domainInfo.verified;
  const hasSSL = domainInfo.certificate_issued;

  const removeDomainReq = useApi({
    apiFunction: REMOVE_DOMAIN,
    key: REMOVE_DOMAIN.name,
    method: 'DELETE',
  });

  const generateSslReq = useApi({
    apiFunction: GENERATE_SSL_CERTIFICATE,
    key: GENERATE_SSL_CERTIFICATE.name,
    method: 'POST',
  });

  const verifyDomainReq = useApi({
    apiFunction: VERIFY_DOMAIN,
    key: VERIFY_DOMAIN.name,
    method: 'POST',
  });

  const handleRemoveDomain = async () => {
    const proceed = await alertPromise(
      'Remove Domain',
      `Are you sure you want to remove ${domainInfo?.domain} This action cannot be undone. Any SSL certificates associated with this domain will be revoked.`,
      'Yes, Remove',
      'Cancel',
      true,
    );

    if (!proceed) return;

    showLoader('Removing Domain...');
    const [response, error] = await removeDomainReq.makeRequest({ id: domainInfo.id });
    hideLoader();
    if (error) {
      showError(error, 'Failed to remove domain');
    }

    showSuccess('Domain removed successfully');
    setDomains(prevDomains => prevDomains.filter(domain => domain.id !== domainInfo.id));
  };

  const handleGenerateSSL = async () => {
    showLoader('Generating SSL Certificate...');
    const [response, error] = await generateSslReq.makeRequest({ id: domainInfo.id });
    hideLoader();

    if (error) {
      showError(error, 'Failed to generate SSL certificate');
    } else {
      showSuccess('SSL certificate generation initiated');

      if (response.data) {
        setDomains(prevDomains =>
          prevDomains.map(domain => (domain.id === domainInfo.id ? { ...domain, ...response.data } : domain)),
        );
      }
    }
  };

  const handleVerifyDomain = async () => {
    showLoader('Verifying Domain...');
    const [response, error] = await verifyDomainReq.makeRequest({ id: domainInfo.id });
    hideLoader();

    if (error) {
      showError(error, 'Failed to verify domain');
    }

    if (response.data?.verified) {
      showSuccess('Domain verified successfully');
    } else {
      showError("Domain verification failed, please ensure you've added the A record to your DNS settings");
    }

    if (response.data) {
      setDomains(prevDomains =>
        prevDomains.map(domain =>
          domain.id === domainInfo.id ? { ...domain, verified: response.data.verified } : domain,
        ),
      );
    }
  };

  const isLoading = removeDomainReq.isLoading || generateSslReq.isLoading || verifyDomainReq.isLoading;

  return (
    <View className={cx('py-14 px-12 bg-white rounded-12 border border-grey-border', { 'opacity-50': isLoading })}>
      <Row className="flex-1">
        <View>
          <Row spread={false} style={{ gap: wp(10) }}>
            <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
              {domainInfo.domain}
            </BaseText>
            <StatusPill
              statusType={isVerified ? StatusType.SUCCESS : StatusType.WARN}
              className="bg-transparentWhite p-0"
              title={isVerified ? 'Verified' : 'Pending Verification'}
            />
            <Row></Row>
          </Row>
          <Row spread={false} style={{ gap: wp(5) }} className="mt-5">
            {hasSSL && (
              <>
                <BaseText fontSize={12} classes={` text-black-placeholder`}>
                  SSL Active
                </BaseText>
                <View className="w-4 h-4 rounded-full bg-black-placeholder" />{' '}
              </>
            )}
            <BaseText fontSize={12} classes="text-black-placeholder">
              {formatDate(domainInfo.created_at, 'D MMM YYYY')}
            </BaseText>
          </Row>
        </View>
        <Pressable onPress={handleRemoveDomain}>
          <CircledIcon className="bg-grey-bgTwo">
            <Trash size={wp(18)} color={colors.accentRed.main} />
          </CircledIcon>
        </Pressable>
      </Row>
      {!isVerified && (
        <Button
          text="Verify Domain"
          size={ButtonSize.SMALL}
          variant={ButtonVariant.LIGHT}
          className="mt-10 self-start"
          onPress={handleVerifyDomain}
          isLoading={verifyDomainReq.isLoading}
        />
      )}
      {isVerified && !hasSSL && (
        <Button
          text="Generate SSL Certificate"
          size={ButtonSize.SMALL}
          variant={ButtonVariant.LIGHT}
          className="mt-10 self-start"
          onPress={handleGenerateSSL}
          isLoading={generateSslReq.isLoading}
        />
      )}
    </View>
  );
};

export default DomainCard;
