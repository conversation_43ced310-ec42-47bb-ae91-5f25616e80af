import { useEffect } from 'react';
import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import { getFieldvalues, showError, showSuccess, wp, Yup } from '@/assets/utils/js';
import colors from '@/theme/colors';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import Button, { ButtonSize, ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import RateInput from '@/components/ui/inputs/rate-input';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import {
  CURRENCIES,
  CURRENCY_OPTIONS,
  GET_ALL_CONVERSION_RATES,
  GET_LATEST_WALLET_REQUEST,
  paymentsEnabledCurrencies,
  StoreCurrencySettings,
  UPDATE_STORE_CURRENCIES,
  UpdateCurrenciesParams,
} from 'catlog-shared';
import SectionContainer from 'src/components/ui/section-container';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import { useFormik } from 'formik';
import Separator from 'src/components/ui/others/separator';
import EnabledCurrenciesModal from './enable-currencies-modal';
import useModals from 'src/hooks/use-modals';
import dayjs from 'dayjs';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import { Warning2 } from 'node_modules/iconsax-react-native/src';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';

interface CurrencyRates {
  [key: string]: {
    [key: string]: number;
  };
}

interface CurrenciesProps {}

const Currencies = ({}: CurrenciesProps) => {
  const { store, updateStore, subscription } = useAuthContext();
  const { modals, toggleModal } = useModals(['enable_currencies']);
  const currencyCache = { ...store.currencies };

  const getLatestWalletRequest = useApi({
    apiFunction: GET_LATEST_WALLET_REQUEST,
    key: GET_LATEST_WALLET_REQUEST.name,
    method: 'GET',
  });

  const conversionRateRequest = useApi({
    apiFunction: GET_ALL_CONVERSION_RATES,
    key: 'get-conversion-rates',
    method: 'GET',
  });

  const updateCurrencyRequest = useApi<UpdateCurrenciesParams, any>({
    apiFunction: UPDATE_STORE_CURRENCIES,
    key: 'UPDATE_STORE_CURRENCIES',
    method: 'PUT',
  });
  const conversionRates: CurrencyRates = conversionRateRequest.response?.data?.rates ?? {};

  const storeEnabledCurrencies = store.wallets?.map(w => w.currency) ?? [];
  const notEnabledCurrencies = CURRENCY_OPTIONS.filter(c => !storeEnabledCurrencies.includes(c.value)).map(
    c => c.value,
  );

  const storefrontOptions = CURRENCY_OPTIONS.filter(c => storeEnabledCurrencies.includes(c.value));

  const form = useFormik<StoreCurrencySettings>({
    initialValues: {
      ...store.currencies,
    },
    validationSchema: validationSchema,
    onSubmit: async values => {
      const [res, err] = await updateCurrencyRequest.makeRequest({ id: store.id, data: { ...values } });

      if (res) {
        updateStore({ currencies: res?.data?.currencies });
        showSuccess('Currency details updated successfully');
      }

      if (err) {
        showError(err);
      }
    },
  });

  const { products, storefront, storefront_default, rates, default: defaultCurr } = form.values;
  const showRates = storefront.length > 1 || storefront[0] !== defaultCurr;

  useEffect(() => {
    if (storefront_default && !storefront.includes(storefront_default)) {
      form.setFieldValue('storefront_default', storefront[0]);
    }
  }, [form.values]);

  const handleStorefrontCurrenciesChange = (value: CURRENCIES) => {
    let newStorefront: CURRENCIES[] = [];
    const existingV = [...form.values.storefront];
    const ValueAlreadyExist = existingV.findIndex(v => v === value);

    if (ValueAlreadyExist === -1) {
      newStorefront = [...form.values.storefront, value];
    } else {
      existingV.splice(ValueAlreadyExist, 1);
      newStorefront = existingV;
    }

    form.setFieldValue('storefront', newStorefront);

    // Preserve existing rates and add new currencies with null rates
    const updatedRates: any = { ...form.values.rates };
    newStorefront.forEach(currency => {
      if (currency !== products && !(currency in updatedRates)) {
        updatedRates[currency] = null;
      }
    });

    // Remove rates for currencies that are no longer in storefront
    Object.keys(updatedRates).forEach(currency => {
      if (!newStorefront.includes(currency as CURRENCIES)) {
        delete updatedRates[currency];
      }
    });

    form.setFieldValue('rates', updatedRates);
  };

  const handleProductsCurrencyChange = (value: CURRENCIES) => {
    const newProductCurrency = value;
    form.setFieldValue('products', newProductCurrency);

    // Update rates to include only storefront currencies excluding the new product currency
    const updatedRates: any = {};
    storefront.forEach(currency => {
      if (currency !== newProductCurrency) {
        updatedRates[currency] = form.values.rates[currency] || null;
      }
    });

    form.setFieldValue('rates', updatedRates);
  };

  // Helper function to get conversion rate from 'from' currency to 'to' currency
  const getConversionRate = (from: string, to: string): number | null => {
    if (!conversionRates) return null;

    if (from === to) return 1;

    // Direct rate available
    if (conversionRates[from] && conversionRates[from][to]) {
      return conversionRates[from][to];
    }

    // Indirect conversion via USD
    if (from !== 'USD' && to !== 'USD') {
      if (
        conversionRates[from] &&
        conversionRates[from]['USD'] &&
        conversionRates['USD'] &&
        conversionRates['USD'][to]
      ) {
        return conversionRates[from]['USD'] * conversionRates['USD'][to];
      }
    }

    return null; // Rate not available
  };

  const placeholderProductCosts = {
    [CURRENCIES.EUR]: 10,
    [CURRENCIES.USD]: 10,
    [CURRENCIES.GBP]: 10,
    [CURRENCIES.NGN]: 10000,
    [CURRENCIES.GHC]: 100,
    [CURRENCIES.ZAR]: 200,
    [CURRENCIES.KES]: 500,
  };

  const hasPendingRequest = getLatestWalletRequest?.response?.data?.status === 'PENDING';
  const hasRejectedRequest = getLatestWalletRequest?.response?.data?.status === 'REJECTED';

  const canRequestInternationalPayments = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.PLAN_PERMISSIONS.CAN_REQUEST_INTERNATIONAL_PAYMENTS,
  });

  if (!canRequestInternationalPayments || !store.kyc_approved)
    return (
      <EmptyState
        classes="py-20 px-20"
        showBtn
        title="Unlock International Payments!"
        icon={<Warning2 variant={'Bold'} color={colors.accentRed.main} size={wp(40)} />}
        btnText="Upgrade to Business+ plan"
        text={`You'll need to be on the Business+ plan to collect payments internationally. Upgrade to collect payments in multiple currencies and expand your reach!`}
        onPressBtn={() => {}}
      />
    );

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Manage how you price your products & what currencies customers see on your store'} />
          <SectionContainer classes="py-15">
            <BaseText type="heading">Enabled Currencies</BaseText>
            <Row spread={false} className="mt-10 flex-wrap" style={{ gap: wp(10) }}>
              {storeEnabledCurrencies.map(i => (
                <StatusPill key={i} whiteBg title={i} />
              ))}
            </Row>
            {notEnabledCurrencies?.length > 0 && (
              <>
                <Separator />
                <BaseText type="heading">Other Available Currencies</BaseText>
                <Row spread={false} className="mt-10 flex-wrap" style={{ gap: wp(10) }}>
                  {notEnabledCurrencies.map(i => (
                    <StatusPill statusType={StatusType.DEFAULT} key={i} whiteBg title={i} />
                  ))}
                </Row>
                {getLatestWalletRequest?.response && !hasRejectedRequest && (
                  <Button
                    onPress={() => toggleModal('enable_currencies')}
                    className="mt-10"
                    textColor={TextColor.PRIMARY}
                    size={ButtonSize.SMALL}
                    variant={ButtonVariant.WHITE}
                    text={hasPendingRequest ? 'Request Pending' : 'Enable Currencies'}
                    disabled={hasPendingRequest}
                  />
                )}
              </>
            )}
          </SectionContainer>
          <SelectDropdown
            items={storefrontOptions}
            onPressItem={v => handleProductsCurrencyChange(v as CURRENCIES)}
            selectedItem={form.values.products}
            isMultiSelect
            label={'Product Currency on Dashboard'}
            containerClasses="mt-15"
          />
          <View className="mt-15">
            <BaseText fontSize={14} weight={'bold'} type={'heading'}>
              Storefront
            </BaseText>
            <SelectDropdown
              items={storefrontOptions}
              selectedItem={''}
              isMultiSelect
              label={'Currencies for storefront'}
              onPressItem={v => handleStorefrontCurrenciesChange(v as CURRENCIES)}
              selectedItems={form.values.storefront}
              closeAfterSelection={false}
              containerClasses="mt-15"
            />
            <SelectDropdown
              items={storefrontOptions}
              // {...getFieldvalues("storefront_default", form)}
              onPressItem={v => form.setFieldValue('storefront_default', v)}
              selectedItem={form.values.storefront_default}
              label={'Default storefront currency'}
              containerClasses="mt-15"
            />
          </View>
          {showRates && (
            <View className="mt-20">
              <BaseText fontSize={14} weight={'bold'} type={'heading'}>
                Markup Product Prices
              </BaseText>
              <InfoBadge
                hideIcon
                className="mt-15"
                text={`To help you avoid losses from exchange rates, set markup percentages for your product prices. The markup will be applied to the final price based on the customer's currency.`}
              />

              {storefront
                .filter(curr => curr !== products)
                .map((curr, i) => {
                  const rate = rates[curr];
                  const markup = rate || 0;
                  const productCost = placeholderProductCosts[products];

                  // Calculate markup price
                  const markupPrice = productCost + (markup / 100) * productCost;

                  // Get conversion rates
                  const currentRate = getConversionRate(products, curr);
                  const newRate = getConversionRate(products, curr);

                  // Calculate current value
                  const currentValue = currentRate ? productCost * currentRate : null;

                  // Calculate new value
                  const newValue = newRate ? markupPrice * newRate : null;

                  return (
                    <View key={i} className="mt-20">
                      <RateInput
                        from={products}
                        to={`${curr} (%)`}
                        key={curr}
                        placeholder="Add markup percentage e.g 10%"
                        {...getFieldvalues(`rates.${curr}`, form, 'number')}
                        value={String(form.values.rates?.[curr])}
                        onChangeText={t => form.setFieldValue(`rates.${curr}`, Number(t))}
                      />
                      {rate > 0 && (
                        <View className="border border-grey-border rounded-12 mt-8">
                          {/* Product Costs */}
                          <Row className="flex justify-between items-center border-b border-grey-border py-12 px-14">
                            <View>
                              <BaseText classes="text-black-muted">Product Cost ({products})</BaseText>
                              <BaseText classes="mt-4">
                                {products}{' '}
                                {productCost.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </BaseText>
                            </View>
                            <View className="text-right">
                              <BaseText classes="text-right">Conversion ({curr})</BaseText>
                              <BaseText classes="mt-4 text-right">
                                {curr}{' '}
                                {currentValue
                                  ? currentValue.toLocaleString(undefined, {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })
                                  : 'N/A'}
                              </BaseText>
                            </View>
                          </Row>

                          {/* Markup Price */}
                          <Row className="flex justify-between items-center py-12 px-14">
                            <View>
                              <BaseText classes="">Markup price ({products})</BaseText>
                              <BaseText classes="text-black-secondary mt-4">
                                {products}{' '}
                                {markupPrice.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                              </BaseText>
                            </View>
                            <View>
                              <BaseText classes="text-right">Final Value ({curr})</BaseText>
                              <BaseText classes="text-accentGreen-main text-right mt-4">
                                {curr}{' '}
                                {newValue
                                  ? newValue.toLocaleString(undefined, {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })
                                  : 'N/A'}
                              </BaseText>
                            </View>
                          </Row>
                        </View>
                      )}
                    </View>
                  );
                })}
            </View>
          )}
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Restore Default',
            variant: ButtonVariant.LIGHT,
            onPress: () => form.setValues({ ...currencyCache }),
          },
          { text: 'Update Changes', onPress: () => form.submitForm(), isLoading: updateCurrencyRequest.isLoading },
        ]}
      />
      <EnabledCurrenciesModal show={modals.enable_currencies} toggle={() => toggleModal('enable_currencies')} />
    </View>
  );
};

export default Currencies;

const validationSchema = Yup.object().shape({
  products: Yup.string()
    .required('Please select a currency')
    .oneOf(paymentsEnabledCurrencies, 'Please select a valid currency'),
  storefront: Yup.array()
    .of(Yup.string().oneOf(paymentsEnabledCurrencies))
    .min(1, 'Please select at least one currency'),
  storefront_default: Yup.string()
    .required('Please select a currency')
    .oneOf(paymentsEnabledCurrencies, 'Please select a valid currency'),
  rates: Yup.lazy(val => {
    return Yup.object().shape(
      Object.fromEntries(
        Object.keys(val ?? {}).map(curr => [
          curr,
          Yup.number()
            .required('Please enter a valid rate')
            .typeError('Please enter a valid rate')
            .min(0, 'Rate should be greater than 0')
            .positive(),
        ]),
      ),
    );
  }),
});
