import { wp } from '@/assets/utils/js';
import { BaseText, Row } from '../ui';
import { InfoCircle } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { ReactNode } from 'react';
import { View } from 'react-native';
import { styled } from 'nativewind';
import { RowProps } from '../ui/row';
import classNames from 'node_modules/classnames';

export interface InfoBadgeProps extends RowProps {
  text?: ReactNode;
  customIcon?: ReactNode;
  hideIcon?: boolean;
  classes?: string;
  addon?: ReactNode;
  rounded?: boolean;
}

const InfoBadge = ({
  customIcon,
  hideIcon = false,
  text,
  classes,
  addon,
  rounded = true,
  ...props
}: InfoBadgeProps) => {
  return (
    <Row
      className={classNames(`justify-between items-start p-10 bg-grey-bgOne`, classes, { 'rounded-10': rounded })}
      {...props}>
      <Row classes="flex-1" alignCenter>
        {hideIcon === false && (
          <>
            {customIcon ? (
              <View className="mr-5">{customIcon}</View>
            ) : (
              <View className="mr-5">
                <InfoCircle size={wp(15)} color={colors.accentGreen.main} />
              </View>
            )}
          </>
        )}
        <View className="flex-1">
          <BaseText fontSize={11} classes={'text-black-placeholder'} lineHeight={0}>
            {text}
          </BaseText>
        </View>
      </Row>
      {addon}
    </Row>
  );
};

export default styled(InfoBadge);
