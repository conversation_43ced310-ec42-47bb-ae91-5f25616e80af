import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import InfoBadge from '../info-badge';
import SelectDropdown from '../../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../../ui/icons';
import Input from '../../ui/inputs/input';
import PhoneNumberInput from '../../ui/inputs/phone-number-input';
import { FormikProps } from 'formik';
import { getFieldvalues, wp } from 'src/assets/utils/js';
import { CustomUpdateStoreInformationParams } from 'src/screens/store-settings/store-information';
import { UpdateStoreParams, CountryInterface, COUNTRIES } from 'catlog-shared';

interface BasicDetailsProps {
  form: FormikProps<CustomUpdateStoreInformationParams>;
  country: CountryInterface | undefined;
  isLoading: boolean;
}

const BasicDetails = ({ form, country, isLoading }: BasicDetailsProps) => {
  const countryList = [
    {
      value: COUNTRIES.NG,
      label: 'Nigeria',
      leftElement: (
        <View className="w-24 h-24">
          <NigeriaFlag size={wp(24)} />
        </View>
      ),
    },
    {
      value: COUNTRIES.GH,
      label: 'Ghana',
      leftElement: (
        <View className="w-24 h-24">
          <GhanaFlag size={wp(24)} />
        </View>
      ),
    },
  ];

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
          <View>
            <SelectDropdown
              items={countryList}
              label={'Select Country'}
              value={country?.name}
              disabled={country?.name ? true : false}
              onPressItem={value => form.setFieldValue('country', value)}
              containerClasses="mt-20"
            />
            <Input label={'Store Name'} containerClasses="mt-15" {...getFieldvalues('name', form)} />
            <PhoneNumberInput
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
            />
            <Input
              label={'Describe your store - What you sell'}
              multiline
              className="h-[80]"
              containerClasses="mt-15"
              {...getFieldvalues('description', form)}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default BasicDetails;
