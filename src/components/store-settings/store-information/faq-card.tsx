import { View } from 'react-native';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import CustomizationCard from '@/components/ui/cards/customization-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import Pressable from '@/components/ui/base/pressable';
import { Add, Edit2, Layer, Trash, Whatsapp } from 'iconsax-react-native/src';
import { getFieldvalues, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Fragment, memo, useEffect, useMemo, useState } from 'react';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { FormikProps } from 'formik';
import { CustomUpdateStoreParams, generateOption } from 'src/screens/store-settings/checkout-options';
import ListItemCard from '@/components/ui/cards/list-item-card';
import * as Animatable from 'react-native-animatable';
import {
  phoneObjectFromString,
  phoneObjectToString,
  removeCountryCode,
  StoreFaq,
  StoreInterface,
  StoreTestimonial,
  UpdateStoreFaqDetails,
  WhatsappCheckoutOption,
} from 'catlog-shared';
import { ChevronDown, ChevronUp } from 'src/components/ui/icons';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
// import Animated from 'node_modules/react-native-reanimated/lib/typescript';

interface FaqCardProps {
  faqItem: StoreFaq;
  updateOption: (option: StoreFaq, index: number) => void;
  deleteOption: (index: number) => void;
  index: number;
  form: FormikProps<UpdateStoreFaqDetails>;
}

const FaqCard = memo(({ faqItem, index, updateOption, deleteOption, form }: FaqCardProps) => {
  const [isCardView, setIsCardView] = useState(false);
  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    toggleDisplayModeToCard();
  }, [faqItem?.id]);

  const toggleDisplayModeToCard = () => {
    if (faqItem.question && faqItem.answer) {
      setIsCardView(true);
      if (faqItem.id) setExpanded(false);
    }
  };

  const isNew = useMemo(() => !faqItem.question && !faqItem.answer, [faqItem.question, faqItem.answer]);

  return (
    <Row className="bg-grey-bgOne rounded-12">
      <View className="w-12" />
      <View className="flex-1 bg-white border border-grey-border py-10 px-12 rounded-12">
        <View className="flex-row justify-between">
          <Pressable
            className="flex-row flex-1 justify-start items-center"
            style={{ gap: wp(5) }}
            disabled={isNew}
            onPress={() => setExpanded(prev => !prev)}>
            {!isNew && (
              <>
                {expanded ? (
                  <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                ) : (
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                )}
              </>
            )}
            {!isNew && (
              <View className="flex-1 mr-5">
                <BaseText
                  fontSize={14}
                  weight="medium"
                  classes="text-black-secondary"
                  adjustsFontSizeToFit
                  numberOfLines={1}>
                  {faqItem.question ? faqItem.question : 'Question'}
                </BaseText>
              </View>
            )}
            {isNew && (
              <View className="flex-1">
                <BaseText fontSize={14} weight="medium" classes="text-black-placeholder">
                  New FAQ
                </BaseText>
              </View>
            )}
          </Pressable>
          <Row className="justify-end" style={{ gap: wp(5) }}>
            {!isNew && (
              <Pressable
                onPress={() => {
                  if (isCardView === false) {
                    setExpanded(true);
                  }
                  setTimeout(() => {
                    setIsCardView(prev => !prev);
                  }, 700);
                }}>
                <CircledIcon className="bg-grey-border mr-10">
                  <Edit2 size={wp(14)} color={colors.black.placeholder} />
                </CircledIcon>
              </Pressable>
            )}
            <Pressable onPress={() => deleteOption(index)}>
              <CircledIcon className="bg-grey-border">
                <Trash size={wp(14)} color={colors.accentRed.main} />
              </CircledIcon>
            </Pressable>
          </Row>
        </View>
        {expanded && (
          <>
            {isCardView && (
              <Animatable.View
                animation="fadeIn"
                easing={'ease-in-out'}
                duration={200}
                className="flex-1 bg-grey-bgOne border border-grey-border py-12 px-14 rounded-12 mt-10">
                <BaseText fontSize={14} lineHeight={22} classes="text-black-placeholder">
                  {faqItem.answer}
                </BaseText>
              </Animatable.View>
            )}
            {!isCardView && (
              <Animatable.View
                animation="fadeIn"
                easing={'ease-in-out'}
                duration={200}
                className="flex-1 bg-white border border-grey-border py-10 px-12 rounded-12 mt-10">
                {/* <BaseText fontSize={14}>Customer Name</BaseText> */}
                <Input
                  label="Question: eg.  What is your return policy"
                  value={faqItem.question}
                  onChangeText={value => updateOption({ ...faqItem, question: value }, index)}
                  {...getFieldvalues(`faqs.${index}.question`, form)}
                  onBlur={toggleDisplayModeToCard}
                  onSubmitEditing={toggleDisplayModeToCard}
                  containerClasses="mt-4"
                />
                <BaseText fontSize={14} classes="mt-15">
                  Your answer
                </BaseText>
                <Input
                  label="Answer"
                  value={faqItem.answer}
                  multiline
                  className="min-h-[100px] max-h-[120px]"
                  containerClasses="mt-15"
                  onChangeText={value => updateOption({ ...faqItem, answer: value }, index)}
                  {...getFieldvalues(`faqs.${index}.answer`, form)}
                  onSubmitEditing={toggleDisplayModeToCard}
                  onBlur={toggleDisplayModeToCard}
                />
              </Animatable.View>
            )}
          </>
        )}
      </View>
    </Row>
  );
});

export default memo(FaqCard);
