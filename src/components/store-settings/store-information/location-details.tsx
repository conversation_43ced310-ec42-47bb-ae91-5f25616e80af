import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import InfoBadge from '../info-badge';
import AvoidKeyboard from '../../ui/layouts/avoid-keyboard';
import SelectDropdown from '../../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../../ui/icons';
import Input from '../../ui/inputs/input';
import { FormikProps } from 'formik';
import { getFieldvalues, getStates } from 'src/assets/utils/js';
import { CustomUpdateStoreInformationParams } from 'src/screens/store-settings/store-information';
import { UpdateStoreParams, CountryInterface, COUNTRIES } from 'catlog-shared';

interface LocationDetailsProps {
  form: FormikProps<CustomUpdateStoreInformationParams>;
  country: CountryInterface | undefined;
  isLoading: boolean;
}

const countrySpecific = {
  [COUNTRIES.NG]: 'State',
  [COUNTRIES.GH]: 'Region',
  [COUNTRIES.ZA]: 'Province',
  [COUNTRIES.KE]: 'County',
}

const countrySpecificLocation = {
  [COUNTRIES.NG]: 'Everywhere in Lagos',
  [COUNTRIES.GH]: 'Everywhere in Accra',
  [COUNTRIES.ZA]: "Everywhere in  Jo'burg",
  [COUNTRIES.KE]: 'Everywhere in Nairobi',
}



const LocationDetails = ({ form, country, isLoading }: LocationDetailsProps) => {
  const states = getStates(country?.code!);

  const statesList = states?.map(item => ({ value: item, label: item })) ?? [];

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
          <View>
            <Input label={'Store Address'} containerClasses="mt-20" {...getFieldvalues('address', form)} />
            <SelectDropdown
              items={statesList}
              label={countrySpecific[country?.code]}
              disabled={!country}
              onPressItem={value => form.setFieldValue('state', value)}
              selectedItem={form?.values?.state}
              containerClasses="mt-20"
            />
            <Input
              label={`Delivery Locations  e.g. ${countrySpecificLocation[country?.code]}`}
              containerClasses="mt-15"
              {...getFieldvalues('delivery_locations', form)}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

export default LocationDetails;
