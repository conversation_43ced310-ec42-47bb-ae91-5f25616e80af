import { useState } from 'react';
import { View, Pressable } from 'react-native';
import { Toast } from 'react-native-toast-message/lib/src/Toast';
import { delay, hp, wp } from 'src/assets/utils/js';
import { BaseText } from '@/components/ui';
import { Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import BottomModal from '@/components/ui/modals/bottom-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useFluxState from 'src/hooks/use-flux-state';
import colors from 'src/theme/colors';
import { UPDATE_STORE_LINK } from 'catlog-shared';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface Props {
  show: boolean;
  toggle: (s: boolean) => void;
}
const EditStoreLinkModal: React.FC<Props> = ({ show, toggle }) => {
  const { store, updateStore } = useAuthContext();
  const [slug, setSlug] = useFluxState(store?.slug);

  const updateLinkReq = useApi({
    apiFunction: UPDATE_STORE_LINK,
    key: UPDATE_STORE_LINK.name,
    method: 'PUT',
  });

  const updateLink = async () => {
    if (slug) {
      const [response, error] = await updateLinkReq.makeRequest({
        store: store?.id!,
        slug: slug,
      });
      if (response) {
        toggle(false);
        await delay(1000);
        Toast.show({
          type: 'success',
          text1: 'Store Link Updated',
          text2: 'Your store link has been updated successfully',
        });
        updateStore({ slug: slug });
      } else if (error) {
        Toast.show({ type: 'error', text1: error?.body?.message });
      }
    }
  };

  return (
    <BottomModal
      size="sm"
      title={'Edit Store Link'}
      buttons={[{ text: 'Update Store Link', onPress: () => updateLink(), isLoading: updateLinkReq.isLoading }]}
      isVisible={show}
      enableDynamicSizing
      enableSnapPoints={false}
      showFooterOnKeyboard
      closeModal={() => toggle(false)}>
      <BottomSheetView style={{ paddingHorizontal: wp(20), paddingBottom: hp(40) }} enableFooterMarginAdjustment>
        <Input
          useBottomSheetInput
          value={slug}
          onChangeText={value => setSlug(value)}
          containerClasses="bg-white"
          placeholder={'Choose a new link'}
          rightAccessory={
            <View className=" px-10 -mr-15 border-l border-grey-border">
              <BaseText classes="text-black-placeholder">
                .{process.env.EXPO_PUBLIC_PUBLIC_URL?.replace('https://', '')}
              </BaseText>
            </View>
          }
        />
      </BottomSheetView>
    </BottomModal>
  );
};
export default EditStoreLinkModal;
