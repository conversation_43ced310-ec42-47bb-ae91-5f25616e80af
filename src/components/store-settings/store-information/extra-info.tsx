import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import AvoidKeyboard from '../../ui/layouts/avoid-keyboard';
import SelectDropdown from '../../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../../ui/icons';
import Input from '../../ui/inputs/input';
import Accordion from '../../ui/others/accordion';
import { FormikProps } from 'formik';
import { getFieldvalues } from 'src/assets/utils/js';
import { CustomUpdateStoreInformationParams } from 'src/screens/store-settings/store-information';
import ExtraInfoImages from './extra-info-images';
import { UpdateStoreParams } from 'catlog-shared';

interface ExtraInfoProps {
  form: FormikProps<CustomUpdateStoreInformationParams>;
  isLoading: boolean;
}

const ExtraInfo = ({ form, isLoading }: ExtraInfoProps) => {
  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
          <View>
            <View className="mt-20">
              <BaseText fontSize={14} weight={'medium'}>
                Average time delivery
              </BaseText>
              <Row className="gap-x-10 mt-15">
                <View className="flex-1">
                  <Input
                    label={'Timeline'}
                    {...getFieldvalues('extra_info.delivery_timeline.count', form)}
                    value={form?.values?.extra_info?.delivery_timeline?.count}
                  />
                </View>
                <View className="flex-1">
                  <SelectDropdown
                    label={'Duration'}
                    items={durationOptions}
                    onPressItem={value => form.setFieldValue('extra_info.delivery_timeline.unit', value)}
                    selectedItem={form?.values?.extra_info?.delivery_timeline?.unit}
                  />
                </View>
              </Row>
            </View>
            <View className="mt-15">
              <BaseText fontSize={14} weight={'medium'}>
                Refund & return policy
              </BaseText>
              <Input
                label={'Policy'}
                multiline
                className="min-h-[113px]"
                containerClasses="mt-15"
                {...getFieldvalues('extra_info.refund_policy', form)}
                value={form?.values?.extra_info?.refund_policy}
              />
            </View>
            <Accordion
              title={'Info image'}
              initiallyOpened
              anchorContainerClasses="justify-between"
              anchorTitleProps={{ fontSize: 14, classes: 'text-black-primary', weight: 'medium' }}
              anchorIconContainerClasses="bg-grey-bgOne p-4 rounded-full">
              <Input
                label={'Title (e.g size chart)'}
                containerClasses="mt-10"
                {...getFieldvalues('extra_info.images_label', form)}
              />
              <ExtraInfoImages
                extraInfoImages={form?.values?.extra_info?.images}
                update={(images: string[]) => form?.setFieldValue('extra_info.images', images)}
              />
            </Accordion>
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter buttons={[{ text: 'Update Changes', onPress: () => form.submitForm(), isLoading }]} />
    </View>
  );
};

const durationOptions = [
  {
    value: 'minute(s)',
    label: 'Minute(s)',
  },
  {
    value: 'hour(s)',
    label: 'Hour(s)',
  },
  {
    value: 'day(s)',
    label: 'Day(s)',
  },
  {
    value: 'week(s)',
    label: 'Week(s)',
  },
  {
    value: 'month(s)',
    label: 'Month(s)',
  },
];

export default ExtraInfo;
