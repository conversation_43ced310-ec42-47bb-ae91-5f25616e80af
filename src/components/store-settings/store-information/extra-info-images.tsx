import colors from 'src/theme/colors';
import useImageUploads from 'src/hooks/use-file-uploads';
import * as ImagePicker from 'expo-image-picker';
import { useApi } from 'src/hooks/use-api';
import useAuthContext from 'src/contexts/auth/auth-context';
import Toast from 'react-native-toast-message';
import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Pressable, ScrollView, View } from 'react-native';
import ProductImage from '@/components/products/storefront-products/product-image';
import { Plus } from '@/components/ui/icons';
import { cx, showLoader, showSuccess, wp } from 'src/assets/utils/js';
import { Image } from 'src/@types/utils';
import { EDIT_ITEM, ProductCreateMethod } from 'catlog-shared';

export interface ProductImagesSectionProps {
  extraInfoImages: string[];
  update: (images: string[]) => void;
}

const ExtraInfoImages = ({ extraInfoImages, update }: ProductImagesSectionProps) => {
  const [images, setImages] = useState<Image[]>(
    extraInfoImages.map((i, idx) => ({
      url: i,
      file: null,
      isUploading: false,
      key: idx.toString(),
      name: '',
      src: i,
      uploadProgress: 100,
      newFile: false,
    })) || [],
  );

  useImageUploads(images, setImages);

  useEffect(() => {
    const hasUploadedImages = images
      .filter(i => i.newFile === true)
      .some(i => i.url !== undefined && i.uploadProgress === 100);

    if (hasUploadedImages) {
      const imageUrls = images.filter(i => i.url !== undefined).map(i => i.url!);
      update(imageUrls);
      showSuccess('Imaged uploaded successfully');
    }
  }, [images]);

  const removePickedImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    const imageUrls = newImages.filter(i => i.url !== undefined).map(i => i.url!);

    update(imageUrls);
    setImages(newImages);
    showSuccess('Imaged removed successfully');
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      aspect: [1, 1],
      quality: 0.5,
      allowsMultipleSelection: true,
      selectionLimit: 5,
      // base64: true,
    });

    if (!result.canceled) {
      const newImages = result.assets.map((item, index) => {
        const uriParts = item.uri.split('/');
        const name = uriParts[uriParts.length - 1];
        const currentTime = new Date().getTime();
        return {
          file: null,
          url: '',
          name: item.fileName ?? name,
          src: item.uri,
          key: item.assetId ?? currentTime.toString(),
          isUploading: false,
          uploadProgress: 0,
          error: false,
          newFile: true,
        };
      });
      setImages([...images, ...newImages]);
      showLoader('Uploading images...', false, true);
    }
  };

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View className="flex-row mt-15" style={{ gap: wp(10) }}>
        {images?.map?.((image, index) => (
          <ProductImage
            className={cx({ 'border-[3px] border-accentGreen-main': image.url !== undefined })}
            key={image.src ?? image.url}
            isThumbnail={false}
            showCloseBtn
            imageUri={image.src ?? image.url}
            onPressDelete={() => removePickedImage(index)}
            onPress={() => null}
          />
        ))}
        <Pressable className="rounded-[12px] p-[22px] bg-grey-bgOne" onPress={pickImage}>
          <Plus primaryColor={colors.black.placeholder} height={wp(16)} width={wp(16)} strokeWidth={1.5} />
        </Pressable>
      </View>
    </ScrollView>
  );
};

export default ExtraInfoImages;
