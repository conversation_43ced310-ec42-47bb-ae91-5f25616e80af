import { View } from 'react-native';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import CustomizationCard from '@/components/ui/cards/customization-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import Pressable from '@/components/ui/base/pressable';
import { Add, Edit2, Layer, Trash, Whatsapp } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Fragment, memo, useEffect, useMemo, useState } from 'react';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { FormikProps } from 'formik';
import { CustomUpdateStoreParams, generateOption } from 'src/screens/store-settings/checkout-options';
import ListItemCard from '@/components/ui/cards/list-item-card';
import * as Animatable from 'react-native-animatable';
import {
  phoneObjectFromString,
  phoneObjectToString,
  removeCountryCode,
  StoreInterface,
  StoreTestimonial,
  WhatsappCheckoutOption,
} from 'catlog-shared';
import { ChevronDown, ChevronUp } from 'src/components/ui/icons';
import SelectDropdown from 'src/components/ui/inputs/select-dropdown';
// import Animated from 'node_modules/react-native-reanimated/lib/typescript';

interface TestimonialCardProps {
  testimonial: StoreTestimonial;
  updateOption: (option: StoreTestimonial, index: number) => void;
  deleteOption: (index: number) => void;
  index: number;
}

const TestimonialCard = memo(({ testimonial, index, updateOption, deleteOption }: TestimonialCardProps) => {
  const [isCardView, setIsCardView] = useState(false);
  const [expanded, setExpanded] = useState(true);

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const toggleDisplayModeToCard = () => {
    if (testimonial.customer_name && testimonial.content) {
      setIsCardView(true);
      if (testimonial.id) setExpanded(false);
    }
  };

  const isNew = useMemo(() => !testimonial.customer_name && !testimonial.content, [
    testimonial.customer_name,
    testimonial.content,
  ]);

  return (
    <Row className="bg-grey-bgOne rounded-12">
      <View className="w-12" />
      <View className="flex-1 bg-white border border-grey-border py-10 px-12 rounded-12">
        <View className="flex-row justify-between">
          <Pressable
            className="flex-row flex-1 justify-start items-center"
            style={{ gap: wp(5) }}
            disabled={isNew}
            onPress={() => setExpanded(prev => !prev)}>
            {!isNew && (
              <>
                {expanded ? (
                  <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                ) : (
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                )}
              </>
            )}
            {!isNew && (
              <>
                <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
                  {testimonial.customer_name ? testimonial.customer_name : 'Customer name'}
                </BaseText>
                <View className="h-4 w-4 rounded-full bg-black-placeholder" />
                <BaseText fontSize={14} classes="text-black-secondary">
                  {testimonial.source?.length > 0 ? testimonial.source : 'Source'}
                </BaseText>
              </>
            )}
            {isNew && (
              <BaseText fontSize={14} weight="medium" classes="text-black-placeholder">
                New Testimonial
              </BaseText>
            )}
          </Pressable>
          <Row className="justify-end" style={{ gap: wp(5) }}>
            {!isNew && (
              <Pressable
                onPress={() => {
                  if (isCardView === false) {
                    setExpanded(true);
                  }
                  setTimeout(() => {
                    setIsCardView(prev => !prev);
                  }, 700);
                }}>
                <CircledIcon className="bg-grey-border mr-10">
                  <Edit2 size={wp(14)} color={colors.black.placeholder} />
                </CircledIcon>
              </Pressable>
            )}
            <Pressable onPress={() => deleteOption(index)}>
              <CircledIcon className="bg-grey-border">
                <Trash size={wp(14)} color={colors.accentRed.main} />
              </CircledIcon>
            </Pressable>
          </Row>
        </View>
        {expanded && (
          <>
            {isCardView && (
              <Animatable.View
                animation="fadeIn"
                easing={'ease-in-out'}
                duration={200}
                className="flex-1 bg-grey-bgOne border border-grey-border py-12 px-14 rounded-12 mt-10">
                <BaseText fontSize={14} lineHeight={22} classes="text-black-placeholder">
                  {testimonial.content}
                </BaseText>
              </Animatable.View>
            )}
            {!isCardView && (
              <Animatable.View
                animation="fadeIn"
                easing={'ease-in-out'}
                duration={200}
                className="flex-1 bg-white border border-grey-border py-10 px-12 rounded-12 mt-10">
                {/* <BaseText fontSize={14}>Customer Name</BaseText> */}
                <Input
                  label="Customer name"
                  value={testimonial.customer_name}
                  onChangeText={value => updateOption({ ...testimonial, customer_name: value }, index)}
                  onBlur={toggleDisplayModeToCard}
                  onSubmitEditing={toggleDisplayModeToCard}
                  containerClasses="mt-4"
                />
                {/* <BaseText fontSize={14} classes="mt-15">
                  Source
                </BaseText> */}
                <SelectDropdown
                  items={testimonialSources}
                  containerClasses="mt-15"
                  label="Source"
                  onPressItem={value => updateOption({ ...testimonial, source: value }, index)}
                  selectedItem={testimonial.source}
                />
                {/* <BaseText fontSize={14} classes="mt-15">
                  Testimonial
                </BaseText> */}
                <Input
                  label="Testimonial"
                  value={testimonial.content}
                  multiline
                  className="min-h-[100px] max-h-[120px]"
                  containerClasses="mt-15"
                  onChangeText={value => updateOption({ ...testimonial, content: value }, index)}
                  onSubmitEditing={toggleDisplayModeToCard}
                  onBlur={toggleDisplayModeToCard}
                />
              </Animatable.View>
            )}
          </>
        )}
      </View>
    </Row>
  );
});

export default memo(TestimonialCard);

const testimonialSources = [
  {
    value: 'Instagram',
    label: 'Instagram',
  },
  {
    value: 'Twitter',
    label: 'Twitter',
  },
  {
    value: 'Snapchat',
    label: 'Snapchat',
  },
  {
    value: 'TikTok',
    label: 'TikTok',
  },
  {
    value: 'Whatsapp',
    label: 'Whatsapp',
  },
  {
    value: 'Facebook',
    label: 'Facebook',
  },
  {
    value: 'In-Person',
    label: 'In-Person',
  },
  {
    value: 'Other',
    label: 'Other',
  },
];
