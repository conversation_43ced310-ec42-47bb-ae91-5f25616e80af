import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { CircledIcon, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import SelectCategorizationForm, {
  SelectCategorizationFormMethod,
} from '@/components/get-started/select-categorization-form';
import { useEffect, useRef, useState } from 'react';
import { useFormik } from 'formik';
import useAuthContext from 'src/contexts/auth/auth-context';
import {
  alertPromise,
  delay,
  getFieldvalues,
  hideLoader,
  hp,
  showError,
  showLoader,
  showSuccess,
  wp,
  Yup,
} from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import { UPDATE_STORE_ABOUT, UPDATE_STORE_FAQS, UpdateStoreFaqDetails } from 'catlog-shared';
import BaseText from 'src/components/ui/base/base-text';
import Input from 'src/components/ui/inputs/input';
import CustomImage from 'src/components/ui/others/custom-image';
import Pressable from 'src/components/ui/base/pressable';
import { Plus } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import * as ImagePicker from 'expo-image-picker';
import useImageUploads from 'src/hooks/use-file-uploads';
import { Image } from 'src/@types/utils';
import Separator from 'src/components/ui/others/separator';
import TestimonialCard from './testimonial-card';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import FaqCard from './faq-card';
import { Bubble, MessageQuestion } from 'iconsax-react-native/src';

interface FaqsProps {}

const Faqs = ({}: FaqsProps) => {
  const { store, updateStore } = useAuthContext();

  const scrollRef = useRef<ScrollView>(null);

  const updateFaqsReq = useApi({
    apiFunction: UPDATE_STORE_FAQS,
    key: 'update-faqs',
    method: 'PUT',
  });

  const form = useFormik<UpdateStoreFaqDetails>({
    initialValues: {
      faqs: store?.faqs ?? [],
    },
    validationSchema,
    onSubmit: async values => {
      showLoader('Updating your FAQs...');
      const { makeRequest } = updateFaqsReq;
      const [response, error] = await makeRequest({
        faqs: values.faqs,
      });

      hideLoader();
      await delay(800);
      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
          showError(error);
        }
      } else {
        form.setValues(response.data);
        form.setFormikState(prevState => ({
          ...prevState,
          initialValues: response.data,
        }));
        updateStore({ faqs: response.data });
        showSuccess('FAQs updated successfully');
      }
    },
  });

  const createEmptyFaq = async () => {
    const faqs = [...form.values.faqs];
    faqs.push({
      id: '',
      answer: '',
      question: '',
      created_at: '',
      is_visible: true,
    });
    form.setFieldValue('faqs', faqs);
    await delay(300);
    scrollRef.current?.scrollToEnd();
  };

  const removeFaq = async (index: number) => {
    const proceed = await alertPromise(
      'Do you want to delete this FAQ?',
      'This FAQ would be completely removed from the list.',
      'Delete',
      'Cancel',
      true,
    );
    if (!proceed) return;

    const faqs = [...form.values.faqs];
    faqs.splice(index, 1);
    form.setFieldValue('faqs', faqs);
  };

  // const toggleAccordion = (index: number) => {
  //   const accordionCopy = [...accordion];

  //   accordionCopy[index] = !accordion[index];
  //   setAccordion(accordionCopy);
  // };

  // const toggleEditing = (index: number, toggle: boolean = false) => {
  //   if (toggle) {
  //     // toggleAccordion(index);
  //   }
  //   const editingCopy = [...editing];

  //   editingCopy[index] = !editing[index];
  //   setEditing(editingCopy);
  // };

  // const deleteFaq = (id: number) => {
  //   setSelected((prev) => ({
  //     ...prev,
  //     delete: id,
  //   }));
  //   toggleModal("delete");
  // };

  const hasFaq = form.values.faqs.length > 0;

  return (
    <View className="flex-1">
      <ScrollView className="flex-1" ref={scrollRef}>
        <Container>
          <InfoBadge text={"Here's a summary of the information you provided"} />
          <View className="mt-15">
            {!hasFaq && (
              <View className="flex-1 justify-center items-center min-h-[100px]">
                <CircledIcon className="bg-grey-bgOne p-15">
                  <MessageQuestion variant="Bulk" size={wp(40)} color={colors.grey.muted} />
                </CircledIcon>
                <BaseText fontSize={14} classes="text-black-muted mt-15">
                  You haven't added any FAQs yet.
                </BaseText>
                <Button
                  text="Add a new Question"
                  onPress={createEmptyFaq}
                  variant={ButtonVariant.LIGHT}
                  size={ButtonSize.MEDIUM}
                  className='mt-15'
                />
              </View>
            )}
            {hasFaq && (
              <View style={{ gap: hp(10) }}>
                {form.values.faqs &&
                  form.values.faqs.map((item, index) => (
                    <FaqCard
                      faqItem={item}
                      key={index}
                      updateOption={(option, idx) => {
                        const faqs = [...form.values.faqs];
                        faqs[idx] = option;
                        form.setFieldValue('faqs', faqs);
                      }}
                      deleteOption={removeFaq}
                      index={index}
                      form={form}
                    />
                  ))}
                <Button
                  text="Add a new Question"
                  onPress={createEmptyFaq}
                  variant={ButtonVariant.LIGHT}
                  size={ButtonSize.MEDIUM}
                />
              </View>
            )}
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save Updates',
            onPress: () => form.handleSubmit(),
            isLoading: updateFaqsReq?.isLoading,
            disabled: !hasFaq,
          },
        ]}
      />
    </View>
  );
};

export default Faqs;

const validationSchema = Yup.object().shape({
  faqs: Yup.array().of(
    Yup.object().shape({
      question: Yup.string().required('Question is required'),
      answer: Yup.string().required('Answer is required'),
    }),
  ),
});
