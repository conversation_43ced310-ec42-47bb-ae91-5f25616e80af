import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { CircledIcon, Container, Row } from '@/components/ui';
import InfoBadge from '../info-badge';
import SelectCategorizationForm, {
  SelectCategorizationFormMethod,
} from '@/components/get-started/select-categorization-form';
import { useEffect, useRef, useState } from 'react';
import { useFormik } from 'formik';
import useAuthContext from 'src/contexts/auth/auth-context';
import { getFieldvalues, hideLoader, hp, showLoader, showSuccess, wp, Yup, alertPromise } from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import { UPDATE_STORE_ABOUT } from 'catlog-shared';
import BaseText from 'src/components/ui/base/base-text';
import Input from 'src/components/ui/inputs/input';
import CustomImage from 'src/components/ui/others/custom-image';
import Pressable from 'src/components/ui/base/pressable';
import { Close, Plus } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import * as ImagePicker from 'expo-image-picker';
import useImageUploads from 'src/hooks/use-file-uploads';
import { Image } from 'src/@types/utils';
import Separator from 'src/components/ui/others/separator';
import TestimonialCard from './testimonial-card';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { User } from 'iconsax-react-native/src';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import CircularProgress from 'react-native-circular-progress-indicator';

interface AboutUsProps {}

const AboutUs = ({}: AboutUsProps) => {
  const { store, updateStore } = useAuthContext();
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const updateAboutReq = useApi({
    apiFunction: UPDATE_STORE_ABOUT,
    key: 'update-about-us',
    method: 'PUT',
  });

  const form = useFormik({
    enableReinitialize: true,
    initialValues: {
      about_us: {
        content: store?.about_us?.content ?? '',
        images: store?.about_us?.images ?? [],
      },
      testimonials: store?.testimonials ?? [],
    },
    validationSchema,
    onSubmit: async values => {
      const { makeRequest } = updateAboutReq;
      showLoader('Updating your info...');
      const [response, error] = await makeRequest({
        about_us: values.about_us,
        testimonials: values.testimonials.map(({ id, created_at, ...rest }) => rest),
      });

      hideLoader();

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
      } else {
        showSuccess('About us updated successfully');
        updateStore({ about_us: response.data.about_us, testimonials: response.data.testimonials });
        form.setValues(response.data);
        form.setFormikState(prevState => ({
          ...prevState,
          initialValues: response.data,
        }));
      }
    },
  });

  const [images, setImages] = useState<Image[]>(
    form.values.about_us.images.map((i, idx) => ({
      url: i,
      file: null,
      isUploading: false,
      key: idx.toString(),
      name: '',
      src: i,
      uploadProgress: 100,
      newFile: false,
    })),
  );

  const createEmptyTestimonial = () => {
    const testimonials = [...form.values.testimonials];
    testimonials.push({
      id: '',
      customer_name: '',
      content: '',
      source: '',
      created_at: '',
      is_visible: true,
    });
    form.setFieldValue('testimonials', testimonials);
  };

  const removeTestimonial = async (index: number) => {
    const proceed = await alertPromise(
      'Do you want to delete this testimonial?',
      'This testimonial would be completely removed from the list.',
      'Delete',
      'Cancel',
      true,
    );
    if (!proceed) return;

    const testimonials = [...form.values.testimonials];
    testimonials.splice(index, 1);
    form.setFieldValue('testimonials', testimonials);
  };

  useImageUploads(images, setImages);

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  useEffect(() => {
    const hasUploadedImages = images.some(i => i.url !== undefined && i.uploadProgress === 100);
    console.log('imageUrls: ', JSON.stringify(images));

    if (hasUploadedImages) {
      const imageUrls = images.filter(i => i.url !== undefined).map(i => i.url!);
      form.setFieldValue('about_us.images', imageUrls);
      showSuccess('Imaged uploaded successfully');
    }
  }, [images]);

  useEffect(() => {
    if (infoMessage) {
      showLoader(infoMessage, false, true);
    }
  }, [infoMessage]);

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={"Here's a summary of the information you provided"} />
          <View className="mt-15">
            <View>
              <BaseText fontSize={14} type="heading">
                Tell your customers more about your business:
              </BaseText>
              <Input
                multiline
                label="Your business story"
                {...getFieldvalues('about_us.content', form)}
                containerClasses="mt-15 min-h-[100] max-h-[150px]"
                // textInputClasses="h-full"
              />
            </View>
            <View className="mt-15">
              <BaseText fontSize={14} type="heading">
                Supporting Images:
              </BaseText>
              <BaseText fontSize={12} classes="text-black-muted mt-5">
                Pictures of you, your team, your store, etc.
              </BaseText>
              {form.errors?.about_us?.images && (
                <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main mt-5">
                  {form.errors?.about_us?.images}
                </BaseText>
              )}
              <Row className="flex-wrap justify-start mt-10" style={{ gap: wp(10) }}>
                {images.map((image, idx) => (
                  <View key={image.key} className="mt-10 w-80 h-80">
                    <CustomImage
                      className="w-full h-full rounded-10"
                      imageProps={{ source: image.url ?? image.src, contentFit: 'cover' }}
                    />
                    {image.isUploading && (
                      <View
                        className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
                        style={{ backgroundColor: '#292D321A' }}>
                        <CircularProgress
                          value={image.uploadProgress ?? 0}
                          radius={wp(16)}
                          duration={500}
                          delay={600}
                          activeStrokeWidth={wp(4)}
                          inActiveStrokeWidth={wp(4)}
                          strokeLinecap={'round'}
                          activeStrokeColor={colors.accentGreen.main}
                          inActiveStrokeColor={colors.white}
                          maxValue={100}
                          valueSuffix={'%'}
                          progressValueStyle={{
                            fontSize: wp(8),
                            fontFamily: 'Inter-Bold',
                            color: colors.white,
                          }}
                        />
                      </View>
                    )}
                    {!image.isUploading && (
                      <Pressable onPress={() => removeImage(idx)} className='absolute -top-5 -right-5'>
                        <View className="h-24 w-24 flex items-center justify-center rounded-full bg-black-placeholder">
                          <Close currentColor={colors.white} size={wp(10)} />
                        </View>
                      </Pressable>
                    )}
                  </View>
                ))}
                <Pressable
                  className="rounded-12 w-80 h-80  border border-grey-border border-dashed items-center justify-center"
                  onPress={() => pickMultipleImages(images, setImages, false, {}, setInfoMessage)}>
                  <Plus primaryColor={colors.black.placeholder} height={wp(24)} width={wp(24)} strokeWidth={1.5} />
                </Pressable>
              </Row>
            </View>
            <Separator className="mx-0" />
            {form.values.testimonials && form.values.testimonials?.length < 1 && (
              <View className="flex-1 justify-center items-center min-h-[100px] mb-15">
                <CircledIcon className="bg-grey-bgOne p-15">
                  <User variant="Bulk" size={wp(40)} color={colors.grey.muted} />
                </CircledIcon>
                <BaseText fontSize={14} classes="text-black-muted mt-15">
                  You haven't added any testimonials yet.
                </BaseText>
              </View>
            )}
            <View style={{ gap: hp(10) }}>
              {form.values.testimonials &&
                form.values.testimonials.map((item, index) => (
                  <TestimonialCard
                    testimonial={item}
                    key={index}
                    updateOption={(option, idx) => {
                      const testimonials = [...form.values.testimonials];
                      testimonials[idx] = option;
                      form.setFieldValue('testimonials', testimonials);
                    }}
                    deleteOption={removeTestimonial}
                    index={index}
                  />
                ))}
              <Button
                text="Add a new testimonial"
                onPress={createEmptyTestimonial}
                variant={ButtonVariant.LIGHT}
                size={ButtonSize.MEDIUM}
              />
            </View>
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save Updates',
            onPress: () => form.handleSubmit(),
            isLoading: updateAboutReq?.isLoading,
          },
        ]}
      />
    </View>
  );
};

export default AboutUs;

const validationSchema = Yup.object().shape({
  about_us: Yup.object().shape({
    content: Yup.string().required('About Us content is required'),
    images: Yup.array().of(Yup.string().url('Each image must be a valid URL')).min(1, 'At least one image is required'),
  }),
  testimonials: Yup.array().of(
    Yup.object().shape({
      customer_name: Yup.string().required('Customer name is required'),
      content: Yup.string().required('Testimonial content is required'),
      source: Yup.string().required('Testimonial source is required'),
    }),
  ),
});
