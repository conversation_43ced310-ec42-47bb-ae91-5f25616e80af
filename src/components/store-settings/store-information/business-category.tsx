import { ScrollView } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import InfoBadge from '../info-badge';
import AvoidKeyboard from '../../ui/layouts/avoid-keyboard';
import SelectDropdown from '../../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../../ui/icons';
import Input from '../../ui/inputs/input';
import SelectCategorizationForm, {
  SelectCategorizationFormMethod,
} from '@/components/get-started/select-categorization-form';
import { useRef, useState } from 'react';

interface BusinessCategoryProps {}

const BusinessCategory = ({}: BusinessCategoryProps) => {
  const categorizationFormRef = useRef<SelectCategorizationFormMethod>(null);
  const [isLoading, setIsLoading] = useState(false);

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge
            text={"Provide information about your business, this data would be used to improve your store's SEO"}
          />
          <View className="mt-15">
            <SelectCategorizationForm
              setIsLoading={s => setIsLoading(s)}
              ref={categorizationFormRef}
              useDropdown={true}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Save business category',
            onPress: () => categorizationFormRef?.current?.submitForm(),
            isLoading,
          },
        ]}
      />
    </View>
  );
};

export default BusinessCategory;
