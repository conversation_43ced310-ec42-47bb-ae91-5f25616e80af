import React from 'react';
import { View, Text } from 'react-native';
import useAuthContext from 'src/contexts/auth/auth-context';
import { SCOPES } from 'src/assets/utils/js/permissions';
import { actionIsAllowed } from 'src/assets/utils/js/permissions';

const PermissionTest = () => {
  const { userRole, store } = useAuthContext();
  
  const canUpdateStoreDetails = actionIsAllowed({
    userRole,
    permission: SCOPES.SETTINGS.UPDATE_STORE_DETAILS,
    plan: store?.subscription?.plan?.type,
    country: store?.country?.code,
  });

  return (
    <View style={{ padding: 20, backgroundColor: '#f5f5f5', marginVertical: 10, borderRadius: 8 }}>
      <Text style={{ fontWeight: 'bold', marginBottom: 5 }}>Permission Test</Text>
      <Text>User Role: {userRole}</Text>
      <Text>Can Update Store Details: {canUpdateStoreDetails ? 'Yes' : 'No'}</Text>
    </View>
  );
};

export default PermissionTest;
