import React from 'react';
import { copyToClipboard, getFieldvalues, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { ArrowRight } from '@/components/ui/icons';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import colors from '@/theme/colors';
import { useFormik } from 'formik';
import { View } from 'react-native';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import Radio from '@/components/ui/buttons/radio';
import ListItemCard from '@/components/ui/cards/list-item-card';
import Input from '@/components/ui/inputs/input';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import SectionContainer from '@/components/ui/section-container';
import useModals from 'src/hooks/use-modals';
import * as Yup from 'yup';
import PermissionInformationModal from './permission-information-modal';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import { useState } from 'react';
import { Copy, Icon, TickCircle } from 'iconsax-react-native/src';
import { ButtonVariant } from '@/components/ui/buttons/button';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { StoreRoles, CREATE_INVITE, toAppUrl } from 'catlog-shared';

interface InviteStoreManagerModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
}

const InviteStoreManagerModal = ({ closeModal, ...props }: InviteStoreManagerModalProps) => {
  const { modals, toggleModal } = useModals(['permissionInfo']);
  const [inviteSent, setInviteSent] = useState(false);
  const [inviteLink, setInviteLink] = useState('');

  const createInviteReq = useApi({
    apiFunction: CREATE_INVITE,
    key: CREATE_INVITE.name,
    method: 'POST',
    autoRequest: false,
  });

  const form = useFormik({
    initialValues: {
      email: '',
      phone: {
        digits: '',
        code: '+234',
      },
      role: StoreRoles.OPERATOR,
      inviteMethod: 'email',
    },
    validationSchema,
    onSubmit: async values => {
      const { email, inviteMethod, phone, role } = values;

      const [res, error] = await createInviteReq.makeRequest({
        email: inviteMethod === 'email' ? email : undefined,
        phone: inviteMethod === 'phone' ? `${phone.code}-${phone.digits}` : undefined,
        role,
      });
      if (res) {
        setInviteLink(toAppUrl(`join-store?invite=${res?.data.id}`, true, EXPO_PUBLIC_PUBLIC_URL));
        setInviteSent(true);
      }
      if (error) {
        Toast.show({ type: 'error', text1: error?.body?.message });
      }
    },
  });

  const storeRole = [
    {
      title: StoreRoles.OWNER,
      description: 'Best for owners of the store with unrestricted controls',
    },
    {
      title: StoreRoles.ADMIN,
      description: 'Best for managers with full control and little restrictions',
    },
    {
      title: StoreRoles.OPERATOR,
      description: 'Best for managers with basic control access',
    },
  ];

  const setInviteMode = (mode: string) => {
    form.setFieldValue('inviteMethod', mode);
  };

  const handleModalAction = () => {
    if (inviteSent) {
      copyToClipboard(inviteLink);
    } else form.submitForm();
  };

  const handleClose = () => {
    closeModal();
    setInviteSent(false);
    form.resetForm();
  };

  const inviteMode = form.values.inviteMethod;

  return (
    <BottomModal
      {...props}
      closeModal={handleClose}
      title={'Invite Store Manager'}
      buttons={[
        {
          text: inviteSent ? 'Copy Invite link' : 'Invite',
          isLoading: createInviteReq.isLoading,
          onPress: () => handleModalAction(),
          variant: inviteSent ? ButtonVariant.LIGHT : ButtonVariant.PRIMARY,
          children: inviteSent && <Copy size={wp(20)} color={colors.primary.main} className="mr-10" />,
        },
      ]}>
      <View className="px-20 pb-20">
        {!inviteSent && (
          <>
            <Row className="justify-start mb-20">
              <Pressable className="flex-1 flex-row items-center" onPress={() => setInviteMode('email')}>
                <Radio active={inviteMode === 'email'} onClick={() => setInviteMode('email')} />
                <BaseText classes="ml-10">Email Address</BaseText>
              </Pressable>
              <Pressable className="flex-1 flex-row items-center" onPress={() => setInviteMode('phone')}>
                <Radio active={inviteMode === 'phone'} onClick={() => setInviteMode('phone')} />
                <BaseText classes="ml-10">Phone</BaseText>
              </Pressable>
            </Row>
            {inviteMode === 'phone' ? (
              <PhoneNumberInput
                {...getFieldvalues('phone', form)}
                onChange={value => form.setFieldValue('phone', value)}
              />
            ) : (
              <Input {...getFieldvalues('email', form)} label={'Email Address'} />
            )}

            <SectionContainer className="mt-15">
              {storeRole.map((item, index) => (
                <ListItemCard
                  key={index}
                  leftElement={
                    <Radio
                      onClick={() => form.setFieldValue('role', item.title)}
                      active={form.values.role === item.title}
                    />
                  }
                  title={item.title}
                  description={item.description}
                  descriptionProps={{ fontSize: 11, weight: 'regular', classes: 'text-black-muted', lineHeight: 14 }}
                  showBorder={index !== storeRole.length - 1}
                  rightElement={
                    <Pressable
                      onPress={() => {
                        form.setFieldValue('role', item.title);
                        toggleModal('permissionInfo');
                      }}>
                      <CircledIcon className="bg-white">
                        <ArrowRight size={wp(16)} currentColor={colors.primary.main} />
                      </CircledIcon>
                    </Pressable>
                  }
                />
              ))}
            </SectionContainer>
          </>
        )}

        {inviteSent && (
          <>
            <View className={'items-center justify-center my-30'}>
              <CircledIcon className="bg-accentGreen-pastel p-15">
                <CircledIcon className="bg-accentGreen-main p-25">
                  <TickCircle variant={'Bold'} size={wp(50)} color={colors.white} />
                </CircledIcon>
              </CircledIcon>
              <BaseText fontSize={18} type={'heading'} classes="text-center mt-10 max-w-[325px]">
                Your invite has{'\n'}been sent successfully
              </BaseText>
            </View>
          </>
        )}
      </View>
      <PermissionInformationModal
        isVisible={modals.permissionInfo}
        activePermission={form.values.role ?? StoreRoles.OWNER}
        closeModal={() => toggleModal('permissionInfo', false)}
      />
    </BottomModal>
  );
};

export default InviteStoreManagerModal;

const validationSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .when('inviteMethod', {
      is: 'email',
      then: () => Yup.string().email('Invalid email address').required('Email Address is required'),
    }),
  phone: Yup.object().when('inviteMethod', {
    is: 'phone',
    then: () => phoneValidation(),
  }),
  role: Yup.string().required('User role is required'),
});
