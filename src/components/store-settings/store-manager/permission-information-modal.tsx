import { useEffect, useState } from 'react';
import { Image, ScrollView, Text, View } from 'react-native';
import { BaseText, CircledIcon, Row, SelectionPill } from '@/components/ui';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import Accordion from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/orders/create/accordion-anchor';
import { StoreRoles } from 'catlog-shared';


interface PermissionInformationModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  activePermission: StoreRoles;
}

const PermissionInformationModal = ({ closeModal, activePermission, ...props }: PermissionInformationModalProps) => {
  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={`${ROLE_DATA[activePermission].title}s Permissions`}
      buttons={[{ text: 'Invite', onPress: () => {} }]}>
      <View>
        <View className="mt-20 px-20">
          <BaseText fontSize={18} type="heading">
            {ROLE_DATA[activePermission].title}
          </BaseText>
          <BaseText fontSize={11} className="text-black-muted ">
            {ROLE_DATA[activePermission].description}
          </BaseText>
        </View>

        <View className="px-20 py-10  mt-10 ">
          {ROLE_DATA[activePermission].permissions.map((permission, index) => (
            <Accordion
              classes="m-0"
              key={index}
              anchorElement={status => (
                <AccordionAnchor
                  classes="border-b border-grey-border py-10"
                  title={permission.scope}
                  isOpened={status}
                  type="plus"
                />
              )}
              ref={() => null}>
              <View>
                {permission.permissions.map((permission, index) => (
                  <Row key={index} className="items-center justify-between py-10 " style={{ gap: 10 }}>
                    <BaseText classes="text-black-muted" type="body">
                      • {permission}
                    </BaseText>
                  </Row>
                ))}
              </View>
            </Accordion>
          ))}
        </View>
      </View>
    </BottomModal>
  );
};

export const ROLE_DATA = {
  [StoreRoles.OWNER]: {
    value: 'OWNER',
    title: 'Owner',
    description: 'Best for owners of the store with unrestricted controls',
    permissions: [
      {
        scope: 'Dashboard',
        permissions: ['Can view analytics', 'Can view Tasks', 'Can view most viewed products'],
      },
      {
        scope: 'Products',
        permissions: ['Can view products', 'Can manage products'],
      },
      {
        scope: 'Orders',
        permissions: ['Can view analytics', 'Can view orders ', 'Can manage orders'],
      },
      {
        scope: 'Customers',
        permissions: ['Can view customers', 'Can manage customers'],
      },
      {
        scope: 'Subscriptions',
        permissions: ['Can view subscriptions', 'Can manage subscriptions', 'Can view payment history'],
      },
      {
        scope: 'Settings',
        permissions: [
          'Can view store managers',
          'Can manage store managers',
          'Can manage store details',
          'Can manage store categories',
        ],
      },
    ],
  },
  [StoreRoles.ADMIN]: {
    value: 'ADMIN',
    title: 'Admin',
    description: 'Best for mangers with full control and little restrictions',
    permissions: [
      {
        scope: 'Dashboard',
        permissions: ['Can view analytics', 'Can view Tasks', 'Can view most viewed products'],
      },
      {
        scope: 'Products',
        permissions: ['Can view products', 'Can manage products'],
      },
      {
        scope: 'Orders',
        permissions: ['Can view analytics', 'Can view orders ', 'Can manage orders'],
      },
      {
        scope: 'Customers',
        permissions: ['Can view customers', 'Can manage customers'],
      },
      {
        scope: 'Subscriptions',
        permissions: ['Can view subscription'],
      },
      {
        scope: 'Settings',
        permissions: [
          'Can view store managers',
          'Can manage store managers',
          'Can manage store details',
          'Can manage store categories',
        ],
      },
    ],
  },
  [StoreRoles.OPERATOR]: {
    value: 'OPERATOR',
    title: 'Operator',
    description: 'Best for managers with basic control access',
    permissions: [
      {
        scope: 'Dashboard',
        permissions: ['Can view analytics', 'Can view Tasks', 'Can view most viewed products'],
      },
      {
        scope: 'Products',
        permissions: ['Can view products', 'Can manage products'],
      },
      {
        scope: 'Orders',
        permissions: ['Can view orders ', 'Can manage orders'],
      },
      {
        scope: 'Customers',
        permissions: ['Can view customers', 'Can manage customers'],
      },
      {
        scope: 'Subscriptions',
        permissions: ['Can view subscription'],
      },
      {
        scope: 'Settings',
        permissions: ['Can view store managers', 'Can manage store categories'],
      },
    ],
  },
};

export default PermissionInformationModal;
