import React from 'react';
import { delay, wp } from '@/assets/utils/js';
import { CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { ArrowRight } from '@/components/ui/icons';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import colors from '@/theme/colors';
import { useState } from 'react';
import { View } from 'react-native';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import Radio from '@/components/ui/buttons/radio';
import ListItemCard from '@/components/ui/cards/list-item-card';
import SectionContainer from '@/components/ui/section-container';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import * as Yup from 'yup';
import PermissionInformationModal from './permission-information-modal';
import useFluxState from 'src/hooks/use-flux-state';
import Toast from 'react-native-toast-message';
import { StoreRoles, CREATE_INVITE, UPDATE_TEAM_MEMBER, TeamMember } from 'catlog-shared';

interface UpdateStoreManagerModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  teamMember: TeamMember;
  onComplete: VoidFunction;
}

const EditStoreManagerModal = ({ closeModal, teamMember, onComplete, ...props }: UpdateStoreManagerModalProps) => {
  const { modals, toggleModal } = useModals(['permissionInfo']);
  const [currentRole, setCurrentRole] = useFluxState(teamMember?.role as StoreRoles);

  const updateTeamMemberReq = useApi({
    apiFunction: UPDATE_TEAM_MEMBER,
    key: UPDATE_TEAM_MEMBER.name,
    method: 'PUT',
    autoRequest: false,
  });

  const storeRole = [
    {
      title: StoreRoles.OWNER,
      description: 'Best for owners of the store with unrestricted controls',
    },
    {
      title: StoreRoles.ADMIN,
      description: 'Best for managers with full control and little restrictions',
    },
    {
      title: StoreRoles.OPERATOR,
      description: 'Best for managers with basic control access',
    },
  ];

  const saveChanges = async () => {
    const [res, error] = await updateTeamMemberReq.makeRequest({
      role: currentRole,
      invite_id: teamMember?.id,
      user_id: teamMember?.user,
    });

    if (res) {
      Toast.show({ type: 'success', text1: 'Store Manager updated successfully' });
      await delay(2000);
      closeModal();
      onComplete();
    }
    if (error) {
      Toast.show({ type: 'error', text1: error?.message });
    }
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Edit Store Manager'}
      buttons={[
        {
          text: 'Save Changes',
          isLoading: updateTeamMemberReq.isLoading,
          onPress: () => saveChanges(),
        },
      ]}>
      <View className="px-20 pb-20">
        <>
          <SectionContainer className="mt-15">
            {storeRole.map((item, index) => (
              <ListItemCard
                key={index}
                leftElement={<Radio onClick={() => setCurrentRole(item.title)} active={currentRole === item.title} />}
                title={item.title}
                description={item.description}
                descriptionProps={{ fontSize: 11, weight: 'regular', classes: 'text-black-muted', lineHeight: 14 }}
                showBorder={index !== storeRole.length - 1}
                rightElement={
                  <Pressable
                    onPress={() => {
                      setCurrentRole(item.title);
                      toggleModal('permissionInfo');
                    }}>
                    <CircledIcon className="bg-white">
                      <ArrowRight size={wp(16)} currentColor={colors.primary.main} />
                    </CircledIcon>
                  </Pressable>
                }
              />
            ))}
          </SectionContainer>
        </>
      </View>
      <PermissionInformationModal
        isVisible={modals.permissionInfo}
        activePermission={currentRole ?? StoreRoles.OWNER}
        closeModal={() => toggleModal('permissionInfo', false)}
      />
    </BottomModal>
  );
};

export default EditStoreManagerModal;
