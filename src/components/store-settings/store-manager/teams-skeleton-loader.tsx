import { hp, wp } from '@/assets/utils/js';
import React from 'react';
import { Row } from '@/components/ui';
import Column from '@/components/ui/column';
import Shimmer from '@/components/ui/shimmer';
import { View } from 'react-native';

const dummyRow = new Array(3).fill(0);

interface StorefrontSkeletonLoaderProps {}

const TeamsSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return dummyRow.map((_, index) => (
    <View key={index} className="p-15 mb-15 bg-white rounded-12 border border-grey-border">
      <Row classes="pt-10">
        <Row>
          <Shimmer borderRadius={wp(999)} height={hp(40)} width={wp(40)} />
          <Shimmer classes="ml-10" borderRadius={wp(10)} height={hp(15)} width={wp(70)} />
        </Row>
      </Row>
    </View>
  ));
};

export default TeamsSkeletonLoader;
