import { Edit2, Trash } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { getFieldvalues, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import colors from '@/theme/colors';
import Pressable from '@/components/ui/base/pressable';
import { useEffect, useRef, useState } from 'react';
import SelectDropdown, { DropDownItem, DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { FormikProps } from 'formik';
import { CURRENCIES, ORDER_FEE_TYPES } from 'node_modules/catlog-shared/dist';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import { CreateOrderFormParams } from 'src/screens/orders/order.types';
import { useCurrencyConverter } from 'src/hooks/useCurrencyConverter';
import Input from 'src/components/ui/inputs/input';
import { FieldComponentModalProps } from './field-component-modal';

interface FeesCardProps {
  data: {
    value: string;
    price: number;
  };
  updateOption: (
    option: {
      value: string;
      price: number;
    },
    index: number,
  ) => void;
  index?: number;
  form: FormikProps<any>;
  currency: CURRENCIES;
  selectedComponent: FieldComponentModalProps['selectedComponent'];
  onPressDelete?: VoidFunction;
  showPrices?: boolean
}

const CustomFormOptionsCard = ({
  data,
  index,
  form,
  selectedComponent,
  currency,
  showPrices,
  onPressDelete,
}: FeesCardProps) => {
  const [displayMode, setDisplayMode] = useState<'form' | 'card'>('form');

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const toggleDisplayModeToCard = () => {
    if (data.value && data.price) {
      setDisplayMode('card');
    }
  };

  const formatCurrency = useCurrencyConverter(form?.values?.currency);

  return (
    <View className=''>
      <View className='mt-10' style={{ display: displayMode === 'form' ? 'flex' : 'none' }}>
        <Row className="gap-x-10 mb-15">
          <View className="flex-1">
            <Input
              useBottomSheetInput
              label={`${selectedComponent.title} Option ${index + 1}`}
              key={index}
              {...getFieldvalues(`options.${index}.value`, form)}
              onBlur={toggleDisplayModeToCard}
             
            />
          </View>
          {showPrices&&<View className="flex-1">
            <LeftLabelledInput
              useBottomSheetInput
              leftText={currency}
              label={'Price'}
              keyboardType={'number-pad'}
              {...getFieldvalues(`options.${index}.price`, form,"number", true)}
              onBlur={toggleDisplayModeToCard}
            />
          </View>}
        </Row>
      </View>
      <View style={{ display: displayMode === 'card' ? 'flex' : 'none' }}>
        <Row className="py-[13px] px-15 mb-15 rounded-[10px] border border-grey-border bg-grey-bgOne">
          <View className="flex-1">
            <BaseText>{data.value??""}</BaseText>
            {(data.price !== undefined && showPrices )&& (
              <BaseText fontSize={11} weight={'semiBold'} classes="mt-5">
                {formatCurrency(data.price, currency)}
              </BaseText>
            )}
          </View>
          <Pressable onPress={() => setDisplayMode('form')}>
            <CircledIcon className="bg-white mr-10">
              <Edit2 size={wp(18)} color={colors.black.placeholder} />
            </CircledIcon>
          </Pressable>
          <Pressable onPress={onPressDelete}>
            <CircledIcon className="bg-white">
              <Trash size={wp(18)} color={colors.black.placeholder} />
            </CircledIcon>
          </Pressable>
        </Row>
      </View>
    </View>
  );
};

export default CustomFormOptionsCard;
