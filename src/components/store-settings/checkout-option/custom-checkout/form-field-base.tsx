import { LayoutAnimation, View } from 'react-native';
import { ReactNode, useState } from 'react';
import { ChevronDown, ChevronUp } from '@/components/ui/icons';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { BaseText, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import cx from 'classnames';
import Radio from '@/components/ui/buttons/radio';
import * as Animatable from 'react-native-animatable';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import classNames from 'classnames';
import MoreOptions, { MoreOptionElementProps } from '@/components/ui/more-options';

export enum FORM_FIELD_TYPE {
  TEXT_INPUT = 'Text Input',
  NUMBER_INPUT = 'Number Input',
  DROP_DOWN = 'Dropdown',
  RADIO_INPUT = 'Radio Input',
  TEXT_AREA = 'Text Area',
}

interface FormFieldBaseProps {
  children: ReactNode;
  title: string;
  fieldTypeName: FORM_FIELD_TYPE;
  moreOptions: MoreOptionElementProps[];
}

const FormFieldBase = ({ children, title, moreOptions, fieldTypeName }: FormFieldBaseProps) => {
  const [expanded, setExpanded] = useState(false);

  const handleExpand = () => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  return (
    <View className="py-10 px-12 rounded-10 bg-grey-bgOne">
      <Pressable className="flex-row items-center justify-start" style={{ gap: wp(4) }} onPress={handleExpand}>
        <View className={cx({ 'rotate-180': expanded })}>
          <ChevronDown size={wp(18)} strokeWidth={2} currentColor={colors.black.muted} />
        </View>
        <View className="flex-1 flex-row items-center justify-start mx-5">
          <View>
            <BaseText type="heading" classes="text-black-main ">
              {title}
            </BaseText>
          </View>
          <View className="bg-white rounded-15 py-6 px-10 ml-10">
            <BaseText fontSize={11} weight="semiBold">
              {fieldTypeName.toUpperCase()}
            </BaseText>
          </View>
        </View>
        <MoreOptions options={moreOptions ?? []} />
      </Pressable>
      {expanded && (
        <View
          className="bg-white  border border-grey-border p-12 mt-10 rounded-10"
          // animation="zoomIn"
          // duration={50}
          >
          {children}
        </View>
      )}
    </View>
  );
};

export default FormFieldBase;

export const TextField = ({
  typeText,
  title,
  moreOptions,
}: {
  title: string;
  typeText: FORM_FIELD_TYPE;
  moreOptions: MoreOptionElementProps[];
}) => {
  return (
    <FormFieldBase title={title} fieldTypeName={typeText} moreOptions={moreOptions}>
      <View className={classNames('p-15 rounded-12 border border-grey-border', {})}>
        <BaseText classes="text-black-placeholder">{title}</BaseText>
      </View>
    </FormFieldBase>
  );
};

export const RadioField = ({
  title,
  options,
  moreOptions,
}: {
  title: string;
  options: string[];
  moreOptions: MoreOptionElementProps[];
}) => {
  return (
    <FormFieldBase title={title} fieldTypeName={FORM_FIELD_TYPE.RADIO_INPUT} moreOptions={moreOptions}>
      <BaseText fontSize={12} weight="medium">
        {title}
      </BaseText>
      {options.map(item => (
        <Row key={item} className="justify-start mt-10">
          <Radio active={false} />
          <BaseText fontSize={12} classes="text-black-placeholder ml-5">
            {item}
          </BaseText>
        </Row>
      ))}
    </FormFieldBase>
  );
};

export const DropdownInputField = ({
  title,
  options,
  moreOptions,
}: {
  title: string;
  options: string[];
  moreOptions: MoreOptionElementProps[];
}) => {
  return (
    <FormFieldBase title={title} fieldTypeName={FORM_FIELD_TYPE.DROP_DOWN} moreOptions={moreOptions}>
      <BaseText fontSize={12} weight="medium">
        {title}
      </BaseText>
      <SelectDropdown
        containerClasses="mt-10"
        onPressItem={() => {}}
        selectedItem=""
        label="Select option"
        items={options.map(item => ({ value: item, label: item }))}
      />
    </FormFieldBase>
  );
};
