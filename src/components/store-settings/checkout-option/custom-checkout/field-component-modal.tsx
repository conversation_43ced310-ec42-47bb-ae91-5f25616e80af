import { ScrollView, View } from 'react-native';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import Input from '@/components/ui/inputs/input';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import { Add, Trash } from 'iconsax-react-native/src';
import { getFieldvalues, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { FormikProps, useFormik } from 'formik';
import { ComponentFormValues } from './custom-checkout-form';
import * as Yup from 'yup';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { Fragment, ReactNode, useEffect, useState } from 'react';
import Pressable from '@/components/ui/base/pressable';
import useLayoutHeight from 'src/hooks/use-layout-height';
import { OrderItem, ProductItemInterface, INPUT_TYPE } from 'catlog-shared';
import CustomFormOptionsCard from './options-card';
import useAuthContext from 'src/contexts/auth/auth-context';

export interface FieldComponentModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  hasOption?: boolean;
  selectedComponent?: {
    title: string;
    type: INPUT_TYPE;
    subtitle: string;
    leftIcon: ReactNode;
    hasOptions: boolean;
    onPress: VoidFunction;
  };
  value?: ComponentFormValues;
  onSubmit: (values: ComponentFormValues) => void;
  onSubmitEditing: (values: ComponentFormValues) => void;
  isEdit?: boolean;
 }

const FieldComponentModal = ({
  closeModal,
  onSubmit,
  onSubmitEditing,
  selectedComponent,
  value,
  isEdit,
  ...props
}: FieldComponentModalProps) => {
  const { onLayout, flexStyle } = useLayoutHeight(0);
  const { store } = useAuthContext();
  const currency = store?.currencies?.default;

  const hasPricedOptions = selectedComponent?.hasOptions && value.options?.some(o => o.price.toString() !== '0');

  const [showPrice, setShowPrice] = useState(hasPricedOptions);

  const getValidationSchema = (hasOptions: boolean) => {
    return Yup.object().shape({
      name: Yup.string().required(),
      label: Yup.string().required(),
      ...(hasOptions && {
        options: Yup.array()
          .of(Yup.object().shape({
            value: Yup.string().required('Option is required'),
            price: Yup.number().optional().min(0)
          }))
          .min(2, 'At least two options are required'),
      }),
    });
  };

  const form = useFormik<ComponentFormValues>({
    initialValues: {
      type: value?.type ?? undefined,
      name: value?.name ?? '',
      is_required: value?.is_required ?? true,
      label: value?.label ?? '',
      options: value?.options ?? [],
    },
    onSubmit: values => {

      console.log(values,"values================>")
      if (isEdit) {
        onSubmitEditing?.(values);
      } else {
        onSubmit?.(values);
      }
      closeModal();
      form.resetForm();
    },
    validationSchema: getValidationSchema(selectedComponent?.hasOptions!),
  });

  useEffect(() => {
    if (selectedComponent) {
      form.setFieldValue('type', selectedComponent.type);
    }
  }, [selectedComponent]);

  useEffect(() => {
    if (value && isEdit) {
      form.setValues(value);
    }
  }, [value]);

  const addOption = () => {
    form.setFieldValue('options', [...form?.values?.options!, '']);
  };

  const deleteOption = (index: number) => {
    form.setFieldValue(
      'options',
      form?.values?.options?.filter((_, i) => i !== index),
    );
  };

  const handleFormLabelChange = (label: string) => {
    form.setFieldValue('label', label);
    form.setFieldValue('name', label.toLowerCase().replace(/\s/g, '_'));
  };

  const handleShowPricesChange = (state: boolean) => {
    if (!state) {
      form.setFieldValue(
        'options',
        form.values.options.map(o => ({ ...o, price: 0 })),
      );
    }

    setShowPrice(state);
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[{ text: isEdit ? 'Update component' : 'Add Component', onPress: () => form.handleSubmit() }]}
      title={isEdit ? 'Edit form component' : 'Add Form Component'}
      containerStyle={flexStyle}
      innerStyle={flexStyle}
      modalStyle={flexStyle}>
      <ScrollView>
        <View onLayout={onLayout} className="mx-20 pb-20 pt-10">
          <Input
            label={`${selectedComponent?.title} Label`}
            {...getFieldvalues('label', form)}
            onChangeText={handleFormLabelChange}
          />
          {selectedComponent?.hasOptions && (
            <View className="mt-15">
              <BaseText weight="medium" classes="text-black-secondary mb-10">
                Options
              </BaseText>
              {form?.errors?.options && typeof form.errors.options === 'string' && (
                <View className="items-center justify-center bg-accentRed-pastel p-8 rounded-8 mt-15">
                  <BaseText weight="semiBold" classes="text-accentRed-main">
                    {form?.errors?.options}
                  </BaseText>
                </View>
              )}
              {form.values.options?.length > 0 && (
                <>
                  <Row className='mb-20 mt-10'>
                    <BaseText classes='text-black-placeholder'>Show prices</BaseText>
                    <CustomSwitch value={showPrice} onValueChange={handleShowPricesChange} />
                  </Row>
                </>
              )}
              {form.values.options?.map((item, index) => (
                <CustomFormOptionsCard
                  selectedComponent={selectedComponent}
                  currency={currency}
                  data={item}
                  form={form}
                  updateOption={d => null}
                  key={index}
                  index={index}
                  showPrices={showPrice}
                  onPressDelete={()=>deleteOption(index)}
                />
              ))}
              <WhiteCardBtn
                onPress={addOption}
                className="bg-white"
                leftIcon={<Add color={colors.black.placeholder} size={wp(14)} />}>
                Add options
              </WhiteCardBtn>
            </View>
          )}
          <Row className="mt-15">
            <BaseText weight="medium" classes="text-black-muted">
              Require customers to fill this field
            </BaseText>
            <CustomSwitch value={form.values.is_required} onValueChange={v => form.setFieldValue('is_required', v)} />
          </Row>
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default FieldComponentModal;
