import { ScrollView, View } from 'react-native';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useFormik } from 'formik';
import { useRef, useState } from 'react';
import { DropDownInput, NumberInput, RadioInput, TextAreaInput, TextInput } from '@/components/ui/icons';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { BaseText, Row } from '@/components/ui';
import FormFieldBase, { DropdownInputField, FORM_FIELD_TYPE, RadioField, TextField } from './form-field-base';
import Pressable from '@/components/ui/base/pressable';
import {
  Add,
  AlignLeft,
  ArrowCircleDown,
  ArrowCircleDown2,
  Card,
  Edit2,
  Hashtag,
  Notepad2,
  Radio,
  Text,
  Trash,
} from 'iconsax-react-native/src';
import BottomModal from '@/components/ui/modals/bottom-modal';
import useModals from 'src/hooks/use-modals';
import CircledIcon from '@/components/ui/circled-icon';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { useApi } from 'src/hooks/use-api';
import * as Yup from 'yup';
import FieldComponentModal from './field-component-modal';
import EmptyState from '@/components/ui/empty-states/empty-state';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import Toast from 'react-native-toast-message';
import { UPDATE_STORE_DETAILS, UpdateStoreParams, INPUT_TYPE, StoreInterface } from 'catlog-shared';
import BaseScrollView from 'src/components/ui/base/base-scrollview';

export type CustomCheckoutFormItem = any;

export type ComponentFormValues = {
  type?: INPUT_TYPE;
  name: string;
  is_required: boolean;
  label?: string;
  options?: { price: number; value: string }[];
  currency?: string;
};

export type FormComponent = {
  name: string;
  type: INPUT_TYPE;
  img: string;
  hasOptions: boolean;
};

function CustomCheckoutForm() {
  const [selectedComponentIndex, setSelectedComponentIndex] = useState<number | null>(null);
  const { modals, toggleModal } = useModals(['addFormComponent', 'fieldComponent']);
  const { store, updateStore } = useAuthContext();
  const scrollRef = useRef<ScrollView>(null);

  const updateStoreDetailsRequest = useApi({
    key: 'update-checkout-details',
    apiFunction: UPDATE_STORE_DETAILS,
    method: 'PUT',
  });

  const form = useFormik<{ components: CustomCheckoutFormItem[]; selectedFieldType?: INPUT_TYPE }>({
    initialValues: {
      components: store?.configuration?.custom_checkout_form ?? [],
    },
    onSubmit: async values => {
      const [res] = await updateStoreDetailsRequest.makeRequest({
        id: store?.id!,
        configuration: { ...store?.configuration, custom_checkout_form: values.components },
      });

      if (res) {
        Toast.show({ text1: 'Custom checkout form saved successfully' });
        updateStore({ configuration: { ...store?.configuration!, custom_checkout_form: values.components } });
      }
    },
    validationSchema: Yup.object().shape({}),
  });

  const hasFormItems = form.values.components.length > 0;

  const openFieldComponentModal = (selectedFieldType: string) => {
    form.setFieldValue('selectedFieldType', selectedFieldType);
    toggleModal('addFormComponent', false);
    setTimeout(() => {
      toggleModal('fieldComponent');
    }, 600);
  };

  const handleRemove = (index: number) => {
    form.setFieldValue(
      'components',
      form.values.components.filter((_, i) => i !== index),
    );
    // form.submitForm();
  };

  const addNewFormField = (values: ComponentFormValues) => {
    form.setFieldValue('components', [...form.values.components, { ...values, options: values.options }]);
  };

  const handleEdit = (index: number) => {
    setSelectedComponentIndex(index);
    form.setFieldValue('selectedFieldType', form.values.components[index].type);
    setTimeout(() => {
      toggleModal('fieldComponent');
    }, 600);
  };

  const selectedFormField =
    selectedComponentIndex !== null ? form.values.components[selectedComponentIndex] : undefined;

  const saveEdit = (values: ComponentFormValues) => {
    form.setFieldValue(
      'components',
      form.values.components.map((c, i) => (i === selectedComponentIndex ? { ...values, options: values.options } : c)),
    );
    setSelectedComponentIndex(null);
  };

  const moreOptionFunc = (index: number) => [
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Edit2 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Edit form field
          </BaseText>
        </Row>
      ),
      onPress: () => handleEdit(index),
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Trash size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Remove form field
          </BaseText>
        </Row>
      ),
      onPress: () => handleRemove(index),
    },
  ];

  const renderFormFields = (
    type: INPUT_TYPE,
    title: string,
    index: number,
    options?: { value: string; price?: number }[],
  ) => {
    const optionsToStrings = () => {
      return (options ?? []).map(
        o => `${o.value} ${o.price > 0 ? toCurrency(o.price, store.currencies.default) : ''} `,
      );
    };

    switch (type) {
      case INPUT_TYPE.RADIO:
        return <RadioField title={title} options={optionsToStrings()} moreOptions={moreOptionFunc(index)} />;
        break;
      case INPUT_TYPE.DROPDOWN:
        return <DropdownInputField title={title} options={optionsToStrings()} moreOptions={moreOptionFunc(index)} />;
        break;
      case INPUT_TYPE.NUMBER:
        return <TextField title={title} typeText={FORM_FIELD_TYPE.NUMBER_INPUT} moreOptions={moreOptionFunc(index)} />;
        break;
      case INPUT_TYPE.TEXT:
        return <TextField title={title} typeText={FORM_FIELD_TYPE.TEXT_INPUT} moreOptions={moreOptionFunc(index)} />;
        break;
      case INPUT_TYPE.TEXTAREA:
        return <TextField title={title} typeText={FORM_FIELD_TYPE.TEXT_AREA} moreOptions={moreOptionFunc(index)} />;
        break;

      default:
        break;
    }
  };

  const inputOptionMapped = [
    {
      title: FORM_FIELD_TYPE.TEXT_INPUT,
      type: INPUT_TYPE.TEXT,
      subtitle: 'Ask basic questions to gather text data',
      leftIcon: (
        <CircledIcon className="bg-accentDarkRed-pastel p-10">
          <TextInput
            size={wp(20)}
            color={colors.accentDarkRed.main}
            style={{ marginVertical: 0, paddingVertical: 0, paddingLeft: 0 }}
          />
        </CircledIcon>
      ),
      hasOptions: false,
      onPress: () => {},
    },
    {
      title: FORM_FIELD_TYPE.NUMBER_INPUT,
      type: INPUT_TYPE.NUMBER,
      subtitle: 'Use to get basic numerical data',
      leftIcon: (
        <CircledIcon className="bg-primary-pastel p-10">
          <NumberInput size={wp(20)} color={colors.primary.main} />
        </CircledIcon>
      ),
      hasOptions: false,
      onPress: () => {},
    },
    {
      title: FORM_FIELD_TYPE.DROP_DOWN,
      type: INPUT_TYPE.DROPDOWN,
      subtitle: 'Collect predefined choices',
      leftIcon: (
        <CircledIcon className="bg-accentRed-pastel p-10">
          <DropDownInput size={wp(20)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      hasOptions: true,
      onPress: () => {},
    },
    {
      title: FORM_FIELD_TYPE.RADIO_INPUT,
      type: INPUT_TYPE.RADIO,
      subtitle: 'Select single-choice options',
      leftIcon: (
        <CircledIcon className="bg-accentGreen-pastel p-10">
          <RadioInput size={wp(20)} color={colors.accentGreen.main} />
        </CircledIcon>
      ),
      hasOptions: true,
      onPress: () => {},
    },
    {
      title: FORM_FIELD_TYPE.TEXT_AREA,
      type: INPUT_TYPE.TEXTAREA,
      subtitle: 'Use to get detailed text responses',
      leftIcon: (
        <CircledIcon className="bg-accentOrange-pastel p-10">
          <TextAreaInput size={wp(20)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      hasOptions: false,
      onPress: () => {},
    },
  ];

  return (
    <View className="flex-1">
      <AvoidKeyboard>
        <BaseScrollView
          className="flex-1 px-20"
          // ref={scrollRef}
          scrollRef={scrollRef}>
          <View className="pb-70">
            <View style={{ rowGap: hp(15) }}>
              {!hasFormItems && (
                <EmptyState
                  title="No custom forms have been created yet"
                  text="It seems you haven't created any forms yet. Start by adding a form item to collect the information you need!"
                  btnText="Create Your First Form"
                  onPressBtn={() => toggleModal('addFormComponent')}
                  icon={<Notepad2 variant={'Bulk'} size={wp(40)} color={colors.grey.muted} />}
                />
              )}
              {form.values.components.map((item, index) => (
                <View key={index}>{renderFormFields(item.type, item.label ?? item.name, index, item.options)}</View>
              ))}
              {hasFormItems && (
                <Pressable
                  onPress={() => toggleModal('addFormComponent')}
                  className="flex-row items-center justify-center border border-grey-border rounded-12 p-15 mt-15">
                  <Add size={wp(14)} color={colors.primary.main} />
                  <BaseText weight="medium" classes="text-primary-main">
                    Add new option
                  </BaseText>
                </Pressable>
              )}
            </View>
          </View>
        </BaseScrollView>
        <FixedBtnFooter
          buttons={[
            {
              text: 'Save Checkout Form',
              onPress: () => form.submitForm(),
              isLoading: updateStoreDetailsRequest?.isLoading,
            },
          ]}
        />
      </AvoidKeyboard>
      <BottomModal
        isVisible={modals.addFormComponent}
        closeModal={() => toggleModal('addFormComponent', false)}
        showButton={false}
        // containerStyle={{ flex: 1 }}
        // contentContainerClass="flex-1"
        title={'Add a form component'}>
        <View className="mx-20">
          {inputOptionMapped.map(item => (
            <ListItemCard
              key={item.title}
              onPress={() => openFieldComponentModal(item.type)}
              leftElement={item.leftIcon}
              title={item.title}
              description={item.subtitle}
            />
          ))}
        </View>
      </BottomModal>
      <FieldComponentModal
        isVisible={modals.fieldComponent}
        onSubmit={addNewFormField}
        onSubmitEditing={saveEdit}
        isEdit={selectedComponentIndex !== null}
        // form={form}
        selectedComponent={inputOptionMapped.find(({ type }) => type === form.values.selectedFieldType)}
        value={{ ...selectedFormField!, options: selectedFormField?.options }}
        hasOption={inputOptionMapped.find(({ type }) => type === form.values.selectedFieldType)?.hasOptions}
        closeModal={() => toggleModal('fieldComponent', false)}
      />
    </View>
  );
}

export default CustomCheckoutForm;
