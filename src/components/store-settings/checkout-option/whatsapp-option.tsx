import { View } from 'react-native';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import CustomizationCard from '@/components/ui/cards/customization-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Input from '@/components/ui/inputs/input';
import Pressable from '@/components/ui/base/pressable';
import { Add, Edit2, Trash, Whatsapp } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Fragment, useEffect, useMemo, useState } from 'react';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import { FormikProps } from 'formik';
import { CustomUpdateStoreParams, generateOption } from 'src/screens/store-settings/checkout-options';
import ListItemCard from '@/components/ui/cards/list-item-card';
import * as Animatable from 'react-native-animatable';
import { phoneObjectFromString, phoneObjectToString, removeCountryCode, StoreInterface, WhatsappCheckoutOption } from 'catlog-shared';

interface WhatsappOptionProps {
  form: FormikProps<CustomUpdateStoreParams>;
  store: StoreInterface;
  toggleWhatsAppCheckout: (state: boolean) => Promise<any>;
}

const WhatsappOption = ({ form,store, toggleWhatsAppCheckout }: WhatsappOptionProps) => {
  const whatsappCheckoutEnabled = store.configuration.whatsapp_checkout_enabled;
  const options = form.values?.whatsapp ?? [];
  const setOptions = (options: WhatsappCheckoutOption[]) => {
    form.setFieldValue('whatsapp', options);
  };

  const updateOption = (option: WhatsappCheckoutOption, index: number) => {
    const optionsCopy = [...options];
    optionsCopy[index] = option;

    setOptions(optionsCopy);
  };

  const deleteOption = (index: number) => {
    const optionsCopy = [...options];
    optionsCopy.splice(index, 1);

    setOptions(optionsCopy);
  };

  const addOption = () => {
    const optionsCopy = [...options, generateOption()];
    setOptions(optionsCopy);
  };

  return (
    <View>
      <ListItemCard
        leftElement={
          <CircledIcon iconBg="bg-accentGreen-pastel p-10">
            <Whatsapp variant={'Bold'} size={wp(16)} color={colors.accentGreen.main} />
          </CircledIcon>
        }
        rightElement={
          <CustomSwitch
            value={whatsappCheckoutEnabled}
            onValueChange={toggleWhatsAppCheckout}
          />
        }
        className="py-24"
        titleProps={{ fontSize: 15, weight: 'bold', type: 'heading' }}
        title={'Whatsapp Options'}
        description={'Phone numbers customers can checkout to'}
        descriptionProps={{ fontSize: 12, weight: 'regular' }}
        disabled={true}
        showBorder={false}
      />

      {whatsappCheckoutEnabled && (
        <Animatable.View animation={'fadeIn'} duration={400} className="mt-0">
          {options?.map((item, index) => (
            <WhatsappContactCard
              index={index}
              whatsappInfo={item}
              key={item.id}
              updateOption={updateOption}
              deleteOption={deleteOption}
            />
          ))}
          <Pressable className="self-start py-8 mt-5" onPress={addOption}>
            <Row className=" justify-start">
              <Add size={wp(14)} color={colors.primary.main} />
              <BaseText weight={'medium'} classes="text-primary-main ml-2">
                Add New Option
              </BaseText>
            </Row>
          </Pressable>
        </Animatable.View>
      )}
    </View>
  );
};

interface WhatsappContactCardProps {
  whatsappInfo: WhatsappCheckoutOption;
  updateOption: (option: WhatsappCheckoutOption, index: number) => void;
  deleteOption: (index: number) => void;
  index: number;
}

const WhatsappContactCard = ({ whatsappInfo, index, updateOption, deleteOption }: WhatsappContactCardProps) => {
  const [displayMode, setDisplayMode] = useState<'form' | 'card'>('form');

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const phone = useMemo(() => {
    return phoneObjectFromString(whatsappInfo.phone ?? '');
  }, [whatsappInfo])

  const toggleDisplayModeToCard = () => {
    if (whatsappInfo.label && phone.digits) {
      console.log("setDisplayMode('card');")
      setDisplayMode('card');
    }
  };

  return (
    <View>
      {displayMode === 'form' && (
        <Row className="gap-x-10 mt-10">
          <View className="flex-1">
            <Input
              label="Option label"
              value={whatsappInfo.label}
              onChangeText={value => updateOption({ ...whatsappInfo, label: value }, index)}
              onBlur={toggleDisplayModeToCard}
              onSubmitEditing={toggleDisplayModeToCard}
            />
          </View>
          <View className="flex-1">
            <PhoneNumberInput
              value={phone}
              onChange={value => updateOption({ ...whatsappInfo, phone: phoneObjectToString(value) }, index)}
              onSubmitEditing={toggleDisplayModeToCard}
              onBlur={toggleDisplayModeToCard}
            />
          </View>
        </Row>
      )}
      {displayMode === 'card' && (
        <CustomizationCard
          textTop={whatsappInfo.label}
          textBottom={removeCountryCode(whatsappInfo?.phone ?? '')}
          className="mt-15"
          textTopProps={{ weight: 'medium' }}
          textBottomProps={{ weight: 'regular', classes: 'text-black-placeholder' }}
          rightElement={
            <Fragment>
              <Pressable onPress={() => setDisplayMode('form')}>
                <CircledIcon className="bg-white mr-10">
                  <Edit2 size={wp(18)} color={colors.black.placeholder} />
                </CircledIcon>
              </Pressable>
              <Pressable onPress={() => deleteOption(index)}>
                <CircledIcon className="bg-white">
                  <Trash size={wp(18)} color={colors.black.placeholder} />
                </CircledIcon>
              </Pressable>
            </Fragment>
          }
        />
      )}
    </View>
  );
};

export default WhatsappOption;
