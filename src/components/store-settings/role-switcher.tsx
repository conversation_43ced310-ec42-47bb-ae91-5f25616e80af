import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { StoreRoles } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import { Profile } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import Button from '@/components/ui/buttons/button';
import Radio from '@/components/ui/buttons/radio';
import ListItemCard from '@/components/ui/cards/list-item-card';
import SectionContainer from '@/components/ui/section-container';
import Toast from 'react-native-toast-message';

const RoleSwitcher = () => {
  const { userRole, store, user, updateStore } = useAuthContext();
  const [selectedRole, setSelectedRole] = useState<StoreRoles>(userRole);

  const roles = [
    {
      title: StoreRoles.OWNER,
      description: 'Best for owners of the store with unrestricted controls',
    },
    {
      title: StoreRoles.ADMIN,
      description: 'Best for managers with full control and little restrictions',
    },
    {
      title: StoreRoles.OPERATOR,
      description: 'Best for managers with basic control access',
    },
  ];

  const simulateRole = () => {
    if (selectedRole === StoreRoles.OWNER) {
      // For OWNER role, we need to set the store owner to the current user's ID
      updateStore({ owner: user.id });
      Toast.show({ type: 'success', text1: `Role switched to ${selectedRole}` });
    } else {
      // For other roles, we need to modify the owners array
      const updatedOwners = store.owners || [];
      
      // Find if the current user is already in the owners array
      const currentUserIndex = updatedOwners.findIndex(owner => owner.user === user.id);
      
      if (currentUserIndex >= 0) {
        // Update the existing entry
        updatedOwners[currentUserIndex] = {
          ...updatedOwners[currentUserIndex],
          role: selectedRole
        };
      } else {
        // Add a new entry
        updatedOwners.push({
          user: user.id,
          role: selectedRole,
          email: user.email,
          name: user.name,
        });
      }
      
      // If switching to a non-owner role, we need to make sure the owner is different
      if (store.owner === user.id) {
        // Set a dummy owner ID
        updateStore({ 
          owner: 'dummy-owner-id',
          owners: updatedOwners
        });
      } else {
        updateStore({ owners: updatedOwners });
      }
      
      Toast.show({ type: 'success', text1: `Role switched to ${selectedRole}` });
    }
  };

  return (
    <View style={{ padding: 20, backgroundColor: '#f5f5f5', marginVertical: 10, borderRadius: 8 }}>
      <Row className="items-center mb-10">
        <CircledIcon iconBg="bg-primary-pastel p-10 mr-10">
          <Profile variant={'Bold'} size={wp(20)} color={colors.primary.main} />
        </CircledIcon>
        <BaseText type={'heading'} fontSize={16}>Role Switcher (Testing Only)</BaseText>
      </Row>
      
      <BaseText fontSize={12} classes="mb-10">Current Role: {userRole}</BaseText>
      
      <SectionContainer className="mb-10">
        {roles.map((role, index) => (
          <ListItemCard
            key={index}
            leftElement={
              <Radio
                onClick={() => setSelectedRole(role.title)}
                active={selectedRole === role.title}
              />
            }
            title={role.title}
            description={role.description}
            descriptionProps={{ fontSize: 11, weight: 'regular', classes: 'text-black-muted', lineHeight: 14 }}
            showBorder={index !== roles.length - 1}
          />
        ))}
      </SectionContainer>
      
      <Button 
        text="Simulate Role" 
        onPress={simulateRole} 
        disabled={selectedRole === userRole}
      />
    </View>
  );
};

export default RoleSwitcher;
