import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import { BaseText, CircledIcon, Container, SelectionPill } from '@/components/ui';
import InfoBadge from '../info-badge';
import useAuthContext from 'src/contexts/auth/auth-context';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { ReactNode, useState } from 'react';
import { Paystack } from '@/components/ui/icons';
import { Coin1, MoneyTick } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { hideLoader, showError, showLoader, showSuccess, wp } from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import Separator from 'src/components/ui/others/separator';
import { CURRENCIES, PAYMENT_METHODS, UPDATE_STORE_PAYMENT_METHODS } from 'catlog-shared';
import SectionContainer from 'src/components/ui/section-container';

interface PaymentOptionsProps {}

interface PaymentOptionsDataType {
  icon: ReactNode;
  description: string;
  name: string;
  enabled: boolean;
  type: Partial<PAYMENT_METHODS>;
}

const PaymentOptions = ({}: PaymentOptionsProps) => {
  const { store, updateStore } = useAuthContext();
  const wallets = store.wallets;
  const [activeWallet, setActiveWallet] = useState(wallets[0].currency);

  const wallet = wallets.find(w => w.currency === activeWallet);

  const paymentOptions = store?.payment_options!;

  const updatePaymentMethodOptionRequest = useApi({
    key: 'update-store-payment-methods',
    apiFunction: UPDATE_STORE_PAYMENT_METHODS,
    method: 'PUT',
  });

  // const paymentOptionsData: PaymentOptionsDataType[] = (paymentOptions[store?.currencies.default] ?? [])?.map(o => ({
  //   ...o,
  //   ...paymentOptionData(activeWallet)[o.type],
  // }));
  const paymentOptionsData = (paymentOptions[wallet?.currency] ?? []).map(o => {
    return {
      ...o,
      ...paymentOptionData(wallet?.currency)[o.type],
    };
  });

  // console.log('paymentOptions ', JSON.stringify(paymentOptions))

  const updatePaymentOptions = async (type: PAYMENT_METHODS, enabled: boolean) => {
    showLoader('Updating payment options...');
    // const updatedPaymentOptions = paymentOptions?.map(o => (o.type === type ? { ...o, enabled } : o));

    const updateReqData = paymentOptions[wallet?.currency].map(o => (o.type === type ? { ...o, enabled } : o));

    const updatedPaymentOptions = {
      ...paymentOptions,
      [wallet.currency]: paymentOptions[wallet?.currency].map(o => (o.type === type ? { ...o, enabled } : o)),
    };

    const [response, error] = await updatePaymentMethodOptionRequest.makeRequest({
      id: store?.id,
      payment_options: paymentOptions[wallet?.currency].map(o => (o.type === type ? { ...o, enabled } : o)),
      currency: activeWallet,
    });

    if (response) {
      showSuccess('Payment options updated successfully');
      updateStore({ payment_options: updatedPaymentOptions });
    }

    if (error) {
      showError(error);
      Toast.show({ text1: "Error, We couldn't update the payment option", type: 'error' });
    }
    hideLoader();
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <View className="pb-40">
          <View className="mx-20">
            <InfoBadge
              text={'Allow customers pay directly on your storefront & select the payment methods you want enabled'}
            />
            <ListItemCard
              className="mt-15"
              leftElement={
                <CircledIcon className="bg-accentGreen-pastel2 p-10">
                  <MoneyTick variant={'Bold'} color={colors.accentGreen.main} size={wp(20)} />
                </CircledIcon>
              }
              title={'Enable Direct Checkout'}
              description={
                'Allow your customers pay before leaving your store, they will still be able to send orders as messages'
              }
              disabled
              descriptionProps={{ classes: 'mt-4 text-black-muted', weight: 'regular' }}
              rightElement={
                <View className="ml-10">
                  <CustomSwitch value={store.configuration?.direct_checkout_enabled} onValueChange={value => {}} />
                </View>
              }
              showBorder={false}
              alignStart
            />
            <Separator className="mx-0" />
          </View>
          <View>
            <BaseText fontSize={15} weight="bold" type="heading" classes="mx-20">
              Which payment options should customers see?
            </BaseText>
            {wallets.length > 1 && (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row py-20 px-20">
                  {wallets.map(i => (
                    <SelectionPill
                      key={i.currency}
                      onPress={() => setActiveWallet(i.currency)}
                      selected={activeWallet === i.currency}
                      title={i.currency}
                    />
                  ))}
                </View>
              </ScrollView>
            )}
          </View>
          <SectionContainer className="mx-20">
            {/* <InfoBadge text={'Manage how you price your products & what currencies customers see on your store'} /> */}
            {paymentOptionsData?.map(item => (
              <ListItemCard
                leftElement={<CircledIcon className="bg-white p-10">{item?.icon}</CircledIcon>}
                key={item.name}
                title={item.name}
                description={item.description}
                disabled
                descriptionProps={{ classes: 'mt-4 text-black-muted', weight: 'regular' }}
                rightElement={
                  <View className="ml-10">
                    <CustomSwitch
                      value={item.enabled}
                      onValueChange={value => updatePaymentOptions(item.type, value)}
                    />
                  </View>
                }
                showBorder
                alignStart
              />
            ))}
          </SectionContainer>
        </View>
      </ScrollView>
    </View>
  );
};

const paymentOptionData = (currency: CURRENCIES) => ({
  [PAYMENT_METHODS.PAYSTACK]: {
    icon: <Paystack />,
    description: 'Allow customers pay with card. Card payments have a 0.7% fee.',
    name: 'Paystack',
  },
  [PAYMENT_METHODS.TRANSFER]: {
    icon: <Coin1 variant={'Bold'} color={colors.accentRed.main} size={wp(15)} />,
    name: 'Pay with Bank Transfer',
    description: 'Collect payments with automatically verified transfers.',
  },
  [PAYMENT_METHODS.THEPEER]: {
    name: 'Thepeer',
    description: 'Allow customers pay directly from Kuda bank, Chipper cash, Pocketapp e.t.c',
  },
  [PAYMENT_METHODS.LEATHERBACK]: {
    name: 'Leatherback',
    description: currency === CURRENCIES.CAD ? 'Accept Interac payments' : 'Collect payments from cards or bank.',
  },
  [PAYMENT_METHODS.STRIPE]: {
    name: 'Stripe',
    description: 'Collect payments from cards and wallets.',
  },
  [PAYMENT_METHODS.STARTBUTTON]: {
    name: 'Paystack',
    description:
      currency === CURRENCIES.ZAR ? 'Collect payments with Card or EFT' : 'Collect Payments with M-Pesa or Card',
  },
  [PAYMENT_METHODS.ZILLA]: {
    name: 'Zilla',
    description: 'Get full payments at once while customers pay in parts. Split payments attract a 0.5% fee.',
  },
  [PAYMENT_METHODS.MONO_DIRECT_PAY]: {
    name: 'Mono Direct Pay',
    description: 'Collect payments with direct bank debits.',
  },
  [PAYMENT_METHODS.MOMO]: {
    name: 'Mobile Money',
    description: 'Collect payments from mobile money wallet.',
  },
});

export default PaymentOptions;
