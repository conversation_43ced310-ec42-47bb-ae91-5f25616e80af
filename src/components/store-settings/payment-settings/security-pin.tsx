import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, Container } from '@/components/ui';
import InfoBadge from '../info-badge';
import { ButtonVariant } from '@/components/ui/buttons/button';
import PinInput, { PinInputMethod } from '@/components/ui/inputs/pin-input';
import Input from '@/components/ui/inputs/input';
import PasswordInput from '@/components/ui/inputs/password-input';
import { useApi } from 'src/hooks/use-api';
import Separator from '@/components/ui/others/separator';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { useFormik } from 'formik';
import useAuthContext from 'src/contexts/auth/auth-context';
import * as Yup from 'yup';
import { getFieldvalues } from 'src/assets/utils/js';
import Toast from 'react-native-toast-message';
import { useEffect, useRef } from 'react';
import { UPDATE_SECURITY_PIN } from 'catlog-shared';

interface SecurityPinProps {}

const SecurityPin = ({}: SecurityPinProps) => {
  const { store, updateStore } = useAuthContext();

  const newPinInputRef = useRef<PinInputMethod>(null);
  const repeatPinInputRef = useRef<PinInputMethod>(null);

  useEffect(() => {
    setTimeout(() => {
      newPinInputRef.current?.focus();
    }, 300);
  }, []);

  const updateSecurityPinRequest = useApi({
    apiFunction: UPDATE_SECURITY_PIN,
    key: 'update-security-pin',
    method: 'PUT',
  });
  const form = useFormik({
    initialValues: {
      password: '',
      new_pin: '',
      repeat_pin: '',
    },
    onSubmit: async values => {
      const { repeat_pin, ...data } = values;
      const [res, error] = await updateSecurityPinRequest.makeRequest({ ...data, id: store?.id! });
      if (res) {
        Toast.show({ text1: 'Security pin updated successfully' });
        updateStore({ onboarding_steps: { ...store?.onboarding_steps, security_pin_added: true } });
      }

      if (error) {
        Toast.show({ text1: error.body.body.message, type: 'error' });
      }
    },
    validationSchema,
  });

  return (
    <View className="flex-1">
      <AvoidKeyboard className="flex-1">
        <ScrollView className="flex-1">
          <Container>
            <InfoBadge text={'Update pin for withdrawing funds from your wallet'} />
            <View className="mt-15">
              <BaseText fontSize={12} classes="text-black-muted">
                Enter New Pin
              </BaseText>
              <PinInput
                value={form.values.new_pin}
                ref={newPinInputRef}
                setValue={value => {
                  form.setFieldValue('new_pin', value);
                  if (value.length === 6) {
                    repeatPinInputRef.current?.focus();
                  }
                }}
              />
              {form.errors.new_pin && (
                <View className="mt-5">
                  <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
                    {form.errors.new_pin}
                  </BaseText>
                </View>
              )}
            </View>
            <View className="mt-15">
              <BaseText fontSize={12} classes="text-black-muted">
                Repeat Pin
              </BaseText>
              <PinInput
                value={form.values.repeat_pin}
                setValue={value => {
                  form.setFieldValue('repeat_pin', value);
                }}
                ref={repeatPinInputRef}
              />
              {form.errors.repeat_pin && (
                <View className="mt-5">
                  <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
                    {form.errors.repeat_pin}
                  </BaseText>
                </View>
              )}
            </View>
            <Separator className="mx-0" />
            <PasswordInput {...getFieldvalues('password', form)} />
          </Container>
        </ScrollView>
      </AvoidKeyboard>
      <FixedBtnFooter
        buttons={[
          { text: 'Update Changes', onPress: () => form.submitForm(), isLoading: updateSecurityPinRequest.isLoading },
        ]}
      />
    </View>
  );
};

const validationSchema = Yup.object().shape({
  new_pin: Yup.string().length(6, 'Security Pin must be 6 digits').required('Pin is required'),
  repeat_pin: Yup.string()
    .oneOf([Yup.ref('new_pin'), null], 'Please ensure the pins match')
    .required('Pin confirmation is required'),
  password: Yup.string().required('Password is required'),
});

export default SecurityPin;
