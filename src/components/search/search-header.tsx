import { useNavigation } from '@react-navigation/native';
import { BaseText, Container, Row } from '@/components/ui';
import { ScrollView, View } from 'react-native';
import useModals from 'src/hooks/use-modals';
import Input, { InputProps } from '../ui/inputs/input';
import { Search } from '../ui/icons';
import { wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import Pressable from '../ui/base/pressable';
import { CloseCircle, Setting5 } from 'iconsax-react-native/src';

interface SearchHeaderProps {
  inputProps?: Partial<InputProps>;
  onPressFilterButton: VoidFunction;
  numberOfActiveFilters?: number;
  onPressClear?: VoidFunction;
  showClear?: boolean;
  showFilter?: boolean;
}

const SearchHeader = ({
  inputProps,
  onPressClear,
  showClear,
  showFilter,
  onPressFilterButton,
  numberOfActiveFilters = 0,
}: SearchHeaderProps) => {
  return (
    <Row className="px-20 mt-15">
      <View className="flex-1 mr-8">
        <Input
          containerClasses="bg-white"
          placeholder={'Search product'}
          rightAccessory={
            <View>
              {(showClear && !(inputProps?.value?.trim() == '' || inputProps?.value === undefined)) ? (
                <Pressable onPress={onPressClear}>
                  <CloseCircle size={wp(18)} variant="Bold" color={colors?.black.muted} />
                </Pressable>
              ) : (
                <Search size={wp(18)} primaryColor={colors?.black.muted} />
              )}
            </View>
          }
          returnKeyType="search"
          {...inputProps}
        />
      </View>
      {showFilter && (
        <Pressable className="border border-grey-border rounded-12 p-15" onPress={onPressFilterButton}>
          <View className="-rotate-90">
            <Setting5 size={wp(20)} color={colors.black.secondary} />
          </View>
          {numberOfActiveFilters > 0 && (
            <View className="h-[22px] w-[22px] rounded-full bg-white p-2 absolute -top-8 -right-8">
              <View className="flex-1 bg-primary-main rounded-full items-center justify-center">
                <BaseText fontSize={10} weight="medium" classes="text-white text-center">
                  {numberOfActiveFilters}
                </BaseText>
              </View>
            </View>
          )}
        </Pressable>
      )}
    </Row>
  );
};

export default SearchHeader;
