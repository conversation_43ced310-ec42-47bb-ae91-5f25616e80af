import { ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, CircledIcon, Row, SelectionPill } from '../ui';
import { Fragment, useMemo, useRef, useState } from 'react';
import Pressable from '../ui/base/pressable';
import AccordionGroup from '../ui/others/accordion/accordion-group';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import colors from 'src/theme/colors';
import { cx, enumToHumanFriendly, hp, optionsMap, wp } from 'src/assets/utils/js';
import Separator from '../ui/others/separator';
import { Add } from 'iconsax-react-native/src';
import SelectProductsModal from '../products/storefront-products/select-products-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import SelectDropdown, { DropDownMethods } from '../ui/inputs/select-dropdown';
import Input from '../ui/inputs/input';
import useLayoutHeight from 'src/hooks/use-layout-height';
import { Close, Search } from '../ui/icons';
import { FormikProps } from 'formik';
import { SearchProductsParams } from 'src/screens/products/storefront/search-products';
import Accordion from '../ui/others/accordion';
import {
  Category,
  CustomerInterface,
  DELIVERY_METHODS,
  GET_CUSTOMERS,
  GET_ITEMS,
  GetItemsParams,
  ORDER_CHANNELS,
  ORDER_PAID_TAG,
  PaginateSearchParams,
  PAYMENT_STATUS,
  phoneObjectFromString,
} from 'catlog-shared';
import { SearchOrdersParams } from 'src/screens/orders/search-orders';
import AddCustomerModal from '../customer/add-customer-modal';
import useModals from 'src/hooks/use-modals';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectCustomer from '../customer/select-customer';
import CustomerInitial from '../customer/customer-initial';
import SelectSpecificProductsModal from '../products/storefront-products/select-specific-product-modal';
import { ProductsResponse } from 'src/screens/products/storefront';

interface SearchOrderFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchOrdersParams>;
}

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const SearchOrderFilterModal = ({ closeModal, onPressContinue, form, ...props }: SearchOrderFilterModalProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { store, categories } = useAuthContext();
  const { modals, toggleModal } = useModals(['selectProducts']);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerInterface>(null);

  const { onLayout, flexStyle } = useLayoutHeight(0);

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      apiFunction: GET_ITEMS,
      method: 'GET',
      key: 'fetch-custom-items',
    },
    {
      per_page: 9007199254740991,
      filter: { store: store.id },
    },
  );

  const filterParams = form?.values;

  const onSelectCustomer = (customer: CustomerInterface) => {
    setSelectedCustomer(customer);
    form.setFieldValue('customer', customer.id);
    // const findCustomerIndex = selectedCustomer.findIndex(d => d.id === customer.id);
    // let selectedIdCopy = [...form.values.customer];
    // const SelectedCustomersCopy = [...selectedCustomer];

    // console.log({ findCustomerIndex });

    // if (findCustomerIndex === -1) {
    //   setSelectedCustomer(prev => [...prev, customer]);
    //   selectedIdCopy = [...selectedIdCopy, customer.id];
    // } else {
    //   SelectedCustomersCopy.splice(findCustomerIndex, 1);
    //   setSelectedCustomer(SelectedCustomersCopy);
    //   selectedIdCopy.splice(findCustomerIndex, 1);
    // }
    // // form.setFieldValue('selectedCustomer', customer);
    // form.setFieldValue('customer', selectedIdCopy);
  };

  const onSelectProducts = (p: string[]) => {
    form.setFieldValue('products', p);
  };

  const filterOptions = [
    {
      title: 'Order Channel',
      options: optionsMap(ORDER_CHANNELS),
      activeFilter: filterParams?.channel,
      fieldName: 'channel',
    },
    {
      title: 'Payment Status',
      options: optionsMap(ORDER_PAID_TAG),
      activeFilter: filterParams?.payment_status,
      fieldName: 'payment_status',
    },
    {
      title: 'Fulfillment Method',
      options: optionsMap(DELIVERY_METHODS),
      activeFilter: filterParams?.fulfillment_method,
      fieldName: 'fulfillment_method',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      size="lg"
      headerAddon={<ClearFilterBtn />}
      containerStyle={{ flex: 1 }}
      innerStyle={{ flex: 1 }}
      modalStyle={{ flex: 1 }}>
      <ScrollView className="px-20">
        <View className="pb-40" onLayout={onLayout}>
          {filterOptions.map((item, index) => (
            <Fragment key={item.title}>
              <Accordion
                initiallyOpened
                anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
                <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                  {item.options.map((option, index) => (
                    <SelectionPill
                      title={enumToHumanFriendly(option?.name as string)}
                      key={index}
                      className="mr-0"
                      selected={item.activeFilter === option.value}
                      onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                      borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                      showBorder={true}
                      capitalize
                    />
                  ))}
                </Row>
              </Accordion>
              <Separator className="m-0 mt-15" />
            </Fragment>
          ))}
          <Accordion
            initiallyOpened
            anchorElement={status => <OthersAnchor title="Customer" onPress={() => dropDownRef.current.open()} />}>
            <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
              {form.values.customer && (
                <SelectionPill
                  title={selectedCustomer?.name}
                  className="mr-0"
                  selected={false}
                  leftElement={
                    <CustomerInitial
                      classes="mr-4"
                      textProps={{ fontSize: 10 }}
                      initial={selectedCustomer?.name?.[0]}
                    />
                  }
                  onPress={() => {
                    setSelectedCustomer(null);
                    form.setFieldValue('customer', '');
                  }}
                  borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                  showBorder={true}>
                  <View className="ml-4">
                    <Close size={wp(12)} currentColor={colors.black.placeholder} />
                  </View>
                </SelectionPill>
              )}
            </Row>
          </Accordion>
          <Separator
            className={cx('m-0', { 'mt-15': Boolean(form.values.customer), 'mt-5': !Boolean(form.values.customer) })}
          />
          <Accordion
            initiallyOpened
            anchorElement={status => <OthersAnchor title="Product" onPress={() => toggleModal('selectProducts')} />}>
            <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
              {form.values.customer && (
                <SelectionPill
                  title={selectedCustomer?.name}
                  className="mr-0"
                  selected={false}
                  leftElement={
                    <CustomerInitial
                      classes="mr-4"
                      textProps={{ fontSize: 10 }}
                      initial={selectedCustomer?.name?.[0]}
                    />
                  }
                  onPress={() => {
                    setSelectedCustomer(null);
                    form.setFieldValue('customer', '');
                  }}
                  borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                  showBorder={true}>
                  <View className="ml-4">
                    <Close size={wp(12)} currentColor={colors.black.placeholder} />
                  </View>
                </SelectionPill>
              )}
            </Row>
          </Accordion>
        </View>
      </ScrollView>
      <SelectCustomer
        showAnchor={false}
        onSelectCustomer={onSelectCustomer}
        selectedCustomer={selectedCustomer?.id}
        showLeftAccessory={false}
        externalRef={dropDownRef}
        closeAfterSelection={false}
        showButton={true}
        buttons={[{ text: 'Continue', onPress: () => dropDownRef.current.close() }]}
        hideAddCustomerBtn
        // isMultiSelect
        // showRadio={false}
      />
      <SelectSpecificProductsModal
        products={getProductsRequest?.response?.data?.items ?? []}
        isVisible={modals.selectProducts}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest}
        loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
        selectedProducts={form.values.products ?? []}
        setSelectedProducts={onSelectProducts}
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
    </BottomModal>
  );
};

export default SearchOrderFilterModal;

export const OthersAnchor = ({ title, onPress }) => {
  return (
    <Row className="flex flex-row py-0">
      <Row className="items-center" style={{ gap: 5 }}>
        <BaseText type="heading" fontSize={15}>
          {title}
        </BaseText>
      </Row>
      <Pressable onPress={onPress}>
        <CircledIcon className="bg-grey-bgOne p-7">
          <Add size={wp(18)} color={colors.black.placeholder} />
        </CircledIcon>
      </Pressable>
    </Row>
  );
};
