import { FormikProps } from 'formik';
import { Fragment } from 'react';
import { ScrollView, View } from 'react-native';
import { enumToHumanFriendly, hp, optionsMap, wp } from 'src/assets/utils/js';
import useLayoutHeight from 'src/hooks/use-layout-height';
import colors from 'src/theme/colors';
import { BaseText, Row, SelectionPill } from '../ui';
import Pressable from '../ui/base/pressable';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Accordion from '../ui/others/accordion';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import Separator from '../ui/others/separator';

interface SearchOrderFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchFiltersTransactionParams>;
}

const SearchTransactionsFilterModal = ({ closeModal, onPressContinue, form, ...props }: SearchOrderFilterModalProps) => {
  const { onLayout } = useLayoutHeight(0);

  const filterParams = form?.values;

  const filterOptions = [
    {
      title: 'Invoice Type',
      options: optionsMap(TRANSACTION_TYPE),
      activeFilter: filterParams?.type,
      fieldName: 'type',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      headerAddon={<ClearFilterBtn />}
      containerStyle={{ flex: 1 }}
      innerStyle={{ flex: 1 }}
      modalStyle={{ flex: 1 }}>
      <ScrollView className="px-20">
        <View className="pb-40" onLayout={onLayout}>
          {filterOptions.map((item, index) => (
            <Fragment key={item.title}>
              <Accordion
                initiallyOpened
                anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
                <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                  {item.options.map((option, index) => (
                    <SelectionPill
                      title={enumToHumanFriendly(option?.name as string)}
                      key={index}
                      className="mr-0"
                      selected={item.activeFilter === option.value}
                      onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                      borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                      showBorder={true}
                      capitalize
                    />
                  ))}
                </Row>
              </Accordion>
              <Separator className="m-0 mt-15" />
            </Fragment>
          ))}
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default SearchTransactionsFilterModal;

export enum TRANSACTION_TYPE {
  DEBIT = 'debit',
  CREDIT = 'credit',
}


export interface SearchFiltersTransactionParams {
  search?: string;
  type?: TRANSACTION_TYPE;
}