import { <PERSON><PERSON><PERSON>iew } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, CircledIcon, Row, SelectionPill } from '../ui';
import { Fragment, useRef } from 'react';
import Pressable from '../ui/base/pressable';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import colors from 'src/theme/colors';
import { hp, wp } from 'src/assets/utils/js';
import Separator from '../ui/others/separator';
import { Add } from 'iconsax-react-native/src';
import useAuthContext from 'src/contexts/auth/auth-context';
import { DropDownMethods } from '../ui/inputs/select-dropdown';
import useLayoutHeight from 'src/hooks/use-layout-height';
import { FormikProps } from 'formik';
import { SearchProductsParams } from 'src/screens/products/storefront/search-products';
import { SearchCouponsParams } from 'src/screens/products/coupons/search-coupons';
import Accordion from '../ui/others/accordion';
import { COUPON_STATUS_TAG, COUPON_TYPE_TAG } from 'catlog-shared';

interface SearchCouponsFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchCouponsParams>;
}

const SearchCouponsFilterModal = ({ closeModal, onPressContinue, form, ...props }: SearchCouponsFilterModalProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { store, categories } = useAuthContext();

  const { onLayout, flexStyle } = useLayoutHeight(0);

  const filterParams = form?.values;

  const optionsMap = (object: {}) => Object.entries(object).map(item => ({ name: item[1], value: item[1] }));

  const filterOptions = [
    {
      title: 'Status',
      options: optionsMap(COUPON_STATUS_TAG),
      activeFilter: filterParams?.status,
      fieldName: 'status',
    },
    {
      title: 'Type',
      options: optionsMap(COUPON_TYPE_TAG),
      activeFilter: filterParams?.type,
      fieldName: 'type',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      headerAddon={<ClearFilterBtn />}
      containerStyle={flexStyle}
      innerStyle={flexStyle}
      modalStyle={flexStyle}>
      <ScrollView className="px-20 pb-20" onLayout={onLayout}>
        {filterOptions.map((item, index) => (
          <Fragment key={item.title}>
            <Accordion
              initiallyOpened
              anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
              <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                {item.options.map((option, index) => (
                  <SelectionPill
                    title={option?.name as string}
                    key={index}
                    className="mr-0"
                    selected={item.activeFilter === option.value}
                    onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                    borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                    showBorder={true}
                    capitalize
                  />
                ))}
              </Row>
            </Accordion>
            <Separator className="m-0 mt-15" />
          </Fragment>
        ))}
      </ScrollView>
    </BottomModal>
  );
};

export default SearchCouponsFilterModal;
