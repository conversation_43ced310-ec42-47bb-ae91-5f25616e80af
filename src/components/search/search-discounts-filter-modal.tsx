import { FormikProps } from 'formik';
import { Fragment, useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import { hp, wp } from 'src/assets/utils/js';
import useAuthContext from 'src/contexts/auth/auth-context';
import useLayoutHeight from 'src/hooks/use-layout-height';
import { SearchDiscountsParams } from 'src/screens/products/discounts/search-discounts';
import colors from 'src/theme/colors';
import { BaseText, Row, SelectionPill } from '../ui';
import Pressable from '../ui/base/pressable';
import { DropDownMethods } from '../ui/inputs/select-dropdown';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Accordion from '../ui/others/accordion';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import Separator from '../ui/others/separator';
import { Category, DISCOUNT_STATUS_TAG, DISCOUNT_TYPE_TAG } from 'catlog-shared';

interface SearchDiscountsFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchDiscountsParams>;
}

const SearchDiscountsFilterModal = ({
  closeModal,
  onPressContinue,
  form,
  ...props
}: SearchDiscountsFilterModalProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { store, categories } = useAuthContext();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const { onLayout, flexStyle } = useLayoutHeight(0);

  const filterParams = form?.values;

  const categoriesMapped = categories!.map((item: Category) => ({
    value: item.id!,
    label: item.name + ' ' + item.emoji,
  }));

  const optionsMap = (object: {}) => Object.entries(object).map(item => ({ name: item[1], value: item[1] }));

  const filterOptions = [
    {
      title: 'Status',
      options: optionsMap(DISCOUNT_STATUS_TAG),
      activeFilter: filterParams?.status,
      fieldName: 'status',
    },
    {
      title: 'Type',
      options: optionsMap(DISCOUNT_TYPE_TAG),
      activeFilter: filterParams?.type,
      fieldName: 'type',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      headerAddon={<ClearFilterBtn />}
      containerStyle={flexStyle}
      innerStyle={flexStyle}
      modalStyle={flexStyle}>
      <ScrollView className="px-20 pb-20" onLayout={onLayout}>
        {filterOptions.map((item, index) => (
          <Fragment key={item.title}>
            <Accordion
              initiallyOpened
              anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
              <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                {item.options.map((option, index) => (
                  <SelectionPill
                    title={option?.name as string}
                    key={index}
                    className="mr-0"
                    selected={item.activeFilter === option.value}
                    onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                    borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                    showBorder={true}
                    capitalize
                  />
                ))}
              </Row>
            </Accordion>
            <Separator className="m-0 mt-15" />
          </Fragment>
        ))}
      </ScrollView>
    </BottomModal>
  );
};

export default SearchDiscountsFilterModal;
