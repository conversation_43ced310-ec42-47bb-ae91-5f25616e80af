import {
  CURRENCIES,
  GET_ITEMS,
  GetItemsParams,
  INVOICE_STATUSES,
  INVOICE_TYPES,
  INVOICES_DRAFT_TAG,
} from 'catlog-shared';
import { FormikProps } from 'formik';
import { Fragment } from 'react';
import { ScrollView, View } from 'react-native';
import { enumToHumanFriendly, hp, optionsMap, wp } from 'src/assets/utils/js';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import useLayoutHeight from 'src/hooks/use-layout-height';
import useModals from 'src/hooks/use-modals';
import { SearchInvoicesParams } from 'src/screens/invoices/search-invoices';
import { ProductsResponse } from 'src/screens/products/storefront';
import colors from 'src/theme/colors';
import SelectSpecificProductsModal from '../products/storefront-products/select-specific-product-modal';
import { BaseText, Row, SelectionPill } from '../ui';
import Pressable from '../ui/base/pressable';
import { Close } from '../ui/icons';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Accordion from '../ui/others/accordion';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import Separator from '../ui/others/separator';
import { OthersAnchor } from './search-orders-filter-modal';

interface SearchOrderFilterModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue?: () => void;
  form?: FormikProps<SearchInvoicesParams>;
}

const SearchInvoicesFilterModal = ({ closeModal, onPressContinue, form, ...props }: SearchOrderFilterModalProps) => {
  const { store } = useAuthContext();
  const { modals, toggleModal } = useModals(['selectProducts']);

  const { onLayout } = useLayoutHeight(0);

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      apiFunction: GET_ITEMS,
      method: 'GET',
      key: 'fetch-custom-items',
    },
    {
      per_page: Number.MAX_SAFE_INTEGER,
      filter: { store: store.id },
    },
  );

  const filterParams = form?.values;

  const onSelectProducts = (p: string[]) => {
    form.setFieldValue('products', p);
  };

  const filterOptions = [
    {
      title: 'Invoice Type',
      options: optionsMap(INVOICE_TYPES),
      activeFilter: filterParams?.type,
      fieldName: 'type',
    },
    {
      title: 'Invoice Status',
      options: optionsMap(INVOICE_STATUSES),
      activeFilter: filterParams?.status,
      fieldName: 'status',
    },
    {
      title: 'Draft Status',
      options: optionsMap(INVOICES_DRAFT_TAG),
      activeFilter: filterParams?.draft_status,
      fieldName: 'draft_status',
    },
    {
      title: 'Invoice Currecy',
      options: optionsMap(CURRENCIES),
      activeFilter: filterParams?.currency,
      fieldName: 'currency',
    },
  ];

  const ClearFilterBtn = () => {
    return (
      <Pressable onPress={() => form?.handleReset()}>
        <BaseText weight="medium" fontSize={11} classes="text-accentRed-main">
          Clear Filters
        </BaseText>
      </Pressable>
    );
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Update Filters',
          onPress: onPressContinue,
        },
      ]}
      title={'Filters'}
      headerAddon={<ClearFilterBtn />}
      containerStyle={{ flex: 1 }}
      innerStyle={{ flex: 1 }}
      modalStyle={{ flex: 1 }}>
      <ScrollView className="px-20">
        <View className="pb-40" onLayout={onLayout}>
          {filterOptions.map((item, index) => (
            <Fragment key={item.title}>
              <Accordion
                initiallyOpened
                anchorElement={status => <AccordionAnchor title={item.title} isOpened={status} />}>
                <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                  {item.options.map((option, index) => (
                    <SelectionPill
                      title={enumToHumanFriendly(option?.name as string)}
                      key={index}
                      className="mr-0"
                      selected={item.activeFilter === option.value}
                      onPress={() => form?.setFieldValue(item.fieldName, option.value)}
                      borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                      showBorder={true}
                      capitalize
                    />
                  ))}
                </Row>
              </Accordion>
              <Separator className="m-0 mt-15" />
            </Fragment>
          ))}
          <Accordion
            initiallyOpened
            anchorElement={status => (
              <OthersAnchor title="Invoice Items" onPress={() => toggleModal('selectProducts')} />
            )}>
            <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
              {form.values.products?.length > 0 && (
                <SelectionPill
                  title={`${form.values?.products?.length} products selected`}
                  className="mr-0"
                  selected={false}
                  onPress={() => toggleModal('selectProducts')}
                  borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
                  showBorder={true}>
                  <View className="ml-4">
                    <Close size={wp(12)} currentColor={colors.black.placeholder} />
                  </View>
                </SelectionPill>
              )}
            </Row>
          </Accordion>
        </View>
      </ScrollView>
      <SelectSpecificProductsModal
        products={getProductsRequest?.response?.data?.items ?? []}
        isVisible={modals.selectProducts}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest}
        loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
        selectedProducts={form.values.products ?? []}
        setSelectedProducts={onSelectProducts}
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
    </BottomModal>
  );
};

export default SearchInvoicesFilterModal;
