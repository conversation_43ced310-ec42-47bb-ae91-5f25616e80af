import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { useNavigation } from '@react-navigation/native';
import {
  ORDER_STATUSES,
  OrderInterface,
  GET_ORDER,
  UPDATE_ORDER,
  toAppUrl,
  GetOrderParms,
  DELIVERY_STATUSES,
  IDelivery,
  humanFriendlyDate,
  getItemThumbnail,
} from 'catlog-shared';
import cx from 'classnames';
import { CloseCircle, Edit2, Link21, Receipt21, TickCircle, Truck } from 'iconsax-react-native/src';
import { ReactNode } from 'react';
import { ImageBackground, View } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';
import * as React from 'react';
import Toast from 'react-native-toast-message';
import {
  alertPromise,
  copyToClipboard,
  hideLoader,
  humanFriendlyRelativeDate,
  openLinkInBrowser,
  showLoader,
  toCurrency,
  wp,
} from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable, { PressableProps } from '@/components/ui/base/pressable';
import CheckBox from '@/components/ui/buttons/check-box';
import ImageTextPlaceholder from '@/components/ui/image-text-placholder';
import MoreOptions, { OptionWithIcon } from '@/components/ui/more-options';
import Separator from '@/components/ui/others/separator';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import colors from '@/theme/colors';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import eventEmitter from 'src/assets/utils/js/event-emitter';

interface OrderItemCardProps extends PressableProps {
  order: OrderInterface;
  orderStatus: ORDER_STATUSES;
  actionCallBack?: (activeKey: string, updatedData?: OrderInterface) => void;
  handleSelection?: VoidFunction;
  isSelectionActive?: boolean;
  toggleMarkAsPaid?: (id: string) => void;
  // isSelectable?: boolean;
}

const OrderItemCard = ({
  order,
  isSelectionActive,
  actionCallBack,
  handleSelection,
  toggleMarkAsPaid,
  // isSelectable = true,
  orderStatus,
  ...props
}: OrderItemCardProps) => {
  const opacity = useSharedValue(0);

  const changeStatusRequest = useApi({ apiFunction: UPDATE_ORDER, method: 'PUT', key: 'update-order' });
  const getOrderRequest = useApi<GetOrderParms, ResponseWithoutPagination<OrderInterface>>(
    { apiFunction: GET_ORDER, method: 'GET', key: 'get-order', autoRequest: false },
    { id: order?.id },
  );
  const navigation = useNavigation();

  const { store } = useAuthContext();
  const deliveryEnabledInCountry = actionIsAllowed({
    countryPermission: SCOPES.DELIVERIES.CAN_BOOK_DELIVERIES,
    country: store?.country?.code,
  });

  // Fade-in animation on mount using Reanimated 3
  const animatedStyle = useAnimatedStyle(() => {
    return { opacity: withTiming(opacity.value, { duration: 200, easing: Easing.ease }) };
  });

  // Trigger animation on mount
  React.useEffect(() => {
    opacity.value = 1;
  }, []);

  const handleStatusUpdate = async (status: ORDER_STATUSES) => {
    const alertResponse = await alertPromise(
      'Update Status',
      `Clicking "Yes, Update" will update your Order status`,
      'Yes, Update',
      'Cancel',
      true,
    );
    if (alertResponse === false) {
      return;
    }

    showLoader('Updating Order Status');
    const [response] = await changeStatusRequest.makeRequest({
      id: order?.id,
      status: order?.status === ORDER_STATUSES.PENDING ? ORDER_STATUSES.PROCESSING : ORDER_STATUSES.FULFILLED,
    });
    hideLoader();

    if (response) {
      Toast.show({ type: 'success', text1: 'Order status updated successfully' });
      getOrderRequest.reset();
      actionCallBack?.(order.id);
      eventEmitter.emit(`${status}_UPDATES`, { ...order, status });
    }
  };

  const getStatus = () => {
    let status: ORDER_STATUSES;
    switch (orderStatus) {
      case ORDER_STATUSES.PENDING:
        status = ORDER_STATUSES.PROCESSING;
        break;
      case ORDER_STATUSES.PROCESSING:
        status = ORDER_STATUSES.FULFILLED;
        break;
      default:
        status = ORDER_STATUSES.PROCESSING;
        break;
    }

    return status;
  };

  const handleRequestDelivery = async () => {
    showLoader('Loading Delivery Request', true);
    const [response] = await getOrderRequest.makeRequest({ id: order.id });
    hideLoader();

    if (response) {
      const initialData: IDelivery = response?.data && {
        items: order?.items?.map(({ item }) => ({
          description: item.name,
          id: item.id,
          // image: item.images[item.thumbnail],
          image: getItemThumbnail(item),
          name: item.name,
          quantity: 1,
          unit_amount: item.price,
          unit_weight: undefined,
        })),
        receiver_address: order?.validated_delivery_address,
        status: DELIVERY_STATUSES.DRAFT,
        currency: order?.currency,
        order: order?.id,
      };
      navigation.navigate('RequestDelivery', { deliveryData: initialData });
    }
  };

  const handleCancelOrder = async () => {
    const alertResponse = await alertPromise(
      'Cancel Order',
      `Clicking "Yes, Cancel" would update the status of this order to Cancelled`,
      'Yes, Update',
      'Cancel',
      true,
    );

    if (alertResponse === false) {
      return;
    }

    showLoader('Updating Order Status');
    const [response] = await changeStatusRequest.makeRequest({ id: order.id, status: ORDER_STATUSES.CANCELLED });
    hideLoader();

    if (response) {
      Toast.show({ type: 'success', text1: 'Order status updated successfully' });
      actionCallBack?.(order.id);
      eventEmitter.emit(`${orderStatus}_UPDATES`, { ...order, status: ORDER_STATUSES.CANCELLED });
    }
  };

  const lingo =
    orderStatus === ORDER_STATUSES.PENDING ? 'Confirmed' : orderStatus === ORDER_STATUSES.PROCESSING ? 'Fulfilled' : '';

  const isSelectable = !(orderStatus === ORDER_STATUSES.FULFILLED || orderStatus === ORDER_STATUSES.CANCELLED);

  const moreOptions = [
    ...(order.status !== ORDER_STATUSES.FULFILLED
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<TickCircle size={wp(15)} color={colors.black.placeholder} />}
                label={`Mark as ${lingo}`}
              />
            ),
            title: 'Mark as confirmed',
            onPress: () => handleStatusUpdate(getStatus()),
          },
        ]
      : []),
    ...(order.is_paid !== true
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<TickCircle size={wp(15)} color={colors.black.placeholder} />}
                label={`Mark as Paid`}
              />
            ),
            title: 'Mark as Paid',
            onPress: () => toggleMarkAsPaid(order.id),
          },
        ]
      : []),
    {
      optionElement: (
        <OptionWithIcon icon={<Link21 size={wp(15)} color={colors.black.placeholder} />} label={'Copy Order Link'} />
      ),
      title: 'Copy Order Link',
      onPress: () => copyToClipboard(toAppUrl(`orders/${order.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
    {
      optionElement: (
        <OptionWithIcon
          icon={<Receipt21 size={wp(15)} color={colors.black.placeholder} />}
          label={'Copy Invoice Link'}
        />
      ),
      title: 'Copy Invoice Link',
      onPress: () => copyToClipboard(toAppUrl(`invoices/${order.invoice}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
    ...(order.status === ORDER_STATUSES.FULFILLED && deliveryEnabledInCountry
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<Truck size={wp(15)} color={colors.black.placeholder} />}
                label={'Request Delivery'}
              />
            ),
            title: 'Request Delivery',
            onPress: handleRequestDelivery,
          },
        ]
      : []),
    {
      optionElement: (
        <Row spread={false}>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Edit2 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Edit Order
          </BaseText>
        </Row>
      ),
      title: 'Edit Order',
      onPress: () => {
        openLinkInBrowser(toAppUrl(`orders/${order.id}?edit_items=true`, true, EXPO_PUBLIC_PUBLIC_URL));
      },
    },
    ...(![ORDER_STATUSES.FULFILLED, ORDER_STATUSES.CANCELLED].includes(order.status)
      ? [
          {
            optionElement: (
              <Row spread={false}>
                <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
                  <CloseCircle size={wp(15)} color={colors.accentRed.main} />
                </CircledIcon>
                <BaseText fontSize={12} classes="text-accentRed-main ml-10">
                  Cancel Order
                </BaseText>
              </Row>
            ),
            title: 'Cancel Order',
            onPress: handleCancelOrder,
          },
        ]
      : []),
  ];

  return (
    <Animated.View style={animatedStyle}>
      <Pressable className="m-10 border-grey-border rounded-12 border" {...props}>
        <Row className="p-12">
          <Pressable onPress={handleSelection} disabled={!isSelectable} className="flex-1 flex-row">
            {isSelectable && <CheckBox checked={isSelectionActive ?? false} />}
            <View className={cx('flex-1', { 'mx-10': isSelectable })}>
              <BaseText fontSize={12} classes="text-black-muted">
                {order.id}
              </BaseText>
            </View>
          </Pressable>
          <MoreOptions options={moreOptions} classes={props?.disabled ? 'opacity-0' : ''} />
        </Row>
        <Separator className="mx-0 my-0" />
        <View className="pt-12 p-15">
          <OrderListCard
            order={order}
            rightAddon={
              <Row className="justify-start">
                <ImageTextPlaceholder text={order?.customer?.name} size="sm" />
                <StatusPill
                  className="bg-grey-bgOne ml-[-5px]"
                  statusType={order?.is_paid ? StatusType.SUCCESS : StatusType.DANGER}
                  title={order?.is_paid ? 'PAID' : 'UNPAID'}
                />
              </Row>
            }
          />
        </View>
      </Pressable>
    </Animated.View>
  );
};

export const OrderListCard = ({
  order,
  disabled = true,
  onPress,
  rightAddon,
}: {
  order: OrderInterface;
  disabled?: boolean;
  onPress?: VoidFunction;
  rightAddon?: ReactNode;
}) => {
  const mainItem = order.items?.[0]?.snapshot;

  return (
    <Pressable disabled={disabled} onPress={onPress}>
      <Row className={cx('items-start')}>
        <ImageBackground
          className={cx('h-40 w-40 rounded-[10px] overflow-hidden justify-end')}
          source={{ uri: getItemThumbnail(mainItem) }}
        />
        <View className={cx('flex-1 items-stretch justify-between mx-10')}>
          <BaseText fontSize={12} classes="text-black-muted leading-[20px]">
            {mainItem?.name}
            {order?.items?.length > 1 ? ` & ${order?.items?.length - 1} items` : ''}
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="text-black-main mt-4">
            {toCurrency(order?.total_amount, order?.currency)}
          </BaseText>

          {order?.created_at && (
            <BaseText fontSize={10} classes="text-grey-muted mt-1.5">
              {humanFriendlyRelativeDate(order?.created_at, true)}
            </BaseText>
          )}
        </View>
        {rightAddon}
      </Row>
    </Pressable>
  );
};

export default OrderItemCard;
