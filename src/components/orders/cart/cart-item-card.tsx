import { View } from 'react-native';
import cx from 'classnames';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { Profile } from 'iconsax-react-native/src';
import { colorAlternates } from 'src/constant/static-data';
import { getColorAlternates, humanFriendlyRelativeDate, wp } from 'src/assets/utils/js';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import dayjs from 'node_modules/dayjs';
import Pressable, { PressableProps } from 'src/components/ui/base/pressable';
import { CartInterface } from 'catlog-shared';

interface CartItemCardProps extends PressableProps {
  cartItem: CartInterface;
  index?: number;
}

const CartItemCard = ({ cartItem, index, ...props }: CartItemCardProps) => {
  const color = getColorAlternates(index);

  return (
    <Pressable className="" {...props}>
      <View className="mx-15">
        <Row className={cx('items-start')}>
          <CircledIcon style={{ backgroundColor: color.bgColor }}>
            <Profile variant="Bold" size={wp(20)} color={color.iconColor} />
          </CircledIcon>
          <View className={cx('flex-1 items-stretch justify-between mx-10')}>
            <BaseText fontSize={12} classes={'leading-[20px]'}>
              {cartItem?.customer?.name ?? 'No name'}
            </BaseText>
            <BaseText classes={'text-black-muted mt-4'}>
              {cartItem?.location?.city}, {cartItem?.location?.country_name}
            </BaseText>
            <BaseText className="text-black-placeholder mt-1.5" fontSize={9}>
              {humanFriendlyRelativeDate(cartItem?.updated_at)}{' '}
            </BaseText>
          </View>
          <View>
            {dateIsPastOneHour(cartItem?.updated_at) ? (
              !cartItem.order ? (
                <StatusPill statusType={StatusType.DANGER} title={`DROPPED OFF`} greyBg />
              ) : (
                <StatusPill statusType={StatusType.SUCCESS} title={`PLACED ORDER`} greyBg />
              )
            ) : (
              <StatusPill statusType={StatusType.WARN} title={`IN PROGRESS`} greyBg />
            )}
          </View>
        </Row>
      </View>
    </Pressable>
  );
};

export default CartItemCard;

export const dateIsPastOneHour = (date: string) => {
  const now = dayjs();
  const targetDate = dayjs(date);

  const isMoreThanOneHour = now.diff(targetDate, 'hour') > 1;

  return isMoreThanOneHour;
};
