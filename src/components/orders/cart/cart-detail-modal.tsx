import { ActivityIndicator, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Bag,
  Box1,
  Calendar,
  Call,
  DollarSquare,
  Location,
  MoneyTick,
  ShoppingBag,
  Sms,
  Status,
  TickCircle,
} from 'iconsax-react-native/src';
import { amountFormat, cx, formatDate, toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from 'src/components/ui';
import { ReactNode, useState } from 'react';
import { ArrowRight, ArrowUpRight, Whatsapp } from 'src/components/ui/icons';
import InfoRow from 'src/components/ui/others/info-row';
import { useApi } from '@/hooks/use-api';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import ProductInfoRow from 'src/components/products/product-info-row';
import CustomerInitial from 'src/components/customer/customer-initial';
import { dateIsPastOneHour } from './cart-item-card';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import dayjs from 'dayjs';
import CustomerInformationModal from 'src/components/customer/customer-information-modal';
import useModals from 'src/hooks/use-modals';
import CustomImage from 'src/components/ui/others/custom-image';
import Separator from 'src/components/ui/others/separator';
import { CartInterface, CartItem, CustomerInterface, getItemThumbnail } from 'catlog-shared';
import SectionContainer from 'src/components/ui/section-container';
import { OrderListCard } from '../list/order-item-card';
import { ImageBackground } from 'expo-image';
import ProductDetailsModal from 'src/components/products/product-details-modal';
import Pressable from 'src/components/ui/base/pressable';

interface CartDetailModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  activeCart: Partial<CartInterface>;
}

const CartDetailModal = ({ closeModal, activeCart, onPressButton, ...props }: CartDetailModalProps) => {
  const [activeCustomer, setActiveCustomer] = useState<Partial<CustomerInterface>>({} as Partial<CustomerInterface>);
  const { modals, toggleModal } = useModals(['customerInfo', 'productDetails']);
  const [activeProduct, setActiveProduct] = useState<null | CartItem>(null);

  const cart = activeCart ?? {};

  const openCustomerModal = () => {
    setActiveCustomer(cart.customer);
    toggleModal('customerInfo');
  };

  // const [customer, setCustomer] = useState<CartInterface>({} as CartInterface);
  // const [loading, setLoading] = useState(false);

  // const getSingleCustomers = useApi({
  //   apiFunction: GET_CUSTOMER,
  //   method: 'GET',
  //   autoRequest: false,
  //   key: 'get-customer',
  // });

  // const handleGetCustomer = async () => {
  //   const [response] = await getSingleCustomers.makeRequest({ id: activeCart.id! });
  //   setCustomer(response?.data);
  //   setLoading(false);
  // };

  const loading = false;

  const navigation = useNavigation();

  const openOrder = () => {
    closeModal();
    navigation.navigate('OrderInfo', { id: cart.order });
  };

  const onPressProduct = (item: CartItem) => {
    setActiveProduct(item);
    toggleModal('productDetails');
  };

  return (
    <BottomModal {...props} closeModal={closeModal} showButton={false} title={'Cart Information'}>
      {loading ? (
        <View className="h-30 items-center justify-center">
          <ActivityIndicator size={'small'} />
        </View>
      ) : (
        <View className="px-20">
          {cart?.customer && (
            <Row className=" pt-15 border-t border-t-grey-border">
              <CustomerInitial initial={cart?.customer?.name?.[0]} classes={'w-36 h-36'} />
              <View className={'flex-1 mx-10'}>
                <BaseText classes="text-black-muted">Customer Name</BaseText>
                <BaseText fontSize={15} weight={'bold'} type={'heading'}>
                  {cart?.customer?.name}
                </BaseText>
              </View>
              <WhiteCardBtn
                className="rounded-15 bg-grey-bgOne"
                onPress={() => openCustomerModal()}
                icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}>
                View Profile
              </WhiteCardBtn>
            </Row>
          )}
          <View className="mt-20">
            <InfoRow
              title={'Status'}
              icon={<TickCircle size={wp(15)} color={colors.black.placeholder} />}
              valueElement={
                <View>
                  {dateIsPastOneHour(cart?.updated_at) ? (
                    !cart.order ? (
                      <StatusPill statusType={StatusType.DANGER} title={`DROPPED OFF`} />
                    ) : (
                      <StatusPill statusType={StatusType.SUCCESS} title={`PLACED ORDER`} />
                    )
                  ) : (
                    <StatusPill statusType={StatusType.WARN} title={`IN PROGRESS`} />
                  )}
                </View>
              }
            />
            {cart?.order && (
              <InfoRow
                title={'Cart Order'}
                icon={<ShoppingBag size={wp(15)} color={colors.black.placeholder} />}
                valueElement={
                  <WhiteCardBtn
                    className="rounded-15 bg-white p-0"
                    onPress={openOrder}
                    icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}>
                    View Order
                  </WhiteCardBtn>
                }
              />
            )}
            <InfoRow
              title={'Location'}
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              value={`${cart.location?.city}, ${cart?.location?.country_name}`}
            />
            <InfoRow
              title={'Currency'}
              icon={<MoneyTick size={wp(15)} color={colors.black.placeholder} />}
              value={cart.currency}
            />
            <InfoRow
              title={'Last Update'}
              icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
              value={formatDate(cart.updated_at, 'D MMM YYYY, hh:mm A')}
            />
          </View>
          <Separator className="mx-0 mt-0 mb-15" />

          <SectionContainer
            isEmpty={cart?.items?.length < 1}
            className="py-12"
            emptyStateProps={{
              text: 'No Orders to show',
              icon: (
                <CircledIcon className="bg-white p-15 mb-12">
                  <Bag variant={'Bulk'} color={colors.grey.muted} size={wp(30)} />
                </CircledIcon>
              ),
            }}
            classes="mt-0">
            {cart?.items?.map((item, index) => (
              <Pressable key={item.id} onPress={() => onPressProduct(item)}>
                <Row>
                  <CustomImage
                    // imageProps={{ source: item?.object?.images[item?.object?.thumbnail] }}
                    imageProps={{ source: getItemThumbnail(item?.object)}}
                    className="h-40 w-40 rounded-10"
                  />
                  <View className={cx('flex-1 items-stretch justify-between mx-10')}>
                    <BaseText fontSize={12} classes="text-black-muted leading-[20px]">
                      {item?.object.name ?? 'No name'}
                    </BaseText>
                    <BaseText fontSize={12} weight="medium" classes="text-black-main mt-4">
                      {toCurrency(item?.object?.price)}
                    </BaseText>
                  </View>
                  <StatusPill title={`${item.quantity} UNIT`} className="bg-white" statusType={StatusType.DEFAULT} />
                </Row>
                {index !== cart?.items?.length - 1 && <Separator className="mx-0" />}
              </Pressable>
            ))}
          </SectionContainer>
          {activeProduct && (
            <ProductDetailsModal
              isVisible={modals.productDetails}
              closeModal={() => {
                toggleModal('productDetails', false);
                setActiveProduct(null);
              }}
              cartItem={activeProduct}
            />
          )}
        </View>
      )}
      <CustomerInformationModal
        isVisible={modals.customerInfo}
        activeCustomer={activeCustomer}
        closeModal={() => toggleModal('customerInfo', false)}
        onPressButton={() => {}}
      />
    </BottomModal>
  );
};

export default CartDetailModal;
