import { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { OrderInterface, GetOrdersParams, ORDER_PAID_TAG, ORDER_CHANNELS, DELIVERY_METHODS } from 'catlog-shared';
import React, { useState, useEffect, useMemo } from 'react';
import { View, RefreshControl } from 'react-native';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import useOrdersApi from 'src/hooks/use-orders-api';
import Input from 'src/components/ui/inputs/input';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import { Search } from 'src/components/ui/icons';
import { Setting5 } from 'iconsax-react-native/src';
import { wp, hp, enumToHumanFriendly } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import EmptyState from 'src/components/ui/empty-states/empty-state';
import { Bag } from 'iconsax-react-native/src';
import { OrderListCard } from 'src/components/orders/list/order-item-card';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import ImageTextPlaceholder from 'src/components/ui/image-text-placholder';
import SearchOrderFilterModal from 'src/components/search/search-orders-filter-modal';
import useModals from 'src/hooks/use-modals';
import { useFormik } from 'formik';
import { getFieldvalues, removeEmptyAndUndefined } from 'src/assets/utils/js';
import Pressable from 'src/components/ui/base/pressable';
import Separator from 'src/components/ui/others/separator';
import Radio from 'src/components/ui/buttons/radio';
import { SearchOrdersParams } from 'src/screens/orders/search-orders';
import { ButtonVariant } from 'src/components/ui/buttons/button';

interface Props extends BottomModalProps {
  onSelectOrder: (order: OrderInterface) => void;
  selectedOrder: string;
  error?: string;
  hasError?: boolean;
  showLeftAccessory?: boolean;
  hideAddCustomerBtn?: boolean;
  label?: string;
  externalRef?: React.MutableRefObject<DropDownMethods>;
  showAnchor?: boolean;
}

const SelectOrder = ({
  onSelectOrder,
  selectedOrder,
  error,
  label = 'Select Order',
  hasError,
  hideAddCustomerBtn,
  showLeftAccessory,
  externalRef,
  showAnchor = true,
  ...rest
}: Props) => {
  const { modals, toggleModal } = useModals(['searchOrderFilter']);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<GetOrdersParams['filter']>({});
  
  const searchFilters = useMemo(() => {
    const baseFilters: GetOrdersParams['filter'] = {};
    
    if (searchTerm) {
      baseFilters.search = searchTerm;
    }
    
    return { ...baseFilters, ...filters };
  }, [searchTerm, filters]);

  const { orders, setOrders, getOrdersRequest, handlePullToRefresh, handleOnEndReach } = useOrdersApi(searchFilters);

  const form = useFormik<SearchOrdersParams>({
    initialValues: {
      search: '',
      payment_status: undefined,
      customer: '',
      fulfillment_method: undefined as any,
      channel: undefined as any,
      products: [],
    },
    onSubmit: value => {
      const filter = removeEmptyAndUndefined(value);
      setFilters(filter);
    },
  });

  const validFilters = removeEmptyAndUndefined(filters);
  const filtersWithoutSearch = (filters: GetOrdersParams['filter']) => {
    const { search, ...rest } = filters;
    return rest;
  };

  const submitForm = () => {
    toggleModal('searchOrderFilter', false);
    form.submitForm();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setFilters({});
    form.resetForm();
  };

  const hasActiveFilters = searchTerm.length > 0 || Object.keys(filtersWithoutSearch(validFilters)).length > 0;

  const renderOrderItem = ({ item }: { item: OrderInterface }) => (
    <Pressable
      className="px-20 py-15"
      onPress={() => onSelectOrder(item)}>
      <Row className="items-center">
        <View className="flex-1">
          <OrderListCard
            onPress={()=> onSelectOrder(item)}
            order={item}
            disabled={false}
            rightAddon={
              <Row className="justify-start">
                <ImageTextPlaceholder text={item?.customer?.name} size="sm" />
                <StatusPill
                  className="bg-grey-bgOne ml-[-5px]"
                  statusType={item?.is_paid ? StatusType.SUCCESS : StatusType.DANGER}
                  title={item?.is_paid ? 'PAID' : 'UNPAID'}
                />
              </Row>
            }
          />
        </View>
        <View className="ml-15">
          <Radio active={selectedOrder === item.id} />
        </View>
      </Row>
      <Separator className="mx-0 mt-15" />
    </Pressable>
  );

  const renderListHeader = () => {
    if (!orders.length || !searchTerm) return null;
    
    return (
      <View className="py-10 px-20">
        <BaseText classes="text-grey-mutedDark">
          {getOrdersRequest?.response?.data?.total ?? 0} Orders found
        </BaseText>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center px-20">
      <EmptyState
        icon={<Bag variant="Bulk" size={wp(40)} color={colors.grey.muted} />}
        text={
          hasActiveFilters
            ? 'No orders match your search criteria'
            : 'No orders found'
        }
        showBtn={false}
      />
    </View>
  );

  return (
    <>
      <BottomModal
        {...rest}
        title={label}
        size="lg"
        useScrollView={false}
        useChildrenAsDirectChild={true}
        buttons={[
          {
            text: 'Clear Filters',
            onPress: clearFilters,
            variant: ButtonVariant.LIGHT,
            disabled: !hasActiveFilters,
          },
          {
            text: 'Done',
            onPress: rest.closeModal,
          },
        ]}>
        
        <View className="px-20 py-15 border-b border-grey-border">
          <Input
            placeholder="Search orders by ID, customer name, or items"
            value={searchTerm}
            onChangeText={setSearchTerm}
            leftAccessory={<Search size={wp(16)} primaryColor={colors?.black.muted} />}
            rightAccessory={
              <Pressable onPress={() => toggleModal('searchOrderFilter')}>
                <CircledIcon className="bg-grey-bgOne p-8">
                  <Setting5 size={wp(14)} color={colors.black.placeholder} />
                  {Object.keys(filtersWithoutSearch(validFilters)).length > 0 && (
                    <View className="absolute -top-2 -right-2 bg-primary-main rounded-full w-5 h-5 items-center justify-center">
                      <BaseText fontSize={10} classes="text-white">
                        {Object.keys(filtersWithoutSearch(validFilters)).length}
                      </BaseText>
                    </View>
                  )}
                </CircledIcon>
              </Pressable>
            }
          />
        </View>

        <BottomSheetFlashList
          data={orders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          estimatedItemSize={100}
          onEndReached={handleOnEndReach}
          onEndReachedThreshold={0.3}
          refreshControl={
            <RefreshControl 
              refreshing={false} 
              onRefresh={handlePullToRefresh}
            />
          }
          ListHeaderComponent={renderListHeader}
          ListEmptyComponent={
            getOrdersRequest.isLoading ? (
              <View className="flex-1 justify-center items-center p-20">
                <BaseText classes="text-grey-muted">Loading orders...</BaseText>
              </View>
            ) : (
              renderEmptyState()
            )
          }
          ListFooterComponent={
            orders.length > 0 && getOrdersRequest.isLoading ? (
              <View className="py-20 items-center">
                <BaseText classes="text-grey-muted">Loading more orders...</BaseText>
              </View>
            ) : null
          }
        />
      </BottomModal>

      <SearchOrderFilterModal
        isVisible={modals.searchOrderFilter}
        onPressContinue={submitForm}
        form={form}
        closeModal={() => toggleModal('searchOrderFilter', false)}
      />
    </>
  );
};

export default SelectOrder;
