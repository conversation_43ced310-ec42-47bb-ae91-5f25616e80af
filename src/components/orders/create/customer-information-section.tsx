import { getFieldvalues, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import { ArrowRight, ChevronDown, ChevronUp, Search } from '@/components/ui/icons';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import colors from '@/theme/colors';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from './accordion-anchor';
import Pressable from '@/components/ui/base/pressable';
import Input from '@/components/ui/inputs/input';
import { View } from 'react-native';
import { FormikProps } from 'formik';
import useModals from 'src/hooks/use-modals';
import { Add, AddCircle } from 'iconsax-react-native/src';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import { ApiData, ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { useMemo, useRef, useState } from 'react';
import useAuthContext from 'src/contexts/auth/auth-context';
import CustomerInitial from '@/components/customer/customer-initial';
import ListItemCard from '@/components/ui/cards/list-item-card';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import {
  GetItemsParams,
  PaginateSearchParams,
  CustomerInterface,
  ProductItemInterface,
  CreateOrderParams,
  GET_CUSTOMERS,
  phoneObjectFromString,
  removeCountryCode,
} from 'catlog-shared';
import { CreateOrderFormParams } from 'src/screens/orders/order.types';

interface CustomerInformationSectionsProps {
  form: FormikProps<CreateOrderFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
}

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const CustomerInformationSection = ({ form, accordionRef, onPressSave }: CustomerInformationSectionsProps) => {
  const [searchQuery, setSearchQuery] = useState<{ query: string; active: string }>({ query: '', active: '' });
  const dropDownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['addCustomerModal']);
  const [customers, setCustomers] = useState<CustomerInterface[]>([]);

  const getCustomersRequest = useApi<PaginateSearchParams, CustomerResponse>(
    {
      key: 'get-customers',
      apiFunction: GET_CUSTOMERS,
      method: 'GET',
      onSuccess: response => {
        setCustomers(response.data.data);
      },
    },
    {
      filter: {
        search: '',
      },
      per_page: 9007199254740991,
      sort: 'desc',
    },
  );

  const customersMapped = useMemo(
    () =>
      customers?.map(item => ({
        value: item.id!,
        label: item.name,
        subTitle: removeCountryCode(item.phone),
        leftElement: <CustomerInitial initial={item.name[0]} />,
      })),
    [customers],
  );

  const handleOpenAddCustomerModal = () => {
    dropDownRef.current?.close();
    setTimeout(() => {
      toggleModal('addCustomerModal');
    }, 600);
  };

  const handleAddCustomer = () => {
    toggleModal('addCustomerModal', false);
    setTimeout(() => {
      dropDownRef.current?.open();
    }, 600);
  };

  const handleSelectCustomer = (customerId: string, customerData: CreateOrderParams['customer']) => {
    form.setFieldValue('selectedCustomerId', customerId);
    form.setFieldValue('customer', customerData);

    if (form.values?.useSameInfoAsCustomer === true) {
      form.setFieldValue('delivery_info.name', customerData?.name);
      const phoneSplit = phoneObjectFromString(customerData?.phone ?? '');
      form.setFieldValue('delivery_phone', phoneSplit);
    }
  };

  return (
    <View>
      <Accordion
        // preOpeningAction={accordionPreOpeningAction}
        anchorElement={status => (
          <AccordionAnchor
            title="Customer Information"
            isOpened={status}
            isSaved={!status && form.values?.customer?.name !== ''}
            isError={!status && !form.values?.customer?.name}
          />
        )}
        ref={accordionRef}>
        <View>
          <SelectDropdown
            ref={dropDownRef}
            selectedItem={form.values?.selectedCustomerId}
            hasSearch
            onPressItem={(customerId: string) => {
              form.setFieldValue('selectedCustomerId', customerId);
              const customer = getCustomerFromList(customers, customerId);
              handleSelectCustomer(customerId, {
                name: customer?.name ?? '',
                email: customer?.email ?? '',
                phone: customer?.phone ?? '',
                store: customer?.store ?? '',
              });
            }}
            error={getFieldvalues('customer.name', form).error}
            hasError={getFieldvalues('customer.name', form).hasError}
            isLoading={getCustomersRequest.isLoading}
            leftAccessory={
              form.values?.selectedCustomerId ? (
                <CustomerInitial
                  classes="px-8"
                  textProps={{ fontSize: 11 }}
                  initial={form.values?.customer?.name?.[0] ?? '-'}
                />
              ) : undefined
            }
            label={'Select Customer'}
            items={customersMapped}
            containerClasses="my-15"
            // headerComponent={
            //   <Input
            //     containerClasses="mt-20"
            //     placeholder={'Search Customers'}
            //     value={searchQuery.query}
            //     onChangeText={t => setSearchQuery(prev => ({ ...prev, query: t }))}
            //     onSubmitEditing={t => {
            //       t.persist();
            //       setSearchQuery(prev => ({ ...prev, active: t.nativeEvent.text }));
            //     }}
            //     returnKeyType="search"
            //     leftPadding={wp(35)}
            //     leftAccessory={<Search size={wp(16)} primaryColor={colors?.black.muted} />}
            //   />
            // }
            listAddOns={
              <ListItemCard
                showBorder={false}
                title={'Add Customer'}
                className="border-t border-t-grey-border"
                titleProps={{ weight: 'medium' }}
                onPress={handleOpenAddCustomerModal}
                leftElement={
                  <CircledIcon className="p-6 bg-white">
                    <AddCircle variant={'Bold'} size={wp(20)} color={colors.primary.main} />
                  </CircledIcon>
                }
                rightElement={
                  <CircledIcon className="p-10 bg-white">
                    <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
                  </CircledIcon>
                }
              />
            }
          />
        </View>
        <Button
          className="self-end"
          style={{ width: 'auto' }}
          text={'Save'}
          onPress={onPressSave}
          disabled={!form.values.selectedCustomerId}
          size={ButtonSize.MEDIUM}
        />
      </Accordion>
      <AddCustomerModal
        isVisible={modals.addCustomerModal}
        closeModal={() => toggleModal('addCustomerModal', false)}
        callBack={(customer: CustomerInterface) => {
          setCustomers([...customers, customer]);
          handleSelectCustomer(customer.id!, {
            name: customer.name,
            email: customer.email,
            phone: customer.phone,
            store: customer.store,
          });
        }}
      />
    </View>
  );
};

const getCustomerFromList = (customers: CustomerInterface[], customerId: string) =>
  customers.find(({ id }) => id === customerId);

export default CustomerInformationSection;
