import { getFieldvalues, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import colors from '@/theme/colors';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from './accordion-anchor';
import Pressable from '@/components/ui/base/pressable';
import { View } from 'react-native';
import { FormikProps } from 'formik';
import { Add, AddCircle } from 'iconsax-react-native/src';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { useRef, useState } from 'react';
import FeesCard from './fees-card';
import LeftLabelledInput from '@/components/ui/inputs/left-labelled-input';
import { GET_COUPONS, PaginateSearchParams, CouponItemInterface, ORDER_FEE_TYPES } from 'catlog-shared';
import { CreateOrderFormParams } from 'src/screens/orders/order.types';

interface DiscountAndFeeSectionProps {
  form: FormikProps<CreateOrderFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  accordionPreOpeningAction?: VoidFunction;
}

interface CouponResponse extends ResponseWithPagination<{ items: CouponItemInterface[] }> {}

const DiscountAndFeeSection = ({
  form,
  accordionRef,
  accordionPreOpeningAction,
  onPressSave,
}: DiscountAndFeeSectionProps) => {
  const [showFeeInput, setShowFeeInput] = useState(false);
  const couponRef = useRef<DropDownMethods>(null);

  const getCouponsRequest = useApi<PaginateSearchParams, CouponResponse>(
    {
      key: 'get-coupons',
      apiFunction: GET_COUPONS,
      method: 'GET',
      onSuccess: response => {},
    },
    {
      filter: {},
      per_page: 9007199254740991,
      sort: 'desc',
    },
  );

  const feeTypes = Object.entries(ORDER_FEE_TYPES).map(data => ({
    value: data[1],
    label: data[1],
  }));

  const coupons = getCouponsRequest?.response?.data?.items?.map(coupon => ({
    value: coupon.coupon_code,
    label: coupon.coupon_code,
  }));

  const addedFeeTypes = form.values.fees?.map(({ type }) => type) ?? [];

  const handleAddCoupon = (couponCode: string) => {
    form.setFieldValue('coupon', couponCode);
  };

  const handleSelectFeeType = (feeType: string) => {
    if (feeType === ORDER_FEE_TYPES.COUPON) {
      setTimeout(() => {
        couponRef.current?.open();
      }, 500);
      return;
    }
    form.setFieldValue('tempSelectedFeeType', feeType);
  };

  const fees = form.values?.fees ?? [];

  const setOptions = (
    fees: {
      type: string;
      amount: number;
    }[],
  ) => {
    form.setFieldValue('fees', fees);
  };

  const updateOption = (
    option: {
      type: string;
      amount: number;
    },
    index: number,
  ) => {
    const feesCopy = [...fees];
    feesCopy[index] = option;

    setOptions(feesCopy);
  };

  const deleteOption = (index: number) => {
    const feesCopy = [...fees];
    feesCopy.splice(index, 1);

    setOptions(feesCopy);
  };

  const addFeeOption = () => {
    const feesCopy = [
      ...fees,
      {
        amount: null,
        type: null,
      },
    ];
    setOptions(feesCopy);
  };

  return (
    <View>
      <Accordion
        // preOpeningAction={accordionPreOpeningAction}
        anchorElement={status => <AccordionAnchor title="Discount & Fees" isOpened={status} isOptional />}
        ref={accordionRef}>
        <View className="mt-15 mb-20">
          {form.values.coupon && (
            <FeesCard
              onPressDelete={() => form.setFieldValue('coupon', '')}
              feeData={{ type: 'COUPON', amount: null }}
              isCoupon
              coupons={coupons}
              couponCode={form.values.coupon}
              updateOption={updateOption}
              availableFeeTypes={feeTypes}
              form={form}
            />
          )}
          {form.values.fees?.map((item, index) => (
            <FeesCard
              index={index}
              key={index}
              feeData={item}
              isCoupon={false}
              coupons={coupons}
              updateOption={updateOption}
              availableFeeTypes={feeTypes}
              form={form}
              onPressDelete={() => deleteOption(index)}
            />
          ))}
          {/* <Row className="gap-x-10 mb-15">
            <View className="flex-1">
              <SelectDropdown
                items={feeTypes.filter(item => !addedFeeTypes.includes(item.value))}
                onPressItem={handleSelectFeeType}
                selectedItem={form.values.tempSelectedFeeType}
                label={'Select Type'}
                isMultiSelect
                selectedItems={addedFeeTypes}
              />
            </View>
            <View className="flex-1">
              <LeftLabelledInput
                leftText="NGN"
                label={'Fee'}
                keyboardType={'number-pad'}
                {...getFieldvalues('tempFeeAmount', form)}
                // onSubmitEditing={handleAddFee}
              />
            </View>
          </Row> */}
          <Pressable className="self-start py-5" onPress={addFeeOption}>
            <Row className=" justify-start">
              <Add size={wp(14)} color={colors.primary.main} />
              <BaseText fontSize={12} weight={'medium'} classes="text-primary-main ml-2">
                Add Fee
              </BaseText>
            </Row>
          </Pressable>
          <Button
            className="self-end"
            style={{ width: 'auto' }}
            text={'Save'}
            size={ButtonSize.MEDIUM}
            onPress={onPressSave}
          />
        </View>
      </Accordion>

      <SelectDropdown
        items={coupons ?? []}
        ref={couponRef}
        showAnchor={false}
        onPressItem={value => handleAddCoupon(value)}
        label={'Select Coupon'}
        containerClasses="my-15"
      />
    </View>
  );
};

export default DiscountAndFeeSection;
