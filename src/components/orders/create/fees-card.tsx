import { Edit2, Trash } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { getFieldvalues, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import colors from '@/theme/colors';
import Pressable from '@/components/ui/base/pressable';
import { useEffect, useRef, useState } from 'react';
import SelectDropdown, { DropDownItem, DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import { FormikProps } from 'formik';
import { ORDER_FEE_TYPES } from 'node_modules/catlog-shared/dist';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import { CreateOrderFormParams } from 'src/screens/orders/order.types';
import { useCurrencyConverter } from 'src/hooks/useCurrencyConverter';

interface FeesCardProps {
  feeData: {
    type: string;
    amount: number;
  };
  updateOption: (
    option: {
      type: string;
      amount: number;
    },
    index: number,
  ) => void;
  index?: number;
  availableFeeTypes: DropDownItem[];
  form: FormikProps<CreateOrderFormParams>;
  onPressDelete?: VoidFunction;
  coupons?: DropDownItem[];
  isCoupon?: boolean;
  couponCode?: string;
}

const FeesCard = ({
  feeData,
  index,
  form,
  coupons,
  couponCode,
  isCoupon,
  availableFeeTypes,
  updateOption,
  onPressDelete,
}: FeesCardProps) => {
  const [displayMode, setDisplayMode] = useState<'form' | 'card'>('form');
  const couponRef = useRef<DropDownMethods>(null);

  const addedFeeTypes = form.values.fees?.map(({ type }) => type) ?? [];

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const toggleDisplayModeToCard = () => {
    if (isCoupon === true) {
      if (couponCode) {
        setDisplayMode('card');
      }
      return;
    }
    if (feeData.type && feeData.amount) {
      setDisplayMode('card');
    }
  };

    const formatCurrency = useCurrencyConverter(form?.values?.currency);


  const handleSelectFeeType = (type: string) => {
    if (type === ORDER_FEE_TYPES.COUPON) {
      setTimeout(() => {
        couponRef.current?.open();
      }, 500);
      return;
    }
    // form.setFieldValue('tempSelectedFeeType', feeType);
    updateOption({ ...feeData, type }, index);
  };

  const selectCoupon = (c: string) => {
    form.setFieldValue('coupon', c);
    toggleDisplayModeToCard();
  };

  return (
    <View>
      <View style={{ display: displayMode === 'form' ? 'flex' : 'none' }}>
        <Row className="gap-x-10 mb-15">
          <View className="flex-1">
            <SelectDropdown
              items={availableFeeTypes}
              onPressItem={handleSelectFeeType}
              selectedItem={feeData.type}
              label={'Select Type'}
              isMultiSelect
              selectedItems={addedFeeTypes}
            />
          </View>
          <View className="flex-1">
            <LeftLabelledInput
              leftText={form.values?.currency}
              label={'Fee'}
              keyboardType={'number-pad'}
              value={String(feeData.amount ?? '')}
              onChangeText={value => updateOption({ ...feeData, amount: Number(value) }, index)}
              onBlur={toggleDisplayModeToCard}
            />
          </View>
        </Row>
      </View>
      <View style={{ display: displayMode === 'card' ? 'flex' : 'none' }}>
        <Row className="py-[13px] px-15 mb-15 rounded-[10px] border border-grey-border bg-grey-bgOne">
          <View className="flex-1">
            <BaseText>{feeData.type}</BaseText>
            {feeData.amount && (
              <BaseText fontSize={11} weight={'semiBold'} classes="mt-5">
                {formatCurrency(feeData.amount)}
              </BaseText>
            )}
            {couponCode && (
              <BaseText fontSize={11} weight={'semiBold'} classes="mt-5">
                {couponCode}
              </BaseText>
            )}
          </View>
          <Pressable onPress={() => (isCoupon ? couponRef.current?.open() : setDisplayMode('form'))}>
            <CircledIcon className="bg-white mr-10">
              <Edit2 size={wp(18)} color={colors.black.placeholder} />
            </CircledIcon>
          </Pressable>
          <Pressable onPress={onPressDelete}>
            <CircledIcon className="bg-white">
              <Trash size={wp(18)} color={colors.black.placeholder} />
            </CircledIcon>
          </Pressable>
        </Row>
      </View>
      <SelectDropdown
        items={coupons ?? []}
        ref={couponRef}
        showAnchor={false}
        onPressItem={selectCoupon}
        label={'Select Coupon'}
        containerClasses="my-15"
      />
    </View>
  );
};

export default FeesCard;
