import { showError, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import { ChevronDown, ChevronUp } from '@/components/ui/icons';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import colors from '@/theme/colors';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from './accordion-anchor';
import Pressable from '@/components/ui/base/pressable';
import Input from '@/components/ui/inputs/input';
import { View } from 'react-native';
import { FormikProps } from 'formik';
import OrderItemCard from '../order-item-card';
import SelectProductsModal from '@/components/products/storefront-products/select-products-modal';
import useModals from 'src/hooks/use-modals';
import { Add } from 'iconsax-react-native/src';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import { ApiData, useApi } from 'src/hooks/use-api';
import {
  GetItemsParams,
  ProductItemInterface,
  VariantItem,
  CreateOrderParams,
  OrderItem,
  CartItem,
  GET_ITEMS,
} from 'catlog-shared';
import { CreateOrderFormParams } from 'src/screens/orders/order.types';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ProductsResponse } from 'src/screens/products/storefront';

export interface CustomVariantItem extends VariantItem {
  baseIndex: number;
}

interface GroupedItem {
  item_id: string;
  item: ProductItemInterface;
  variantItems: CustomVariantItem[];
  quantity: number;
}

interface OrderItemsSectionsProps {
  form: FormikProps<CreateOrderFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  accordionPreOpeningAction?: VoidFunction;
  onPressSave: VoidFunction;
}

const OrderItemsSection = ({ onPressSave, accordionRef, form }: OrderItemsSectionsProps) => {
  const { modals, toggleModal } = useModals(['selectProducts']);

  const { store } = useAuthContext();

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    { apiFunction: GET_ITEMS, method: 'GET', key: 'fetch-custom-items' },
    { per_page: 9007199254740991, filter: { store: store?.id } },
  );

  const handleDeleteItem = (index: number) => {
    const selectedItems = form.values.items;
    selectedItems.splice(index, 1);
    form.setFieldValue('items', selectedItems);
  };

  const getProductItem = (id: string) => {
    const allProducts = getProductsRequest?.response?.data?.items;
    const selectedProducts = allProducts;
    const productItem = selectedProducts.find(item => item.id === id);
    return productItem;
  };

  const getMinOrderQty = (id: string) => {
    const allProducts = getProductsRequest?.response?.data?.items;
    const selectedProducts = allProducts;
    const productItem = selectedProducts.find(item => item.id === id);
    return productItem.minimum_order_quantity;
  };

  const selectProductItem = (id: string, variantId?: string) => {
    const productItem = getProductItem(id);
    let variantItem: VariantItem;
    if (variantId) {
      variantItem = productItem?.variants?.options.find(v => v.id === variantId);
    }
    return variantId ? variantItem : productItem;
  };

  const handleSetProductSelection = (
    selections?: { id: string; variant_id?: string | null; quantity?: number | null }[],
  ) => {
    const selectedItems: CreateOrderParams['items'] = [];

    for (let index = 0; index < selections?.length!; index++) {
      const id = selections?.[index].id!;
      const variant_id = selections?.[index].variant_id!;
      const quantity = selections?.[index].quantity!;
      const minQty = getMinOrderQty(id);
      const productQuantity = quantity > minQty ? quantity : (minQty ?? 1);
      const item = {
        item_id: id,
        item: selectProductItem(id) as ProductItemInterface,
        variant: selectProductItem(id, variant_id) as VariantItem,
        quantity: productQuantity,
        variant_id: variant_id,
      };
      selectedItems.push(item);
    }
    form.setFieldValue('items', selectedItems);
  };

  const handleToggleQuantity = (index: number, action: 'decrease' | 'increase') => {
    const availableQuantity = form.values.items[index].item?.quantity ?? 0;
    const selectedItemsCopy = form.values.items;
    const selectedItem = form.values.items[index];
    const minOrderQty = selectedItem?.item?.minimum_order_quantity ?? 1;
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        if (selectedItem.quantity >= availableQuantity) {
          showError(
            null,
            `Not enough quantity available, you only have ${availableQuantity} quantity of this item in stock`,
          );
          return;
        }
        selectedItem.quantity = selectedItem.quantity + 1;
        form.setFieldValue('items', selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1 || selectedItem.quantity === minOrderQty) {
          handleDeleteItem(index);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          form.setFieldValue('items', selectedItemsCopy);
        }
        return;
      }
    }
  };

  const groupItemsByItemId = (items: CartItem[]): GroupedItem[] => {
    const itemMap = new Map<string, GroupedItem>();

    items.forEach((itemEntry, index) => {
      const { item_id, item, variant, quantity } = itemEntry;

      const variantFormat = { ...variant, quantity, baseIndex: index };

      if (itemMap.has(item_id)) {
        const existingGroup = itemMap.get(item_id)!;
        existingGroup.variantItems.push(variantFormat);
        // existingGroup.quantity += quantity;
      } else {
        itemMap.set(item_id, { item_id, item: item!, variantItems: [variantFormat], quantity });
      }
    });
    return Array.from(itemMap.values());
  };
  {
    /* todo: @silas: review the commented code above */
  }

  return (
    <View>
      <Accordion
        anchorElement={status => (
          <AccordionAnchor
            title="Order Items"
            isSaved={form.values.items?.length > 0 && status === false}
            isOpened={status}
          />
        )}
        ref={accordionRef}
        initiallyOpened>
        {form.values.items?.length < 1 && (
          <Pressable onPress={() => toggleModal('selectProducts')} className="mt-15">
            <Input
              editable={false}
              onPressIn={() => toggleModal('selectProducts')}
              label={'Select Items'}
              rightAccessory={
                <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
                </View>
              }
              containerClasses={`py-0`}
            />
          </Pressable>
        )}
        <View className="mt-15">
          {groupItemsByItemId(form.values.items ?? [])?.map((item, index) => (
            <OrderItemCard
              key={index}
              item={item}
              product={item.item!}
              activeCurrency={form.values.currency}
              variants={item.variantItems}
              onPressDelete={() => handleDeleteItem(index)}
              onPressToggleQuality={(index, action) => handleToggleQuantity(index, action)}
            />
          ))}
          {/* todo: @silas: review the commented code above */}
        </View>
        {form.values.items?.length > 0 && (
          <WhiteCardBtn
            leftIcon={<Add size={wp(14)} color={colors.primary.main} />}
            onPress={() => toggleModal('selectProducts')}>
            Add Item
          </WhiteCardBtn>
        )}
        <Button
          className="self-end"
          style={{ width: 'auto' }}
          text={'Save'}
          onPress={onPressSave}
          disabled={form.values.items?.length < 1}
          size={ButtonSize.MEDIUM}
        />
      </Accordion>

      <SelectProductsModal
        products={getProductsRequest?.response?.data?.items ?? []}
        isVisible={modals.selectProducts}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest}
        selectedProducts={form.values?.items}
        size="lg"
        setSelectedProducts={handleSetProductSelection}
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
    </View>
  );
};

export default OrderItemsSection;
