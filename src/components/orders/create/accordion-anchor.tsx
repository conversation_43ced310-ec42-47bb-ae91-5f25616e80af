import { cx, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import { ChevronDown, ChevronUp, Plus } from '@/components/ui/icons';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import colors from '@/theme/colors';
import { Add, ElementPlus, InfoCircle } from 'iconsax-react-native/src';

const AccordionAnchor = ({
  title,
  isSaved,
  isError,
  isOptional,
  isOpened,
  classes,
  type = 'chevron',
}: {
  title: string;
  isSaved?: boolean;
  isError?: boolean;
  isOpened?: boolean;
  isOptional?: boolean;
  classes?: string;
  type?: 'chevron' | 'plus';
}) => {
  return (
    <Row className={cx('flex flex-row py-0', classes)}>
      <Row className="items-center" style={{ gap: 5 }}>
        <BaseText type="heading" fontSize={15}>
          {title}
        </BaseText>
        {isSaved && <Radio active />}
        {isError && <InfoCircle size={wp(18)} color={colors.accentRed.main} variant={'Bold'} />}
        {isOptional && <StatusPill statusType={StatusType.DEFAULT} className="bg-grey-bgOne" title="Optional" />}
      </Row>
      <CircledIcon className="bg-grey-bgOne p-7">
        {type === 'chevron' &&
          (isOpened ? (
            <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
          ) : (
            <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
          ))}
        {type === 'plus' &&
          (isOpened ? (
            <Add
              style={{ transform: [{ rotate: '45deg' }] }}
              size={wp(16)}
              strokeWidth={2}
              color={colors.black.muted}
            />
          ) : (
            <Add size={wp(16)} strokeWidth={2} color={colors.black.muted} />
          ))}
      </CircledIcon>
    </Row>
  );
};

export default AccordionAnchor;
