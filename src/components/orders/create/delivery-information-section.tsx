import { getFieldvalues, toCurrency, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import colors from '@/theme/colors';
import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import AccordionAnchor from './accordion-anchor';
import Pressable from '@/components/ui/base/pressable';
import Input from '@/components/ui/inputs/input';
import { View } from 'react-native';
import { FormikProps } from 'formik';
import { CreateOrderFormParams } from 'src/screens/orders/record-order';
import OrderItemCard from '../order-item-card';
import SelectProductsModal from '@/components/products/storefront-products/select-products-modal';
import useModals from 'src/hooks/use-modals';
import { Add, AddCircle } from 'iconsax-react-native/src';
import Button, { ButtonSize } from '@/components/ui/buttons/button';
import { ApiData, ResponseWithoutPagination, ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { useRef } from 'react';
import useAuthContext from 'src/contexts/auth/auth-context';
import CustomerInitial from '@/components/customer/customer-initial';
import ListItemCard from '@/components/ui/cards/list-item-card';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import PhoneNumberInput from '@/components/ui/inputs/phone-number-input';
import {
  GET_DELIVERY_AREAS,
  GetDeliveryAreasParams,
  GetItemsParams,
  PaginateSearchParams,
  CustomerInterface,
  DELIVERY_METHODS,
  DeliveryArea,
  ProductItemInterface,
  CreateOrderParams,
  GET_CUSTOMERS,
  phoneObjectFromString,
} from 'catlog-shared';

interface DeliveryInformationSectionsProps {
  form: FormikProps<CreateOrderFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  accordionPreOpeningAction?: VoidFunction;
}

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const DeliveryInformationSection = ({
  form,
  accordionRef,
  accordionPreOpeningAction,
  onPressSave,
}: DeliveryInformationSectionsProps) => {
  const { storeId } = useAuthContext();

  const getDeliveryAreasRequest = useApi<GetDeliveryAreasParams, ResponseWithoutPagination<DeliveryArea[]>>(
    {
      key: 'get-customers',
      apiFunction: GET_DELIVERY_AREAS,
      method: 'GET',
      onSuccess: response => {},
    },
    {
      store: storeId!,
    },
  );

  const deliveryAreas = getDeliveryAreasRequest?.response?.data?.map(item => ({
    value: item.id!,
    label: item.name,
    subTitle: item.fee ? toCurrency(item.fee) : '-',
  }));

  const handleUseCustomerInformation = () => {
    if (form.values?.useSameInfoAsCustomer === false) {
      form.setFieldValue('delivery_info.name', form.values?.customer?.name);
      const phoneSplit = phoneObjectFromString(form.values?.customer?.phone ?? '');
      form.setFieldValue('delivery_phone', phoneSplit);
    }
    form.setFieldValue('useSameInfoAsCustomer', !form.values?.useSameInfoAsCustomer);
  };

  return (
    <Accordion
      // preOpeningAction={accordionPreOpeningAction}
      anchorElement={status => (
        <AccordionAnchor
          title="Delivery Information"
          isOpened={status}
          // isSaved={!status && form.values?.customer?.name !== ''}
          // isError={!status && !form.values?.customer?.name}
        />
      )}
      ref={accordionRef}>
      <View>
        <SelectDropdown
          items={deliveryType}
          onPressItem={value => form.setFieldValue('delivery_method', value)}
          selectedItem={form.values.delivery_method}
          label={'Delivery Method'}
          containerClasses="my-15"
        />
        {form.values.delivery_method === DELIVERY_METHODS.DELIVERY && (
          <View>
            {form.values.selectedCustomerId && (
              <Row className="mb-15">
                <CustomerInitial
                  classes="px-8"
                  textProps={{ fontSize: 11 }}
                  initial={form.values?.customer?.name?.[0]!}
                />
                <BaseText classes=" flex-1 mx-8">Use same information as customer</BaseText>
                <Pressable onPress={handleUseCustomerInformation}>
                  <Radio active={form.values?.useSameInfoAsCustomer} onClick={handleUseCustomerInformation} />
                </Pressable>
              </Row>
            )}
            {!form.values?.useSameInfoAsCustomer && (
              <View>
                <Input
                  label={'Full name'}
                  {...getFieldvalues('delivery_info.name', form)}
                  value={form.values.delivery_info.name}
                />
                <PhoneNumberInput
                  label={'Phone Number'}
                  containerClasses="mt-15"
                  {...getFieldvalues('delivery_phone', form)}
                  onChange={value => form.setFieldValue('delivery_phone', value)}
                />
              </View>
            )}
            <SelectDropdown
              items={deliveryAreas ?? []}
              onPressItem={value => form.setFieldValue('delivery_info.area', value)}
              selectedItem={form.values.delivery_info.area}
              titleProps={{ fontSize: 13, weight: 'regular', classes: 'text-black-muted' }}
              descriptionProps={{ fontSize: 11, weight: 'semiBold', classes: 'text-black-primary' }}
              label={'Select Delivery Area'}
              containerClasses="mt-15"
              error={getFieldvalues('delivery_info.area', form).error}
              hasError={getFieldvalues('delivery_info.area', form).hasError}
            />
            <Input
              label={'Delivery Address'}
              multiline
              className="h-[80]"
              containerClasses="my-15"
              {...getFieldvalues('delivery_info.delivery_address', form)}
              value={form.values.delivery_info.delivery_address}
            />
          </View>
        )}
      </View>
      <Button
        className="self-end"
        style={{ width: 'auto' }}
        text={'Save'}
        size={ButtonSize.MEDIUM}
        onPress={onPressSave}
      />
    </Accordion>
  );
};

const deliveryType = Object.entries(DELIVERY_METHODS).map(data => ({
  value: data[1],
  label: data[1],
}));

export default DeliveryInformationSection;
