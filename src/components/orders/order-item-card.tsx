import React from 'react';
import { styled } from 'nativewind';
import { View, ViewProps } from 'react-native';
import cx from 'classnames';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../ui';
import Pressable, { PressableProps } from '../ui/base/pressable';
import { Add, Minus, Trash } from 'iconsax-react-native/src';
import { enumToHumanFriendly, toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { useState } from 'react';
import SectionContainer, { ContainerType } from '../ui/section-container';
import CustomImage from '../ui/others/custom-image';
import { CustomVariantItem } from './create/order-items-section';
import { ProductItemInterface, VariantItem, OrderItem, CartItem, CURRENCIES, getItemThumbnail } from 'catlog-shared';
import { useCurrencyConverter } from 'src/hooks/useCurrencyConverter';

export enum OrderItemType {
  'IMAGES',
  'CUSTOM',
}

interface OrderItemCardProps extends ViewProps {
  item: Omit<CartItem, 'variant'>;
  product: ProductItemInterface;
  type?: OrderItemType;
  onPressDelete?: VoidFunction;
  activeCurrency?: CURRENCIES;
  onPressToggleQuality?: (index: number, action: 'decrease' | 'increase') => void;
  variants?: CustomVariantItem[];
}

const OrderItemCard = ({
  item,
  onPressDelete,
  product,
  variants,
  activeCurrency,
  onPressToggleQuality,
  ...props
}: OrderItemCardProps) => {
  const hasVariants = variants?.length! > 1 || Object.keys(variants?.[0]?.values ?? []).length > 0;
  const isImageVariant = variants?.[0].image;

  const isImageWithVariant = Object.keys(variants?.[0]?.values ?? []).length > 0;

  const formatCurrency = useCurrencyConverter(activeCurrency);

  const minOrderQty = item?.item?.minimum_order_quantity!

  return (
    <SectionContainer containerType={ContainerType.OUTLINED} className="p-12">
      {(!hasVariants || isImageWithVariant) && (
        <View className="bg-grey-bgOne rounded-12">
          <Row className=" px-12 py-8">
            <CustomImage imageProps={{ source: getItemThumbnail(product) }} className="h-40 w-40 rounded-5" />
            <View className="flex-1 mx-8">
              <BaseText weight="medium" classes="text-black-secondary">
                {product?.name}
              </BaseText>
            </View>
            {!hasVariants && (
              <Pressable onPress={onPressDelete}>
                <CircledIcon>
                  <Trash size={wp(17)} color={colors.accentRed.main} />
                </CircledIcon>
              </Pressable>
            )}
          </Row>
          {!hasVariants && (
            <PriceContainer
              price={formatCurrency(item.item?.price)!} //todo: @silas take a look at this
              quantity={item.quantity!}
              toggleQuantity={a => onPressToggleQuality?.(variants?.[0].baseIndex!, a)}
            />
          )}
        </View>
      )}
      {hasVariants && (
        <View>
          {!isImageVariant && (
            <>
              {variants?.map((i, idx) => (
                <SectionContainer key={idx} className="p-0 mt-15 rounded-12">
                  {Object.entries(i.values! ?? []).map((item, index) => (
                    <Row
                      key={index}
                      className={cx('p-12', {
                        'border-b border-b-grey-border': index !== Object.entries(i.values! ?? []).length - 1,
                      })}>
                      <BaseText classes="text-black-placeholder">
                        {enumToHumanFriendly(item[0])}:{' '}
                        <BaseText weight="medium" classes="text-black-placeholder">
                          {item[1]}
                        </BaseText>
                      </BaseText>
                    </Row>
                  ))}
                  <PriceContainer
                    price={formatCurrency(item.item?.price)!} //todo: @silas take a look at this
                    quantity={i.quantity!}
                    toggleQuantity={a => onPressToggleQuality?.(i.baseIndex, a)}
                  />
                </SectionContainer>
              ))}
            </>
          )}
          {isImageVariant && (
            <>
              {variants?.map((i, idx) => (
                <View className={cx('bg-grey-bgOne rounded-12', { 'mt-15': idx !== 0 || isImageWithVariant })}>
                  {!isImageWithVariant && (
                    <Row className=" px-12 py-8">
                      <CustomImage imageProps={{ source: i.image }} className="h-40 w-40 rounded-5" />
                      <View className="flex-1 mx-8">
                        <BaseText weight="medium" classes="text-black-secondary">
                          {product?.name}
                        </BaseText>
                      </View>
                      <Pressable>
                        <CircledIcon>
                          <Trash size={wp(17)} color={colors.accentRed.main} />
                        </CircledIcon>
                      </Pressable>
                    </Row>
                  )}
                  {isImageWithVariant && (
                    <>
                      {Object.entries(i.values! ?? []).map((item, index) => (
                        <Row
                          className={cx('p-12', {
                            'border-b border-b-grey-border': index !== Object.entries(i.values! ?? []).length - 1,
                          })}>
                          <BaseText classes="text-black-placeholder">
                            {enumToHumanFriendly(item[0])}:{' '}
                            <BaseText weight="medium" classes="text-black-placeholder">
                              {item[1]}
                            </BaseText>
                          </BaseText>
                        </Row>
                      ))}
                    </>
                  )}
                  <PriceContainer
                    price={formatCurrency(item.item?.price)!} //todo: @silas take a look at this
                    quantity={i.quantity!}
                    toggleQuantity={a => onPressToggleQuality?.(i.baseIndex, a)}
                  />
                </View>
              ))}
            </>
          )}
        </View>
      )}
    </SectionContainer>
  );
};

export default OrderItemCard;

const PriceContainer = ({
  price,
  quantity,
  toggleQuantity,
}: {
  price: string;
  quantity: number;
  toggleQuantity?: (action: 'decrease' | 'increase') => void;
}) => {
  return (
    <Row className="bg-white px-15 py-12 rounded-12 border border-grey-border">
      <BaseText weight="semiBold" classes="text-black-muted">
        {price}
      </BaseText>
      <Row className="justify-start gap-x-10">
        <Pressable
          className="p-5 border-grey-border border rounded-full bg-white"
          onPress={() => toggleQuantity?.('decrease')}>
          {quantity === 1 ? (
            <Trash size={wp(16)} color={colors.accentRed.main} />
          ) : (
            <Minus size={wp(16)} color={colors.black.placeholder} />
          )}
        </Pressable>
        <View className="py-5 px-14 border-grey-border border rounded-[6px] bg-white">
          <BaseText fontSize={12} weight={'semiBold'}>
            {quantity}
          </BaseText>
        </View>
        <Pressable
          className="p-5 border-grey-border border rounded-full bg-white"
          onPress={() => toggleQuantity?.('increase')}>
          <Add size={wp(16)} color={colors.black.placeholder} />
        </Pressable>
      </Row>
    </Row>
  );
};
