import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import useModals from 'src/hooks/use-modals';
import { View } from 'react-native';
import { ArrowUpRight } from 'src/components/ui/icons';
import { AutoUpdateFormProps, BulkUpdateForm, BulkUpdateMethod } from 'src/screens/products/types';
import CustomImage from 'src/components/ui/others/custom-image';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import UpdateOptionsPriceModal from './update-option-price-modal';
import { useState } from 'react';
import AutoUpdatePrice from './auto-update-price';
import { BulkUpdateFormItem } from 'catlog-shared';
import { FormikProps } from 'node_modules/formik/dist';

enum UPDATE_PRICE_STEPS {
  SELECT_PRODUCT = 'SELECT_PRODUCT',
  SELECT_UPDATE_TYPE = 'SELECT_UPDATE_TYPE',
  INCREASE_PRICE_FORM = 'INCREASE_PRICE_FORM',
}

interface UpdatePriceStepProps {
  method: BulkUpdateMethod;
  selected: BulkUpdateFormItem[];
  currency: string;
  batchData: {
    batchStart: number;
    batchEnd: number;
  };
  handleFormUpdate: (id: string, data: BulkUpdateFormItem) => void;
  autoUpdateForm: FormikProps<AutoUpdateFormProps>;
  calcNewPrice: (old_price: number, values: AutoUpdateFormProps) => number;
  getUpdatedItem: (
    item: BulkUpdateFormItem,
    values: AutoUpdateFormProps,
    computeVariants?: boolean,
  ) => BulkUpdateFormItem;
}

const UpdatePriceStep = ({
  currency,
  method,
  selected,
  batchData,
  handleFormUpdate,
  autoUpdateForm,
  calcNewPrice,
  getUpdatedItem,
}: UpdatePriceStepProps) => {
  const [activeItemIndex, setActiveItemIndex] = useState<number>(null);
  const { modals, toggleModal } = useModals(['selectProducts', 'updateOptionsPriceModal']);

  const updateItemPrice = (value: string, item: BulkUpdateFormItem) => {
    const price = Number.parseInt(value);
    const itemUpdate = { ...item, price };
    handleFormUpdate(item.id, itemUpdate);
  };

  const openOptionsModal = (index: number) => {
    setActiveItemIndex(index);
    toggleModal('updateOptionsPriceModal');
  };

  return (
    <View>
      <View style={{ display: method === BulkUpdateMethod.MANUAL ? 'flex' : 'none' }}>
        <BaseText type="heading">Update Price Manually</BaseText>
        <View className="mt-20" style={{ rowGap: hp(15) }}>
          {selected.map(
            (p, i) =>
              i >= batchData.batchStart &&
              i < batchData.batchEnd && (
                <View className="bg-grey-bgOne rounded-12" key={i}>
                  <Row className="justify-start py-8 px-12">
                    <CustomImage
                      imageProps={{ source: { uri: p?.image }, contentFit: 'cover' }}
                      className="h-40 w-40 rounded-8"
                    />
                    <View className="flex-1">
                      <BaseText weight="medium" classes="text-black-secondary ml-10">
                        {p.name}
                      </BaseText>
                      {/* <BaseText weight="medium" classes="text-black-placeholder ml-10 mt-2">
                        Current Price: {toCurrency(p.old_price)}
                      </BaseText> */}
                    </View>
                  </Row>
                  {p.variants?.options?.length < 1 && (
                    <View className="bg-white rounded-12">
                      <LeftLabelledInput
                        leftText={currency}
                        value={p.price}
                        onChangeText={v => updateItemPrice(v, p)}
                      />
                    </View>
                  )}
                  {p.variants?.options?.length > 1 && (
                    <View className="bg-white rounded-12 p-15 border border-grey-border">
                      <WhiteCardBtn
                        icon={<ArrowUpRight size={wp(14)} currentColor={colors.primary.main} strokeWidth={2} />}
                        className="py-0 px-0"
                        onPress={() => openOptionsModal(i)}>
                        {p.variants?.options?.length} Available
                      </WhiteCardBtn>
                    </View>
                  )}
                </View>
              ),
          )}
        </View>
      </View>
      <View style={{ display: method === BulkUpdateMethod.AUTO ? 'flex' : 'none' }}>
        <AutoUpdatePrice {...{ selected, currency, autoUpdateForm, calcNewPrice, getUpdatedItem }} />
      </View>
      <UpdateOptionsPriceModal
        item={selected[activeItemIndex]}
        {...{ currency }}
        update={d => handleFormUpdate(selected[activeItemIndex].id, d)}
        isVisible={modals.updateOptionsPriceModal}
        closeModal={() => toggleModal('updateOptionsPriceModal', false)}
      />
    </View>
  );
};

export default UpdatePriceStep;
