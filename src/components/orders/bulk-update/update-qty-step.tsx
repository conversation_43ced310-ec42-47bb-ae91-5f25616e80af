import colors from '@/theme/colors';
import { cx, getDummyArray, hp, wp } from '@/assets/utils/js';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import useModals from 'src/hooks/use-modals';
import { View } from 'react-native';
import { ArrowUpRight } from 'src/components/ui/icons';
import { AutoUpdateFormProps, BulkUpdateForm, BulkUpdateMethod } from 'src/screens/products/types';
import CustomImage from 'src/components/ui/others/custom-image';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import updateOptionsQuantityModal from './update-option-price-modal';
import { useState } from 'react';
import AutoUpdatePrice from './auto-update-price';
import { BulkUpdateFormItem, VariantItem } from 'catlog-shared';
import { FormikProps } from 'node_modules/formik/dist';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import QuantityToggle from 'src/components/ui/buttons/quantity-toggle';
import Pressable from 'src/components/ui/base/pressable';
import { InfoCircle, Trash } from 'node_modules/iconsax-react-native/src';
import CircledIcon from 'src/components/ui/circled-icon';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import UpdateOptionsQuantityModal from './update-option-quantity-modal';
import Shimmer from 'src/components/ui/shimmer';
import { ApiData } from 'src/hooks/use-api';
import { GetItemsParams, ProductItemInterface } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';

interface UUpdateQtyStepProps {
  selected: BulkUpdateFormItem[];
  currency: string;
  batchData: { batchStart: number; batchEnd: number };
  handleFormUpdate: (id: string, data: BulkUpdateFormItem) => void;
  getProductsRequest?: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
  fromLowStockNotification?: boolean;
  updateType?: 'lowStock' | 'outOfStock' | 'bulkUpdate';
}

const UpdateQtyStep = ({
  currency,
  selected,
  batchData,
  handleFormUpdate,
  getProductsRequest,
  fromLowStockNotification,
  updateType,
}: UUpdateQtyStepProps) => {
  const [activeItemIndex, setActiveItemIndex] = useState<number>(null);
  const { modals, toggleModal } = useModals(['selectProducts', 'updateOptionsQuantityModal']);
  const { store } = useAuthContext();

  const updateItem = (value: number, item: BulkUpdateFormItem) => {
    const quantity = value;
    const itemUpdate = { ...item, quantity };
    handleFormUpdate(item.id, itemUpdate);
  };

  const openOptionsModal = (index: number) => {
    setActiveItemIndex(index);
    toggleModal('updateOptionsQuantityModal');
  };

  const updateIsAlwaysAvailable = (value: boolean, item: BulkUpdateFormItem) => {
    const itemUpdate: BulkUpdateFormItem = { ...item, is_always_available: value };
    handleFormUpdate(item.id, itemUpdate);
  };

  const deleteItem = (i: number) => {
    // const selectedCopy = form;
    // selectedCopy.slice(i, 1);
    // setForm(selectedCopy);
  };

  // Show skeleton loader when products are loading
  if (getProductsRequest?.isLoading) {
    return (
      <View>
        <BaseText type="heading">Update Price Manually</BaseText>
        <View className="mt-20" style={{ rowGap: hp(15) }}>
          {getDummyArray(3).map((_, index) => (
            <UpdateQtyItemSkeleton key={index} />
          ))}
        </View>
      </View>
    );
  }

  return (
    <View>
      <View>
        {!fromLowStockNotification && (
          <BaseText type="heading" classes="mb-10">
            Update Quantities Manually
          </BaseText>
        )}
        <View className="mt-10" style={{ rowGap: hp(15) }}>
          {selected.map(
            (p, i) =>
              i >= batchData.batchStart &&
              i < batchData.batchEnd && (
                <View className="bg-grey-bgOne rounded-12" key={i}>
                  <Row className="justify-start py-8 px-12">
                    <CustomImage
                      imageProps={{ source: { uri: p?.image }, contentFit: 'cover' }}
                      className="h-40 w-40 rounded-8"
                    />
                    <View className="flex-1">
                      <Row className="justify-between py-8 px-12 w-full">
                        <BaseText
                          weight="medium"
                          classes="text-black-secondary mr-10"
                          numberOfLines={1}
                          ellipsizeMode="tail"
                          style={{ maxWidth: p.variants?.options?.length > 0 ? '40%' : '60%' }}>
                          {p.name}
                        </BaseText>
                        {/* {p?.variants?.options?.length > 0 && (
                          <StatusPill
                            statusType={StatusType.DEFAULT}
                            title={`${p?.variants?.options?.length} Options`}
                          />
                        )} */}

                        {p.variants?.options?.length > 0 && !p?.is_always_available && (
                          <Row className="justify-center bg-accentRed-whiteTransparent rounded-[30px] px-10 py-6 -mr-5">
                            <InfoCircle size={wp(12)} color={colors.accentRed.main} strokeWidth={2} />
                            <BaseText type="body" fontSize={wp(10)} classes="text-accentRed-main ml-5">
                              {getLowStockItemsCount(
                                p.variants?.options,
                                updateType,
                                store.configuration.global_stock_threshold,
                                p?.low_stock_threshold,
                              )}{' '}
                              {updateType === 'outOfStock' ? 'out of' : 'low'} stock Option(s)
                            </BaseText>
                          </Row>
                        )}

                        {p.variants?.options?.length < 1 &&
                          !p?.is_always_available &&
                          p.quantity > -1 &&
                          p.quantity <= (p.low_stock_threshold || store.configuration.global_stock_threshold) && (
                            <Row className="justify-center bg-accentRed-whiteTransparent rounded-[30px] px-10 py-6 -mr-5">
                              <InfoCircle size={wp(12)} color={colors.accentRed.main} strokeWidth={2} />
                              <BaseText type="body" fontSize={wp(10)} classes="text-accentRed-main ml-5">
                                {p.quantity === 0 ? 'Out of stock' : 'Low stock'}
                              </BaseText>
                            </Row>
                          )}
                      </Row>
                    </View>
                    {/* <Pressable onPress={() => deleteItem(i)}>
                      <CircledIcon>
                        <Trash color={colors.accentRed.main} size={wp(18)} />
                      </CircledIcon>
                    </Pressable> */}
                  </Row>
                  <View className="bg-white rounded-12 px-15 py-20 border border-grey-border">
                    <Row>
                      <Row className="justify-start">
                        <BaseText classes="text-black-placeholder mr-10">Always in stock</BaseText>
                        <CustomSwitch
                          value={p?.is_always_available}
                          onValueChange={v => updateIsAlwaysAvailable(v, p)}
                        />
                      </Row>

                      {!p.is_always_available && p.variants?.options?.length > 1 && (
                        <WhiteCardBtn
                          icon={<ArrowUpRight size={wp(14)} currentColor={colors.primary.main} strokeWidth={2} />}
                          className="py-0 px-0"
                          onPress={() => openOptionsModal(i)}>
                          {/* {p.quantity} Units Left */}
                          {p.variants?.options?.length} Options
                        </WhiteCardBtn>
                      )}
                      {p.variants?.options?.length < 1 && !p.is_always_available && (
                        <QuantityToggle
                          disabled={p.is_always_available && p.variants?.options?.length < 1}
                          quantity={p?.quantity ?? 10}
                          showDelete={false}
                          onPressAdd={() => updateItem(p.quantity + 1, p)}
                          onPressMinus={() => updateItem(p.quantity - 1, p)}
                        />
                      )}
                    </Row>
                  </View>
                </View>
              ),
          )}
        </View>
      </View>
      <UpdateOptionsQuantityModal
        item={selected[activeItemIndex] ?? undefined}
        {...{ currency }}
        update={d => handleFormUpdate(selected[activeItemIndex]?.id, d)}
        isVisible={modals.updateOptionsQuantityModal}
        closeModal={() => {
          toggleModal('updateOptionsQuantityModal', false);
        }}
        size="lg"
      />
    </View>
  );
};

// Skeleton loader for individual update quantity item
const UpdateQtyItemSkeleton = () => {
  return (
    <View className="bg-grey-bgOne rounded-12">
      {/* Header Row */}
      <Row className="justify-start py-8 px-12">
        {/* Product Image skeleton */}
        <Shimmer width={wp(40)} height={wp(40)} borderRadius={8} />

        {/* Product Info skeleton */}
        <View className="flex-1">
          <Row className="justify-start py-8 px-12">
            <View className="ml-10 mr-10 flex-1">
              <Shimmer width={wp(150)} height={hp(14)} borderRadius={6} classes="mb-5" />
              <Shimmer width={wp(80)} height={hp(12)} borderRadius={6} />
            </View>
          </Row>
        </View>

        {/* Delete button skeleton */}
        <View className="p-8 bg-grey-bgTwo rounded-full">
          <Shimmer width={wp(18)} height={wp(18)} borderRadius={wp(9)} />
        </View>
      </Row>

      {/* Content Card skeleton */}
      <View className="bg-white rounded-12 p-15 border border-grey-border">
        <Row>
          {/* Switch section skeleton */}
          <Row className="justify-start flex-1">
            <Shimmer width={wp(100)} height={hp(12)} borderRadius={6} classes="mr-10" />
            <Shimmer width={wp(40)} height={hp(20)} borderRadius={10} />
          </Row>

          {/* Right side button/toggle skeleton */}
          <Shimmer width={wp(80)} height={hp(32)} borderRadius={8} />
        </Row>
      </View>
    </View>
  );
};

export default UpdateQtyStep;
export { UpdateQtyItemSkeleton };

const getLowStockItemsCount = (
  options: VariantItem[],
  type: 'lowStock' | 'outOfStock' | 'bulkUpdate',
  storeThreshold: number,
  itemThreshold?: number,
) => {
  let threshold = !itemThreshold || itemThreshold === 0 ? storeThreshold : itemThreshold;

  if (type === 'outOfStock') {
    threshold = 0;
  }

  const lowOrOutOfStockOptions = options.filter(o => {
    if (type !== 'outOfStock') {
      return o.quantity <= threshold;
    }
    return o.quantity === 0;
  });

  return lowOrOutOfStockOptions.length;
};
