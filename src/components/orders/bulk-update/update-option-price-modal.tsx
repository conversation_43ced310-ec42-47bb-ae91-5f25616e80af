import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SectionContainer, { ContainerType } from 'src/components/ui/section-container';
import { BaseText, Row } from 'src/components/ui';
import CustomImage from 'src/components/ui/others/custom-image';
import { hp, removeUnderscores, toCurrency } from 'src/assets/utils/js';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import { ScrollView } from 'react-native-gesture-handler';
import useLayoutHeight from 'src/hooks/use-layout-height';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import { BulkUpdateFormItem } from 'catlog-shared';


interface UpdateOptionsPriceModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  item: BulkUpdateFormItem;
  currency: string;
  update: (data: BulkUpdateFormItem) => void;
}

const UpdateOptionsPriceModal = ({ closeModal, item, update, currency, ...props }: UpdateOptionsPriceModalProps) => {
  const updateOptionPrice = (index: number, value: string) => {
    const price = Number.parseInt(value);
    const itemUpdate = { ...item };
    itemUpdate.variants.options[index].price = price;
    update(itemUpdate);
  };

  const updateItemPrice = (value: string) => {
    const price = Number.parseInt(value);
    const itemUpdate = { ...item, price };
    update(itemUpdate);
  };

  const toggleOptionAvailability = (index: number, state: boolean) => {
    const itemUpdate = { ...item };
    itemUpdate.variants.options[index].is_available = state;
    update(itemUpdate);
  };

  const extraOption = item?.variants?.options[0]?.values ? Object.keys(item?.variants?.options[0]?.values)[0] : null;
  const { onLayout, flexStyle } = useLayoutHeight(0);

  return (
    <BottomModal
      {...props}
      modalStyle={flexStyle}
      containerStyle={flexStyle}
      innerStyle={flexStyle}
      closeModal={closeModal}
      buttons={[{ text: 'Continue', onPress: closeModal }]}
      title={'Update Option Prices'}>
      <ScrollView>
        <View className="px-20 pb-40" style={flexStyle} onLayout={onLayout}>
          <SectionContainer className="p-12 mt-0" containerType={ContainerType.OUTLINED}>
            <View className="bg-grey-bgOne rounded-12">
              <Row className="justify-start py-8 px-12">
                <CustomImage
                  imageProps={{ source: { uri: item?.image }, contentFit: 'cover' }}
                  className="h-40 w-40 rounded-8"
                />
                <View className="flex-1">
                  <BaseText weight="medium" classes="text-black-secondary ml-10">
                    {item?.name}
                  </BaseText>
                  {/* <BaseText weight="medium" classes="text-black-placeholder ml-10 mt-2">
                    Current Price: {toCurrency(item?.old_price)}
                  </BaseText> */}
                </View>
              </Row>
              <View className="bg-white rounded-12">
                <LeftLabelledInput leftText={currency} value={item?.price} onChangeText={v => updateItemPrice(v)} />
              </View>
            </View>
            <View className="mt-20" style={{ rowGap: hp(15) }}>
              {item?.variants?.options.map((o, idx) =>
                item?.variants.type === 'images' ? (
                  <View key={idx}>
                    <View className="bg-grey-bgOne rounded-12" key={idx}>
                      <Row className="justify-start py-8 px-12">
                        <CustomImage
                        imageProps={{ source: { uri: o?.image }, contentFit: 'cover' }}
                        className="h-40 w-40 rounded-8"
                      />
                        <View className="flex-1 ml-10">
                          <BaseText weight="medium" classes="text-black-secondary">
                            {o?.values ? `${removeUnderscores(extraOption)} ${o.values[extraOption]}` : item?.name}
                          </BaseText>
                        </View>
                        <Row>
                          <CustomSwitch value={o.is_available} onValueChange={v => toggleOptionAvailability(idx, v)} />
                        </Row>
                      </Row>
                      <View className="bg-white rounded-12">
                        <LeftLabelledInput
                          leftText={currency}
                          editable={o.is_available}
                          value={o?.price}
                          onChangeText={v => updateOptionPrice(idx, v)}
                        />
                      </View>
                    </View>
                  </View>
                ) : (
                  <View className="bg-grey-bgOne rounded-12" key={idx}>
                    <Row className="justify-start py-8 px-12">
                      {/* <CustomImage
                        imageProps={{ source: { uri: o?.image }, contentFit: 'cover' }}
                        className="h-40 w-40 rounded-8"
                      /> */}
                      <View className="flex-1">
                        <BaseText weight="medium" classes="text-black-secondary">
                          {o?.values && (
                            <BaseText>
                              {Object.keys(o.values)?.[0] ?? '-'}: {genarateStringFromVariantValues(o.values)}
                            </BaseText>
                          )}
                        </BaseText>
                        {/* <BaseText weight="medium" classes="text-black-placeholder mt-2">
                          Current Price: {toCurrency(item?.old_price)}
                        </BaseText> */}
                      </View>
                      <Row>
                        <CustomSwitch value={o.is_available} onValueChange={v => toggleOptionAvailability(idx, v)} />
                      </Row>
                    </Row>
                    <View className="bg-white rounded-12">
                      <LeftLabelledInput
                        leftText={currency}
                        editable={o.is_available}
                        value={o?.price}
                        onChangeText={v => updateOptionPrice(idx, v)}
                      />
                    </View>
                  </View>
                ),
              )}
            </View>
          </SectionContainer>
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default UpdateOptionsPriceModal;

const genarateStringFromVariantValues = (values: { [key: string]: string }) => {
  let string = '';

  if (!values) {
    return '-';
  }

  Object.values(values).forEach((val, index) => {
    string += index === 0 ? val : ` - ${val}`;
  });

  return string;
};
