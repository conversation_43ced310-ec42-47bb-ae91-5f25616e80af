import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import SectionContainer, { ContainerType } from 'src/components/ui/section-container';
import { BaseText, Row, WhiteCardBtn } from 'src/components/ui';
import CustomImage from 'src/components/ui/others/custom-image';
import { hp, removeUnderscores, toCurrency, wp } from 'src/assets/utils/js';
import LeftLabelledInput from 'src/components/ui/inputs/left-labelled-input';
import { ScrollView } from 'react-native-gesture-handler';
import useLayoutHeight from 'src/hooks/use-layout-height';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import { BulkUpdateFormItem } from 'catlog-shared';
import { ArrowUpRight } from 'src/components/ui/icons';
import QuantityToggle from 'src/components/ui/buttons/quantity-toggle';
import { useEffect, useMemo } from 'react';
import React from 'react';

interface UpdateOptionsQuantityModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  item: BulkUpdateFormItem;
  currency: string;
  update: (data: BulkUpdateFormItem) => void;
}

const UpdateOptionsQuantityModal = ({
  closeModal,
  item,
  update,
  currency,
  ...props
}: UpdateOptionsQuantityModalProps) => {
  const updateIsAlwaysAvailable = () => {
    update({ ...item, is_always_available: !Boolean(item?.is_always_available) });
  };

  const quantity = useMemo(() => {
    return item?.variants?.options?.reduce((p, c) => {
      return p + c.quantity ? p + c.quantity : 0;
    }, 0);
  }, [item]);

  useEffect(() => {
    if (item !== undefined) {
      const itemUpdate = { ...item };
      itemUpdate.quantity = quantity;
      update(itemUpdate);
    }
  }, [quantity]);

  const addQuantityToOptions = () => {
    const itemUpdate = { ...item };
    itemUpdate.variants.options = item?.variants?.options?.map(o => ({ ...o, quantity: 10 }));
    update(itemUpdate);
  };

  const updateOptionQuantity = (index: number, quantity: number) => {
    const itemUpdate = { ...item };
    itemUpdate.variants.options[index].quantity = quantity;
    update(itemUpdate);
  };

  const toggleOptionAvailability = (index: number, state: boolean) => {
    const itemUpdate = { ...item };
    itemUpdate.variants.options[index].is_available = state;
    update(itemUpdate);
  };

  const extraOption = item?.variants?.options[0]?.values ? Object.keys(item?.variants?.options[0]?.values)[0] : null;
  const { onLayout, flexStyle } = useLayoutHeight(0);

  return (
    <BottomModal
      {...props}
      modalStyle={flexStyle}
      containerStyle={flexStyle}
      innerStyle={flexStyle}
      closeModal={closeModal}
      buttons={[{ text: 'Continue', onPress: closeModal }]}
      title={'Update Option Prices'}>
      <ScrollView>
        <View className="px-20 pb-40" style={flexStyle} onLayout={onLayout}>
          <SectionContainer className="p-12 mt-0" containerType={ContainerType.OUTLINED}>
            <View className="bg-grey-bgOne rounded-12">
              <Row className="justify-start py-8 px-12">
                <CustomImage
                  imageProps={{ source: { uri: item?.image }, contentFit: 'cover' }}
                  className="h-40 w-40 rounded-8"
                />
                <View className="flex-1">
                  <BaseText weight="medium" classes="text-black-secondary ml-10">
                    {item?.name}
                  </BaseText>
                  {/* <BaseText weight="medium" classes="text-black-placeholder ml-10 mt-2">
                    Current Price: {toCurrency(item?.old_price)}
                  </BaseText> */}
                </View>
              </Row>
              <View className="bg-white rounded-12 p-15 border border-grey-border">
                <Row className="justify-start">
                  <BaseText classes="text-black-placeholder mr-10">Always in stock</BaseText>
                  <CustomSwitch value={item?.is_always_available} onValueChange={updateIsAlwaysAvailable} />
                </Row>
              </View>
            </View>
            <View className="mt-20" style={{ rowGap: hp(15) }}>
              {item?.variants?.options.map((o, idx) => (
                <View key={idx}>
                  <View>
                    <View className="bg-grey-bgOne rounded-12" key={idx}>
                      <Row className="justify-start py-8 px-12">
                        {item?.variants.type === 'images' && (
                          <CustomImage
                            imageProps={{ source: { uri: o?.image }, contentFit: 'cover' }}
                            className="h-40 w-40 rounded-8"
                          />
                        )}
                        <View className="flex-1 ml-10">
                          <BaseText weight="medium" classes="text-black-secondary">
                            {item?.variants.type === 'images' &&
                              (o?.values ? `${removeUnderscores(extraOption)} ${o.values[extraOption]}` : item?.name)}
                            {item?.variants.type !== 'images' && o?.values && (
                              <BaseText>
                                {Object.keys(o.values)?.[0] ?? '-'}: {genarateStringFromVariantValues(o.values)}
                              </BaseText>
                            )}
                          </BaseText>
                        </View>
                      </Row>
                      <View className="bg-white rounded-12 p-15 border border-grey-border">
                        <Row>
                          <Row className="justify-start">
                            <BaseText classes="text-black-placeholder mr-10">Is Available</BaseText>
                            <CustomSwitch
                              value={o?.is_available}
                              onValueChange={v => toggleOptionAvailability(idx, v)}
                            />
                          </Row>

                          <View style={{ opacity: !o.is_available ? 0.4 : 1 }}>
                            <QuantityToggle
                              disabled={!o.is_available}
                              quantity={o?.quantity ?? 10}
                              showDelete={false}
                              onPressAdd={() => updateOptionQuantity(idx, o.quantity + 1)}
                              onPressMinus={() => updateOptionQuantity(idx, o.quantity - 1)}
                            />
                          </View>
                        </Row>
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </SectionContainer>
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default UpdateOptionsQuantityModal;

const genarateStringFromVariantValues = (values: { [key: string]: string }) => {
  let string = '';

  if (!values) {
    return '-';
  }

  Object.values(values).forEach((val, index) => {
    string += index === 0 ? val : ` - ${val}`;
  });

  return string;
};

// {item?.variants.type === 'images' ? (
//   <View key={idx}>
//     <View className="bg-grey-bgOne rounded-12" key={idx}>
//       <Row className="justify-start py-8 px-12">
//         <CustomImage
//           imageProps={{ source: { uri: o?.image }, contentFit: 'cover' }}
//           className="h-40 w-40 rounded-8"
//         />
//         <View className="flex-1 ml-10">
//           <BaseText weight="medium" classes="text-black-secondary">
//             {o?.values ? `${removeUnderscores(extraOption)} ${o.values[extraOption]}` : item?.name}
//           </BaseText>
//         </View>
//       </Row>
//       <View className="bg-white rounded-12 p-15 border border-grey-border">
//         <Row>
//           <Row className="justify-start">
//             <BaseText classes="text-black-placeholder mr-10">Always in stock</BaseText>
//             <CustomSwitch
//               value={o?.is_available}
//               onValueChange={v => toggleOptionAvailability(idx, v)}
//             />
//           </Row>

//           <View style={{ opacity: o.is_available ? 0.5 : 1 }}>
//             <QuantityToggle
//               disabled={o.is_available}
//               quantity={o?.quantity ?? 10}
//               showDelete={false}
//               onPressAdd={() => updateOptionQuantity(idx, o.quantity + 1)}
//               onPressMinus={() => updateOptionQuantity(idx, o.quantity - 1)}
//             />
//           </View>
//         </Row>
//       </View>
//     </View>
//   </View>
// ) : (
//   <View className="bg-grey-bgOne rounded-12" key={idx}>
//     <Row className="justify-start py-8 px-12">
//       {/* <CustomImage
//       imageProps={{ source: { uri: o?.image }, contentFit: 'cover' }}
//       className="h-40 w-40 rounded-8"
//     /> */}
//       <View className="flex-1">
//         <BaseText weight="medium" classes="text-black-secondary">
//           {o?.values && (
//             <BaseText>
//               {Object.keys(o.values)?.[0] ?? '-'}: {genarateStringFromVariantValues(o.values)}
//             </BaseText>
//           )}
//         </BaseText>
//         {/* <BaseText weight="medium" classes="text-black-placeholder mt-2">
//         Current Price: {toCurrency(item?.old_price)}
//       </BaseText> */}
//       </View>
//       <Row>
//         <CustomSwitch value={o.is_available} onValueChange={v => toggleOptionAvailability(idx, v)} />
//       </Row>
//     </Row>
//   </View>
// )}
