import { Activity, BagTick2, Profile2User, ShoppingBag } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify, toCurrency } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import AnalyticsCard from '../ui/cards/analytics-card';
import { StoreSummary } from '@/screens/orders/order-analytics';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';

export enum AnalyticsVariants {
  'TOTAL_ORDERS' = 'total_orders',
  'TOTAL_CUSTOMERS' = 'total_customers',
  'TOTAL_ORDERS_AMOUNT' = 'total_orders_amount',
  'TOTAL_COMPLETED' = 'total_completed_orders',
}

interface OrderAnalyticsProps {
  analytics: StoreSummary;
  loading?: boolean;
  currency?: string;
}

const OrderAnalyticsCards = ({ analytics = {} as StoreSummary, currency, loading }: OrderAnalyticsProps) => {
  const analyticsCardsInfo: AnalyticsCardInfo[] = [
    {
      title: 'Total Orders',
      iconBg: 'bg-accentRed-main',
      icon: <ShoppingBag variant="Bold" size={wp(18)} color={colors?.white} />,
      cardBg: 'bg-transparent',
      key: AnalyticsVariants.TOTAL_ORDERS,
      value: analytics[AnalyticsVariants.TOTAL_ORDERS] ? analytics[AnalyticsVariants.TOTAL_ORDERS] : '-',
      change: 0.5,
    },
    {
      title: 'Customers',
      cardBg: 'bg-transparent',
      icon: <Profile2User variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentOrange-main',
      key: AnalyticsVariants.TOTAL_CUSTOMERS,
      value: analytics[AnalyticsVariants.TOTAL_CUSTOMERS] ? analytics[AnalyticsVariants.TOTAL_CUSTOMERS] : '-',
      change: -3.6,
    },
    {
      title: 'Total Volume',
      cardBg: 'bg-transparent',
      icon: <Activity variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.TOTAL_ORDERS_AMOUNT,
      value: analytics[AnalyticsVariants.TOTAL_ORDERS_AMOUNT]
        ? millify(analytics[AnalyticsVariants.TOTAL_ORDERS_AMOUNT], 2, currency)
        : '-',
      change: 0,
    },
    {
      title: 'Fulfilled',
      cardBg: 'bg-transparent',
      icon: <BagTick2 variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentGreen-main',
      key: AnalyticsVariants.TOTAL_COMPLETED,
      value: analytics[AnalyticsVariants.TOTAL_COMPLETED] ? analytics[AnalyticsVariants.TOTAL_COMPLETED] : '-',
      change: 0,
    },
  ];

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo, analytics]);

  return (
    <View className="border border-grey-border rounded-[15px]">
      {loading && <AnalyticsSkeletonLoader />}
      {!loading && (
        <Fragment>
          {splitCards.map((group, i) => (
            <Fragment key={i}>
              <View className="flex-row last:mb-0">
                {group.map((info, index) => (
                  <Fragment key={index}>
                    <AnalyticsCard
                      className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                      title={info.title}
                      value={info.value}
                      iconBg={info.iconBg}
                      change={info.change}
                      showChange={false}
                      icon={info.icon}
                      addon={info.addon}
                      theme="white"
                    />
                    {index === 0 && <View className="w-1 bg-grey-border" />}
                  </Fragment>
                ))}
              </View>
              {i === 0 && <View className="h-1 bg-grey-border" />}
            </Fragment>
          ))}
        </Fragment>
      )}
    </View>
  );
};

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

const analyticsCardsInfo: AnalyticsCardInfo[] = [
  {
    title: 'Total Orders',
    iconBg: 'bg-accentRed-main',
    icon: <ShoppingBag variant="Bold" size={wp(18)} color={colors?.white} />,
    cardBg: 'bg-transparent',
    key: AnalyticsVariants.TOTAL_ORDERS,
    value: 209,
    change: 0.5,
  },
  {
    title: 'Customers',
    cardBg: 'bg-transparent',
    icon: <Profile2User variant="Bold" size={wp(18)} color={colors?.white} />,
    iconBg: 'bg-accentOrange-main',
    key: AnalyticsVariants.TOTAL_CUSTOMERS,
    value: '245',
    change: -3.6,
  },
  {
    title: 'Total Volume',
    cardBg: 'bg-transparent',
    icon: <Activity variant="Bold" size={wp(18)} color={colors?.white} />,
    iconBg: 'bg-accentYellow-main',
    key: AnalyticsVariants.TOTAL_ORDERS_AMOUNT,
    value: 'NGN 3.42M',
    change: 0,
  },
  {
    title: 'Fulfilled',
    cardBg: 'bg-transparent',
    icon: <BagTick2 variant="Bold" size={wp(18)} color={colors?.white} />,
    iconBg: 'bg-accentGreen-main',
    key: AnalyticsVariants.TOTAL_COMPLETED,
    value: 0,
    change: 0,
  },
];

export default OrderAnalyticsCards;
