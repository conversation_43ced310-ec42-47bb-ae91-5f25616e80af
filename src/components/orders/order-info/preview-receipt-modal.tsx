import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { toAppUrl } from 'catlog-shared';
import React, { useState } from 'react';
import { Dimensions, ScrollView, View } from 'react-native';
import Toast from 'react-native-toast-message';
import WebView from 'react-native-webview';
import { useFileDownload } from 'src/hooks/use-file-download';

import { cx } from '@/assets/utils/js';
import { ContainerLoader } from '@/components/products/add-product-controller/instagram-import/instagram-webview';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface Props extends Partial<BottomModalProps> {
  receiptId: string;
  // onAction: (action: string) => void;
  isInPreview?: boolean;
}
const PreviewReceiptModal: React.FC<Props> = ({ isInPreview, receiptId, ...props }) => {
  const [siteLoaded, setSiteLoaded] = useState(false);
  const url = toAppUrl(`receipts/pdf/${receiptId}`, true, EXPO_PUBLIC_PUBLIC_URL);

  const { downloadFile, isLoading } = useFileDownload();

  const handleDownloadReceipt = async () => {
    try {
      if (receiptId === null) return;

      // const subUrl = `orders/export?${paramsFromObject(reqData)}`;
      const subUrl = `receipts/pdf/${receiptId}`;
      const fileName = `receipt-${receiptId}-${new Date().toDateString()}.pdf`;
      const downloadResult = await downloadFile(subUrl, fileName);
    } catch (error) {
      Toast.show({ text1: 'Error processing your request', type: 'error' });
    }
  };

  const handleClose = () => {
    props.closeModal && props.closeModal();
    setSiteLoaded(false);
  };

  return (
    <>
      <BottomModal
        wrapChildren
        title="Preview Receipt"
        useChildrenAsDirectChild
        customSnapPoints={[90]}
        childrenWrapperStyle={{ padding: 20 }}
        {...props}
        closeModal={() => handleClose()}
        buttons={[{ text: 'Download Receipt', onPress: handleDownloadReceipt, isLoading }]}>
        <BottomSheetView style={{ flex: 1 }} className="flex-1" enableFooterMarginAdjustment>
          <WebView
            onLoad={() => setSiteLoaded(true)}
            // style={{ height: Dimensions.get('window').height / 1.5 }}
            className={cx('w-full flex-1', { 'opacity-0': !siteLoaded })}
            source={{ uri: url }}
            onError={syntheticEvent => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error: ', nativeEvent);
            }}
            onHttpError={syntheticEvent => {
              const { nativeEvent } = syntheticEvent;
              console.error('HTTP error: ', nativeEvent);
            }}
          />
          {!siteLoaded && <ContainerLoader message="Loading Preview..." />}
        </BottomSheetView>
      </BottomModal>
    </>
  );
};
export default PreviewReceiptModal;
