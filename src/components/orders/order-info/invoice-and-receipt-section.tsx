import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import { toAppUrl, OrderInterface, GetInvoicesParams, GET_INVOICES } from 'catlog-shared';
import { Copy, Link21, Receipt2, Receipt21 } from 'iconsax-react-native/src';
import React from 'react';
import { View } from 'react-native';
import Toast from 'react-native-toast-message';

import PreviewReceiptModal from './preview-receipt-modal';

import { copyToClipboard, hideLoader, showLoader, wp } from '@/assets/utils/js';
import InvoiceInfoModal from '@/components/invoices/invoice-info-modal';
import MoreOptionsModal from '@/components/invoices/more-options-modal';
import PreviewInvoiceModal from '@/components/invoices/preview-modal';
import SendInvoiceModal from '@/components/invoices/send-invoice-modal';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { ArrowUpRight, ChevronDown, ChevronUp } from '@/components/ui/icons';
import Accordion from '@/components/ui/others/accordion';
import InfoRow from '@/components/ui/others/info-row';
import Separator from '@/components/ui/others/separator';
import SectionContainer from '@/components/ui/section-container';
import { useApi } from '@/hooks/use-api';
import useInvoiceActions from '@/hooks/use-invoice-actions';
import { InvoicesResponse } from '@/screens/invoices/dashboard';
import colors from '@/theme/colors';

const InvoiceAndReceiptSection = ({ order }: { order?: OrderInterface }) => {
  const getInvoicesReq = useApi<GetInvoicesParams, InvoicesResponse>({
    apiFunction: GET_INVOICES,
    key: 'single-invoice-search',
    method: 'GET',
    autoRequest: false,
  });

  const { openInvoice, handleInvoiceItemAction, currentInvoice, setCurrentInvoice, modals, toggleModal } =
    useInvoiceActions([], () => {});

  const handleOpenInvoice = async (invoice: string) => {
    showLoader('Getting Invoice Details');
    const [res, err] = await getInvoicesReq.makeRequest({
      filter: {
        search: invoice,
      },
      page: 1,
      per_page: 10,
    });
    hideLoader();

    if (res) {
      setCurrentInvoice(res.data.invoices[0]);
      openInvoice(res.data.invoices[0]);
    }

    if (err) {
      Toast.show({ text1: 'Error previewing invoice', type: 'error' });
    }
  };

  const hasReceipt = Boolean(order?.receipt);
  const hasInvoice = Boolean(order?.invoice);

  return (
    <View className="mx-20">
      <Accordion
        anchorElement={status => (
          <Row className="flex flex-row py-7">
            <BaseText type="heading" fontSize={15}>
              Invoice and Receipt
            </BaseText>
            <CircledIcon className="bg-grey-bgOne p-7">
              {status ? (
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              ) : (
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              )}
            </CircledIcon>
          </Row>
        )}>
        <SectionContainer className="pt-15">
          {hasInvoice && (
            <View>
              <InfoRow
                iconBg="white"
                title="Invoice Link"
                icon={<Link21 size={wp(15)} color={colors.black.placeholder} />}
                valueElement={
                  <WhiteCardBtn
                    className="bg-grey-bgOne rounded-full py-0 px-0"
                    onPress={() => copyToClipboard(toAppUrl(`invoices/${order?.invoice}`, true, EXPO_PUBLIC_PUBLIC_URL))}
                    icon={<Copy size={wp(13)} color={colors.primary.main} strokeWidth={1.75} className="ml-2.5" />}>
                    Copy Link
                  </WhiteCardBtn>
                }
              />
              <Pressable onPress={() => handleOpenInvoice(order?.invoice as string)}>
                <InfoRow
                  iconBg="white"
                  title="View & Manage Invoice"
                  icon={<Receipt21 size={wp(15)} color={colors.black.placeholder} />}
                  valueElement={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}
                />
              </Pressable>
            </View>
          )}
          {!hasInvoice && (
            <Pressable>
              <InfoRow
                iconBg="white"
                title="Generate Invoice"
                icon={<Link21 size={wp(15)} color={colors.black.placeholder} />}
                valueElement={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}
              />
            </Pressable>
          )}
          {order?.is_paid && (
            <>
              <Separator className="mx-0 mt-0" />
              {hasReceipt && (
                <>
                  <InfoRow
                    iconBg="white"
                    title="Receipt Link"
                    icon={<Link21 size={wp(15)} color={colors.black.placeholder} />}
                    valueElement={
                      <WhiteCardBtn
                        className="bg-grey-bgOne rounded-full py-0 px-0"
                        onPress={() => copyToClipboard(toAppUrl(`receipts/${order?.receipt}`, true, EXPO_PUBLIC_PUBLIC_URL))}
                        icon={<Copy size={wp(13)} strokeWidth={1.75} color={colors.primary.main} className="ml-2.5" />}>
                        Copy Link
                      </WhiteCardBtn>
                    }
                  />
                  <Pressable
                    onPress={() => {
                      toggleModal('previewReceipt');
                      // openLinkInBrowser(toAppUrl(`receipts/pdf/${order.receipt}`, true, EXPO_PUBLIC_PUBLIC_URL));
                    }}>
                    <InfoRow
                      iconBg="white"
                      title="View & Share Receipt"
                      icon={<Receipt2 size={wp(15)} color={colors.black.placeholder} />}
                      valueElement={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}
                    />
                  </Pressable>
                </>
              )}
              {!hasReceipt && (
                <Pressable>
                  <InfoRow
                    iconBg="white"
                    title="Generate Receipt"
                    icon={<Link21 size={wp(15)} color={colors.black.placeholder} />}
                    valueElement={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}
                  />
                </Pressable>
              )}
            </>
          )}
        </SectionContainer>
      </Accordion>
      {currentInvoice && (
        <View>
          <InvoiceInfoModal
            copyInvoiceLink={() => handleInvoiceItemAction('copy')}
            editInvoice={() => handleInvoiceItemAction('edit')}
            openOptions={() => handleInvoiceItemAction('options')}
            activeInvoice={currentInvoice}
            {...{ modals, toggleModal, handleInvoiceItemAction }}
            isVisible={modals.info}
            closeModal={() => toggleModal('info', false)}
          />
          <MoreOptionsModal
            activeInvoice={currentInvoice}
            isVisible={modals.options}
            closeModal={() => toggleModal('options', false)}
            onAction={(action: actionType) => handleInvoiceItemAction(action, currentInvoice)}
          />
          <PreviewInvoiceModal
            activeInvoice={currentInvoice}
            isVisible={modals.preview}
            closeModal={() => toggleModal('preview', false)}
            onAction={(action: actionType) => handleInvoiceItemAction(action, currentInvoice)}
          />
          <SendInvoiceModal
            activeInvoice={currentInvoice}
            isVisible={modals.send}
            closeModal={() => toggleModal('send', false)}
          />
        </View>
      )}
      {/* <PreviewReceiptModal
        receiptId={order?.receipt}
        isVisible={modals.previewReceipt}
        closeModal={() => toggleModal('previewReceipt', false)}
      /> */}
    </View>
  );
};

type actionType = 'edit' | 'delete' | 'options' | 'download' | 'copy' | 'preview' | 'send' | 'mark';

export default InvoiceAndReceiptSection;
