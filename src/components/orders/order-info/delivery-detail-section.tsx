import { Linking, View } from 'react-native';
import colors from '@/theme/colors';
import { ArrowUpRight, CheckCircle, ChevronDown, ChevronUp } from '@/components/ui/icons';
import { openLinkInBrowser, wp } from '@/assets/utils/js';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { Box1, Location, Map1, Profile } from 'iconsax-react-native/src';
import SectionContainer from '@/components/ui/section-container';
import Separator from '@/components/ui/others/separator';
import cx from 'classnames';
import { ReactNode } from 'react';
import Pressable from '@/components/ui/base/pressable';
import Accordion from '@/components/ui/others/accordion';
import { useNavigation } from '@react-navigation/native';
import { ApiData, ResponseWithoutPagination } from 'src/hooks/use-api';
import {
  OrderInterface,
  GetOrderParms,
  DELIVERY_STATUSES,
  IDelivery,
  toAppUrl,
  removeCountryCode,
  getItemThumbnail,
} from 'catlog-shared';
import Can from 'src/components/ui/permissions/can';
import { SCOPES } from 'src/assets/utils/js/permissions';

const DeliveryDetailSection = ({
  order,
  getOrderRequest,
}: {
  order: OrderInterface;
  getOrderRequest: ApiData<GetOrderParms, ResponseWithoutPagination<OrderInterface>>;
}) => {
  const navigation = useNavigation();

  const handleRequestDelivery = async () => {
    const [response, error] = await getOrderRequest.makeRequest({
      id: order.id,
    });

    if (response) {
      const initialData: IDelivery = response?.data && {
        items: order?.items?.map(({ item }) => ({
          description: item.name,
          id: item.id,
          // image: item.images[item.thumbnail],
          image: getItemThumbnail(item),
          name: item.name,
          quantity: 1,
          unit_amount: item.price,
          unit_weight: undefined,
        })),
        receiver_address: order?.validated_delivery_address,
        status: DELIVERY_STATUSES.DRAFT,
        currency: order?.currency,
        order: order?.id,
      };
      navigation.navigate('RequestDelivery', { deliveryData: initialData });
      // navigation.navigate('DeliveriesStack', { screen: 'InitiateDelivery', initial: false, params: { deliveryData: initialData } });
    }
  };

  /*************  ✨ Codeium Command ⭐  *************/
  /**
   * Navigates to TrackDelivery screen with given delivery's id.
   * @param delivery Delivery object with id and status.
   */
  /******  7de33ebb-98c5-4a07-991a-558e6e27fa70  *******/ const trackDelivery = async (delivery: {
    id: string;
    status: DELIVERY_STATUSES;
  }) => {
    navigation.navigate('TrackDelivery', { id: delivery.id });
  };

  return (
    <View className="mx-20">
      <Accordion
        anchorElement={status => (
          <Row className="flex flex-row py-7">
            <BaseText type="heading" fontSize={15}>
              Delivery Details
            </BaseText>
            <CircledIcon className="bg-grey-bgOne p-7">
              {status ? (
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              ) : (
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              )}
            </CircledIcon>
          </Row>
        )}>
        <SectionContainer className="py-15">
          <DeliveryInfo
            first
            icon={<Map1 size={wp(15)} color={colors.black.placeholder} />}
            title={'Delivery Method'}
            rightElement={<StatusPill title={order?.delivery_method!} statusType={StatusType.DANGER} whiteBg />}
          />
          {order?.delivery_method?.toLowerCase() === 'delivery' && (
            <DeliveryInfo
              icon={<Profile size={wp(15)} color={colors.black.placeholder} />}
              title={order?.delivery_info?.name!}
              subTitle={order?.delivery_info?.phone ? removeCountryCode(order?.delivery_info?.phone!) : '-'}
            />
          )}
          {order?.delivery_method?.toLowerCase() === 'delivery' && (
            <DeliveryInfo
              icon={<Map1 size={wp(15)} color={colors.black.placeholder} />}
              title={'Delivery Area'}
              subTitle={order?.delivery_info?.delivery_area?.name!}
            />
          )}
          {order?.delivery_method?.toLowerCase() === 'delivery' && (
            <DeliveryInfo
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              title={'Delivery Address'}
              subTitle={order?.delivery_info?.delivery_address!}
            />
          )}
          {order?.delivery_method?.toLowerCase() === 'pickup' && (
            <DeliveryInfo
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              title={'Pickup Address'}
              subTitle={'Pickup address'}
            />
          )}
          <Separator className="mb-0" />
          <Can data={{ countryPermission: SCOPES.DELIVERIES.CAN_BOOK_DELIVERIES }}>
            <Pressable
              onPress={() => (order?.delivery ? trackDelivery(order?.delivery as any) : handleRequestDelivery())}>
              <DeliveryInfo
                icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
                title={order?.delivery ? 'Track Delivery' : 'Request Delivery'}
                rightElement={<ArrowUpRight size={wp(12)} strokeWidth={2} currentColor={colors.primary.main} />}
              />
            </Pressable>
          </Can>
        </SectionContainer>
      </Accordion>
    </View>
  );
};

const DeliveryInfo = ({
  icon,
  title,
  subTitle,
  rightElement,
  first = false,
}: {
  icon: ReactNode;
  title: string;
  subTitle?: string;
  rightElement?: ReactNode;
  first?: boolean;
}) => {
  return (
    <Row className={cx(!first ? 'mt-15' : '', { 'items-start': subTitle !== undefined })}>
      <CircledIcon iconBg="bg-white" className={cx('p-6')}>
        {icon}
      </CircledIcon>
      <View className="flex-1 mx-15">
        <BaseText weight={'medium'} classes="text-black-secondary">
          {title}
        </BaseText>
        {subTitle && <BaseText classes="text-black-muted mt-3 leading-[20px]">{subTitle}</BaseText>}
      </View>
      {rightElement && rightElement}
    </Row>
  );
};

export default DeliveryDetailSection;
