import { useCallback, useMemo, useState } from 'react';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import {
  toAppUrl,
  UPDATE_ORDER_PAYMENT_STATUS,
  ORDER_FEES,
  OrderInterface,
  humanFriendlyDate,
  CartItem,
  ProductItemInterface,
} from 'catlog-shared';
import {
  Clock,
  Edit2,
  FlashCircle,
  Link21,
  MoneyForbidden,
  Moneys,
  MoneyTick,
  ReceiptSearch,
  ShoppingBag,
  Tag2,
  TruckFast,
} from 'iconsax-react-native/src';
import { LayoutAnimation, View } from 'react-native';
import Toast from 'react-native-toast-message';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import CustomImage from 'src/components/ui/others/custom-image';
import { useApi } from 'src/hooks/use-api';

import {
  alertPromise,
  capitalizeWords,
  copyToClipboard,
  cx,
  delay,
  enumToHumanFriendly,
  normalizeEnums,
  openLinkInBrowser,
  showLoader,
  toCurrency,
  wp,
} from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { ArrowUpRight, CheckCircle, ChevronDown, ChevronUp, Ellipse } from '@/components/ui/icons';
import MoreOptions, { OptionWithIcon } from '@/components/ui/more-options';
import InfoRow from '@/components/ui/others/info-row';
import Separator from '@/components/ui/others/separator';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import SectionContainer, { ContainerType } from '@/components/ui/section-container';
import colors from '@/theme/colors';
import ProductDetailsModal from 'src/components/products/product-details-modal';
import useModals from 'src/hooks/use-modals';
import Pressable from 'src/components/ui/base/pressable';
import { CustomVariantItem } from '../create/order-items-section';
import { useNavigation } from '@react-navigation/native';

interface GroupedItem {
  item_id: string;
  item: ProductItemInterface;
  variantItems: CustomVariantItem[];
  quantity: number;
  hasVariant: boolean;
}

const OrderDetailSection = ({
  order,
  callBack,
}: {
  order: OrderInterface;
  callBack?: (updatedData: Partial<OrderInterface>) => void;
}) => {
  const [activeProduct, setActiveProduct] = useState<null | CartItem>(null);
  const { modals, toggleModal } = useModals(['productDetails']);

  const navigation = useNavigation();

  const getFee = (feeType: ORDER_FEES) => {
    const allFees = order?.fees ?? [];
    return allFees.find(data => data.type === feeType);
  };

  const updatePaymentStatusRequest = useApi({
    apiFunction: UPDATE_ORDER_PAYMENT_STATUS,
    method: 'PUT',
    key: 'update-payment-status',
  });

  const onPressEditOrder = async () => {
    await delay(700);
    const link = toAppUrl(`orders/${order.id}?edit_items=true`, true, EXPO_PUBLIC_PUBLIC_URL);
    await openLinkInBrowser(link);
  };

  // console.log(JSON.stringify(order));

  const handlePaymentStatusUpdate = async () => {
    const alertResponse = await alertPromise(
      'Update payment Status',
      `Clicking on "Mark as paid" would update this order to PAID, this action cannot be reversed.`,
      'Mark as paid',
      'Cancel',
      false,
    );
    if (alertResponse === false) {
      return;
    }
    showLoader('Updating Payment Status');
    const [response, error] = await updatePaymentStatusRequest.makeRequest({
      id: order?.id,
    });

    if (response) {
      Toast.show({ type: 'success', text1: 'Payment status updated successfully' });
      callBack?.({ is_paid: true });
      return;
    }

    Toast.show({ type: 'error', text1: error?.message ?? 'An error occurred' });
  };

  // const routeToPayment = () => {
  //   navigation.navigate('PaymentInfo', { id: order?.payment_id });
  // }

  const moreOptions = [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Order'} />
      ),
      title: 'Edit Order',
      onPress: onPressEditOrder,
    },
    ...(order?.is_paid
      ? []
      : [
          {
            optionElement: (
              <OptionWithIcon
                icon={<MoneyTick size={wp(15)} color={colors.black.placeholder} />}
                label={'Mark as Paid'}
              />
            ),
            title: 'Mark as Paid',
            onPress: handlePaymentStatusUpdate,
          },
        ]),
    {
      optionElement: (
        <OptionWithIcon icon={<Link21 size={wp(15)} color={colors.black.placeholder} />} label={'Copy Order Link'} />
      ),
      title: 'Copy Order Link',
      onPress: () => copyToClipboard(toAppUrl(`orders/${order?.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
  ];

  const onPressProduct = (item: CartItem) => {
    setActiveProduct(item);
    toggleModal('productDetails');
  };

  const hasVariant = (i: CartItem) => {
    return Boolean(i?.variant);
  };

  const variantData = (i: CartItem) => {
    const d = Object.entries(i?.variant?.values ?? {});
    return d.map(i => i[1]).join(' . ');
  };

  const groupItemsByItemId = useCallback((items: CartItem[]): GroupedItem[] => {
    const itemMap = new Map<string, GroupedItem>();

    items.forEach((itemEntry, index) => {
      const { item_id, snapshot, variant, quantity } = itemEntry;

      const variantFormat = {
        ...variant,
        quantity,
        baseIndex: index,
      };

      if (itemMap.has(item_id)) {
        const existingGroup = itemMap.get(item_id)!;
        existingGroup.variantItems.push(variantFormat);
        // If any variant exists, set hasVariant to true
        if (variant) {
          existingGroup.hasVariant = true;
        }
        // existingGroup.quantity += quantity;
      } else {
        itemMap.set(item_id, {
          item_id,
          item: snapshot!,
          variantItems: [variantFormat],
          quantity,
          hasVariant: !!snapshot.variants?.options?.length,
        });
      }
    }, []);

    return Array.from(itemMap.values());
  }, []);

  const itemAmount = (item: GroupedItem) => {
    if (item?.hasVariant) {
      return item?.variantItems?.reduce((total, item) => total + item?.price! * item?.quantity!, 0);
    }
    return item?.item?.price! * item?.quantity;
  };

  return (
    <View className="mx-20">
      <Row>
        <BaseText type="heading" fontSize={15}>
          Order Details
        </BaseText>
        <MoreOptions options={moreOptions} />
      </Row>
      <SectionContainer className="pt-12">
        {groupItemsByItemId(order?.items ?? []).map((item, index) => (
          <View key={index}>
            <ItemsCard
              item={item}
              currency={order?.currency}
              onPress={() => onPressProduct(item)}
              formattedAmount={toCurrency(itemAmount(item), order?.currency)}
            />
          </View>
        ))}
        <Separator className="mx-0" />
        <InfoRow
          iconBg="white"
          title="Date"
          icon={<Clock size={wp(15)} color={colors.black.placeholder} />}
          value={humanFriendlyDate(order?.created_at, true)}
        />
        <InfoRow
          iconBg="white"
          title="Payment Status"
          icon={<CheckCircle size={wp(15)} primaryColor={colors.black.placeholder} />}
          valueElement={
            <StatusPill
              title={order?.is_paid ? 'PAID' : 'UNPAID'}
              // className="bg-accentGreen-pastel"
              statusType={order?.is_paid ? StatusType.SUCCESS : StatusType.DANGER}
            />
          }
        />
        {/* <InfoRow
          iconBg="white"
          title="Payment Info"
          icon={<ReceiptSearch size={wp(15)} color={colors.black.placeholder} />}
          valueElement={
            <WhiteCardBtn
              onPress={routeToPayment}
              className="bg-grey-bgOne rounded-full py-0 px-0 self-center"
              icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
              View Payment
            </WhiteCardBtn>
          }
        /> */}
        <InfoRow
          iconBg="white"
          title="Order Channel"
          icon={<ShoppingBag size={wp(15)} color={colors.black.placeholder} />}
          value={capitalizeWords(normalizeEnums(order?.channel ?? 'STOREFRONT'))}
        />
        <Separator className="mx-0 mt-0" />
        <InfoRow
          iconBg="white"
          title={`${getFee(ORDER_FEES.DELIVERY)?.label ?? 'Delivery Fee'}`}
          icon={<TruckFast size={wp(15)} color={colors.black.placeholder} />}
          value={
            getFee(ORDER_FEES.DELIVERY)?.amount
              ? `${toCurrency(getFee(ORDER_FEES.DELIVERY)?.amount, order?.currency)}`
              : '-'
          }
        />
        <InfoRow
          iconBg="white"
          title={`${getFee(ORDER_FEES.VAT)?.label ?? 'VAT'}`}
          icon={<Tag2 size={wp(15)} color={colors.black.placeholder} />}
          value={
            getFee(ORDER_FEES.VAT)?.amount ? `${toCurrency(getFee(ORDER_FEES.VAT)?.amount, order?.currency)}` : '-'
          }
        />
        <InfoRow
          iconBg="white"
          title={`${getFee(ORDER_FEES.DISCOUNT)?.label ?? 'Discount Applied'}`}
          iconElement={
            <CircledIcon iconBg="bg-accentYellow-main" className={cx('p-6')}>
              <MoneyForbidden variant="Bold" size={wp(15)} color={colors.white} />
            </CircledIcon>
          }
          value={
            getFee(ORDER_FEES.DISCOUNT)?.amount
              ? `${toCurrency(getFee(ORDER_FEES.DISCOUNT)?.amount, order?.currency)}`
              : '-'
          }
        />
        <InfoRow
          iconBg="white"
          title="Total Price"
          iconElement={
            <CircledIcon iconBg="bg-accentRed-main" className={cx('p-6')}>
              <Moneys variant="Bold" size={wp(15)} color={colors.white} />
            </CircledIcon>
          }
          value={<BaseText weight="semiBold">{toCurrency(order?.total_amount, order?.currency)}</BaseText>}
        />
      </SectionContainer>
      {activeProduct && (
        <ProductDetailsModal
          isVisible={modals.productDetails}
          closeModal={() => {
            toggleModal('productDetails', false);
            setActiveProduct(null);
          }}
          cartItem={activeProduct}
        />
      )}
    </View>
  );
};

export default OrderDetailSection;

const ItemsCard = ({
  item,
  currency,
  formattedAmount,
  onPress,
}: {
  item: GroupedItem;
  currency: string;
  formattedAmount: string;
  onPress: VoidFunction;
}) => {
  const [expanded, setExpanded] = useState(false);

  const variantsLength = item?.variantItems?.length ?? 0;
  const hasVariants = item?.hasVariant;
  const isImageVariant = item?.variantItems?.[0]?.image;

  const toogleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  return (
    <Pressable className="bg-white rounded-12 p-10 my-3" disabled>
      <ListItemCard
        className="p-0"
        leftElement={
          <CustomImage
            className="w-40 h-40 rounded-8"
            imageProps={{ source: { uri: item?.item?.images![item?.item?.thumbnail ?? 0] } }}
          />
        }
        title={item?.item?.name}
        spreadTitleContainer={false}
        onPress={onPress}
        titleProps={{ weight: 'medium', fontSize: 12, classes: 'text-black-secondary' }}
        description={formattedAmount}
        descriptionProps={{ classes: 'text-black-muted' }}
        rightElement={
          hasVariants ? (
            <Pressable onPress={toogleExpand}>
              {expanded ? (
                <CircledIcon className="p-5 bg-grey-bgOne">
                  <ChevronUp size={wp(15)} currentColor={colors.black.placeholder} />
                </CircledIcon>
              ) : (
                <CircledIcon className="p-5 bg-grey-bgOne">
                  <ChevronDown size={wp(15)} currentColor={colors.black.placeholder} />
                </CircledIcon>
              )}
            </Pressable>
          ) : (
            <View className="rounded-full py-8 px-10 bg-grey-bgOne">
              <BaseText fontSize={10} weight="medium" classes="text-black-muted">
                QTY: {item.quantity}
              </BaseText>
            </View>
          )
        }
        bottomElement={
          <Row className="mt-5" spread={false} style={{ gap: 5 }}>
            {hasVariants && (
              <>
                <Row style={{ gap: 3 }} spread={false} className="items-center">
                  <FlashCircle variant="Bold" size={wp(12)} color={colors.primary.light} />
                  <BaseText fontSize={11} classes={cx(`text-black-muted`)}>
                    {variantsLength} Variants
                  </BaseText>
                </Row>
                <View className="w-2 h-2 rounded-full bg-black-placeholder" />
              </>
            )}
            <BaseText fontSize={11} classes={cx(`text-black-muted`)}>
              {formattedAmount}
            </BaseText>
          </Row>
        }
      />
      {hasVariants && expanded && (
        <View className="">
          <Separator className="mx-0 my-10" />
          <View>
            {item.variantItems.map((i, idx) => (
              <SectionContainer key={idx} className="p-0 mt-0 mb-10 rounded-12" containerType={ContainerType.OUTLINED}>
                {isImageVariant && (
                  <Row className=" px-12 py-8 border-b border-b-grey-border bg-grey-bgOne">
                    <CustomImage imageProps={{ source: i?.image }} className="h-28 w-28 rounded-8" />
                    <View className="flex-1 mx-8">
                      <BaseText weight="medium" classes="text-black-secondary">
                        {item?.item?.name}
                      </BaseText>
                    </View>
                  </Row>
                )}
                {Object.entries(i.values! ?? []).map((item, index) => (
                  <Row key={index} className={cx('p-12 border-b border-b-grey-border bg-grey-bgOne')}>
                    <BaseText fontSize={12} classes="text-black-placeholder">
                      {enumToHumanFriendly(item[0])}:{' '}
                      <BaseText fontSize={12} weight="medium" classes="text-black-muted">
                        {item[1]}
                      </BaseText>
                    </BaseText>
                  </Row>
                ))}
                <Row spread={false}>
                  <View className="min-w-[65%] p-12">
                    <BaseText fontSize={12} weight="medium" classes="text-black-muted">
                      {toCurrency(i.price, currency)}
                    </BaseText>
                  </View>
                  <View className="p-12 border-l border-l-grey-border">
                    <BaseText fontSize={12} weight="medium" classes="text-black-muted">
                      QTY: {i.quantity}
                    </BaseText>
                  </View>
                </Row>
              </SectionContainer>
            ))}
          </View>
        </View>
      )}
    </Pressable>
  );
};
