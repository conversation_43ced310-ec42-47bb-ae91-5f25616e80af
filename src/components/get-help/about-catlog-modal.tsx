import { ButtonVariant } from 'src/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, Row } from 'src/components/ui';
import { Dimensions, View } from 'react-native';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import CustomImage from '../ui/others/custom-image';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import SectionContainer from '../ui/section-container';
import { TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { CountryInterface, GET_PLANS, Plan, PLAN_TYPE, PlanOption } from 'catlog-shared';
import { useMemo } from 'react';
import { CatlogLogo } from '../ui/icons';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}
const AboutCatlogModal = ({ closeModal, ...props }: Props) => {
  return (
    <BottomModal {...props} enableDynamicSizing closeModal={closeModal} title="About Catlog">
      <Container className="pb-70">
        <View>
          <BaseText fontSize={14} classes="text-black-secondary" lineHeight={22}>
            Catlog is a social-commerce company focused on enhancing transactions between buyers and merchants on social
            media platforms in Africa. The company originated from the founders’ personal challenges when purchasing
            items via Instagram and WhatsApp, which often involved inefficient back-and-forth communications.
            {'\n'}
            {'\n'}
            Initially, Catlog provided merchants with a straightforward way to list their products and receive orders
            through WhatsApp. Over time, the platform has expanded its offerings to include tools for conversations,
            payments, deliveries, and business management, aiming to create a seamless experience for both buyers and
            sellers.
            {'\n'}
            {'\n'}
            Catlog’s mission is to establish an ecosystem that facilitates social commerce transactions, starting in
            Africa and eventually extending globally. 
          </BaseText>
        </View>
      </Container>
    </BottomModal>
  );
};

export default AboutCatlogModal;
