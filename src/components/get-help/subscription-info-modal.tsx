import { ButtonVariant } from 'src/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, Row } from 'src/components/ui';
import { Dimensions, View } from 'react-native';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import CustomImage from '../ui/others/custom-image';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import SectionContainer from '../ui/section-container';
import { TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { CountryInterface, GET_PLANS, Plan, PLAN_TYPE, PlanOption } from 'catlog-shared';
import { useMemo, useState } from 'react';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const width = Dimensions.get('window').width;

const SubscriptionInfoModal = ({ closeModal, ...props }: Props) => {
  const { store } = useAuthContext();
  const [plans, setPlans] = useState<Plan[]>([]);
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
      onSuccess: res => {
        const thePlans: Plan[] = res.data?.plans ?? [];
        let planFiltered: Plan[] = [];
        thePlans.forEach(plan => {
          if (plan.type === PLAN_TYPE.KITCHEN || plan.type === PLAN_TYPE.STARTER) return;
          // if (plan.interval_text === 'Monthly') return;
          planFiltered.push(plan);
        });
        planFiltered.sort((a, b) => (a?.amount < b?.amount ? -1 : 1));

        setPlans(planFiltered);
      },
    },
    { country: typeof store?.country === 'string' ? store?.country : store?.country?.code },
  );

  // const plans: Plan[] = useMemo(() => {
  //   if (getPlansReq.response) {
  //     const thePlans: Plan[] = getPlansReq.response?.data?.plans ?? [];
  //     let planFiltered: Plan[] = [];
  //     thePlans.forEach(plan => {
  //       if (plan.type === PLAN_TYPE.KITCHEN || plan.type === PLAN_TYPE.STARTER) return;
  //       // if (plan.interval_text === 'Monthly') return;
  //       planFiltered.push(plan);
  //     });
  //     planFiltered.sort((a, b) => (a?.amount < b?.amount ? -1 : 1));
  //     return planFiltered;
  //   }
  //   return [];
  // }, [getPlansReq.response]);

  return (
    <BottomModal {...props} enableDynamicSizing closeModal={closeModal} title="How Subscriptions Work">
      <Container className="pb-70">
        <View className="mt-15 rounded-[15px] overflow-hidden h-[156px]">
          <CustomImage
            imageProps={{ source: require('@/assets/images/account-balance.png'), contentFit: 'cover' }}
            className="h-full w-full"
          />
        </View>
        <View>
          <BaseText fontSize={14} classes="text-black-secondary mt-10" lineHeight={22}>
            Your Subscription helps us keep your store running smoothly and ensure you always have access to the tools
            you need to sell with confidence.
          </BaseText>
          <BaseText fontSize={14} classes="text-black-secondary mt-10" lineHeight={22}>
            We offer two plans. The Basic Plan gives you up to 100 product uploads, discounts, coupons, and most of our
            core features. The Business+ Plan unlocks more — unlimited products, multiple stores, product videos,
            international payments, and extra flexibility for growing businesses.
          </BaseText>

          <BaseText fontSize={14} classes="text-black-secondary mt-10" lineHeight={22}>
            You can pay monthly, quarterly, bi-annually, or annually, with bigger discounts on longer intervals.
            Subscriptions renew automatically, so if you don’t want to be charged again, please cancel before your next
            due date.
          </BaseText>
        </View>
        <SectionContainer className="py-12">
          <BaseText fontSize={14} type="heading">
            The subscription options are as follows
          </BaseText>
          <View className="mt-12" style={{ gap: hp(10) }}>
            {plans.map((p, idx) => (
              <Row key={idx} disableSpread style={{ gap: wp(10) }}>
                <TickCircle variant={'Bold'} size={hp(15)} color={colors.accentGreen.main} />
                <BaseText classes="text-black-muted">
                  {p.name} Plan ({p.interval_text}):{' '}
                  <BaseText classes="text-black-muted" weight="medium">
                    {toCurrency(p.actual_amount, p.currency)}
                  </BaseText>
                </BaseText>
              </Row>
            ))}
          </View>
        </SectionContainer>
      </Container>
    </BottomModal>
  );
};

export default SubscriptionInfoModal;
