import { getAvatarBg } from '@/assets/utils/js';
import cx from 'classnames';
import { BaseText, CircledIcon } from '../ui';
import { BaseTextProps } from '../ui/base/base-text';
import colors from 'src/theme/colors';

interface CustomerInitialProps {
  initial: string;
  textProps?: BaseTextProps;
  classes?: string;
  greyBg?: boolean;
}

const CustomerInitial = ({ initial, textProps, classes, greyBg = false }: CustomerInitialProps) => {
  const bgColor = getAvatarBg(initial);

  return (
    <CircledIcon
      className={cx('py-0 px-10', classes)}
      style={{ aspectRatio: 1 / 1, backgroundColor: greyBg ? colors.grey.bgOne : bgColor }}>
      <BaseText
        fontSize={15}
        weight="bold"
        classes="leading-[0]"
        {...textProps}
        style={{ color: greyBg ? bgColor : colors.white }}>
        {initial?.toUpperCase()}
      </BaseText>
    </CircledIcon>
  );
};

export default CustomerInitial;
