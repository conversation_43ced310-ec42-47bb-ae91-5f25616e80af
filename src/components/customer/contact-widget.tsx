import { removeCountryCode } from 'catlog-shared';
import { Call, Whatsapp } from 'iconsax-react-native/src';
import { Linking } from 'react-native';
import colors from 'src/theme/colors';

import { CircledIcon, Row } from '../ui';
import Pressable from '../ui/base/pressable';

import { wp } from '@/assets/utils/js';

interface ContactWidgetProps {
  phone: string;
}

const ContactWidget = ({ phone }: ContactWidgetProps) => {
  const hasPhone = Boolean(phone);

  const callCustomer = async () => {
    const formattedPhone = removeCountryCode(phone);
    const url = `tel:${formattedPhone}`;

    try {
      const supported = await Linking.canOpenURL(url);

      if (supported) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error calling number:', error);
    }
  };

  const sendWhatsappMessage = () => {
    const link = getWhatsappLink(phone.replace('+', ''), 'Hi');
    Linking.openURL(link);
  };

  return (
    <Row className="mt-5 gap-x-5 justify-start">
      <Pressable onPress={sendWhatsappMessage} disabled={!hasPhone} style={{ opacity: hasPhone ? 1 : 0.5 }}>
        <CircledIcon className="bg-grey-bgOne p-8">
          <Whatsapp size={wp(16)} color={colors.black.placeholder} />
        </CircledIcon>
      </Pressable>
      <Pressable onPress={callCustomer} disabled={!hasPhone} style={{ opacity: hasPhone ? 1 : 0.5 }}>
        <CircledIcon className="bg-grey-bgOne p-8">
          <Call size={wp(16)} color={colors.black.placeholder} />
        </CircledIcon>
      </Pressable>
    </Row>
  );
};

export default ContactWidget;

function resolvePhone(phone: string) {
  // if (phone?.startsWith('0')) {
  //   return phone?.replace('0', '234');
  // }

  return phone?.replace('-', '');
}

const getWhatsappLink = (phone?: string, message: string = '') =>
  `https://api.whatsapp.com/send/?phone=${resolvePhone(phone)}&text=${message}`;
