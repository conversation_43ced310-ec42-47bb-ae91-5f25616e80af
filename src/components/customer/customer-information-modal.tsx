import { CustomerInterface, humanFriendlyDate, GET_CUSTOMER, removeCountryCode } from 'catlog-shared';
import { Box1, Calendar, Call, DollarSquare, Sms } from 'iconsax-react-native/src';
import { useState } from 'react';
import { View } from 'react-native';

import ProductInfoRow from '../products/product-info-row';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../ui';
import ContactWidget from './contact-widget';
import CustomerInitial from './customer-initial';
import CustomerOrdersModal from './customer-orders-modal';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import InfoRow from '../ui/others/info-row';
import Shimmer from '../ui/shimmer';
import QueryErrorBoundary from '../ui/query-error-boundary';

import { hp, toCurrency, wp } from '@/assets/utils/js';
import { ArrowUpRight } from '@/components/ui/icons';
import { useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';

interface CustomerInformationModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  activeCustomer: Partial<CustomerInterface>;
  canManageCustomers?: boolean;
}

const SHIMMER_LENGTH = new Array(4).fill(0);

const CustomerInformationModal = ({
  closeModal,
  activeCustomer,
  onPressButton,
  canManageCustomers,
  ...props
}: CustomerInformationModalProps) => {
  const [customer, setCustomer] = useState<CustomerInterface>({} as CustomerInterface);
  const [loading, setLoading] = useState(true);

  const { modals, toggleModal } = useModals(['orders']);

  const getSingleCustomers = useApi({
    apiFunction: GET_CUSTOMER,
    method: 'GET',
    autoRequest: false,
    key: 'get-customer',
  });

  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    handleGetCustomer();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  const handleGetCustomer = async () => {
    setLoading(true);
    const [response, error] = await getSingleCustomers.makeRequest({ id: activeCustomer.id! });
    // console.log(JSON.stringify(response));
    if (response) {
      setCustomer(response?.data);
    }
    setLoading(false);
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      onModalShow={() => {
        handleGetCustomer();
      }}
      onModalHide={() => setLoading(true)}
      buttons={
        canManageCustomers
          ? [{ text: 'Edit Customer', onPress: onPressButton, disabled: getSingleCustomers.isLoading }]
          : []
      }
      title="Customer Information">
      <QueryErrorBoundary
        error={getSingleCustomers.error}
        isLoading={loading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="fullPage"
        errorTitle="Failed to load customer details"
        customErrorMessage="We couldn't load the customer details. Please check your connection and try again."
      >
        {loading ? (
          <CustomerSkeletonLoader />
        ) : (
          <View className="px-20">
          <Row className="py-25 border-t border-t-grey-border">
            <CustomerInitial initial={customer?.name?.[0]} classes="w-36 h-36" />
            <View className="flex-1 mx-10">
              <BaseText classes="text-black-muted">Customer Name</BaseText>
              <BaseText fontSize={15} weight="bold" type="heading">
                {customer?.name}
              </BaseText>
            </View>
            <ContactWidget phone={customer?.phone} />
          </Row>
          <ProductInfoRow
            className="border-y border-grey-border py-15"
            leftItem={{
              icon: (
                <CircledIcon iconBg="bg-accentGreen-pastel">
                  <DollarSquare variant="Bold" size={wp(15)} color={colors.accentGreen.main} />
                </CircledIcon>
              ),
              value: customer?.total_order_amount ? toCurrency(customer?.total_order_amount) : '-',
              title: 'Purchase Volume',
            }}
          />
          <View className="mt-15">
            <InfoRow
              title="Phone Number"
              icon={<Call size={wp(15)} color={colors.black.placeholder} />}
              value={removeCountryCode(customer?.phone ?? '')}
            />
            <InfoRow
              title="Email Address"
              icon={<Sms size={wp(15)} color={colors.black.placeholder} />}
              value={customer?.email}
            />
            <InfoRow
              title="Date Added"
              icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
              value={humanFriendlyDate(customer?.created_at)}
            />
            <InfoRow
              title="Total Orders"
              icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
              value="16 Aug - 30 Aug"
              valueElement={
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => toggleModal('orders')}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  {customer?.orders?.length} Orders
                </WhiteCardBtn>
              }
            />
          </View>
        </View>
        )}
      </QueryErrorBoundary>
      <CustomerOrdersModal
        orders={customer?.orders}
        isVisible={modals.orders}
        closeParentModal={closeModal}
        closeModal={() => toggleModal('orders', false)}
        customerName={customer?.name?.split(' ')[0] ?? ''}
      />
    </BottomModal>
  );
};

const CustomerSkeletonLoader = () => {
  return (
    <View>
      <View className="items-start flex-row px-20 mt-30">
        <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
        <View className="flex-1 ml-10">
          {/* <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(120)} /> */}
          <BaseText classes="text-black-muted">Customer Name</BaseText>
          <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(180)} marginTop={hp(5)} />
        </View>
        <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(50)} />
      </View>
      <View className="px-20">
        <View className="h-1 bg-grey-border my-15" />
        <Row>
          <Shimmer {...{ height: hp(30), width: wp(30), borderRadius: wp(1000) }} />
          <View className="mx-12 flex-1">
            <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
              Purchase Volume
            </BaseText>
            <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10), marginTop: hp(5) }} />
          </View>
          {/* <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} /> */}
        </Row>
        <View className="h-1 bg-grey-border my-10" />
      </View>
      <View className="px-20" style={{ columnGap: 10 }}>
        {SHIMMER_LENGTH.map((d, i) => (
          <Row className="my-10" key={i}>
            <Shimmer {...{ height: hp(20), width: wp(20), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
        ))}
      </View>
    </View>
  );
};

export default CustomerInformationModal;
