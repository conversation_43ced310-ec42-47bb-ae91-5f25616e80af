import { useNavigation } from '@react-navigation/native';
import { ORDER_STATUSES, OrderInterface } from 'catlog-shared';
import { View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import useLayoutHeight from 'src/hooks/use-layout-height';
import colors from 'src/theme/colors';

import { OrderListCard } from '../orders/list/order-item-card';
import { WhiteCardBtn } from '../ui';
import { ArrowUpRight } from '../ui/icons';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Separator from '../ui/others/separator';
import SectionContainer from '../ui/section-container';
import StatusPill from '../ui/others/status-pill';
import { orderStatusPillTypeMap } from 'src/screens/orders/order-info';

interface CustomerOrdersModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  closeParentModal: () => void;
  orders: OrderInterface[];
  isLoading?: boolean;
  customerName: string;
}

const CustomerOrdersModal = ({
  closeModal,
  closeParentModal,
  isLoading,
  orders,
  customerName,
  ...props
}: CustomerOrdersModalProps) => {
  const navigation = useNavigation();

  const { onLayout, flexStyle } = useLayoutHeight(0);

  const openOrder = (id: string) => {
    closeModal();
    closeParentModal?.();
    navigation.navigate('OrderInfo', { id: id });
  };

  return (
    <BottomModal
      {...props}
      // modalStyle={flexStyle}
      // containerStyle={flexStyle}
      // innerStyle={flexStyle}
      closeModal={closeModal}
      showButton={false}
      title={`Orders from ${customerName}`}>
      <View className="mx-20" 
      // style={flexStyle} 
      onLayout={onLayout}>
        <SectionContainer className="py-15">
          {orders?.map((item, index) => (
            <View key={item.id}>
              <OrderListCard
                order={item}
                disabled={false}
                rightAddon={
                  <StatusPill
                    title={item?.status}
                    className="bg-white"
                    statusType={orderStatusPillTypeMap[item?.status]}
                  />
                }
                onPress={() => openOrder(item.id)}
              />
              {index !== orders.length - 1 && <Separator className="mx-0" />}
            </View>
          ))}
        </SectionContainer>
      </View>
    </BottomModal>
  );
};

export default CustomerOrdersModal;
