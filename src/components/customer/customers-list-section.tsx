import { useNavigation } from '@react-navigation/native';
import { CustomerInterface, DELETE_CUSTOMER, GET_CUSTOMERS, PaginateSearchParams, removeCountryCode } from 'catlog-shared';
import { Bag, Edit2, Profile2User, Trash } from 'iconsax-react-native/src';
import { useEffect, useState } from 'react';
import { Alert, LayoutAnimation, RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import Toast from 'react-native-toast-message';

import { delay, ensureUniqueItems, updateOrDeleteItemFromList, wp } from '@/assets/utils/js';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import CustomerCard from '@/components/customer/customer-card';
import CustomerInformationModal from '@/components/customer/customer-information-modal';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import AnalyticsBtn from '@/components/ui/buttons/analytics-btn';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import Shimmer from '@/components/ui/shimmer';
import QueryErrorBoundary from '@/components/ui/query-error-boundary';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import usePagination from '@/hooks/use-pagination';
import useScrollHandler from '@/hooks/use-scroll-handler';
import colors from '@/theme/colors';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import Can from 'src/components/ui/permissions/can';

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

interface CustomerListSectionProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  customers: CustomerInterface[];
  setCustomers: React.Dispatch<React.SetStateAction<CustomerInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading: boolean;
  isLoading: boolean;
  total_pages: number;
  currentPage: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  fromFiltersPage?: boolean;
  isSearch?: boolean;
  error?: any;
}

const PER_PAGE = 10;
const CustomersListSection = ({
  scrollHandler,
  customers,
  setCustomers,
  pullToRefreshFunc,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  isSearch,
  error,
}: CustomerListSectionProps) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = () => {
    setIsRetrying(true);
    if (pullToRefreshFunc) {
      pullToRefreshFunc();
      setTimeout(() => setIsRetrying(false), 2000);
    }
  };
  const { userRole } = useAuthContext();
  const [isEdit, setIsEdit] = useState(false);
  // const [customers, setCustomers] = useState<CustomerInterface[]>([]);
  const [activeCustomer, setActiveCustomer] = useState<CustomerInterface>({} as CustomerInterface);

  const { toggleModal, modals } = useModals(['customerInfo', 'addCustomerModal']);

  // const { currentPage, setPage, goNext } = usePagination();

  const navigation = useNavigation();

  const deleteCustomersRequest = useApi({ key: 'delete-customer', apiFunction: DELETE_CUSTOMER, method: 'DELETE' });

  // const handlePullToRefresh = () => {
  //   setCustomers([]);

  //   if (currentPage === 1) {
  //     getCustomersRequest.makeRequest({
  //       filter: { search: '' },
  //       page: currentPage,
  //       per_page: PER_PAGE,
  //       // sort: 'desc',
  //     });
  //     return;
  //   }

  //   setPage(1);
  // };

  const handleOnPressEdit = () => {
    setIsEdit(true);
    toggleModal('customerInfo', false);
    setTimeout(() => {
      toggleModal('addCustomerModal', true);
    }, 600);
  };

  const handleDeleteCustomer = async (item: CustomerInterface, index: number) => {
    const layoutAnimConfig = {
      duration: 300,
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
      delete: {
        duration: 100,
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    };
    const delFunc = async () => {
      if (item.id) {
        const [response, error] = await deleteCustomersRequest.makeRequest({
          id: item.id!,
        });
        if (response) {
          Toast.show({ type: 'success', text1: 'Customer deleted successfully' });
          customers.splice(index, 1);
          LayoutAnimation.configureNext(layoutAnimConfig);
          setCustomers(customers);
        }
        if (error) {
          Toast.show({ type: 'error', text1: error.body.message });
        }
      }
    };

    Alert.alert('Delete Customer', 'Delete customer permanently', [
      { text: 'Cancel', onPress: () => {} },
      { text: 'Delete', onPress: delFunc, style: 'destructive' },
    ]);
  };

  const canManageCustomers = actionIsAllowed({
    permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS,
    userRole,
  });

  const createCustomerCallback = (updatedData?: CustomerInterface) => {
    toggleModal('addCustomerModal', false);
    if (isEdit) {
      setCustomers(updateOrDeleteItemFromList(customers, 'id', activeCustomer.id, updatedData ?? null));
    }
  };

  const moreOptions = (item: CustomerInterface, index: number) => [
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Edit2 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Edit Customer
          </BaseText>
        </Row>
      ),
      title: 'Edit Customer',
      onPress: () => {
        setActiveCustomer(item);
        handleOnPressEdit();
      },
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Trash size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Delete Customer
          </BaseText>
        </Row>
      ),
      title: 'Delete Customer',
      onPress: () => handleDeleteCustomer(item, index),
    },
  ];

  // const { scrollHandler, headerStyle, inputStyle } = useScrollHandler(100);

  return (
    <View style={{ flex: 1 }}>
      <QueryErrorBoundary
        error={error}
        isLoading={isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="section"
        customErrorMessage="We couldn't load your customers. Please check your connection and try again."
        errorTitle="Failed to load customers"
        padAround
        classes="mt-20">
        <Animated.FlatList
          data={customers}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          keyExtractor={(item, index) => item.id + index}
          refreshControl={<RefreshControl refreshing={false} onRefresh={() => pullToRefreshFunc()} />}
          ListEmptyComponent={() =>
            isLoading ? (
              <CustomerProductSkeletonLoader />
            ) : (
              <EmptyState
                icon={
                    <Profile2User variant="Bulk" size={wp(40)} color={colors.grey.muted} />
                }
                onPressBtn={() => {
                  setIsEdit(false);
                  toggleModal('addCustomerModal');
                }}
                btnText="Add new customer"
                text={"Your customer's list \n  will appear here"}
              />
            )
          }
          className="flex-1 px-20"
          contentContainerStyle={{ flexGrow: 1 }}
          renderItem={({ item, index }) => (
            <CustomerCard
              title={item?.name}
              index={index}
              description={item?.phone ? removeCountryCode(item?.phone) : ''}
              onPress={() => {
                setActiveCustomer(item);
                toggleModal('customerInfo');
              }}
              moreOptions={moreOptions(item, index)}
              showOptions={canManageCustomers}
            />
          )}
          onEndReached={() => {
            if (!isLoading && customers?.length > 0 && currentPage < total_pages) {
              goNext(total_pages);
            }
          }}
          ListFooterComponent={
            <View style={{ marginBottom: 120 }}>
              {customers?.length > 0 && isLoading && (
                <View className="mt-5">
                  <CustomerProductSkeletonLoader />
                </View>
              )}
            </View>
          }
        />
      </QueryErrorBoundary>
      {!isSearch && (
        <Can data={{ permission: SCOPES.CUSTOMERS.UPDATE_CUSTOMERS }}>
          <FAB
            onPress={() => {
              setIsEdit(false);
              toggleModal('addCustomerModal');
            }}
          />
        </Can>
      )}
      <AddCustomerModal
        isEdit={isEdit}
        activeCustomer={activeCustomer}
        isVisible={modals.addCustomerModal}
        closeModal={() => toggleModal('addCustomerModal', false)}
        callBack={data => createCustomerCallback(data)}
      />
      <CustomerInformationModal
        isVisible={modals.customerInfo}
        activeCustomer={activeCustomer}
        closeModal={() => toggleModal('customerInfo', false)}
        onPressButton={() => handleOnPressEdit()}
        canManageCustomers={canManageCustomers}
      />
    </View>
  );
};

export default CustomersListSection;

export const CustomerSkeleton = () => {
  return (
    <View className="flex-row items-center border-b border-b-grey-border py-15">
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className="mx-12 flex-1">
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 150, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 5, width: 15, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 70, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const CustomerProductSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <CustomerSkeleton key={i} />
      ))}
    </View>
  );
};
