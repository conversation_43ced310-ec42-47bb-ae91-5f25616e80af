import { getFieldvalues, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import { ArrowRight, Search } from '@/components/ui/icons';
import colors from '@/theme/colors';
import Input from '@/components/ui/inputs/input';
import { View } from 'react-native';
import { FormikProps } from 'formik';
import useModals from 'src/hooks/use-modals';
import { Add, AddCircle } from 'iconsax-react-native/src';
import Button from '@/components/ui/buttons/button';
import { ApiData, ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods, DropDownProps } from '@/components/ui/inputs/select-dropdown';
import { useMemo, useRef, useState } from 'react';
import useAuthContext from 'src/contexts/auth/auth-context';
import CustomerInitial from '@/components/customer/customer-initial';
import ListItemCard from '@/components/ui/cards/list-item-card';
import AddCustomerModal from '@/components/customer/add-customer-modal';
import {
  GetItemsParams,
  PaginateSearchParams,
  CustomerInterface,
  ProductItemInterface,
  CreateOrderParams,
  GET_CUSTOMERS,
  phoneObjectFromString,
  removeCountryCode,
  GET_CUSTOMERS_LEAN,
} from 'catlog-shared';
import React from 'react';

interface SelectCustomerProps extends Partial<DropDownProps> {
  onSelectCustomer: (customer: CustomerInterface) => void;
  selectedCustomer: string;
  error?: string;
  hasError?: boolean;
  showLeftAccessory?: boolean;
  hideAddCustomerBtn?: boolean;
  label?: string;
  externalRef?: React.MutableRefObject<DropDownMethods>;
  showAnchor?: boolean;
}

interface CustomerResponseWithPagination extends ResponseWithPagination<CustomerInterface[]> {}
interface CustomerResponse {
  data: CustomerResponseWithPagination;
}

const SelectCustomer = ({
  onSelectCustomer,
  selectedCustomer,
  error,
  label = 'Select Customer',
  hasError,
  hideAddCustomerBtn,
  showLeftAccessory,
  externalRef,
  showAnchor = true,
  ...rest
}: SelectCustomerProps) => {
  const [refetchTrigger, setRefetchTrigger] = useState(0);
  const [searchQuery, setSearchQuery] = useState<{ query: string; active: string }>({ query: '', active: '' });

  const dropDownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['addCustomerModal']);

  const getCustomersRequest = useApi<PaginateSearchParams & { refetchTrigger: number }, CustomerResponse>(
    { key: GET_CUSTOMERS_LEAN.name, apiFunction: GET_CUSTOMERS_LEAN, method: 'GET', onSuccess: response => {} },
    { filter: { search: searchQuery?.active }, per_page: 9007199254740991, sort: 'desc', refetchTrigger },
  );

  const allCustomers = getCustomersRequest?.response?.data?.data ?? [];

  const customersMapped = useMemo(
    () =>
      allCustomers?.map(item => ({
        value: item.id!,
        label: item.name,
        subTitle: removeCountryCode(item.phone),
        leftElement: <CustomerInitial initial={item.name[0]} />,
      })),
    [allCustomers],
  );

  const selectedCustomerObj = useMemo(() => {
    const selectedCustomer = allCustomers.find(({ id }) => id == selectedCustomer);
    return selectedCustomer;
  }, [selectedCustomer, allCustomers]);

  const handleOpenAddCustomerModal = () => {
    if (externalRef) {
      externalRef.current.close();
    } else {
      dropDownRef.current?.close();
    }
    setTimeout(() => {
      toggleModal('addCustomerModal', true);
    }, 600);
  };

  const handleAddCustomer = () => {
    toggleModal('addCustomerModal', false);
    setRefetchTrigger(Math.random() * 1000);
    setTimeout(() => {
      if (externalRef) {
        externalRef.current.open();
      } else {
        dropDownRef.current?.open();
      }
    }, 600);
  };

  const handleSelectCustomer = (customerId: string) => {
    // console.log('handleSelectCustomer: ', customerId);
    const customers = getCustomersRequest?.response?.data?.data ?? [];
    const selectedCustomer = customers.find(({ id }) => id === customerId);
    // console.log('selectedCustomer===>: ', selectedCustomer);
    const customerItem = {
      id: customerId,
      name: selectedCustomer?.name!,
      email: selectedCustomer?.email!,
      phone: selectedCustomer?.phone!,
      store: selectedCustomer?.store!,
    };
    onSelectCustomer?.(customerItem);
  };

  return (
    <>
      <View>
        <SelectDropdown
          ref={externalRef ?? dropDownRef}
          // showAnchor={false}
          selectedItem={selectedCustomer ?? ''}
          onPressItem={handleSelectCustomer}
          error={error}
          hasError={hasError}
          isLoading={getCustomersRequest.isLoading}
          leftAccessory={
            showLeftAccessory && (
              <>
                selectedCustomer?.id ? (
                <CustomerInitial
                  classes="px-8 mr-10"
                  textProps={{ fontSize: 11 }}
                  initial={selectedCustomerObj?.name[0] ?? '-'}
                />
                ) : undefined
              </>
            )
          }
          label={label}
          items={customersMapped ?? []}
          containerClasses="mb-15"
          showLabel
          hasSearch
          searchLabel="Search Customers"
          showAnchor={showAnchor}
          listAddOns={
            hideAddCustomerBtn ? undefined : (
              <ListItemCard
                showBorder={false}
                title={'Add Customer'}
                className="border-t border-t-grey-border"
                titleProps={{ weight: 'medium' }}
                onPress={handleOpenAddCustomerModal}
                leftElement={
                  <CircledIcon className="p-10 bg-white">
                    <AddCircle variant={'Bold'} size={wp(24)} color={colors.primary.main} />
                  </CircledIcon>
                }
                rightElement={
                  <CircledIcon className="p-10 bg-white">
                    <ArrowRight size={wp(16)} strokeWidth={1.32} currentColor={colors.primary.main} />
                  </CircledIcon>
                }
              />
            )
          }
          {...rest}
        />
      </View>
      <AddCustomerModal
        isVisible={modals.addCustomerModal}
        isEdit={false}
        callBack={handleAddCustomer}
        closeModal={() => toggleModal('addCustomerModal', false)}
        // onPressButton={handleAddCustomer}
      />
    </>
  );
};

export default SelectCustomer;
