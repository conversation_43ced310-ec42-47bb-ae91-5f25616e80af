import { Activity, Bag, BagTick2, Box, Chart2, Health, Profile, Profile2User, ProfileAdd, ShoppingBag, StatusUp } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo, useState } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify, toCurrency, toNaira } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import AnalyticsCard from '../ui/cards/analytics-card';
import { StoreSummary } from '@/screens/orders/order-analytics';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';
import { AnalyticsData } from 'src/screens/payments/payment-analytics';
import { CURRENCIES, CustomerStatisticsInterface } from 'catlog-shared';
import { BaseText, Row } from '../ui';
import { ChevronDown } from '../ui/icons';
import MoreOptions from '../ui/more-options';
import { TimeRange } from 'src/@types/utils';

export enum AnalyticsVariants {
  'NEW_CUSTOMERS' = 'new_customers',
  'RETURNING_CUSTOMERS' = 'returning_customers',
  'AVERAGE_ORDERS_PER_CUSTOMER' = 'avg_orders_per_customer',
  'TOTAL_CUSTOMERS' = 'total_customers',
}

interface OrderAnalyticsProps {
  analytics: CustomerStatisticsInterface;
  isLoading?: boolean;
  setRange: (range: TimeRange) => void;
  range: TimeRange;
}

const CustomerAnalyticsCards = ({
  analytics = {} as CustomerStatisticsInterface,
  isLoading,
  setRange,
  range,
}: OrderAnalyticsProps) => {
  // const [range, setRange] = useState(TimeRange.THIS_YEAR);

  const analyticsCardsInfo: AnalyticsCardInfo[] = [
    {
      title: 'New Customers',
      iconBg: 'bg-accentRed-main',
      icon: <ProfileAdd variant="Bold" size={wp(18)} color={colors?.white} />,
      cardBg: 'bg-transparent',
      key: AnalyticsVariants.NEW_CUSTOMERS,
      value: analytics[AnalyticsVariants.NEW_CUSTOMERS] ? analytics[AnalyticsVariants.NEW_CUSTOMERS].count : '-',
      change: analytics[AnalyticsVariants.NEW_CUSTOMERS] ? analytics[AnalyticsVariants.NEW_CUSTOMERS].trend : 0,
    },
    {
      title: 'Returning Customers',
      cardBg: 'bg-transparent',
      icon: <Profile2User variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentGreen-main',
      key: AnalyticsVariants.RETURNING_CUSTOMERS,
      value: analytics[AnalyticsVariants.RETURNING_CUSTOMERS]
        ? analytics[AnalyticsVariants.RETURNING_CUSTOMERS].count
        : '-',
      change: analytics[AnalyticsVariants.RETURNING_CUSTOMERS]
        ? analytics[AnalyticsVariants.RETURNING_CUSTOMERS].trend
        : 0,
    },
    {
      title: 'Total Customers',
      cardBg: 'bg-transparent',
      icon: <Chart2 variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.TOTAL_CUSTOMERS,
      value: analytics[AnalyticsVariants.TOTAL_CUSTOMERS] ? analytics[AnalyticsVariants.TOTAL_CUSTOMERS] : '-',
      change: 0,
    },
    {
      title: 'Average Orders/Customer',
      cardBg: 'bg-transparent',
      icon: <ShoppingBag variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentOrange-main',
      key: AnalyticsVariants.AVERAGE_ORDERS_PER_CUSTOMER,
      value:analytics[AnalyticsVariants.AVERAGE_ORDERS_PER_CUSTOMER]
        ? analytics[AnalyticsVariants.AVERAGE_ORDERS_PER_CUSTOMER]
        : '-',
      change: 0,
    },
  ];

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  const optionElement = [
    {
      title: 'All Time',
      onPress: () => setRange?.(TimeRange.ALL_TIME),
    },
    {
      title: 'This Year',
      onPress: () => setRange?.(TimeRange.THIS_YEAR),
    },
    {
      title: 'Last 30 Days',
      onPress: () => setRange?.(TimeRange.LAST_30_DAYS),
    },
    {
      title: 'Last week',
      onPress: () => setRange?.(TimeRange.LAST_WEEK),
    },
    {
      title: 'This week',
      onPress: () => setRange?.(TimeRange.THIS_WEEK),
    },
    {
      title: 'Today',
      onPress: () => setRange?.(TimeRange.TODAY),
    },
  ];

  return (
    <View>
      <Row className="mb-15">
        <Row>
          <BaseText fontSize={15} weight="bold" type="heading">
            Customer Overview
          </BaseText>
          <ChevronDown size={15} primaryColor={colors.grey.muted} />
        </Row>
        <MoreOptions
          options={optionElement ?? []}
          customMenuElement={
            <Row className="items-center bg-grey-bgOne py-7 px-10 rounded-full">
              <BaseText fontSize={12} classes="mr-4" style={{ color: colors.black.muted }}>
                {range}
              </BaseText>
              <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
            </Row>
          }
        />
      </Row>
      <View className="border border-grey-border rounded-[15px]">
        {isLoading && <AnalyticsSkeletonLoader />}
        {!isLoading && (
          <Fragment>
            {splitCards.map((group, i) => (
              <Fragment key={i}>
                <View className="flex-row last:mb-0">
                  {group.map((info, index) => (
                    <Fragment key={index}>
                      <AnalyticsCard
                        className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                        title={info.title}
                        value={info.value}
                        iconBg={info.iconBg}
                        change={info.change}
                        showChange={false}
                        icon={info.icon}
                        addon={info.addon}
                        theme="white"
                      />
                      {index === 0 && <View className="w-1 bg-grey-border" />}
                    </Fragment>
                  ))}
                </View>
                {i === 0 && <View className="h-1 bg-grey-border" />}
              </Fragment>
            ))}
          </Fragment>
        )}
      </View>
    </View>
  );
};

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

export default CustomerAnalyticsCards;
