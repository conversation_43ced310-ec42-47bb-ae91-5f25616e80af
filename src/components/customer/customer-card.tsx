import { Text, TouchableOpacity, View } from 'react-native';
import { ReactNode, useState } from 'react';
import { styled } from 'nativewind';
import { TextProps } from 'react-native';
import cx from 'classnames';
import { TouchableHighlightProps } from 'react-native';
import { BaseText, CircledIcon } from '../ui';
import { BaseTextProps } from '../ui/base/base-text';
import { Copy, Edit2, PercentageCircle, Profile, TicketDiscount, Trash } from 'iconsax-react-native/src';
import { copyToClipboard, getColorAlternates, randomColor, wp } from '@/assets/utils/js';
import MoreOptions, { MoreOptionElementProps } from '../ui/more-options';
import Row from '../ui/row';
import colors from '@/theme/colors';
import Pressable from '../ui/base/pressable';
import Toast from 'react-native-toast-message';
import { removeCountryCode } from 'catlog-shared';

export interface CustomerCardProps extends Partial<TouchableHighlightProps> {
  onPress?: () => void;
  showBorder?: boolean;
  title: string;
  description?: string;
  index: number;
  moreOptions: MoreOptionElementProps[];
  bottomComponent?: ReactNode;
  showOptions?:boolean
}

const CustomerCard = ({
  onPress,
  showBorder = true,
  moreOptions = [],
  title,
  index = 0,
  description,
  bottomComponent,
  showOptions,
  ...props
}: CustomerCardProps) => {
  const color = getColorAlternates(index);

  const copyPhone = async () => {
    const status = await copyToClipboard(description!);
    if (status === true) {
      Toast.show({ type: 'success', text1: 'Copied to clipboard' });
    }
  };

  return (
    <View>
      <Pressable
        className={`flex-row items-center py-15 ${showBorder && 'border-b border-b-grey-border'}`}
        activeOpacity={0.8}
        onPress={onPress}
        {...props}>
        <CircledIcon style={{ backgroundColor: color.bgColor }}>
          <Profile size={wp(20)} variant={'Bold'} color={color.iconColor} />
        </CircledIcon>
        <View className={'mx-12'}>
          {title && (
            <BaseText fontSize={12} classes={cx('font-fhOscarBold mr-12')} numberOfLines={1}>
              {title}
            </BaseText>
          )}
          {description ? (
            <View className="flex-row mt-5 items-center rounded-full self-start">
              <BaseText fontSize={12} weight={'medium'} classes="leading-[16px] text-black-muted">
                {removeCountryCode(description)}
              </BaseText>
              <Pressable onPress={() => copyPhone()}>
                <CircledIcon className={'bg-primary-pastel p-5 ml-8'}>
                  <Copy size={wp(10)} color={colors?.primary.main} />
                </CircledIcon>
              </Pressable>
            </View>
          ) : (
            bottomComponent
          )}
        </View>
        <View className={'flex-1 ml-12 items-end self-stretch justify-between'}>
         { showOptions && <MoreOptions options={moreOptions} />}
        </View>
      </Pressable>
    </View>
  );
};

export default CustomerCard;
