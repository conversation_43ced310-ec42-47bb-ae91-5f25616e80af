import React from 'react';
import CircledIcon from './circled-icon';
import { BaseText } from './base';
import { getAvatarBg, wp } from '@/assets/utils/js';
import { ProductItemInterface } from 'catlog-shared';

interface ImageTextPlaceholderProps {
  text: string;
  size: keyof typeof sizeProps;
}

const ImageTextPlaceholder = ({ text, size }: ImageTextPlaceholderProps) => {
  const { size: iconSize, fontSize } = sizeProps[size];
  const firstLetter = text?.[0] ?? '';

  return (
    <CircledIcon
      className="items-center justify-center flex p-0"
      style={{
        height: wp(iconSize),
        width: wp(iconSize),
        backgroundColor: getAvatarBg(firstLetter),
      }}>
      <BaseText fontSize={fontSize} weight="bold" classes="text-white leading-none">
        {firstLetter?.toUpperCase()}
      </BaseText>
    </CircledIcon>
  );
};

const sizeProps = {
  sm: {
    size: 25,
    fontSize: 11,
  },
  midi: {
    size: 30,
    fontSize: 12,
  },
  md: {
    size: 40,
    fontSize: 15,
  },
  lg: {
    size: 50,
    fontSize: 18,
  },
};

export default ImageTextPlaceholder;
