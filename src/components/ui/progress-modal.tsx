import { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import Modal from 'react-native-modal';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { hp } from 'src/assets/utils/js';
import { ProgressStep } from 'src/hooks/useProgess';
import { BaseText } from './base';

interface Props {
  isEmbedded?: boolean;
  steps: ProgressStep[];
  currentStep: number;
  stepCount?: number;
  show?: boolean;
  toggle?: VoidFunction;
  customLabel?: string;
}

const ProgressModal: React.FC<Props> = ({
  steps,
  currentStep,
  customLabel,
  stepCount,
  show,
  toggle,
  isEmbedded = false,
}) => {
  const [progress, setProgress] = useState(0);
  const unitStepPercentage = 100.0 / steps.length;
  const intervalId = useRef<NodeJS.Timeout>();
  const progressWidth = useSharedValue(0);
  const insertTop = useSafeAreaInsets().top + hp(12);

  useEffect(() => {
    progressWidth.value = progress === 100 ? 100 : withSpring(progress);
  }, [progress]);

  const progressStyle = useAnimatedStyle(() => {
    return { width: `${progressWidth.value}%` };
  });

  useEffect(() => {
    const step = steps[currentStep];
    let count = 0;

    if (step.progress !== undefined) {
      intervalId.current && clearInterval(intervalId.current);
      intervalId.current = undefined;
      const progress = unitStepPercentage * currentStep + unitStepPercentage * (step.progress / 100.0);
      setProgress(progress);
      return;
    }

    if (step?.isLoading === true && intervalId.current === undefined && step.complete !== true) {
      intervalId.current && clearInterval(intervalId.current);
      const id = setInterval(async () => {
        if (count < 90 && step.isLoading) {
          const progress = unitStepPercentage * currentStep + unitStepPercentage * (count / 100.0);
          setProgress(progress);
          count += stepCount ?? 10;
        } else if (step.complete === true) {
          count = 100;
          setProgress(unitStepPercentage * currentStep + unitStepPercentage);
          intervalId.current = undefined;
          clearInterval(id);
        }
      }, 100);
      intervalId.current = id;
    }
  }, [currentStep, steps]);

  const Main = () => (
    <View className="px-20 py-20 w-full ">
      <BaseText fontSize={15} type="heading">
        Import Progress
      </BaseText>
      {/* Todo: @kayode isn't this supposed to be a dynamic text? */}
      <View className="flex-row w-full justify-between items-center mt-5">
        <BaseText fontSize={14} className="text-black-placeholder">
          {customLabel ?? steps[currentStep].label}
        </BaseText>
        <BaseText type="heading" fontSize={15} className="text-center">{`${Math.floor(progress)}%`}</BaseText>
      </View>
      <Animated.View className={`rounded-full h-6 py-1 bg-primary-main mt-15`} style={progressStyle}></Animated.View>
    </View>
  );

  if (isEmbedded) return <Main />;

  return (
    <Modal
      avoidKeyboard={true}
      isVisible={show}
      onBackdropPress={() => toggle!()}
      backdropColor={'#1E1E1E80'}
      useNativeDriverForBackdrop={true}
      onBackButtonPress={() => toggle!()}
      style={[{ flex: 1, justifyContent: 'flex-start', margin: 0 }]}>
      <View style={[{ justifyContent: 'flex-end', marginTop: insertTop }]} className="bg-white m-2.5 rounded-lg">
        <Main />
      </View>
    </Modal>
  );
};

export default ProgressModal;
