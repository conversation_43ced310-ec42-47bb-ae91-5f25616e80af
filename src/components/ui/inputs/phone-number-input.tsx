import { sluggify, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { countryCodes } from 'catlog-shared';
import { ArrowDown2 } from 'iconsax-react-native/src';
import { useEffect, useRef, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { BaseText } from '..';
import Row from '../row';
import Input, { InputProps } from './input';
import SelectDropdown, { DropDownItem, DropDownMethods } from './select-dropdown';

interface PhoneNumberInputProps extends Partial<Omit<InputProps, 'onChange'>> {
  onChange: (value: { code: string; digits: string }) => void;
}

const PhoneNumberInput = ({ value, onChange, error, ...rest }: PhoneNumberInputProps) => {
  const [code, setCode] = useState(value?.code ?? '+234');
  const [digits, setDigits] = useState(value?.digits);
  const valueRef = useRef({ code: value?.code ?? '+234', digits: value?.digits })

  const [accessoryWidth, setAccessoryWidth] = useState(50);
  const dropDownRef = useRef<DropDownMethods>(null);
  const theError = error?.code || error?.digits || '';


  useEffect(() => {
    if (value?.code !== valueRef.current.code && value?.code !== '') {
      setCode(value?.code);
      valueRef.current = { ...valueRef.current, code: value.code }
    }

    if (value?.digits !== valueRef.current.digits && value?.digits !== '') {
      setDigits(value?.digits);
      valueRef.current = { ...valueRef.current, digits: value.digits }
    }
  }, [value]);

  const handleChange = ({ code = undefined, digits = undefined }) => {
    const copy = { ...valueRef.current }
    copy.code = code ?? copy.code
    copy.digits = digits ?? copy.digits
    valueRef.current = copy
    
    onChange(copy)
    setDigits(copy.digits)
    setCode(copy.code)
  }

  const LeftAccessory = () => (
    <TouchableOpacity
      onPress={() => dropDownRef.current?.open()}
      onLayout={e => setAccessoryWidth(e.nativeEvent.layout.width + 10)}>
      <Row>
        <BaseText fontSize={13} weight={'medium'} classes={'mr-2'}>
          {code}
        </BaseText>
        <ArrowDown2 variant={'Linear'} size={wp(12)} color={colors.black.main} />
      </Row>
    </TouchableOpacity>
  );

  return (
    <View>
      <Input
        leftPadding={accessoryWidth + 10}
        keyboardType={'number-pad'}
        label={'Phone Number'}
        leftAccessory={<LeftAccessory />}
        {...rest}
        value={digits}
        onChangeText={d => handleChange({ digits: d })}
        error={theError}
        hasError={!!theError}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem={code}
        onPressItem={c => handleChange({ code: c })}
        label={'Categories'}
        hasSearch
        items={countryCodeOptions}
        containerClasses="mt-15"
        genItemKeysFun={value => sluggify(value.label)}
      />
    </View>
  );
};

const countryCodeOptions: DropDownItem[] = countryCodes.map(country => ({
  value: country.dial_code,
  label: `(${country.dial_code}) ${country.name} ${country.emoji}`,
}));

export default PhoneNumberInput;
