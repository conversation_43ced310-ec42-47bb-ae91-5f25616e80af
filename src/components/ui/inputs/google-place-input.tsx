import { TouchableOpacity, View, Text, ScrollView, ActivityIndicator } from 'react-native';
import colors from '@/theme/colors';
import { getColorAlternates, hp, wp } from '@/assets/utils/js';
import { ReactNode, Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Input, { InputProps } from './input';
import BottomModal, { BottomModalProps } from '../modals/bottom-modal';
import SectionContainer from '../section-container';
import ListItemCard from '../cards/list-item-card';
import Radio from '../buttons/radio';
import { ChevronDown } from '../icons';
import Pressable from '../base/pressable';
import { ButtonProps } from '../buttons/button';
import { BaseTextProps } from '../base/base-text';
import cx from 'classnames';
import { Location } from 'iconsax-react-native/src';
import useModals from 'src/hooks/use-modals';
import {
  GooglePlaceData,
  GooglePlaceDetail,
  GooglePlacesAutocomplete,
  GooglePlacesAutocompleteProps,
  GooglePlacesAutocompleteRef,
} from 'react-native-google-places-autocomplete';
import { colorAlternates } from 'src/constant/static-data';
import { CircledIcon } from '..';
import { getRandString } from 'catlog-shared';

interface GooglePlaceInputProps extends Partial<InputProps> {
  modalProps?: Partial<BottomModalProps>;
  onPressItem: (value: GooglePlaceDetail) => void;
  disabled?: boolean;
}

const GooglePlaceInput = ({ onPressItem, disabled, modalProps, ...rest }: GooglePlaceInputProps) => {
  const { modals, toggleModal } = useModals(['googlePlace']);
  const [sessionToken, setSessionToken] = useState(getRandString(32));

  const googlePlaceRef = useRef<GooglePlacesAutocompleteRef>(null);

  const handleInputFocus = () => {
    if (rest.value) {
      googlePlaceRef.current?.setAddressText(rest.value);
    }
    setTimeout(() => {
      googlePlaceRef.current?.focus();
    }, 200);
  };

  const RightAccessory = () => (
    <View className="p-3 my-12 bg-grey-bgOne rounded-full">
      <Location size={wp(16)} color={colors.grey.muted} />
    </View>
  );

  const RenderCustomRow = ({ data, index }: { data: GooglePlaceData; index: number }) => {
    const color = getColorAlternates(index);
    
    const LeftElement = () => (
      <CircledIcon style={{ backgroundColor: color.bgColor }}>
        <Location variant={'Bold'} color={color.iconColor} size={wp(16)} />
      </CircledIcon>
    );
    return (
      <View className="flex-1">
        <ListItemCard
          key={data.place_id}
          className="py-8"
          title={data.description ?? 'rrrrr'}
          descriptionClasses="mt-5"
          description={data.structured_formatting.main_text}
          titleProps={{ weight: 'medium' }}
          titleClasses={cx('text-black-secondary')}
          onPress={() => {
            googlePlaceRef.current?.setAddressText(data.description);
          }}
          disabled={true}
          leftElement={<LeftElement />}
        />
      </View>
    );
  };

  return (
    <View>
      <Pressable onPress={() => toggleModal('googlePlace')} disabled={disabled}>
        <Input
          editable={false}
          onPressIn={disabled ? undefined : () => toggleModal('googlePlace', true)}
          rightAccessory={<RightAccessory />}
          label={'Address'}
          {...rest}
          containerClasses={`py-0 ${rest.containerClasses}`}
        />
      </Pressable>
      <BottomModal
        showButton={false}
        onModalShow={handleInputFocus}
        isVisible={modals.googlePlace}
        containerStyle={{ flex: 1 }}
        innerStyle={{ flex: 1 }}
        contentContainerClass="flex-1"
        title={'Enter your address'}
        closeModal={() => toggleModal('googlePlace', false)}
        {...modalProps}>
        <View className="flex-1 px-20">
          <GooglePlacesAutocomplete
            placeholder="Search"
            fetchDetails
            autoFillOnNotFound
            ref={googlePlaceRef}
            enablePoweredByContainer={false}
            onPress={(data, details) => {
              onPressItem?.(details ?? ({} as GooglePlaceDetail));
              toggleModal('googlePlace', false);
            }}
            query={{
              key: 'AIzaSyBL_fl7b9ozmJbuMhZi1klcstUgw4qaB1s',
              sessiontoken: sessionToken,
              language: 'en',
              locationbias: 'IP bias',
            }}
            renderRow={(data, index) => <RenderCustomRow data={data} index={index} />}
            styles={{
              textInputContainer: {
                borderWidth: 1,
                borderColor: colors.grey.mutedLight,
                borderRadius: wp(12),
              },
              container: {
                flex: 1,
              },
            }}
          />
        </View>
      </BottomModal>
    </View>
  );
};

export default GooglePlaceInput;
