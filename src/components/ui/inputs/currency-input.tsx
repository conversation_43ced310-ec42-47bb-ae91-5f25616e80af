import { View } from 'react-native';
import Input, { InputProps } from './input';
import { useRef, useState } from 'react';
import Row from '../row';
import { BaseText } from '..';
import SelectDropdown, { DropDownItem, DropDownMethods } from './select-dropdown';
import { sluggify, wp } from 'src/assets/utils/js';
import Pressable from '../base/pressable';
import { ChevronDown, NigeriaFlag } from '../icons';
import colors from 'src/theme/colors';
import cx from 'classnames';
import { countryCodes, CURRENCIES } from 'catlog-shared';
import { Calendar } from 'node_modules/iconsax-react-native/src';
import { currencyDetails } from 'src/components/payments/wallet-cards';

interface CurrencyInputProps extends Partial<InputProps> {
  currencyOptions: DropDownItem[];
  selectedCurrency: CURRENCIES;
  onChangeCurrency: (currency: CURRENCIES) => void;
}

const CurrencyInput = ({ currencyOptions, selectedCurrency, onChangeCurrency, ...rest }: CurrencyInputProps) => {
  const [accessoryWidth, setAccessoryWidth] = useState(50);
  const dropDownRef = useRef<DropDownMethods>(null);

  const RightAccessory = () => (
    <Pressable onPress={() => dropDownRef.current?.open()}>
      <Row
        className={cx('justify-start', { 'bg-grey-bgOne rounded-8 p-8': false, 'p-0': true })}
        onLayout={e => setAccessoryWidth(e.nativeEvent.layout.width)}>
        <View className="w-2 h-16 bg-grey-border mr-6" />
        <View className="w-20 h-20">{currencyDetails[selectedCurrency].icon({})}</View>
        <BaseText fontSize={13} weight={'semiBold'} classes="text-black-placeholder ml-4">
          {selectedCurrency}
        </BaseText>
        <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
      </Row>
    </Pressable>
  );

  return (
    <View>
      <Input
        rightAccessory={<RightAccessory />}
        {...rest}
        containerClasses={cx(rest.containerClasses, { 'py-8 pr-8': false })}
        keyboardType={'numeric'}
        inputMode="numeric"
        onChangeText={value => {
          const formatted = handleNumberInput(value);

          if (formatted || value === '') {
            rest?.onChangeText ? rest?.onChangeText(formatted ?? '') : null;
          }
        }}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem={selectedCurrency}
        onPressItem={item => onChangeCurrency(item as CURRENCIES)}
        label={'Categories'}
        items={currencyOptions}
        containerClasses="mt-15"
        genItemKeysFun={value => sluggify(value.label)}
      />
    </View>
  );
};

const handleNumberInput = (text: string) => {
  // Remove non-numeric except one optional dot
  let cleaned = text.replace(/[^0-9.]/g, '');

  // Prevent multiple dots
  const parts = cleaned.split('.');
  if (parts.length > 2) return;

  // Limit to two decimal places
  if (parts[1]?.length > 2) return;

  // Handle leading zeros:
  // - If starts with '0' and not '0.' => remove leading zeros
  // - Allow '0.' or '0.5'
  if (parts[0]) {
    if (parts[0].startsWith('0') && parts[0] !== '0') {
      parts[0] = String(parseInt(parts[0], 10)); // removes leading zeros
    }
  }

  // Reconstruct the cleaned value
  const newValue = parts.join('.');

  return newValue;
};

export default CurrencyInput;
