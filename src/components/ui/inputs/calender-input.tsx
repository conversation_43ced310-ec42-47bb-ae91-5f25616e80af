import { View } from 'react-native';
import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import Input, { InputProps } from './input';
import { BottomModalProps } from '../modals/bottom-modal';
import Pressable from '../base/pressable';
import { Calendar } from 'iconsax-react-native/src';
import useModals from 'src/hooks/use-modals';
import CalendarModal, { CalendarModalProps } from '../modals/calendar-modal';
import { DateChangedCallback } from 'react-native-calendar-picker';
import dayjs from 'node_modules/dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

interface CalenderInputProps {
  onDateChange: DateChangedCallback;
  disabled?: boolean;
  calenderModalProps?: Partial<CalendarModalProps>;
  inputProps?: InputProps;
  initialDate?: Date;
  classes?: string;
}

const CalenderInput = ({ onDateChange, inputProps, calenderModalProps, disabled, classes, initialDate }: CalenderInputProps) => {
  const { modals, toggleModal } = useModals(['calender']);

  return (
    <View className={classes}>
      <Pressable onPress={() => toggleModal('calender')} disabled={disabled}>
        <Input
          editable={false}
          type="select"
          selectDisabled={disabled}
          onPressIn={disabled ? undefined : () => toggleModal('calender')}
          rightAccessory={
            <View className="border-grey-bgOne rounded-full">
              <Calendar size={wp(16)} color={colors.grey.muted} />
            </View>
          }
          {...inputProps}
        />
      </Pressable>
      <CalendarModal
        isVisible={modals.calender}
        closeModal={() => toggleModal('calender', false)}
        singleSelectMode
        onDateChange={onDateChange}
        // initialDate={inputProps?.value ? dayjs(inputProps?.value, 'DD-MM-YYYY').toDate() : new Date()}
        initialDate={initialDate}
        {...calenderModalProps}
      />
    </View>
  );
};

export default CalenderInput;
