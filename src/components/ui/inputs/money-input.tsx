import { TextInput } from 'react-native';
import Input, { InputProps } from './input';
import { NumericFormat } from 'react-number-format';

interface MoneyInputProps extends Partial<InputProps> {
  inputRef?: React.MutableRefObject<TextInput>;
}

const MoneyInput = ({ inputRef, ...rest }: MoneyInputProps) => {
  const value = rest?.value?.toString().length > 0 ? Number(rest.value) : null;
  return (
    <NumericFormat
      value={value}
      displayType="text"
      renderText={formattedValue => (
        <Input
          {...rest}
          value={value === null ? '' : formattedValue}
          onChangeText={t => {
            const cleanedUpText = String(t).replace(/,/g, '');
            rest.onChangeText?.(cleanedUpText);
          }}
          keyboardType={'number-pad'}
        />
      )}
      thousandSeparator
    />
  );
};

export default MoneyInput;
