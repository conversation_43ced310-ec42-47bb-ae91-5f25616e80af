import { View } from 'react-native';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import Input, { InputProps } from './input';
import Row from '../row';
import { BaseText } from '..';
import { ArrowRight } from '../icons';
import { useCallback, useState } from 'react';
import Pressable from '../base/pressable';
import { Grammerly } from 'iconsax-react-native/src';
import EmojiPicker, { EmojiType } from 'rn-emoji-keyboard';

export interface CategoryInputProps extends Partial<InputProps> {}

const CategoryInput = ({ ...rest }: CategoryInputProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [emoji, setEmoji] = useState<string | null>(null);

  const handlePick = (emojiObject: EmojiType) => {
    setEmoji(emojiObject.emoji);
  };

  const RightAccessory = useCallback(
    () => (
      <Pressable
        className="w-28 h-28 m-6 items-center justify-center bg-grey-bgOne rounded-full "
        onPress={() => setIsOpen(true)}>
        {emoji ? <BaseText fontSize={15}>{emoji}</BaseText> : <Grammerly size={wp(18)} color={colors.grey.muted} />}
      </Pressable>
    ),
    [emoji],
  );

  return (
    <>
      <Input
        rightAccessory={<RightAccessory />}
        placeholder={'Enter category name'}
        {...rest}
        containerClasses={`py-0 px-0 pl-16 ${rest?.containerClasses}`}
      />
      <EmojiPicker onEmojiSelected={handlePick} open={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
};

export default CategoryInput;
