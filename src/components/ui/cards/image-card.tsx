import React from 'react';
import { View } from 'react-native';
import { Image } from 'src/@types/utils';
import { wp } from 'src/assets/utils/js';
import Pressable from 'src/components/ui/base/pressable';
import { Close } from 'src/components/ui/icons';
import CustomImage from 'src/components/ui/others/custom-image';
import colors from 'src/theme/colors';
import CircularProgress from 'react-native-circular-progress-indicator';

interface ImageCardProps {
  image: Image;
  index: number;
  onRemove: (index: number) => void;
}

const ImageCard = ({ image, index, onRemove }: ImageCardProps) => {
  return (
    <View key={image.key} className="mt-10 w-80 h-80">
      <CustomImage
        className="w-full h-full rounded-10"
        imageProps={{ source: image.url ?? image.src, contentFit: 'cover' }}
      />
      {image.isUploading && (
        <View
          className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
          style={{ backgroundColor: '#292D321A' }}>
          <CircularProgress
            value={image.uploadProgress ?? 0}
            radius={wp(16)}
            duration={500}
            delay={600}
            activeStrokeWidth={wp(4)}
            inActiveStrokeWidth={wp(4)}
            strokeLinecap={'round'}
            activeStrokeColor={colors.accentGreen.main}
            inActiveStrokeColor={colors.white}
            maxValue={100}
            valueSuffix={'%'}
            progressValueStyle={{
              fontSize: wp(8),
              fontFamily: 'Inter-Bold',
              color: colors.white,
            }}
          />
        </View>
      )}
      {!image.isUploading && (
        <Pressable onPress={() => onRemove(index)} className='absolute -top-5 -right-5'>
          <View className="h-24 w-24 flex items-center justify-center rounded-full bg-black-placeholder">
            <Close currentColor={colors.white} size={wp(10)} />
          </View>
        </Pressable>
      )}
    </View>
  );
};

export default ImageCard;