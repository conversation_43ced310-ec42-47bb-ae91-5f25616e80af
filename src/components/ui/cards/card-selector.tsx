import { View } from 'react-native';
import { cx, wp } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui/base';
import Pressable from 'src/components/ui/base/pressable';
import Container from 'src/components/ui/container';
import { CheckActive } from 'src/components/ui/icons';
import Row from 'src/components/ui/row';
import colors from 'src/theme/colors';

export interface CardSelectorItem {
  title: string;
  value: string;
  description: string;
  icon?: React.ReactNode;
}
interface Props {
  items: CardSelectorItem[];
  onPress: (value: string) => void;
  selected: string;
  hasError?: boolean;
}
const CardSelector: React.FC<Props> = ({ items, onPress, selected, hasError }) => {
  return (
    <Container
      className={cx('w-full p-10 bg-grey-bgOne flex-col rounded-12', {
        'border border-accentRed-main': hasError,
      })}>
      {items.map((item, index) => {
        return (
          <Pressable
            className={cx('bg-white p-10 rounded-10')}
            style={{ marginBottom: index !== items.length - 1 ? wp(10) : 0 }}
            onPress={() => onPress(item.value)}
            key={index}>
            <Row className="items-start">
              <View className="flex-1">
                <View className="mb-10">{item.icon}</View>

                <BaseText fontSize={16} type="heading" classes="text-black-main " weight="medium">
                  {item.title}
                </BaseText>
                <BaseText fontSize={13} classes="text-black-muted mt-1.5">
                  {item.description}
                </BaseText>
              </View>
              {selected === item.value ? (
                <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
              ) : (
                <View className="rounded-full border-[3px] border-grey-border w-20 h-20" />
              )}
            </Row>
          </Pressable>
        );
      })}
    </Container>
  );
};
export default CardSelector;
