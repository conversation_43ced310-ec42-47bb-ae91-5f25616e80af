import { View } from 'react-native';
import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import { Edit2, Trash } from 'iconsax-react-native/src';
import { BaseTextProps } from '../base/base-text';
import { styled } from 'nativewind';
import { RowProps } from '../row';
import { ReactNode } from 'react';

interface CustomizationCardProps extends RowProps {
  textTop: string;
  textBottom: string;
  showTools?: boolean;
  textTopProps?: BaseTextProps;
  textBottomProps?: BaseTextProps;
  rightElement?: ReactNode;
}

const CustomizationCard = ({
  textTop,
  textBottom,
  textTopProps,
  textBottomProps,
  rightElement,
  showTools = false,
  ...props
}: CustomizationCardProps) => {
  return (
    <Row className="py-[13px] px-15 rounded-[10px] border border-grey-border bg-grey-bgOne" {...props}>
      <View className="flex-1 justify-stretch">
        <BaseText {...textTopProps}>{textTop}</BaseText>
        <BaseText fontSize={11} weight={'semiBold'} {...textBottomProps} classes={`mt-5 ${textBottomProps?.classes}`}>
          {textBottom}
        </BaseText>
      </View>
      {showTools && (
        <>
          <CircledIcon className="bg-white mr-10">
            <Edit2 size={wp(18)} color={colors.black.placeholder} />
          </CircledIcon>
          <CircledIcon className="bg-white">
            <Trash size={wp(18)} color={colors.black.placeholder} />
          </CircledIcon>
        </>
      )}
      {rightElement}
    </Row>
  );
};

export default styled(CustomizationCard);
