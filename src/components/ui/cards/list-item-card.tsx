import { Text, TouchableOpacity, View } from 'react-native';
import { ReactNode } from 'react';
import { BaseText } from '../base';
import { TextProps } from 'react-native';
import cx from 'classnames';
import { BaseTextProps } from '../base/base-text';
import SectionContainer, { ContainerType, SectionContainerProps } from '../section-container';
import Pressable, { PressableProps } from '../base/pressable';
import Can from '../permissions/can';
import { Permission } from 'src/assets/utils/js/permissions';

export interface ListItemCardProps extends Partial<PressableProps> {
  // onPress?: (e:any) => void;
  leftElement?: ReactNode;
  bottomElement?: ReactNode;
  rightElement?: ReactNode;
  showBorder?: boolean;
  spreadTitleContainer?: boolean;
  title?: string;
  titleProps?: BaseTextProps;
  description?: string;
  descriptionProps?: BaseTextProps;
  descriptionClasses?: TextProps['className'];
  titleClasses?: TextProps['className'];
  titleAddon?: React.ReactElement;
  containerClasses?: string;
  alignStart?: boolean;
  permissionData?: {
    permission?: Permission;
    planPermission?: Permission;
    countryPermission?: Permission;
  };
}

const ListItemCard = ({
  onPress,
  leftElement,
  rightElement,
  showBorder = false,
  spreadTitleContainer = true,
  title,
  titleClasses,
  description,
  bottomElement,
  titleProps,
  descriptionProps,
  descriptionClasses = '',
  titleAddon,
  containerClasses,
  alignStart = false,
  permissionData,
  ...props
}: ListItemCardProps) => {
  const renderContent = (isAllowed = true) => {
    const isDisabled = !isAllowed || props.disabled;
    const opacity = isAllowed ? 1 : 0.5;

    return (
      <Pressable
        className={`flex-row py-15 ${showBorder && 'border-b border-b-grey-border'} ${containerClasses} ${alignStart ? 'items-start' : 'items-center'}`}
        onPress={isAllowed ? onPress : undefined}
        disabled={isDisabled}
        style={[props.style, { opacity }]}
        {...props}>
        {leftElement && leftElement}
        <View className={'flex-1 ml-12'}>
          <View
            className={cx('flex-row items-center', {
              'justify-between': spreadTitleContainer,
              'justify-start': !spreadTitleContainer,
            })}>
            {title && (
              <BaseText
                fontSize={13}
                weight={'semiBold'}
                numberOfLines={1}
                {...titleProps}
                classes={cx({ 'mr-12': leftElement !== null }, titleClasses, titleProps?.classes)}>
                {title}
              </BaseText>
            )}
            {titleAddon ? titleAddon : null}
          </View>
          {bottomElement && bottomElement}
          {description && !bottomElement && (
            <BaseText
              fontSize={12}
              weight={'medium'}
              {...descriptionProps}
              classes={cx(
                `text-black-muted ${descriptionClasses}`,
                {
                  'mt-5': Boolean(title),
                },
                descriptionProps?.classes,
              )}>
              {description}
            </BaseText>
          )}
        </View>
        {rightElement && rightElement}
      </Pressable>
    );
  };

  if (permissionData) {
    return <Can data={permissionData}>{isAllowed => renderContent(isAllowed)}</Can>;
  }

  return renderContent();
};

interface ListCardProps extends Omit<SectionContainerProps, 'children'> {
  titleClasses?: string;
  descriptionClasses?: string;
  items: ListItemCardProps[];
  titleProps?: { [key: string]: any };
  descriptionProps?: { [key: string]: any };
  containerType?: ContainerType;
  staticRightElement?: React.ReactNode;
}

export const ListCard: React.FC<ListCardProps> = props => {
  const {
    titleClasses,
    descriptionClasses,
    items,
    descriptionProps,
    classes,
    isEmpty,
    emptyStateProps,
    containerType,
    titleProps,
    staticRightElement,
  } = props;

  return (
    <SectionContainer {...{ isEmpty: items.length === 0, emptyStateProps, containerType, classes }}>
      {items.map((item, index) => (
        <ListItemCard
          key={item.title ?? index}
          {...{ titleClasses, descriptionClasses, descriptionProps, titleProps }}
          {...item}
          rightElement={staticRightElement ?? item.rightElement}
          showBorder={index < items.length - 1}
        />
      ))}
    </SectionContainer>
  );
};

export default ListItemCard;
// export default styled(ListItemCard, { props: { titleClasses: true, descriptionClasses: true } });
