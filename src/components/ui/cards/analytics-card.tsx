import { useNavigation } from '@react-navigation/native';
import React, { ReactNode } from 'react';
import { Text, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';
import colors from '@/theme/colors';
import { ArrowDownRight, ArrowUpRight } from '../icons';
import cx from 'classnames';
import { wp } from '@/assets/utils/js';
import Row from '../row';
import { BaseText } from '../base';
import { styled } from 'nativewind';
import classNames from 'classnames';

export enum AnalyticsVariants {
  'STORE_VISITS' = 'store_visits',
  'TOTAL_ORDERS' = 'total_orders',
  'PAYMENTS' = 'payments',
  'CREDIT_BALANCE' = 'credit_balance',
}

interface TAnalyticsCardProps extends TouchableOpacityProps {
  value?: number | string;
  title: string;
  // cardBg?: string;
  iconBg: string;
  change: number;
  showChange?: boolean;
  icon: ReactNode;
  addon: ReactNode;
  theme: 'grey' | 'white';
  classes?: string;
}

const AnalyticsCard = ({
  value,
  title,
  theme = 'grey',
  iconBg,
  change,
  showChange = true,
  icon,
  addon,
  classes,
  ...props
}: TAnalyticsCardProps) => {
  const navigation = useNavigation();

  return (
    <TouchableOpacity
      className={classNames(`px-15 items-start py-15 flex-1 ${themeColors[theme].cardBg}`, classes)}
      {...props}>
      <Row className="w-full items-start">
        <Row className={`p-10 rounded-full items-center justify-center ${iconBg}`}>{icon}</Row>
        {addon ? (
          addon
        ) : (
          <>
            {showChange && (
              <Row
                className={`flex flex-row items-center justify-center ml-4 rounded-3xl py-3 px-5 self-center ${themeColors[theme].badgeBg}`}>
                <Text
                  className={cx('leading-none  font-interSemiBold text-[11px]', {
                    'text-accentGreen-main': change >= 0,
                    'text-accentRed-main': change < 0,
                  })}>
                  {Math.abs(change)}%
                </Text>
                {change >= 0 ? (
                  <ArrowUpRight currentColor={colors.accentGreen.main} size={14} strokeWidth={2} />
                ) : (
                  <ArrowDownRight currentColor={colors.accentRed.main} size={14} strokeWidth={2} />
                )}
              </Row>
            )}
          </>
        )}
      </Row>
      <View className="mt-15">
        <BaseText fontSize={12} classes="leading-4 text-black-muted">
          {title}
        </BaseText>
        <Row className="justify-start mt-4">
          <BaseText
            fontSize={18}
            weight={'black'}
            classes={cx({
              'text-black-muted': value === 0,
              'text-black-main': value !== 0,
            })}
            type="heading">
            {value}
          </BaseText>
        </Row>
      </View>
    </TouchableOpacity>
  );
};

const themeColors = {
  grey: {
    cardBg: 'bg-grey-bgOne',
    badgeBg: 'bg-white',
  },
  white: {
    cardBg: 'bg-white',
    badgeBg: 'bg-grey-bgOne',
  },
};

export default AnalyticsCard;
