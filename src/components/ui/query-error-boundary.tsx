import React from 'react';
import { View, ActivityIndicator, Linking } from 'react-native';
import { BaseText } from './base';
import CircledIcon from './circled-icon';
import { ArrowRotateLeft, CloseCircle, Danger, RotateLeft } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { cx, wp } from '@/assets/utils/js';
import Button, { ButtonSize, ButtonVariant } from './buttons/button';
import SectionContainer, { ContainerType } from './section-container';
import Row from './row';
import { CONTACT_SUPPORT_WA_LINK } from 'src/assets/utils/js/constants';

interface QueryErrorBoundaryProps {
  error: any;
  isLoading: boolean;
  refetch: () => void;
  children: React.ReactNode;
  customErrorMessage?: string;
  variant?: 'fullPage' | 'section';
  classes?: string;
  isRetrying?: boolean;
  errorTitle?: string;
  padAround?: boolean;
  customErrorElement?: React.ReactNode;
}

const QueryErrorBoundary: React.FC<QueryErrorBoundaryProps> = ({
  error,
  isLoading,
  refetch,
  children,
  customErrorMessage,
  variant = 'section',
  classes = '',
  isRetrying = false,
  errorTitle = 'Failed to load data',
  padAround = false,
  customErrorElement,
}) => {
  if (!error) return <>{children}</>;

  const errorMessage = customErrorMessage || getErrorMessage(error);
  const isInLoadingState = isLoading || isRetrying;

  if (customErrorElement) {
    return customErrorElement
  }

  switch (variant) {
    case 'fullPage':
      return (
        <View className={`flex-1 items-center justify-center p-20 ${classes}`}>
          <CircledIcon className="bg-accentYellow-pastel2 p-16 self-center mb-10">
            <Danger variant={'Bold'} color={colors.accentYellow.main} size={wp(32)} />
          </CircledIcon>
          <BaseText type={'heading'} fontSize={18} classes="text-center mb-5">
            {errorTitle}
          </BaseText>
          <View style={{ width: '60%' }}>
            <BaseText fontSize={13} classes="text-black-placeholder text-center">
              {errorMessage}
            </BaseText>
          </View>

          <Row className="space-x-10 w-full px-20 mt-20">
            <View className="flex-1">
              <Button
                text={isInLoadingState ? 'Retrying...' : 'Contact Support'}
                onPress={() => Linking.openURL(CONTACT_SUPPORT_WA_LINK)}
                disabled={isInLoadingState}
                size={ButtonSize.MEDIUM}
                variant={ButtonVariant.LIGHT}
                btnStyle="w-full"
              />
            </View>
            <View className="flex-1">
              <Button
                text={isInLoadingState ? 'Retrying...' : 'Reload'}
                onPress={refetch}
                disabled={isInLoadingState}
                size={ButtonSize.MEDIUM}
                variant={ButtonVariant.PRIMARY}
                btnStyle="w-full"
              />
            </View>
          </Row>
          {/* <Button
            text={isInLoadingState ? 'Retrying...' : 'Retry'}
            onPress={refetch}
            disabled={isInLoadingState}
            leftAddOn={
              isInLoadingState ? (
                <ActivityIndicator size="small" color={colors.white} style={{ marginRight: 8 }} />
              ) : (
                <ArrowRotateLeft size={wp(18)} color={colors.white} style={{ marginRight: 8 }} />
              )
            }
            size={ButtonSize.MEDIUM}
            variant={ButtonVariant.PRIMARY}
            btnStyle="w-auto px-20 mt-15"
          /> */}
        </View>
      );

    case 'section':
      return (
        <View className={cx({ 'px-20': padAround })}>
          <SectionContainer className={`my-10 ${classes} bg-grey-bgTwo`} scrollEnabled={false}>
            <View className="items-center justify-center py-30">
              <CircledIcon className="bg-accentYellow-pastel2 p-12 self-center mb-10">
                <Danger variant={'Bold'} color={colors.accentYellow.main} size={wp(24)} />
              </CircledIcon>
              <BaseText type={'heading'} fontSize={16} classes="text-center mb-5">
                {errorTitle}
              </BaseText>
              <View style={{ width: '80%' }}>
                <BaseText fontSize={13} classes="text-black-placeholder text-center">
                  {errorMessage}
                </BaseText>
              </View>
              <Row className="space-x-10 w-full">
                <View className="flex-1">
                  <Button
                    text={isInLoadingState ? 'Retrying...' : 'Contact Support'}
                    onPress={() => Linking.openURL(CONTACT_SUPPORT_WA_LINK)}
                    disabled={isInLoadingState}
                    size={ButtonSize.MEDIUM}
                    variant={ButtonVariant.LIGHT}
                    btnStyle="mt-15 px-20 w-full"
                  />
                </View>
                <View className="flex-1">
                  <Button
                    text={isInLoadingState ? 'Retrying...' : 'Reload'}
                    onPress={refetch}
                    disabled={isInLoadingState}
                    // leftAddOn={
                    //   isInLoadingState ? (
                    //     <ActivityIndicator size="small" color={colors.white} style={{ marginRight: 8 }} />
                    //   ) : (
                    //     <ArrowRotateLeft size={wp(16)} color={colors.white} style={{ marginRight: 8 }} />
                    //   )
                    // }
                    size={ButtonSize.MEDIUM}
                    variant={ButtonVariant.PRIMARY}
                    btnStyle="mt-15 px-20 w-full"
                  />
                </View>
              </Row>
            </View>
          </SectionContainer>
        </View>
      );

    default:
      return <>{children}</>;
  }
};

// Helper function to get appropriate error message
const getErrorMessage = (error: any): string => {
  // Network error
  if (error?.message?.includes('Network') || !navigator.onLine) {
    return 'Network error. Please check your connection and try again.';
  }

  // Server error (5xx)
  if (error?.status >= 500 || error?.body?.status >= 500) {
    return 'Server error. Please try again later.';
  }

  // Client error (4xx)
  if (error?.status >= 400 || error?.body?.status >= 400) {
    return error?.body?.message || 'Request failed. Please try again.';
  }

  // Default error message
  return error?.message || 'Something went wrong. Please try again.';
};

export default QueryErrorBoundary;
