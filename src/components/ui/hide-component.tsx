import React from 'react';
import { ViewProps, View } from 'react-native';

interface HideComponentProps {
  show: boolean;
  children: React.ReactNode;
  classNames?: string;
  style?: ViewProps['style'];
}

const HideComponent = ({ show, children, classNames, style = {} }: HideComponentProps) => {
  return (
    <View 
    // style={show ? style : { height: 0, width: 0, opacity: 0 }}
    style={show ? style : { display: 'none' }}
    className={show ? classNames : ''}>
      {children}
    </View>
  );
};

export default HideComponent;
