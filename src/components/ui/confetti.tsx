import React, { useEffect, useRef } from 'react';
import React<PERSON>on<PERSON>tti from 'react-native-confetti';

const Confetti = () => {
  const confettiRef = useRef<any>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      if (confettiRef.current) {
        confettiRef.current.startConfetti();
        clearInterval(interval);
      }
    }, 50);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <ReactConfetti ref={confettiRef} untilStopped={false} duration={2000} size={1.5} timeout={0} />
    </>
  );
};

export default Confetti;
