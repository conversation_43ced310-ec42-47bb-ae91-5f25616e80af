import { styled } from 'nativewind';
import { ReactNode } from 'react';
import { Text, View, ViewProps, ViewStyle } from 'react-native';
import cx from 'classnames';

export interface RowProps extends Partial<ViewProps> {
  children?: ReactNode;
  classes?: string;
  spread?: boolean;
  alignCenter?: boolean;
}

const Column = ({ children, classes, spread = true, alignCenter = false, ...props }: RowProps) => {
  return (
    <View
      className={cx(
        'flex',
        {
          'justify-between': spread,
          'items-center': alignCenter,
        },
        classes,
      )}
      {...props}>
      {children}
    </View>
  );
};

export default styled(Column);
