import { ReactNode } from 'react';
import { Text } from 'react-native';
import BaseText from '@/components/ui/base/base-text';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from '../base/pressable';
import cx from 'classnames';
import { capitalizeStr, wp } from 'src/assets/utils/js';

interface SelectionPillProps extends Partial<PressableProps> {
  title: string;
  selected: boolean;
  children?: ReactNode;
  backgroundColor?: { active?: string; inActive?: string };
  textColor?: { active?: string; inActive?: string };
  borderColor?: { active?: string; inActive?: string };
  showBorder?: boolean;
  capitalize?: boolean;
  leftElement?: ReactNode;
}

const SelectionPill = ({
  selected,
  title,
  children,
  showBorder = false,
  leftElement,
  backgroundColor = { active: colors.primary.pastel, inActive: colors.grey.bgOne },
  textColor = { active: colors.primary.main, inActive: colors.black.muted },
  borderColor = { active: colors.primary.main, inActive: colors.black.muted },
  capitalize = false,
  ...props
}: SelectionPillProps) => {
  const bg = selected ? backgroundColor.active : backgroundColor.inActive;
  const titleColor = selected ? textColor.active : textColor.inActive;
  const borderColorMap = selected ? borderColor.active : borderColor.inActive;

  return (
    <Pressable
      {...props}
      className={cx('flex flex-row py-10 px-12 mr-10 items-center justify-center rounded-40', props.className)}
      style={{ backgroundColor: bg, ...(showBorder && { borderWidth: wp(1), borderColor: borderColorMap }) }}>
      {leftElement}
      <BaseText
        weight={selected ? 'medium' : 'regular'}
        classes="text-black-secondary"
        style={{ color: titleColor }}
        fontSize={12}>
        {capitalize ? capitalizeStr(title) : title}
      </BaseText>
      {children}
    </Pressable>
  );
};

export default SelectionPill;
