import React from 'react';
import { ScrollView, View } from 'react-native';
import { styled } from 'nativewind';
import Row from '../row';
import SelectionPill from './selection-pill';

interface ScrollableSelectionPillsProps {
  pills: {
    title: string;
    onPress?: VoidFunction;
  }[];
  selectedPill: string;
}

const ScrollableSelectionPills = (props: ScrollableSelectionPillsProps) => {
  const { pills, selectedPill, ...rest } = props;

  return (
    <View className="pt-25 pb-5">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}
        {...rest}>
        <Row>
          {pills.map(item => (
            <SelectionPill
              key={item?.title}
              selected={selectedPill === item?.title}
              title={item?.title}
              onPress={item?.onPress!}
            />
          ))}
        </Row>
      </ScrollView>
    </View>
  );
};

export default styled(ScrollableSelectionPills);
