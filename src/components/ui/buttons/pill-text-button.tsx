import { ReactNode } from 'react';
import { Text } from 'react-native';
import BaseText from '@/components/ui/base/base-text';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from '../base/pressable';
import { cx } from 'src/assets/utils/js';

interface PillTextButtonProps extends Partial<PressableProps> {
  label: string;
  textClassName?: string;
}

const PillTextButton = ({ label, textClassName, ...props }: PillTextButtonProps) => {
  return (
    <Pressable
      className="flex flex-row py-8 px-12 items-center justify-center rounded-40 bg-grey-bgOne"
      // style={{ backgroundColor }}
      {...props}>
      <BaseText weight={'medium'} classes={cx('text-primary-main', textClassName)} fontSize={12}>
        {label}
      </BaseText>
    </Pressable>
  );
};

export default PillTextButton;
