import { ReactNode } from 'react';
import { Text, View } from 'react-native';
import BaseText from '@/components/ui/base/base-text';
import colors from '@/theme/colors';
import Pressable, { PressableProps } from '../base/pressable';
import Row from '../row';
import { cx, wp } from 'src/assets/utils/js';
import { Add, Minus, Trash } from 'iconsax-react-native/src';

interface QuantityToggleProps extends Partial<PressableProps> {
  quantity: number;
  onPressMinus: VoidFunction;
  onPressAdd: VoidFunction;
  disabled?: boolean;
  showDelete?: boolean;
  showToggleButtons?: boolean;
}

const QuantityToggle = ({
  onPressMinus,
  onPressAdd,
  quantity,
  showDelete = true,
  showToggleButtons = true,
  disabled,
  ...props
}: QuantityToggleProps) => {
  return (
    <Row className="justify-start gap-x-10">
      {showToggleButtons && (
        <Pressable
          disabled={disabled}
          className={cx('p-5 border-grey-border border rounded-full bg-white', { 'opacity-60': disabled })}
          onPress={onPressMinus}>
          {quantity === 1 && showDelete ? (
            <Trash size={wp(16)} color={colors.accentRed.main} />
          ) : (
            <Minus size={wp(16)} color={colors.black.placeholder} />
          )}
        </Pressable>
      )}
      <View className="py-5 px-14 border-grey-border border rounded-[6px] bg-white">
        <BaseText className="text-black-main" fontSize={12} weight={'semiBold'}>
          {quantity}
        </BaseText>
      </View>
      {showToggleButtons && (
        <Pressable
          disabled={disabled}
          className={cx('p-5 border-grey-border border rounded-full bg-white', { 'opacity-60': disabled })}
          onPress={onPressAdd}>
          <Add size={wp(16)} color={colors.black.placeholder} />
        </Pressable>
      )}
    </Row>
  );
};

export default QuantityToggle;
