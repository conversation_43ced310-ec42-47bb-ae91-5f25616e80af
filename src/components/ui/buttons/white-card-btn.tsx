import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import Row from '../row';
import { BaseText } from '../base';
import colors from '@/theme/colors';
import { styled } from 'nativewind';
import Pressable, { PressableProps } from '../base/pressable';
import classNames from 'node_modules/classnames';

interface WhiteCardButtonProps extends Partial<PressableProps> {
  children: React.ReactNode;
  icon?: React.ReactNode;
  leftIcon?: React.ReactNode;
  align?: 'center' | 'start' | 'end';
}

const WhiteCardButton = ({ children, leftIcon, icon, align = 'start', ...props }: WhiteCardButtonProps) => {
  return (
    <Pressable
      className={classNames('items-center rounded-[5px] py-8 px-12 bg-white', alignmentClasses[align])}
      {...props}>
      <Row>
        {leftIcon ? <View className="mr-2">{leftIcon}</View> : null}
        <BaseText fontSize={12} weight={'medium'} classes="text-primary-main">
          {children}
        </BaseText>
        {icon ? <View className="ml-2">{icon}</View> : null}
      </Row>
    </Pressable>
  );
};

const alignmentClasses = {
  center: 'self-center',
  start: 'self-start',
  end: 'self-end',
};

export default styled(WhiteCardButton);
