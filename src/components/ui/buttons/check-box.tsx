import classNames from 'classnames';
import { View } from 'react-native';
import { Check } from '../icons';
import colors from '@/theme/colors';

interface CheckBoxProps {
  checked: boolean;
}

const CheckBox = ({ checked }: CheckBoxProps) => {
  return (
    <View
      className={classNames('rounded-[4px] border-[1.5px] p-2 w-[17px] h-[17px] flex items-center justify-center', {
        'border-grey-border': !checked,
        'border-accentGreen-main': checked,
      })}>
      <Check size={12} strokeWidth={3} currentColor={colors.accentGreen.main} style={{ opacity: checked ? 100 : 0 }} />
    </View>
  );
};

export default CheckBox;
