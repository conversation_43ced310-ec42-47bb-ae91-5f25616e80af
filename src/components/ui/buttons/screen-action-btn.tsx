import React from 'react';
import Row from '../row';
import { BaseText } from '../base';
import colors from '@/theme/colors';
import { styled } from 'nativewind';
import Pressable, { PressableProps } from '../base/pressable';
import CircledIcon from '../circled-icon';
import { ArrowUpRight } from '../icons';
import { wp } from 'src/assets/utils/js';

interface ScreenActionBtnProps extends Partial<PressableProps> {
  icon?: React.ReactNode;
  customIcon?: React.ReactNode;
  title:string;
}

const ScreenActionBtn = ({ children, customIcon, title, icon, ...props }: ScreenActionBtnProps) => {
  return (
    <Pressable className="px-20 py-15 bg-primary-pastel" {...props}>
      <Row>
        <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
          {title}
        </BaseText>
        {!customIcon && (
          <CircledIcon className="bg-white">
            <ArrowUpRight size={wp(13)} strokeWidth={2} currentColor={colors.primary.main} />
          </CircledIcon>
        )}
        {customIcon && customIcon}
      </Row>
    </Pressable>
  );
};

export default styled(ScreenActionBtn);
