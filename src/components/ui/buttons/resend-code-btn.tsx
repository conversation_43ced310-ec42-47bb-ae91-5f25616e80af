import { View, ViewProps } from 'react-native';
import { BaseText } from '../base';
import Row from '../row';
import { ArrowUpRight } from '../icons';
import colors from 'src/theme/colors';
import Pressable from '../base/pressable';
import cx from 'classnames';
import { timeToClock, wp } from 'src/assets/utils/js';
import { Refresh, Refresh2 } from 'node_modules/iconsax-react-native/src';

interface ResendCodeBtnProps extends ViewProps {
  nextResendTime: number;
  disableResend?: boolean;
  onPressResend?: VoidFunction;
}

const ResendCodeBtn = ({ nextResendTime, disableResend, onPressResend, ...props }: ResendCodeBtnProps) => {
  return (
    <View {...props}>
      {nextResendTime > 0 && (
        <BaseText classes="text-center text-grey-muted" fontSize={12}>
          Resend in {timeToClock(nextResendTime)}
        </BaseText>
      )}
      {nextResendTime <= 0 && (
        <Pressable
          className={cx('self-center py-10 px-15 rounded-full bg-grey-bgOne', {
            'opacity-50': disableResend,
          })}
          onPress={onPressResend}
          disabled={disableResend}>
          <Row classes="justify-start">
            <BaseText fontSize={12} weight={'semiBold'} classes={'text-primary-main mr-4'}>
              Resend Code
            </BaseText>
            <Refresh2 size={wp(13)} strokeWidth={2} color={colors.primary.main} />
          </Row>
        </Pressable>
      )}
    </View>
  );
};

export default ResendCodeBtn;
