import React, { ReactNode } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import BaseText from '../base/base-text';
import Row from '../row';
import { MoreHorizontal } from '../icons';
import colors from '@/theme/colors';
import ActionPill from './action-pill';

interface ScrollableActionPillsProps {
  pills: {
    LeftIcon: (...args: any) => React.JSX.Element;
    title: string;
    addon?: ReactNode;
    onPress?: VoidFunction;
  }[];
  title?: string;
  addon?: ReactNode;
}

const ScrollableActionPills = (props: ScrollableActionPillsProps) => {
  const { pills, title, addon } = props;

  return (
    <View>
      {title ? (
        <Row className="px-20">
          <BaseText weight={'bold'} type={'heading'} fontSize={14}>
            {title}
          </BaseText>
          {addon ? addon : null}
        </Row>
      ) : null}
      <ScrollView horizontal className={`${title && 'mt-10'}`} showsHorizontalScrollIndicator={false}>
        <Row className="gap-x-16 px-20">
          {pills.map(({ LeftIcon, title, addon, onPress }, index) => (
            <ActionPill
              key={title}
              leftIcon={<LeftIcon color={colors?.black.placeholder} size={15} strokeWidth={2} />}
              title={title}
              addon={addon}
              onPress={onPress}
            />
          ))}
        </Row>
      </ScrollView>
    </View>
  );
};

export default ScrollableActionPills;
