import { GestureResponderEvent, View } from 'react-native';
import colors from '@/theme/colors';
import { CheckActive } from '../icons';
import { hp, wp } from '@/assets/utils/js';
import * as Animatable from 'react-native-animatable';
import Pressable from '../base/pressable';

interface RadioProps {
  active: boolean;
  onClick?: VoidFunction;
  className?: string;
}

const Radio = ({ active = false, onClick, className }: RadioProps) => {
  return (
    <Pressable onPress={onClick} disabled={!onClick}>
      {active ? (
        <Animatable.View className={className}>
          <CheckActive
            width={wp(18)}
            height={hp(18)}
            primaryColor={colors.accentGreen.main}
            secondaryColor={colors.white}
          />
        </Animatable.View>
      ) : (
        <Animatable.View
          animation={'bounceIn'}
          duration={200}
          className={`rounded-full border-[3px] border-grey-border w-20 h-20 bg-grey-bgOne ${className}`}
        />
      )}
    </Pressable>
  );
};

export default Radio;
