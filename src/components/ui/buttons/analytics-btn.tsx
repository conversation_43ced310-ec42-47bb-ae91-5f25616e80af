import React from 'react';

import { CircledIcon } from '..';
import { BaseText } from '../base';
import Pressable, { PressableProps } from '../base/pressable';
import { ArrowUpRight } from '../icons';
import Row from '../row';

import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';

export interface AnalyticsBtnProps extends PressableProps {
  onPress: VoidFunction;
  bgColorVariant: 'YELLOW' | 'RED' | 'GREEN' | 'ORANGE' | 'DEFAULT';
}

export enum COLOR_VARIANT {
  YELLOW,
  RED,
  GREEN,
  ORANGE,
  DEFAULT,
}

const AnalyticsBtn = ({ onPress, bgColorVariant = 'RED' }: AnalyticsBtnProps) => {
  const colorVariants = {
    YELLOW: '#FFFAF4',
    RED: '#FFF8FB',
    GREEN: colors.accentGreen.pastel2,
    ORANGE: colors.accentOrange.pastel,
  };
  return (
    <Pressable onPress={onPress} className="px-20 py-15" style={{ backgroundColor: colorVariants[bgColorVariant] }}>
      <Row>
        <BaseText fontSize={14} weight="medium" classes="text-black-secondary">
          See Analytics
        </BaseText>
        <CircledIcon className="bg-white">
          <ArrowUpRight size={wp(13)} strokeWidth={2} currentColor={colors.primary.main} />
        </CircledIcon>
      </Row>
    </Pressable>
  );
};

export default AnalyticsBtn;
