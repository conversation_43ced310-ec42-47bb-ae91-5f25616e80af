import colors from 'src/theme/colors';
import { BaseText } from './base';
import Pressable from './base/pressable';
import Button from './buttons/button';
import { Close } from './icons';
import Row from './row';
import { cx, wp } from 'src/assets/utils/js';
import { View } from 'react-native';

interface Props {
  setSearchValue: (search:string)=> void;
  searchValue: string;
  classes?: string;
}
const ClearSearch: React.FC<Props> = ({ setSearchValue, searchValue, classes: className }) => {
  if (searchValue == '') return undefined;
  return (
    <Row classes={cx('p-10 rounded-12 border border-grey-border ', className)}>
      <BaseText className="text-black-muted">
        Showing result for{' '}
        <BaseText classes="text-black-main" weight="medium">
          "{searchValue}"
        </BaseText>
      </BaseText>

      <Pressable
        onPress={() => setSearchValue('')}
        className="p-5 px-10 flex-row items-center rounded-full bg-grey-bgOne">
        <BaseText fontSize={12} classes="text-black-muted" weight={'medium'}>
          Clear
        </BaseText>
        <View className="h-20 w-20 flex items-center justify-center rounded-full bg-grey-bgOne">
          <Close currentColor={colors.black.main} size={wp(10)} />
        </View>
      </Pressable>
    </Row>
  );
};
export default ClearSearch;
