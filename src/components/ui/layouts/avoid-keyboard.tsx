import { KeyboardAvoidingView, KeyboardAvoidingViewProps } from 'react-native';
import { ReactNode } from 'react';
import { Platform } from 'react-native';
import { styled } from 'nativewind';

interface AvoidKeyboardProps extends Partial<KeyboardAvoidingViewProps> {
  children?: ReactNode;
}

const AvoidKeyboard = ({ children, ...rest }: AvoidKeyboardProps) => {
  // const isIos = Platform.OS === 'ios';

  return (
    <KeyboardAvoidingView className="flex-1" behavior={'padding'} {...rest}>
      {children}
    </KeyboardAvoidingView>
  );
};

export default styled(AvoidKeyboard);
