import { ArrowCircleLeft2, ArrowCircleRight, HambergerMenu, Notification } from 'iconsax-react-native/src';
import { Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { wp } from '@/assets/utils/js';
import { BaseText } from '..';

export interface AuthHeaderProps {
  backgrounColor: string;
  pageTitle?: string;
}

const AuthHeader = ({ backgrounColor, pageTitle }: AuthHeaderProps) => {
  const top = useSafeAreaInsets().top;

  const navigation = useNavigation();

  return (
    <View
      className={`flex-row justify-between items-center px-20 pb-16 border-b border-b-grey-border bg-white ${backgrounColor}`}
      style={{ paddingTop: top + 16 }}>
      {pageTitle && (
        <>
          <BaseText fontSize={14} classes="flex-1 font-fhOscarBold" type="heading">
            {pageTitle}
          </BaseText>
          <TouchableOpacity className="p-10 ml-10 rounded-full bg-white flex items-center justify-center">
            <HambergerMenu color={colors?.black?.main} size={wp(20)} />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default AuthHeader;
