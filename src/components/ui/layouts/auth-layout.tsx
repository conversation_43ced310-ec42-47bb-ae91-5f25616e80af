import { View } from 'react-native';
import { ReactNode, useEffect } from 'react';
import AuthHeader, { AuthHeaderProps } from '@/components/ui/layouts/auth-header';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useStatusbar from 'src/hooks/use-statusbar';

interface AuthLayoutProps {
  children: ReactNode;
  headerProps: AuthHeaderProps;
}

const AuthLayout = ({ children, headerProps }: AuthLayoutProps) => {
  const { setStatusBarStyle, setStatusBar } = useStatusbar();
  setStatusBar('dark', 'transparent', true);

  return (
    <View className="flex-1 bg-white">
      <AuthHeader {...headerProps} />
      {children}
    </View>
  );
};

export default AuthLayout;
