import React, { useMemo, useRef } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { DrawerContentComponentProps, DrawerContentScrollView } from '@react-navigation/drawer';
import Row from '../row';
import CustomerInitial from '@/components/customer/customer-initial';
import { ActionPill } from '../buttons';
import { copyToClipboard, delay, hp, showLoader, showSuccess, wp } from '@/assets/utils/js';
import { BaseText } from '../base';
import CircledIcon from '../circled-icon';
import {
  ArrowDown2,
  Box,
  CardTick,
  Copy,
  Messages3,
  Profile,
  Profile2User,
  Receipt1,
  Setting2,
  TagUser,
  TransmitSqaure2,
  UserOctagon,
} from 'iconsax-react-native/src';
import Separator from '../others/separator';
import Pressable from '../base/pressable';
import { CommonActions, DrawerActions } from '@react-navigation/native';
import useAuthContext from '@/contexts/auth/auth-context';
import SelectDropdown, { DropDownMethods } from '../inputs/select-dropdown';
import { colorAlternates } from 'src/constant/static-data';
import ScreenModal from '../modals/screen-modal';
import { ButtonVariant } from '../buttons/button';
import Shimmer from '../shimmer';
import CustomImage from '../others/custom-image';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import { useApi } from 'src/hooks/use-api';
import { UPDATE_STORE_MAINTENANCE_MODE } from 'catlog-shared';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Intercom from '@intercom/intercom-react-native';

export enum DrawerItemsContent {
  MAIN_NAVIGATION = 'MainNavigation',
  STORE_SETTINGS = 'StoreSettingsStack',
  MY_PROFILE = 'UserProfile',
  MANAGE_SUBSCRIPTION = 'ManageSubscription',
  STORE_MANAGERS = 'StoreManagers',
  CUSTOMERS = 'Customers',
  INVOICES = 'InvoicesStack',
  AFFILIATES = 'AffiliateStack',
  DELIVERIES = 'DeliveriesStack',
  GET_HELP = 'GetHelp',
}

interface CustomDrawerProps extends DrawerContentComponentProps {
  showRoutes?: boolean;
}

const CustomDrawer = (props: CustomDrawerProps) => {
  const { logout, switchStore, updateStore, isSwitchingStore, store, storeLink, stores } = useAuthContext();
  const { state, descriptors, navigation, showRoutes = false } = props;

  const filteredStores = useMemo(() => stores?.filter(s => s.id !== store?.id), [stores, store?.id]);
  const dropDownRef = useRef<DropDownMethods>(null);
  const insets = useSafeAreaInsets();

  const showSwitchStore = Boolean(filteredStores?.length > 0 && store?.subscription && showRoutes);

  const updateMaintenanceModeReq = useApi({
    apiFunction: UPDATE_STORE_MAINTENANCE_MODE,
    key: UPDATE_STORE_MAINTENANCE_MODE.name,
    method: 'PUT',
  });

  const updateStoreMaintenanceMode = async (state: boolean) => {
    showLoader('Updating store...');

    const [res, err] = await updateMaintenanceModeReq.makeRequest({ id: store.id, state });
    if (res) {
      updateStore({ maintenance_mode: state });
      showSuccess('Store updated successfully');
    }
  };

  const handleLogout = async () => {
    navigation.closeDrawer();
    await delay(500);
    logout();
    AsyncStorage.clear();
  };

  const onPress = (route: { key: string; name: string }, isFocused: boolean) => {
    const event = navigation.emit({ type: 'drawerItemPress', target: route.key, canPreventDefault: true });

    if (!event.defaultPrevented) {
      navigation.dispatch({
        ...(isFocused ? DrawerActions.closeDrawer() : CommonActions.navigate({ name: route.name, merge: true })),
        target: state.key,
      });
    }
  };

  const handleSwitchStore = async (id: string) => {
    dropDownRef?.current?.close();
    navigation.closeDrawer();
    await delay(500);
    switchStore(id);
  };

  return (
    <View className="flex-1 bg-white">
      <DrawerContentScrollView {...props} contentContainerStyle={{ marginHorizontal: 20 }}>
        {!store && (
          <View className={'flex-1 mb-10 mt-10'}>
            <Row>
              <Shimmer height={hp(50)} width={hp(50)} borderRadius={hp(50)}></Shimmer>
              <Shimmer height={hp(25)} width={hp(70)} borderRadius={hp(30)}></Shimmer>
            </Row>

            <BaseText fontSize={16} type={'heading'} weight={'bold'} classes={'mt-10 text-black-muted'}>
              Finish Your Setup
            </BaseText>
            {/* <Shimmer height={hp(25)} width={hp(150)} borderRadius={hp(30)} marginTop={hp(20)}></Shimmer> */}
            <Separator className={'mx-0 mt-15 mb-5'} />
          </View>
        )}
        {store && (
          <View className={'flex-1 mb-10'}>
            <Row>
              {/* <CustomerInitial classes={'w-50 h-50'} initial={store?.name.charAt(0)} textProps={{ fontSize: 24 }} /> */}
              {store?.logo ? (
                <CustomImage imageProps={{ source: { uri: store.logo } }} className="w-50 h-50 rounded-full" />
              ) : (
                <CustomerInitial
                  classes="w-50 h-50"
                  initial={store?.name?.charAt(0) ?? ''}
                  textProps={{ fontSize: 24 }}
                />
              )}
              {
                showSwitchStore ? (
                  <ActionPill
                    onPress={() => dropDownRef?.current?.open()}
                    title={'Switch Store'}
                    addon={<ArrowDown2 size={wp(14)} color={colors.black.secondary} />}
                  />
                ) : null
                // <Shimmer height={hp(25)} width={hp(70)} borderRadius={hp(30)}></Shimmer>
              }
            </Row>
            <BaseText
              fontSize={18}
              type={'heading'}
              weight={'bold'}
              classes={showRoutes ? 'mt-10 text-black-secondary' : 'mt-10 text-black-muted'}>
              {store?.name}
            </BaseText>
            {
              showRoutes ? (
                <Pressable onPress={() => copyToClipboard(storeLink)}>
                  <Row className={'flex-1 justify-start pr-10 mt-2'}>
                    <View>
                      <BaseText weight={'medium'} classes="text-black-muted max-w-[220px]" numberOfLines={1}>
                        {storeLink}
                      </BaseText>
                    </View>
                    <CircledIcon className={'bg-grey-bgOne rounded-[8px] p-5 ml-8'}>
                      <Copy size={wp(12)} color={colors?.primary.main} strokeWidth={1.5} />
                    </CircledIcon>
                  </Row>
                </Pressable>
              ) : null
              // <Shimmer height={hp(12)} width={hp(150)} borderRadius={hp(30)} marginTop={wp(10)}></Shimmer>
            }
            {showRoutes && (
              <>
                <Separator className={'mx-0 mt-15'} />
                <Row className="py-5">
                  <BaseText weight={'medium'} classes="text-grey-muted">
                    Maintenance Mode
                  </BaseText>
                  <CustomSwitch value={store.maintenance_mode} onValueChange={updateStoreMaintenanceMode} />
                </Row>
              </>
            )}
            <Separator className={'mx-0  mb-5'} />
          </View>
        )}

        {/* <DrawerItemList {...props} /> */}
        {showRoutes &&
          state.routes.map((route, index) => {
            const label = route.name as DrawerItemsContent;
            const tabConfig = drawerItems[label];
            const isFocused = state.index === index;

            return (
              <View key={label}>
                {label !== DrawerItemsContent.MAIN_NAVIGATION && (
                  <View>
                    {(label === DrawerItemsContent.CUSTOMERS || label === DrawerItemsContent.GET_HELP) && (
                      <Separator className={'mx-0 my-5'} />
                    )}
                    <Pressable className={'my-10'} activeOpacity={0.95} onPress={() => onPress(route, isFocused)}>
                      <Row className={'justify-start'}>
                        {tabConfig?.icon()}
                        <BaseText fontSize={14} classes="ml-8">
                          {tabConfig?.label ? tabConfig?.label : label}
                        </BaseText>
                      </Row>
                    </Pressable>
                  </View>
                )}
              </View>
            );
          })}
        {/* {!showRoutes &&
          [1, 2, 3, 4].map((item, index) => (
            <Row spread={false} key={index} classes="py-10">
              <Shimmer height={hp(25)} width={wp(25)} classes="rounded-full" />
              <Shimmer height={hp(20)} width={wp(130 + (index % 2) * 30)} classes="rounded-full ml-10" />
            </Row>
          ))} */}
      </DrawerContentScrollView>
      <View className={'mx-20 pb-10'} style={{ marginBottom: insets.bottom }}>
        {!showRoutes && (
          <>
            <Pressable className={'my-10'} activeOpacity={0.95} onPress={() => Intercom.present()}>
              <Row className={'justify-start'}>
                <CircledIcon className={'bg-grey-bgOne p-7'}>
                  <Messages3 size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
                </CircledIcon>
                <BaseText fontSize={14} classes={'ml-8 text-black-secondary'}>
                  Got Issues? Talk to Us
                </BaseText>
              </Row>
            </Pressable>
            <Separator className={'mx-0 my-5'} />
          </>
        )}
        <Pressable className={'my-10'} activeOpacity={0.95} onPress={handleLogout}>
          <Row className={'justify-start'}>
            <CircledIcon className={'bg-accentRed-pastel p-7'}>
              <TransmitSqaure2 size={wp(20)} variant={'Bold'} color={colors.accentRed.main} />
            </CircledIcon>
            <BaseText fontSize={14} classes={'ml-8 text-accentRed-main'}>
              Logout
            </BaseText>
          </Row>
        </Pressable>
      </View>
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        onPressItem={handleSwitchStore}
        showLabel
        label={'Switch Store'}
        descriptionProps={{}}
        closeAfterSelection={false}
        showButton
        buttons={[
          {
            text: 'Create New Store',
            variant: ButtonVariant.PRIMARY,
            onPress: () => {
              dropDownRef?.current?.close();
              navigation.navigate('CreateStore');
            },
          },
        ]}
        items={
          filteredStores?.map((s, idx) => ({
            label: s.name,
            value: s.id,
            leftElement: (
              <CircledIcon
                className="h-40 w-40"
                style={{ backgroundColor: colorAlternates[idx ? idx % colorAlternates.length : 0].iconColor }}>
                <BaseText fontSize={15} className="font-fhOscarBold capt text-white">
                  {s.name[0]}
                </BaseText>
              </CircledIcon>
            ),
          })) ?? []
        }
      />
      <ScreenModal show={isSwitchingStore} toggleModal={() => null}>
        <View className="min-h-[25%] w-full rounded-xl bg-transparent justify-center items-center">
          <ActivityIndicator className="text-white" size="small" animating />
          <BaseText classes="mt-10 text-white">{'Switching Store...'}</BaseText>
        </View>
      </ScreenModal>
    </View>
  );
};

const drawerItems = {
  [DrawerItemsContent.MAIN_NAVIGATION]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Setting2 size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Store Settings',
  },
  [DrawerItemsContent.STORE_SETTINGS]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Setting2 size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Store Settings',
  },
  [DrawerItemsContent.MY_PROFILE]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Profile size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'My Profile',
  },
  [DrawerItemsContent.MANAGE_SUBSCRIPTION]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <CardTick size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Manage Subscription',
  },
  [DrawerItemsContent.STORE_MANAGERS]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Profile2User size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Store Manager',
  },
  [DrawerItemsContent.CUSTOMERS]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <TagUser size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Customers',
  },
  [DrawerItemsContent.INVOICES]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Receipt1 size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Invoices',
  },
  [DrawerItemsContent.AFFILIATES]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <UserOctagon size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Affiliates',
  },
  [DrawerItemsContent.DELIVERIES]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Box size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Deliveries',
  },
  [DrawerItemsContent.GET_HELP]: {
    icon: () => (
      <CircledIcon className={'bg-grey-bgOne p-5'}>
        <Messages3 size={wp(20)} variant={'Bold'} color={colors.grey.muted} />
      </CircledIcon>
    ),
    label: 'Get Help',
  },
};

export default CustomDrawer;
