import { ReactNode } from 'react';
import { ActivityIndicator, View } from 'react-native';
import useInteractionWait from 'src/hooks/use-interaction-wait';
import LoadingModal from '../others/loading-modal';
import Header, { HeaderProps } from './header';
import CustomImage from '../others/custom-image';

interface DashboardLayoutProps {
  children?: ReactNode;
  headerProps: HeaderProps;
  isLoading?: boolean;
  waitOnReady?: boolean;
}

const DashboardLayout = ({ children, isLoading, waitOnReady = true, headerProps }: DashboardLayoutProps) => {
  const { ready } = useInteractionWait();
  if ((!ready || isLoading) && waitOnReady) {
    return (
      <View className="flex-1 bg-white">
        <Header {...headerProps} />
        <View className="flex-1 items-center justify-center">
          <CustomImage imageProps={{source: require('@/assets/gif/loader.gif')}} className='w-40 h-40 rounded-8' />
        </View>
      </View>
    );
  }
  return (
    <View className="flex-1 bg-white">
      <Header {...headerProps} />
      {/* {isLoading ? <BaseText>Loading...</BaseText> : children} */}
      {children}
      {/* {isLoading && <LoadingModal isVisible={isLoading} />} */}
    </View>
  );
};

export default DashboardLayout;
