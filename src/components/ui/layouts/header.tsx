import { ArrowCircleLeft2, HambergerMenu, Notification } from 'iconsax-react-native/src';
import { GestureResponderEvent, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import BaseText from '../base/base-text';
import Row from '../row';
import { wp } from '@/assets/utils/js';
import Pressable from '../base/pressable';
import { LayoutChangeEvent } from 'react-native';
import { useApi } from 'src/hooks/use-api';
import { GET_USER_UNREAD_NOTIFICATIONS_COUNT } from 'catlog-shared';
import { memo } from 'react';
import useAuthContext from 'src/contexts/auth/auth-context';

export interface HeaderProps {
  headerBg: string;
  pageTitle: string;
  variant?: HeaderVariants;
  onBackPress?: () => void;
  showNotifications?: boolean;
  showDrawer?: boolean;
  addInsetTop?: boolean;
  showBackButton?: boolean;
  handleLayout?: (e: LayoutChangeEvent['nativeEvent']) => void;
}

export enum HeaderVariants {
  'ROOT_LEVEL' = 'rootLevel',
  'SUB_LEVEL' = 'subLevel',
}

const Header = ({
  headerBg,
  pageTitle,
  variant = HeaderVariants.SUB_LEVEL,
  onBackPress,
  handleLayout,
  showNotifications = true,
  showDrawer = true,
  addInsetTop = true,
  showBackButton = true,
}: HeaderProps) => {
  const top = useSafeAreaInsets().top;

  const navigation = useNavigation();

  const { unreadNotificationCount } = useAuthContext();

  const handleBackButton = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  const RootLevelInfo = () => (
    <View className="flex-1 mr-20" style={{ maxWidth: '60%' }}>
      <BaseText fontSize={20} className="flex-1" type="heading" numberOfLines={1} ellipsizeMode="tail">
        {pageTitle}
      </BaseText>
    </View>
  );

  const SubLevelInfo = () => (
    <View className={'flex-1 flex-row items-center justify-start'}>
      {showBackButton && (
        <Pressable className="rounded-full bg-white p-8 flex items-center justify-center" onPress={handleBackButton}>
          <ArrowCircleLeft2 variant="Bold" color={colors?.black?.main} size={wp(20)} />
        </Pressable>
      )}
      <View className={'flex flex-row ml-5 items-center py-5 px-10 justify-center rounded-3xl bg-white'}>
        <BaseText fontSize={14} weight={'bold'} type="heading">
          {pageTitle}
        </BaseText>
      </View>
    </View>
  );

  const screenInfo = { rootLevel: <RootLevelInfo />, subLevel: <SubLevelInfo /> };

  return (
    <View
      className={`flex-row justify-between items-center px-20 pb-16 border-b border-transparentWhite bg-white z-50 ${headerBg}`} //Todo: come back to transparentWhite
      style={{ paddingTop: addInsetTop ? top + 16 : 16 }}
      onLayout={e => handleLayout?.(e.nativeEvent)}>
      {screenInfo[variant]}
      <View>
        <Row>
          {showNotifications && (
            <Pressable
              className="p-10 rounded-full bg-white flex items-center justify-center"
              onPress={() => navigation.navigate('NotificationList')}>
              <Notification color={colors?.black?.main} size={wp(20)} />
              {unreadNotificationCount > 0 && (
                <View className="absolute bg-grey-border min-w-[15px] p-2 rounded-full justify-center items-center right-6 top-6">
                  <View className="bg-accentRed-main h-15 w-15 rounded-full justify-center items-center">
                    <View className="flex-1 items-center justify-center">
                      <Text
                        style={{ fontFamily: 'FHOscar-Bold', fontSize: wp(8), color: colors.white }}
                        allowFontScaling={false}
                        numberOfLines={1}>
                        {unreadNotificationCount
                          ? Number(unreadNotificationCount) > 9
                            ? '9+'
                            : unreadNotificationCount
                          : '0'}
                      </Text>
                    </View>
                  </View>
                </View>
              )}
            </Pressable>
          )}
          {showDrawer && (
            <TouchableOpacity
              className="p-10 ml-10 rounded-full bg-white flex items-center justify-center"
              onPress={() => (navigation as any).toggleDrawer()}>
              <HambergerMenu color={colors?.black?.main} size={wp(20)} />
            </TouchableOpacity>
          )}
        </Row>
      </View>
    </View>
  );
};

export default memo(Header);
