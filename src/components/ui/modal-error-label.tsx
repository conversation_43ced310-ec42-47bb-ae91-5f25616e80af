import React from 'react';
import { View } from 'react-native';
import { BaseText } from './base';
import classNames from 'node_modules/classnames';

interface Props {
  error: string;
  classes?: string;
}

const ModalErrorLabel = ({ error, classes }: Props) => {
  if (!error) return null;

  return (
    <View className={classNames('flex flex-row py-8 px-10 items-center justify-center bg-accentRed-pastel', classes)}>
      <BaseText fontSize={11} classes="text-accentRed-main">
        {error}
      </BaseText>
    </View>
  );
};

export default ModalErrorLabel;
