import { Notification } from 'iconsax-react-native/src';
import { TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode } from 'react';
import Row from '../ui/row';
import colors from '@/theme/colors';
import { cx, delay, showError, updateOrDeleteItemFromList, wp } from '@/assets/utils/js';
import { BaseText } from '../ui';
import CircledIcon from '../ui/circled-icon';
import CustomImage from '../ui/others/custom-image';
import { MARK_NOTIFICATION_AS_READ, NOTIFICATION_TYPE, NotificationInterface } from 'catlog-shared';
import dayjs from 'node_modules/dayjs';
import { useApi } from 'src/hooks/use-api';
import useNotificationHandler from 'src/hooks/use-notification-handler';
import useAuthContext from 'src/contexts/auth/auth-context';

export interface NotificationListCardProps {
  isLast?: boolean;
  content?: NotificationInterface;
  setNotifications?: React.Dispatch<React.SetStateAction<NotificationInterface[]>>;
}

const SquareIcon = ({ children, bg }: { children: ReactNode; bg: string }) => {
  return <View className={cx('rounded-10 p-5', bg)}>{children}</View>;
};

const NotificationListCard = ({ content, isLast, setNotifications }: NotificationListCardProps) => {
  const navigation = useNavigation();

  const { setUnreadNotificationCount, unreadNotificationCount } = useAuthContext();
  const { handleNotificationNavigation } = useNotificationHandler();

  const iconElementKeys = Object.entries(iconElement).map(item => item[0]);

  const markAsReadReq = useApi({
    apiFunction: MARK_NOTIFICATION_AS_READ,
    method: 'PATCH',
    key: MARK_NOTIFICATION_AS_READ.name,
  });

  const onCLickNotification = async () => {
    handleNotificationNavigation({ type: content?.type, id: content?.data?.id }, false);
    // await delay(700)
    if (!content?.read) {
      const [res, err] = await markAsReadReq.makeRequest({ notificationId: content.id });
      if (res) {
        setNotifications(prev => updateOrDeleteItemFromList(prev, 'id', content.id, { ...content, read: true }));
        setUnreadNotificationCount(unreadNotificationCount - 1);
      }
    }
  };

  return (
    <TouchableOpacity
      className={cx(
        `flex-row items-center py-15 pl-15 pr-7`,
        { 'border-b border-b-grey-border': !isLast },
        { 'bg-[#F5F5F5]': !content?.read },
        { 'bg-[#FAFAFA]': content?.read },
      )}
      activeOpacity={0.8}
      onPress={onCLickNotification}>
      <Row className="items-start">
        <View>
          {iconElementKeys.includes(content?.type) ? (
            iconElement[content?.type]
          ) : (
            <CircledIcon className="rounded-8 p-5">
              <Notification variant="Bold" color={colors.accentGreen.main} size={wp(30)} />
            </CircledIcon>
          )}
          {!content?.read && (
            <View className="absolute bg-[#F2F2F2] p-3 rounded-full justify-center items-center -right-5 -top-5">
              <View className="bg-accentRed-main w-10 h-10 rounded-full" />
            </View>
          )}
        </View>
        <View className="flex-1 ml-12">
          <Row className="mr-12 flex-1">
            <View className="flex-1">
              <BaseText
                type="heading"
                fontSize={14}
                numberOfLines={1}
                classes={!content?.read ? 'text-black-secondary' : 'text-black-muted'}>
                {content?.title}
              </BaseText>
            </View>
          </Row>
          <BaseText lineHeight={18.2} fontSize={13} classes={'text-black-muted mr-7'}>
            {content?.message}
          </BaseText>
          <Row className="items-center">
            {/* {!content?.read && <View className="h-3 w-3 bg-black-muted opacity-70 rounded-full" />} */}
            <BaseText fontSize={11} classes={'text-grey-muted mt-5'} style={{ textTransform: 'uppercase' }}>
              {dayjs(content?.created_at).format('hh:mm a')}
            </BaseText>
          </Row>
        </View>
      </Row>
    </TouchableOpacity>
  );
};

export default NotificationListCard;

const iconElement = {
  [NOTIFICATION_TYPE.NEW_ORDER]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/cart_2.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.PAYMENT_RECEIVED_ORDER]: (
    <SquareIcon bg="bg-accentYellow-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/catlog_coin.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.PAYMENT_RECEIVED_INVOICE]: (
    <SquareIcon bg="bg-accentGreen-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/invoice.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.PAYMENT_RECEIVED_BANK]: (
    <SquareIcon bg="bg-accentYellow-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/catlog_coin.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.KYC_APPROVED]: (
    <SquareIcon bg="bg-accentGreen-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/user_profile.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.KYC_DECLINED]: (
    <SquareIcon bg="bg-accentGreen-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/user_profile.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.DELIVERY_STATUS]: (
    <SquareIcon bg="bg-accentOrange-pastel">
      <CustomImage imageProps={{ source: require('@/assets/images/truck.png') }} className="w-30 h-30 bg-transparent" />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.SUBSCRIPTION_EXPIRING]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/subscription.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.SUBSCRIPTION_CANCELLED]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage
        imageProps={{ source: require('@/assets/images/subscription.png') }}
        className="w-30 h-30 bg-transparent"
      />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.MILESTONE_ORDER]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage imageProps={{ source: require('@/assets/images/graph.png') }} className="w-30 h-30 bg-transparent" />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.MILESTONE_PAYMENT]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage imageProps={{ source: require('@/assets/images/graph.png') }} className="w-30 h-30 bg-transparent" />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.MILESTONE_SALES]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage imageProps={{ source: require('@/assets/images/graph.png') }} className="w-30 h-30 bg-transparent" />
    </SquareIcon>
  ),
  [NOTIFICATION_TYPE.MILESTONE_VISIT]: (
    <SquareIcon bg="bg-accentRed-pastel">
      <CustomImage imageProps={{ source: require('@/assets/images/graph.png') }} className="w-30 h-30 bg-transparent" />
    </SquareIcon>
  ),
};
