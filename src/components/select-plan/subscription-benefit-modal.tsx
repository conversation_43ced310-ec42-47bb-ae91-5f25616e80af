import { View } from 'react-native';
import colors from '@/theme/colors';
import { CheckActive } from '@/components/ui/icons';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import ListItemCard from '../ui/cards/list-item-card';
import CircledIcon from '../ui/circled-icon';
import { BaseText } from 'src/components/ui';

interface SubscriptionBenefitModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
}

const SubscriptionBenefitModal = ({ closeModal, ...props }: SubscriptionBenefitModalProps) => {
  return (
    <BottomModal size="md" snapPointIndex={1} {...props} showButton={false} closeModal={closeModal}>
      <View className="px-20 pb-40">
        <BaseText fontSize={16} className="font-fhOscarBold">
          Subscription Benefits
        </BaseText>
        <SectionContainer>
          {subscriptionBenefits.map((item, index) => (
            <ListItemCard
              key={item}
              className="py-10"
              leftElement={
                <CircledIcon iconBg="bg-accentGreen-pastel">
                  <CheckActive
                    width={wp(18)}
                    height={hp(18)}
                    primaryColor={colors.accentGreen.main}
                    secondaryColor={colors.white}
                  />
                </CircledIcon>
              }
              showBorder={index !== subscriptionBenefits.length - 1}
              title={item}
              titleProps={{ weight: 'light' }}
              spreadTitleContainer={false}
            />
          ))}
        </SectionContainer>
      </View>
    </BottomModal>
  );
};

export default SubscriptionBenefitModal;

const subscriptionBenefits = [
  'Fully functional online store',
  'SEO to help customers find you from Google',
  'Multiple payment methods for your customers',
  'Automated records of all your orders & customers',
  'Professional invoices & receipts generator',
  'Easy to run sales with discounts & coupons',
  'Free bank account [NG]',
];
