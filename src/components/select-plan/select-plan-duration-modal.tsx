import {
  Activity,
  BagHappy,
  Chart,
  Copy,
  ExportCircle,
  Moneys,
  Notification,
  TagUser,
  WalletMoney,
} from 'iconsax-react-native/src';
import { Dimensions, ImageBackground, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { ReactNode, useMemo } from 'react';
import Row from '../ui/row';
import { ArrowUp } from '@/components/ui/icons';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import ListItemCard, { ListCard } from '../ui/cards/list-item-card';
import Radio from '../ui/buttons/radio';

interface SelectPlanDurationModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  planName: string;
  selectedDuration: string;
  onDurationSelection: (duration: string) => void;
}

const SelectPlanDurationModal = ({
  planName,
  selectedDuration,
  onDurationSelection,
  closeModal,
  ...props
}: SelectPlanDurationModalProps) => {
  const navigation = useNavigation();

  return (
    <BottomModal closeModal={closeModal} buttonText={'Continue'} {...props}>
      <View className="px-20">
        <Text className="text-sm font-fhOscarBold">{planName}</Text>
        <SectionContainer className="mb-20">
          <ListItemCard
            title={'Monthly Plan'}
            showBorder
            description={'NGN 4, 000.00'}
            titleProps={{
              fontSize: 12,
              classes: 'text-black-muted font-interRegula',
            }}
            descriptionProps={{
              fontSize: 12,
              classes: 'text-black-secondary font-interSemiBold',
            }}
            rightElement={<Radio active={selectedDuration === 'monthly'} />}
            onPress={() => onDurationSelection('monthly')}
          />
          <ListItemCard
            title={'Quarterly Plan'}
            description={'NGN 1, 500.00'}
            titleProps={{
              fontSize: 12,
              classes: 'text-black-muted font-interRegula',
            }}
            descriptionProps={{
              fontSize: 12,
              classes: 'text-black-secondary font-interSemiBold',
            }}
            rightElement={<Radio active={selectedDuration === 'quarterly'} />}
            onPress={() => onDurationSelection('quarterly')}
          />
        </SectionContainer>
      </View>
    </BottomModal>
  );
};

export default SelectPlanDurationModal;
