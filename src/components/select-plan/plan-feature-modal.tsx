import { View } from 'react-native';
import colors from '@/theme/colors';
import { CheckActive } from '@/components/ui/icons';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import ListItemCard from '../ui/cards/list-item-card';
import CircledIcon from '../ui/circled-icon';
import { BaseText } from 'src/components/ui';

interface PlanFeatureModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  features: string[];
  planName: string;
}

const PlanFeatureModal = ({ closeModal, features, planName, ...props }: PlanFeatureModalProps) => {
  return (
    <BottomModal size="lg" snapPointIndex={1} {...props} showButton={false} closeModal={closeModal}>
      <View className="px-20 pb-40">
        <BaseText fontSize={16} className="font-fhOscarBold">
          {planName} Plan
        </BaseText>
        <SectionContainer>
          {features.map((item, index) => (
            <ListItemCard
              key={item}
              className="py-10"
              leftElement={
                <CircledIcon iconBg="bg-accentGreen-whiteTransparent">
                  <CheckActive
                    width={wp(18)}
                    height={hp(18)}
                    primaryColor={colors.accentGreen.main}
                    secondaryColor={colors.white}
                  />
                </CircledIcon>
              }
              showBorder={index !== features.length - 1}
              title={item}
              titleProps={{ weight: 'light' }}
              spreadTitleContainer={false}
            />
          ))}
        </SectionContainer>
      </View>
    </BottomModal>
  );
};

export default PlanFeatureModal;
