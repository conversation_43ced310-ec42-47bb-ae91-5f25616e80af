import React, { ReactNode } from 'react';
import { View, ViewProps } from 'react-native';
import cx from 'classnames';
import Pressable, { PressableProps } from '../ui/base/pressable';
import { styled } from 'nativewind';
import { BaseText } from '../ui';
import Row from '../ui/row';
import { toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { FOOD_COMPARISONS, Plan, PLAN_HOOKS, PLAN_TYPE, UPFRONT_SUBSCRIPTION_AMOUNTS } from 'catlog-shared';
import { GroupedPlan } from 'src/screens/setup/pick-plan';
import Button, { ButtonSize, ButtonVariant } from '../ui/buttons/button';
import { ArrowRight2, TickCircle, Verify } from 'node_modules/iconsax-react-native/src';
import Separator from '../ui/others/separator';

export interface PlanCardProps extends Partial<PressableProps> {
  children?: ReactNode;
  plan?: GroupedPlan;
  selected: boolean;
  greatChoice: boolean;
  onPressPlansFeatures: () => void;
  planDescription?: string;
  buttonText?: string;
  isUpfront?: boolean;
  isFreeTrial?: boolean;
  isSetup?: boolean;
  ctaDisabled?: boolean;
  isCurrentPlan?: boolean;
}

const PlanCard = ({
  greatChoice,
  plan,
  onPressPlansFeatures,
  selected,
  planDescription,
  buttonText,
  isSetup = true,
  isUpfront = false,
  isFreeTrial = false,
  ctaDisabled = false,
  isCurrentPlan = false,
  ...props
}: PlanCardProps) => {
  const currency = plan?.country?.currency;
  const amount = plan?.amount;
  const interval_text = plan?.interval_text;

  const pricingData = {
    amount: isUpfront ? UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][currency] / 100 : amount,
    interval_text: isUpfront ? 'For 30 Days' : interval_text,
  };

  return (
    <Pressable className={cx('p-15 pt-0 rounded-[15px] w-full bg-white border border-grey-border overflow-hidden')}>
      {isCurrentPlan && (
        <Row classes="justify-center items-center py-15 border-b border-b-grey-border">
          <Verify variant="Bold" size={wp(15)} color={colors.accentGreen.main} />
          <BaseText fontSize={12} weight="semiBold" classes={'leading-[20px] text-accentGreen-main ml-5'}>
            Current Plan
          </BaseText>
        </Row>
      )}
      <View className="pt-15">
        <Row classes="justify-start mb-10">
          <BaseText type={'heading'} fontSize={16}>
            {plan.name}
          </BaseText>
          {/* <View className="flex flex-row py-6 px-10 items-center justify-center rounded-40 bg-grey-bgOne">
            <BaseText weight={'semiBold'} classes={cx('text-black-placeholder')} fontSize={11}>
              7 DAYS FREE
            </BaseText>
          </View> */}
        </Row>
        <BaseText type={'heading'} fontSize={25} classes={'font-fhOscarBold leading-[40px]'}>
          {toCurrency(pricingData.amount, plan.country?.currency, false, 0)}
          <BaseText fontSize={12} weight="medium" classes={'leading-[20px] text-black-placeholder'}>
            {' '}
            / {pricingData.interval_text}
          </BaseText>
        </BaseText>
        {isUpfront && (
          <BaseText
            fontSize={16}
            weight="medium"
            classes={'leading-[20px] text-black-placeholder'}
            style={{
              textDecorationLine: 'line-through',
            }}>
            {toCurrency(plan.amount, plan.country?.currency, false, 0)}
          </BaseText>
        )}

        <View>
          <Separator className="mx-0 my-15" />
          <BaseText fontSize={12} classes={'text-black-muted'}>
            {PLAN_HOOKS[plan.type].hook} for the price of {plan.type === PLAN_TYPE.BASIC ? '1' : '2'}{' '}
            {FOOD_COMPARISONS[currency].fooditem}
            {plan.type === PLAN_TYPE.BASIC ? '' : 's'} {FOOD_COMPARISONS[currency].emoji}
          </BaseText>
        </View>

        <Pressable onPress={onPressPlansFeatures}>
          <Row classes="justify-start py-10 mt-10">
            <BaseText fontSize={14} weight={'medium'} classes={'text-primary-main mr-2'}>
              What’s included
            </BaseText>
            <ArrowRight2 size={wp(15)} strokeWidth={2} color={colors.primary.main} />
          </Row>
        </Pressable>
        <Button
          text={buttonText ?? (isCurrentPlan ? 'Change Interval' : 'Select Plan')}
          className="mt-10"
          onPress={props?.onPress}
          disabled={ctaDisabled}
        />
      </View>
    </Pressable>
  );
};

export default styled(PlanCard);
