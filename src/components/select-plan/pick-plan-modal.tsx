import { useNavigation } from '@react-navigation/native';
import {
  COUNTRY_CURRENCY_MAP,
  CountryInterface,
  CREATE_FREE_SUBSCRIPTION,
  CURRENCIES,
  PAYMENT_TYPES,
  Plan,
  PlanOption,
  UPFRONT_SUBSCRIPTION_AMOUNTS,
} from 'catlog-shared';
import { InfoCircle, Tag2 } from 'iconsax-react-native/src';
import { useEffect, useMemo, useState } from 'react';
import { Text, View } from 'react-native';
import { delay, hideLoader, showLoader, showSuccess, toCurrency, toNaira, wp } from 'src/assets/utils/js';
import PaymentsWidget from 'src/components/payments/payments-widget';
import { BaseText } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import Radio from 'src/components/ui/buttons/radio';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import CircledIcon from 'src/components/ui/circled-icon';
import Container from 'src/components/ui/container';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import SectionContainer from 'src/components/ui/section-container';
import { useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import useSteps from 'src/hooks/use-steps';
import { GroupedPlan } from 'src/screens/setup/pick-plan';
import colors from 'src/theme/colors';
import Row from '../ui/row';
import Toast from 'node_modules/react-native-toast-message/lib';
import useAuthContext from 'src/contexts/auth/auth-context';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  plan: GroupedPlan;
  plans: Plan[];
  isSetup?: boolean;
  onComplete: VoidFunction;
  payUpfront: boolean;
  freeTrialInterval: number;
  currentPlanOption?: PlanOption;
}

const PickPlanModal: React.FC<Props> = ({
  closeModal,
  onComplete,
  payUpfront,
  plan,
  plans,
  isSetup = true,
  freeTrialInterval,
  currentPlanOption,
  ...props
}) => {
  // const [payUpfront, setPayUpfront] = useState(true);
  const { decideNextRoute, refetchSession, setIsDroppedOffUser } = useAuthContext();
  const { step, next, changeStep } = useSteps(['interval', 'confirm', 'error'], 0);
  const { modals, toggleModal } = useModals(['payment', 'planPreference']);
  const [selectedOption, setSelectedOption] = useState<PlanOption>(currentPlanOption ?? null);
  const [selectedPlan, setSelectedPlan] = useState<Plan>(null);
  const navigation = useNavigation();
  const createFreeSubRequest = useApi({
    apiFunction: CREATE_FREE_SUBSCRIPTION,
    key: CREATE_FREE_SUBSCRIPTION.name,
    method: 'POST',
  });

  useEffect(() => {
    setSelectedOption(currentPlanOption ?? null);
  }, [currentPlanOption]);

  const selectedPlanOption = useMemo(() => {
    return plans.find(p => selectedPlan?.id === p.id && selectedOption?.id === p.plan_option_id);
  }, [selectedPlan, selectedOption]);

  async function subscribe(plan: string, plan_option: string) {
    console.log({ payUpfront });
    if (isSetup && !payUpfront) {
      console.log('{ plan, plan_option }: ', { plan, plan_option });
      showLoader('Subscribing to Plan...');
      const [res, err] = await createFreeSubRequest.makeRequest({ plan, plan_option });
      hideLoader();

      if (res) {
        closeModal();
        showLoader('Updating your profile...');
        await refetchSession();
        setIsDroppedOffUser(false);

        console.log({ isSetup });

        if (isSetup) {
          const { nextRoute } = await decideNextRoute({ skipProgress: true, fromSetupComplete: true });
          hideLoader();
          console.log({ nextRoute });
          navigation.navigate(nextRoute as any);

          //most times navigation won't be triggered here
          //because it's the last step of any setup process
          // the app-navigation handles the navigation to the dashboard when userHasSetup is set to true
          // but this takes sometimes so we have to fake a loader with delay
        }

        hideLoader();
      } else {
        Toast.show({ type: 'error', text1: err?.message ?? 'Something went wrong' });
      }

      return;
    }

    //since we're only getting the plan id
    //search the actual list of plans for the plan - so the subscribe modal can use it.
    const actualPlan = plans.find(p => plan === p.id && plan_option === p.plan_option_id);

    if (actualPlan) {
      const updatedPlan = {
        ...actualPlan,
        amount:
          payUpfront && selectedPlanOption && isSetup
            ? toNaira(
                UPFRONT_SUBSCRIPTION_AMOUNTS[actualPlan.type][
                  (selectedPlanOption?.country as CountryInterface).currency
                ],
              )
            : actualPlan.amount,
      };
      console.log('updatedPlan: ', JSON.stringify(updatedPlan));
      await delay(700);
      setSelectedPlan(updatedPlan);
    }

    toggleModal('payment');
    return;
  }

  const handleButton = () => {
    switch (step) {
      case 'interval':
        setSelectedPlan(plans.find(p => p.id === plan.id && p.plan_option_id === selectedOption.id));
        if (isSetup) {
          subscribe(plans.find(p => p.id === plan.id && p.plan_option_id === selectedOption.id).id, selectedOption.id);
          return;
        }
        // else
        toggleModal('payment');

        break;
      case 'confirm':
        subscribe(selectedPlan.id, selectedOption.id);
        break;
      case 'error':
        subscribe(selectedPlan.id, selectedOption.id);
        break;
      default:
        break;
    }
  };

  const handlePaymentSuccess = async (ref: string) => {
    onComplete();
  };

  const optionsArr = Object.values(plan.options);
  const stepBtnText = { confirm: 'Yes Subscribe', error: 'Retry' };

  const payUpfrontAmount = toCurrency(
    UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][plan?.country?.currency] / 100,
    plan?.country?.currency,
  );

  return (
    <BottomModal
      closeModal={closeModal}
      enableDynamicSizing
      title={`${plan.name} Plan`}
      {...props}
      buttons={[
        ...(step === 'confirm'
          ? [
              {
                text: 'Back',
                onPress: () => {
                  changeStep('interval');
                },
                variant: ButtonVariant.LIGHT,
              },
            ]
          : []),
        {
          onPress: handleButton,
          isLoading: createFreeSubRequest.isLoading,
          text: createFreeSubRequest.isLoading ? 'Subscribing to Plan...' : (stepBtnText[step] ?? 'Continue'),
          disabled: (step === 'interval' ? !Boolean(selectedOption) : false) || createFreeSubRequest.isLoading,
        },
      ]}>
      <Container className="">
        {step === 'interval' && (
          <View className="flex-row items-center mt-10 py-10 px-12 bg-grey-bgOne rounded-12">
            <CircledIcon className="bg-white p-3">
              <InfoCircle variant="Bold" size={wp(14)} color={colors.accentYellow.main} />
            </CircledIcon>
            <View className="flex-1 ml-10">
              <BaseText fontSize={12} classes="text-black-secondary">
                {payUpfront ? (
                  <>
                    You'll only pay <Text className="font-interSemiBold">{payUpfrontAmount}</Text> today — your
                    selection will only take effect after 30 days
                  </>
                ) : (
                  <>
                    You won't be charged today — payment only required after your{' '}
                    <Text className="font-interSemiBold">{freeTrialInterval}-day</Text> free trial.
                  </>
                )}
              </BaseText>
            </View>
          </View>
        )}
        {step === 'interval' && (
          <>
            <SectionContainer className="">
              {optionsArr.map((p, i) => {
                return (
                  <ListItemCard
                    key={i}
                    title={p.interval_text}
                    showBorder={optionsArr.length !== i + 1}
                    alignStart={true}
                    titleProps={{ fontSize: 13, type: 'body', weight: 'light', classes: 'text-black-muted' }}
                    bottomElement={
                      <View className="mt-5">
                        <Row disableSpread>
                          <BaseText fontSize={15} type="heading" weight={'bold'} classes={'text-black-main'}>
                            {toCurrency(p.amount, plan?.country?.currency)}
                          </BaseText>
                          {p.discount ? (
                            <StatusPill
                              statusType={StatusType.SUCCESS}
                              className="py-[5px] self-start ml-5"
                              title={`${p.discount}% OFF`}
                            />
                          ) : null}
                        </Row>
                        {p.actual_amount && p.amount !== p.actual_amount && (
                          <BaseText
                            fontSize={12}
                            type="body"
                            weight={'medium'}
                            classes={'text-black-muted line-through'}>
                            {toCurrency(p.actual_amount, plan?.country?.currency)}
                          </BaseText>
                        )}
                      </View>
                    }
                    rightElement={<Radio active={selectedOption?.id === p.id} />}
                    onPress={() => setSelectedOption(p)}
                    spreadTitleContainer={false}
                  />
                );
              })}
            </SectionContainer>
          </>
        )}
        {step === 'confirm' && (
          <>
            <View className="items-center flex-col justify-center flex-1 mt-40">
              <CircledIcon className="w-[60px] h-[60px] bg-primary-main">
                <Tag2 size={30} color={colors.white} variant="Bold" />
              </CircledIcon>
              <BaseText fontSize={20} type="heading">
                Subscribe to {plan.name}
              </BaseText>
              <BaseText fontSize={20} type="heading">
                {selectedOption.interval_text} Plan
              </BaseText>
              <BaseText fontSize={14} className="text-black-placeholder mt-15 text-center" type="body">
                {/* {selectedOption.interval_text} */}
                You can try this plan free for <Text className="font-interSemiBold text-black-main">14 days</Text> after
                that you’ll be charged{' '}
                <Text className="font-interSemiBold text-black-main">
                  {toCurrency(selectedOption.amount, plan.country.currency)}
                </Text>{' '}
                every <Text className="font-interSemiBold text-black-main">{selectedOption.interval} days</Text>
              </BaseText>
            </View>
          </>
        )}

        {step === 'error' && (
          <>
            <View className="items-center flex-col justify-center flex-1 mt-40">
              <SuccessCheckmark />
              <BaseText fontSize={20} type="heading">
                Subscription Failed
              </BaseText>
              <BaseText fontSize={14} className="text-black-placeholder mt-15 text-center" type="body">
                Your subscription was not successful, please try again
              </BaseText>
            </View>
          </>
        )}
        <View className="h-40" />
      </Container>

      {(selectedPlan || (payUpfront && isSetup)) && (
        <PaymentsWidget
          data={{
            paymentType: PAYMENT_TYPES.SUBSCRIPTION,
            plan: {
              ...selectedPlan,
              amount:
                payUpfront && isSetup
                  ? toNaira(UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][plan?.country?.currency])
                  : selectedPlan.amount,
              currency: COUNTRY_CURRENCY_MAP[plan?.country?.code],
              country: plan?.country?.code,
            },
            upfrontSubscription: payUpfront && isSetup,
          }}
          onComplete={handlePaymentSuccess}
          show={modals.payment}
          toggle={() => toggleModal('payment', false)}
        />
      )}
    </BottomModal>
  );
};
export default PickPlanModal;
