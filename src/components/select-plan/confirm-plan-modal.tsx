import { Tag2 } from 'iconsax-react-native/src';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js/responsive-dimension';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, CircledIcon } from '../ui';

interface ConfirmPlanModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  planName: string;
}

const ConfirmPlanModal = ({ planName, closeModal, ...props }: ConfirmPlanModalProps) => {
  return (
    <BottomModal closeModal={closeModal} buttonText={'Continue'} {...props}>
      <View className="px-50">
        <CircledIcon iconBg={'bg-accentOrange-main'} className="self-center mt-15 p-15">
          <Tag2 variant={'Bold'} color={colors.white} size={wp(30)} />
        </CircledIcon>
        <BaseText fontSize={20} classes="text-center font-fhOscarBold mt-10">
          Are you sure you want to continue with the {planName} plan?
        </BaseText>
        <BaseText fontSize={14} classes="text-center mt-10 mb-40 text-black-muted">
          The basic plan is free for 14 days, so you wont have to pay for two weeks, we also won’t ask for your card.
        </BaseText>
      </View>
    </BottomModal>
  );
};

export default ConfirmPlanModal;
