import { GetItemsParams, ProductItemInterface, IDeliveryItem } from 'catlog-shared';
import cx from 'classnames';
import { Add, Edit2, Minus, Trash } from 'iconsax-react-native/src';
import { useRef, useState } from 'react';
import { View } from 'react-native';
import { ApiData } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';

import { SharedInitiateSectionsProps } from './initiate-delivery-form';

import { toCurrency, wp } from '@/assets/utils/js';
import SelectProductsModal from '@/components/products/storefront-products/select-products-modal';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import Button from '@/components/ui/buttons/button';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { ChevronDown } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import Accordion from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import CustomImage from '@/components/ui/others/custom-image';
import Separator from '@/components/ui/others/separator';
import colors from '@/theme/colors';
import SelectSpecificProductsModal from 'src/components/products/storefront-products/select-specific-product-modal';

interface DeliveryItemInformationProps extends SharedInitiateSectionsProps {
  getProductsRequest: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
  firstStepIsComplete: boolean;
}

const DeliveryItemInformation = ({
  getProductsRequest,
  onPressSave,
  accordionRef,
  isOpened,
  toggleStep,
  firstStepIsComplete,
  isCompleted,
  isLoading,
  form,
}: DeliveryItemInformationProps) => {
  const [selectedItemKey, setSelectedItemKey] = useState<number | null>(null);

  const { modals, toggleModal } = useModals(['selectProducts']);

  const dropDownRef = useRef<DropDownMethods>(null);

  const packageDimensionMapped = mock_presets?.map((item, index) => {
    return {
      value: String(item.value)!,
      subTitle: `${String(item.value)}KG`!,
      label: item.name ?? '',
      leftElement: <CustomImage className="h-40 w-40 rounded-8" imageProps={{ source: item.image }} />,
    };
  });

  const itemsErrors = Array.isArray(form.errors?.items) ? form.errors?.items : [];

  const handleDeleteItem = (index: number) => {
    const selectedItems = form.values?.items;
    selectedItems?.splice(index, 1);
    form.setFieldValue('items', selectedItems);
  };

  const selectProductItem = (id: string) => {
    const allProducts = getProductsRequest?.response?.data?.items;
    const selectedProducts = allProducts;
    return selectedProducts.find(item => item.id === id);
  };

  const handleSetProductSelection = (selectionIds: string[]) => {
    const selectedItems: IDeliveryItem[] = [];

    for (let index = 0; index < selectionIds.length; index++) {
      const id = selectionIds[index];
      const item = {
        id,
        name: selectProductItem(id)?.name!,
        description: selectProductItem(id)?.description!,
        unit_weight: 0,
        unit_amount: selectProductItem(id)?.price ?? 0,
        quantity: 1,
        image: selectProductItem(id)?.images[selectProductItem(id)?.thumbnail ?? 0]!,
      };
      selectedItems.push(item);
    }
    form.setFieldValue('items', selectedItems);
  };

  const toggleQuantity = (index: number, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = form.values.items;
    const selectedItem = form?.values?.items?.[index];
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        form.setFieldValue('items', selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          handleDeleteItem(index);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          form.setFieldValue('items', selectedItemsCopy);
        }
      }
    }
  };

  const updateWeight = (weight: number) => {
    const selectedItemsCopy = form.values.items;
    if (selectedItemKey === null) return;
    const selectedItem = form?.values?.items?.[selectedItemKey];
    if (selectedItem) {
      selectedItem.unit_weight = weight;
      form.setFieldValue('items', selectedItemsCopy);
    }
  };

  const submitIsEnabled = (() => {
    const nonEmptyItems =
      form?.values?.items?.filter(i => i.name !== '' && i.unit_amount !== 0 && i.unit_weight > 0) ?? [];

    return (
      nonEmptyItems.length > 0 && nonEmptyItems.every(i => i.name !== '' && i.unit_amount !== 0 && i.quantity !== 0)
    );
  })();

  const itemError = (index: number) => {
    if (form.touched?.items?.length === 0) return null;
    return getError(itemsErrors[index] as any);
  };

  return (
    <View>
      <Accordion
        useOutsideTrigger
        isOpened={isOpened}
        onPressAnchor={firstStepIsComplete ? toggleStep : () => {}}
        anchorElement={status => (
          <AccordionAnchor
            title="Items Information"
            isSaved={isCompleted && status === false}
            isError={!isCompleted && status === false}
            isOpened={status}
            showChevronIcon={firstStepIsComplete}
          />
        )}
        ref={accordionRef}
        initiallyOpened>
        {form.values.items?.length! < 1 && (
          <Pressable onPress={() => toggleModal('selectProducts')} className="mt-15">
            <Input
              editable={false}
              onPressIn={() => toggleModal('selectProducts')}
              label="Select Items"
              rightAccessory={
                <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                  <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
                </View>
              }
              containerClasses="py-0"
            />
          </Pressable>
        )}
        <View className="mt-15">
          {form?.values?.items?.map?.((item, index) => (
            <DeliveryItemCard
              key={item.id}
              item={item}
              onPressDelete={() => handleDeleteItem(index)}
              error={itemError(index)}
              onPressToggleQuality={action => toggleQuantity(index, action)}
              onPressChangeWeight={() => {
                setSelectedItemKey(index);
                dropDownRef.current?.open();
              }}
            />
          ))}
        </View>
        {form.values.items?.length! > 0 && (
          <WhiteCardBtn
            leftIcon={<Add size={wp(14)} color={colors.primary.main} />}
            onPress={() => toggleModal('selectProducts')}>
            Add Item
          </WhiteCardBtn>
        )}
        <Button
          className="self-end py-10"
          style={{ width: 'auto' }}
          text="Save"
          onPress={onPressSave}
          isLoading={isLoading}
          loadingText="Saving..."
          disabled={!submitIsEnabled}
        />
      </Accordion>

      <SelectSpecificProductsModal
        products={getProductsRequest?.response?.data?.items ?? []}
        isVisible={modals.selectProducts}
        closeModal={() => toggleModal('selectProducts', false)}
        getProductsRequest={getProductsRequest}
        loadingStates={{isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading}}
        // onProductSelection={handleProductSelection}
        selectedProducts={form.values?.items ? form.values?.items?.map(item => item?.id as string) : []}
        setSelectedProducts={handleSetProductSelection} //Todo: @silas look into this
        onPressContinue={() => toggleModal('selectProducts', false)}
      />
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem=""
        onPressItem={value => updateWeight(Number(value))}
        items={packageDimensionMapped ?? []}
        containerClasses="mb-15"
        label="Add Product Weight"
        genItemKeysFun={value => value.label}
        showButton
        buttons={[{ text: 'Proceed', onPress: () => dropDownRef.current?.close() }]}
        headerComponent={
          <Input
            keyboardType="number-pad"
            label="Enter weight"
            onChangeText={text => updateWeight(Number(text))}
            value={form.values?.items?.[selectedItemKey!]?.unit_weight}
          />
        }
        showLabel
      />
    </View>
  );
};

const DeliveryItemCard = ({
  item,
  onPressDelete,
  onPressToggleQuality,
  onPressChangeWeight,
  error,
  ...props
}: {
  item: IDeliveryItem;
  onPressDelete?: VoidFunction;
  onPressChangeWeight?: VoidFunction;
  error: string | null;
  onPressToggleQuality?: (action: 'decrease' | 'increase') => void;
}) => {
  return (
    <View className="mb-15">
      <View
        className={cx('px-15 pb-15 bg-grey-bgOne rounded-12 border border-grey-border', {
          'border-grey-border': !error,
          'border-accentRed-main': error,
        })}
        {...props}>
        <ListItemCard
          leftElement={
            <View className="rounded-[10px] h-40 w-40 overflow-hidden justify-end">
              <CustomImage className="w-full h-full" imageProps={{ source: { uri: item.image } }} />
            </View>
          }
          title={item.name}
          disabled
          titleProps={{ weight: 'semiBold', classes: 'text-black-muted' }}
          description={toCurrency(item.unit_amount)}
          descriptionProps={{ weight: 'bold', classes: 'text-black-main' }}
          rightElement={
            <Pressable onPress={onPressDelete}>
              <CircledIcon className="bg-white p-5">
                <Trash size={wp(18)} color={colors.accentRed.main} />
              </CircledIcon>
            </Pressable>
          }
        />
        <Separator className="mx-0 my-0 mb-15" />
        <Row>
          <WhiteCardBtn
            className="rounded-full"
            icon={<Edit2 size={wp(14)} color={colors.primary.main} />}
            onPress={onPressChangeWeight}>
            <BaseText fontSize={12} weight="medium">
              {item?.unit_weight ? `${item?.unit_weight} KG` : 'Add Weight'}{' '}
            </BaseText>
          </WhiteCardBtn>
          <Row className="justify-start gap-x-10">
            <Pressable
              className="p-5 border-grey-border border rounded-full bg-white"
              onPress={() => onPressToggleQuality?.('decrease')}>
              {item?.quantity === 1 ? (
                <Trash size={wp(16)} color={colors.accentRed.main} />
              ) : (
                <Minus size={wp(16)} color={colors.black.placeholder} />
              )}
            </Pressable>
            <View className="py-5 px-14 border-grey-border border rounded-[6px] bg-white">
              <BaseText fontSize={12} weight="semiBold">
                {item?.quantity}
              </BaseText>
            </View>
            <Pressable
              className="p-5 border-grey-border border rounded-full bg-white"
              onPress={() => onPressToggleQuality?.('increase')}>
              <Add size={wp(16)} color={colors.black.placeholder} />
            </Pressable>
          </Row>
        </Row>
      </View>
      {error && (
        <BaseText fontSize={11} classes="text-accentRed-main text-center mt-5">
          {error}
        </BaseText>
      )}
    </View>
  );
};

const getError = (errors: string | { [key: string]: string | string[] }) => {
  if (!errors) return null;

  if (typeof errors === 'string') {
    return errors;
  }

  const error = Object.values(errors)[0];
  return Array.isArray(error) ? error[0] : error;
};

const mock_presets = [
  {
    image: require('@/assets/images/item-weights/food.jpg'),
    name: 'Food Pack',
    value: 1,
    id: '16',
  },
  {
    image: require('@/assets/images/item-weights/shoe.jpg'),
    name: 'Shoe Box',
    value: 0.4,
    id: '1',
  },
  {
    image: require('@/assets/images/item-weights/dress.jpg'),
    name: 'Dress',
    value: 0.3,
    id: '2',
  },
  {
    image: require('@/assets/images/item-weights/laptop.jpg'),
    name: 'Laptop',
    value: 3,
    id: '3',
  },
  {
    image: require('@/assets/images/item-weights/phone.jpg'),
    name: 'Mobile Device',
    value: 0.75,
    id: '4',
  },
  {
    image: require('@/assets/images/item-weights/kitchen-utensils.jpg'),
    name: 'Kitchen Utensils',
    value: 0.8,
    id: '11',
  },
  {
    image: require('@/assets/images/item-weights/wine-bottle.jpeg'),
    name: 'Drinks',
    value: 1.2,
    id: '13',
  },
  {
    image: require('@/assets/images/item-weights/bag.jpg'),
    name: 'Hand Bag',
    value: 2.5,
    id: '14',
  },
  {
    image: require('@/assets/images/item-weights/gadget-accessories.jpg'),
    name: 'Mobile Accessories',
    value: 0.2,
    id: '12',
  },
  {
    image: require('@/assets/images/item-weights/book.jpg'),
    name: 'Book',
    value: 0.4,
    id: '5',
  },
  {
    image: require('@/assets/images/item-weights/television.jpg'),
    name: 'Television',
    value: 25,
    id: '6',
  },
  {
    image: require('@/assets/images/item-weights/fridge.jpg'),
    name: 'Fridge',
    value: 125,
    id: '7',
  },
  {
    image: require('@/assets/images/item-weights/beauty.jpg'),
    name: 'Beauty Products',
    value: 1,
    id: '8',
  },
  {
    image: require('@/assets/images/item-weights/chair.jpg'),
    name: 'Small Furniture',
    value: 40,
    id: '9',
  },
  {
    image: require('@/assets/images/item-weights/bed.jpg'),
    name: 'Large Furniture',
    value: 150,
    id: '10',
  },
];

export default DeliveryItemInformation;
