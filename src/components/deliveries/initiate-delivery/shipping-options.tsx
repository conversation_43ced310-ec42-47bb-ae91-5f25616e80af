import { LayoutAnimation, ScrollView, View } from 'react-native';
import colors from '@/theme/colors';
import Container from '@/components/ui/container';
import { hp, toCurrency, wp } from '@/assets/utils/js';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import Pressable from '@/components/ui/base/pressable';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { ApiData, ResponseWithPagination, useApi } from '@/hooks/use-api';
import useAuthContext from '@/contexts/auth/auth-context';
import * as Yup from 'yup';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { SharedStepProps, ShippingOptionsResponse } from 'src/screens/deliveries/initiate-delivery';
import { RouteSquare } from 'iconsax-react-native/src';
import cx from 'classnames';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CustomImage from '@/components/ui/others/custom-image';
import Radio from '@/components/ui/buttons/radio';
import Separator from '@/components/ui/others/separator';
import * as Animatable from 'react-native-animatable';
import InfoBadge from '@/components/store-settings/info-badge';
import { ProductItemInterface, StoreInterface, GetShippingRatesParams, IDeliveryCourier } from 'catlog-shared';

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

interface ShippingOptionsProps extends SharedStepProps {
  getShippingRatesRequest: ApiData<GetShippingRatesParams, ShippingOptionsResponse>;
  style?: any;
}

const ShippingOptions = ({ form, formSteps, getShippingRatesRequest }: ShippingOptionsProps) => {
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;
  const [selectedCourier, setSelectedCourier] = useState<string | null>(null);

  const handleToggle = (courier: IDeliveryCourier) => {
    setSelectedCourier(courier?.courier_id!);
    form.setFieldValue('courier', courier);
  };

  // const toggleStep = (s: string) => {
  //   LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  //   return step === s ? changeStep('EMPTY') : changeStep(s);
  // };

  const shippingOptions = getShippingRatesRequest?.response?.data?.couriers ?? [];

  return (
    <Animatable.View className="flex-1">
      <AvoidKeyboard>
        <ScrollView keyboardDismissMode={'interactive'} keyboardShouldPersistTaps={'handled'}>
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.ORANGE}
            iconElement={
              <CircledIcon className="bg-accentOrange-main p-15">
                <RouteSquare variant={'Bold'} color={colors.white} size={wp(30)} />
              </CircledIcon>
            }
            customElements={
              <BaseText fontSize={22} lineHeight={hp(27)} classes="mt-10 leading-[30px]" type="heading">
                Shipping Options
              </BaseText>
            }
          />
          <Container className={'mt-20'}>
            <Animatable.View animation={'fadeIn'}>
              <InfoBadge text={"Here's a summary of the information you provided"} />
            </Animatable.View>
            <View className="mt-10">
              {shippingOptions.map((item, index) => (
                <ShippingItemCard
                  key={item.courier_id}
                  item={item}
                  itemIndex={index}
                  onPress={() => {
                    handleToggle(item);
                  }}
                  selected={item.courier_id === selectedCourier}
                />
              ))}
            </View>
          </Container>
        </ScrollView>
      </AvoidKeyboard>
    </Animatable.View>
  );
};

const ShippingItemCard = ({
  item,
  itemIndex,
  selected,
  onPress,
}: {
  item: IDeliveryCourier;
  selected: boolean;
  itemIndex: number;
  onPress: VoidFunction;
}) => {
  return (
    <Animatable.View animation={'slideInUp'} easing={'ease-in-out'} duration={150 * (itemIndex + 1)}>
      <Pressable
        onPress={onPress}
        className={cx('px-15 mb-15 pb-15 bg-grey-bgOne rounded-12 border', {
          'border-primary-main': selected,
          'border-grey-border': !selected,
        })}>
        <ListItemCard
          leftElement={
            <View className="rounded-[10px] h-40 w-40 overflow-hidden justify-end">
              <CustomImage className="w-full h-full" imageProps={{ source: { uri: item?.courier_image } }} />
            </View>
          }
          disabled
          title={item?.courier_name ?? ''}
          titleProps={{ weight: 'semiBold' }}
          description={`Pickup: ${item.pickup_eta}\nDelivery: ${item.delivery_eta}`}
          descriptionProps={{ weight: 'regular' }}
          descriptionClasses="mt-5"
          rightElement={<Radio active={selected} />}
        />
        <Separator className="mx-0 my-0 mb-15" />
        <Row>
          <BaseText fontSize={12} classes="text-black-muted">
            Delivery Price
          </BaseText>
          <View className="flex-1">
            <BaseText weight="semiBold" classes="text-right mr-5">
              {toCurrency(item?.total, item.currency)}
            </BaseText>
          </View>
          {item.discount && (
            <View
              className={cx('flex flex-row py-3 px-6 items-center justify-center rounded-40', 'bg-accentGreen-main')}>
              <BaseText fontSize={8} weight={'semiBold'} classes="text-white">
                {`${item.discount.percentage}${item.discount.symbol} OFF`}
              </BaseText>
            </View>
          )}
        </Row>
      </Pressable>
    </Animatable.View>
  );
};

export default ShippingOptions;
