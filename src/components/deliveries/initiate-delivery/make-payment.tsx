import { <PERSON><PERSON>View } from 'react-native';
import colors from '@/theme/colors';
import Container from '@/components/ui/container';
import { hp, toCurrency, wp } from '@/assets/utils/js';
import React, { useEffect } from 'react';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { ResponseWithPagination } from '@/hooks/use-api';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { SharedStepProps } from 'src/screens/deliveries/initiate-delivery';
import {
  ArrowRight,
  CardTick,
  Coin,
  Coin1,
  MoneyTick,
  Note1,
  RouteSquare,
  Wallet,
  Wallet2,
} from 'iconsax-react-native/src';
import * as Animatable from 'react-native-animatable';
import InfoBadge from '@/components/store-settings/info-badge';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { IDelivery, PAYMENT_TYPES, ProductItemInterface, StoreInterface } from 'catlog-shared';
import PaymentsWidget, { PAYMENT_WIDGET_ADDONS } from 'src/components/payments/payments-widget';
import useModals from 'src/hooks/use-modals';

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

interface MakePaymentProps extends SharedStepProps {}

const MakePayment = ({ form }: MakePaymentProps) => {
  const { modals, toggleModal } = useModals(['payments']);
  const deliveryData = form.values;

  useEffect(() => {
    setTimeout(() => {
      toggleModal('payments');
    }, 500);
  }, [])

  const RightElement = () => (
    <CircledIcon className="bg-white p-8">
      <ArrowRight size={wp(15)} color={colors.primary.main} strokeWidth={wp(2)} />
    </CircledIcon>
  );

  return (
    <Animatable.View className="flex-1">
      <AvoidKeyboard>
        <ScrollView keyboardDismissMode={'interactive'} keyboardShouldPersistTaps={'handled'}>
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.GREEN}
            iconElement={
              <CircledIcon className="bg-accentGreen-main p-15">
                <CardTick variant={'Bold'} color={colors.white} size={wp(30)} />
              </CircledIcon>
            }
            customElements={
              <BaseText fontSize={22} lineHeight={hp(27)} classes="mt-10 leading-[30px]" type="heading">
                Pay
              </BaseText>
            }
          />
          <Container className={'mt-20'}>
            <InfoBadge text={'Choose any of the methods below to pay for this delivery'} />

            {/* <SectionContainer>
              {extraActions?.map((value, index) => (
                <ListItemCard
                  key={value.title}
                  showBorder={index !== extraActions.length - 1}
                  title={value.title}
                  description={value?.description ?? undefined}
                  titleProps={{ type: 'heading', fontSize: 15 }}
                  titleClasses="text-black-main"
                  onPress={() => {}}
                  // disabled={isMultiSelect ? isActive(value.value)! : false}
                  leftElement={value?.leftElement}
                  rightElement={value?.rightElement}
                />
              ))}
            </SectionContainer> */}
          </Container>
        </ScrollView>
      </AvoidKeyboard>

      <PaymentsWidget
        onComplete={d => console.log(d)}
        data={{
          delivery: deliveryData as unknown as IDelivery,
          addon: PAYMENT_WIDGET_ADDONS.DELIVERY,
          paymentType: PAYMENT_TYPES.DELIVERY,
          successMessage: 'Your payment for this delivery was successful. Click continue to get delivery info',
        }}
        show={modals.payments}
        toggle={() => toggleModal('payments')}
      />
    </Animatable.View>
  );
};

export default MakePayment;
