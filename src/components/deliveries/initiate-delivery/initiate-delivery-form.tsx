import { ProductItemInterface, StoreInterface, GET_ITEMS, GetItemsParams } from 'catlog-shared';
import { FormikProps } from 'formik';
import React, { useEffect, useState } from 'react';
import { LayoutAnimation, ScrollView } from 'react-native';
import usePreventFirstRun from 'src/hooks/use-prevent-first-run';
import { CreateDeliveryFormParams, SharedStepProps } from 'src/screens/deliveries/initiate-delivery';
import * as Yup from 'yup';

import AddressSection from '@/components/deliveries/initiate-delivery/address-section';
import DeliveryItemInformation from '@/components/deliveries/initiate-delivery/delivery-item-information';
import ExtraInfo from '@/components/deliveries/initiate-delivery/extra-info';
import { BaseText } from '@/components/ui';
import Container from '@/components/ui/container';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { AccordionMethod } from '@/components/ui/others/accordion';
import Separator from '@/components/ui/others/separator';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import useAuthContext from '@/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import {
  AddressValidationShema,
  DELIVERY_STEPS,
  ExtraInfoValidationSchema,
  ItemsValidationShema,
} from 'src/screens/deliveries/types';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import CustomImage from 'src/components/ui/others/custom-image';

export interface SharedInitiateSectionsProps {
  form: FormikProps<CreateDeliveryFormParams>;
  accordionRef?: React.RefObject<AccordionMethod>;
  onPressSave: VoidFunction;
  accordionPreOpeningAction?: VoidFunction;
  isOpened: boolean;
  isLoading?: boolean;
  isCompleted?: boolean;
  toggleStep: VoidFunction;
}

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

interface InitiateDeliveryFormProps extends SharedStepProps {
  firstStepIsComplete: boolean;
  setIsInitiateFormValid: any;
}

const InitiateDeliveryForm = ({
  form,
  formSteps,
  isLoading,
  setIsInitiateFormValid,
  firstStepIsComplete,
}: InitiateDeliveryFormProps) => {
  const { step, isActive, stepIndex, next, previous, canNext, canPrevious, steps, changeStep } = formSteps;

  const toggleStep = (s: string) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    return step === s ? changeStep('EMPTY') : changeStep(s);
  };

  const { store } = useAuthContext();
  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      apiFunction: GET_ITEMS,
      method: 'GET',
      key: 'fetch-custom-items',
    },
    {
      per_page: 9007199254740991,
      filter: { store: store?.id },
    },
  );
  const [stepsStatus, setStepsStatus] = useState({
    [DELIVERY_STEPS.ITEMS]: false,
    [DELIVERY_STEPS.PICKUP]: false,
    [DELIVERY_STEPS.DROP_OFF]: false,
    [DELIVERY_STEPS.EXTRA_INFO]: false,
  });

  useEffect(() => {
    computeStepStatuses(form, true);
  }, []);

  usePreventFirstRun(() => {
    computeStepStatuses(form);
  }, [form.values]);

  const computeStepStatuses = async (form: FormikProps<CreateDeliveryFormParams>, firstRun: boolean = false) => {
    const statusCopy = { ...stepsStatus };
    const errorStatuses = await validateSchema(form?.values);

    type StatusKey = keyof typeof stepsStatus;
    steps.forEach(value => (statusCopy[value as StatusKey] = errorStatuses[value as StatusKey]));
    setIsInitiateFormValid(Object.values(errorStatuses).every(v => v));

    setStepsStatus(statusCopy);

    //attempts to move user to the uncompleted step if they're continuing from a draft
    if (firstRun) {
      const keys = Object.keys(statusCopy);

      for (let index = 0; index < keys.length; index++) {
        const key = keys[index];

        if (!statusCopy[key as StatusKey]) {
          changeStep(key as DELIVERY_STEPS);
          break;
        }
      }
    }
  };

  return (
    <AvoidKeyboard>
      <BaseScrollView>
        <ScreenInfoHeader
          colorPalette={ColorPaletteType.ORANGE}
          iconElement={
            <CustomImage
              imageProps={{ source: require('@/assets/images/delivery.png'), contentFit: 'cover' }}
              className="w-80 h-80"
            />
          }
          customElements={
            <BaseText fontSize={22} classes="mt-10 leading-[30px]" type="heading">
              Initiate Delivery
            </BaseText>
          }
        />
        <Container className="mt-20">
          <DeliveryItemInformation
            form={form}
            firstStepIsComplete={firstStepIsComplete || Boolean(form.values.id)}
            toggleStep={() => toggleStep('ITEMS')}
            getProductsRequest={getProductsRequest}
            isOpened={isActive('ITEMS')}
            isLoading={isLoading}
            isCompleted={stepsStatus['ITEMS']}
            onPressSave={() => form.handleSubmit()}
          />
          {Boolean(form.values.id) && (
            <>
              <Separator className="mx-0 mb-0" />
              <AddressSection
                form={form}
                isPickup
                toggleStep={() => toggleStep('PICKUP')}
                isOpened={isActive('PICKUP')}
                isLoading={isLoading}
                isCompleted={stepsStatus['PICKUP']}
                onPressSave={() => form.handleSubmit()}
              />
              <Separator className="mx-0 mb-0" />
              <AddressSection
                form={form}
                isPickup={false}
                toggleStep={() => toggleStep('DROP_OFF')}
                isOpened={isActive('DROP_OFF')}
                isLoading={isLoading}
                isCompleted={stepsStatus['DROP_OFF']}
                onPressSave={() => form.handleSubmit()}
              />
              <Separator className="mx-0 mb-0" />
              <ExtraInfo
                form={form}
                toggleStep={() => toggleStep('EXTRA_INFO')}
                isOpened={isActive('EXTRA_INFO')}
                isLoading={isLoading}
                isCompleted={stepsStatus['EXTRA_INFO']}
                onPressSave={() => form.handleSubmit()}
              />
            </>
          )}
        </Container>
      </BaseScrollView>
    </AvoidKeyboard>
  );
};

interface ValidationResult {
  [DELIVERY_STEPS.ITEMS]: boolean;
  [DELIVERY_STEPS.PICKUP]: boolean;
  [DELIVERY_STEPS.DROP_OFF]: boolean;
  [DELIVERY_STEPS.EXTRA_INFO]: boolean;
  isValid: boolean;
}

const validateSection = async (schema: Yup.Schema, data: any): Promise<boolean> => {
  try {
    await schema.validate(data, { abortEarly: false });
    return true;
  } catch (error) {
    return false;
  }
};

const validateSchema = async (values: CreateDeliveryFormParams): Promise<ValidationResult> => {
  const extraInfoSchema = Yup.object().shape(ExtraInfoValidationSchema);

  const validations = await Promise.all([
    validateSection(ItemsValidationShema, values.items),
    validateSection(AddressValidationShema, values.pickup_address),
    validateSection(AddressValidationShema, values.dropoff_address),
    validateSection(extraInfoSchema, {
      package_category: values.package_category,
      package_dimensions: values.package_dimensions,
      pickup_date: values.pickup_date,
    }),
  ]);

  const result: ValidationResult = {
    isValid: validations.every(Boolean),
    [DELIVERY_STEPS.ITEMS]: validations[0],
    [DELIVERY_STEPS.PICKUP]: validations[1],
    [DELIVERY_STEPS.DROP_OFF]: validations[2],
    [DELIVERY_STEPS.EXTRA_INFO]: validations[3],
  };

  return result;
};

export default InitiateDeliveryForm;
