import { Dimensions, ScrollView, View } from 'react-native';
import colors from '@/theme/colors';
import Container from '@/components/ui/container';
import { hp, toCurrency, wp } from '@/assets/utils/js';
import React from 'react';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import AvoidKeyboard from '@/components/ui/layouts/avoid-keyboard';
import { ResponseWithPagination } from '@/hooks/use-api';
import ScreenInfoHeader, { ColorPaletteType } from '@/components/ui/screen-info-header';
import { CreateDeliveryFormParams, SharedStepProps } from 'src/screens/deliveries/initiate-delivery';
import { Box1, InfoCircle, Location, Note1, RouteSquare } from 'iconsax-react-native/src';
import cx from 'classnames';
import CustomImage from '@/components/ui/others/custom-image';
import * as Animatable from 'react-native-animatable';
import AccordionGroup from '@/components/ui/others/accordion/accordion-group';
import Accordion from '@/components/ui/others/accordion';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import InfoBadge from '@/components/store-settings/info-badge';
import dayjs from 'dayjs';
import {
  ProductItemInterface,
  StoreInterface,
  IDeliveryItem,
  DeliveryFormAddress,
  IDeliveryAddress,
  phoneObjectToString,
} from 'catlog-shared';

export interface ProductsResponse
  extends ResponseWithPagination<{ store: StoreInterface; items: ProductItemInterface[] }> {}

interface DeliverySummaryProps extends SharedStepProps {}

const DeliverySummary = ({ form }: DeliverySummaryProps) => {
  const deliveryData = form.values;

  const section = [
    {
      title: 'Items Information',
      iconElement: (
        <CircledIcon className="bg-accentYellow-pastel mr-10">
          <Box1 variant={'Bold'} size={wp(20)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      content: <ItemsSection items={deliveryData?.items ?? []} />,
    },
    {
      title: 'Pick-up Address',
      iconElement: (
        <CircledIcon className="bg-accentGreen-pastel mr-10">
          <Location variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
        </CircledIcon>
      ),
      content: <AddressSection address={deliveryData.pickup_address ?? ({} as DeliveryFormAddress)} />,
    },
    {
      title: 'Drop-off Address',
      iconElement: (
        <CircledIcon className="bg-accentOrange-pastel mr-10">
          <Location variant={'Bold'} size={wp(20)} color={colors.accentOrange.main} />
        </CircledIcon>
      ),
      content: <AddressSection address={deliveryData.dropoff_address ?? ({} as DeliveryFormAddress)} />,
    },
    {
      title: 'Extra Information',
      iconElement: (
        <CircledIcon className="bg-accentRed-pastel mr-10">
          <InfoCircle variant={'Bold'} size={wp(20)} color={colors.accentRed.main} />
        </CircledIcon>
      ),
      content: <ExtraInformationSection deliveryData={deliveryData} />,
    },
    {
      title: 'Courier Information',
      iconElement: (
        <CircledIcon className="bg-accentYellow-pastel mr-10">
          <RouteSquare variant={'Bold'} size={wp(20)} color={colors.accentYellow.main} />
        </CircledIcon>
      ),
      content: <CourierInformationSection courier={deliveryData?.courier} />,
    },
  ];

  return (
    <Animatable.View className="flex-1">
      <AvoidKeyboard>
        <ScrollView keyboardDismissMode={'interactive'} keyboardShouldPersistTaps={'handled'}>
          <ScreenInfoHeader
            colorPalette={ColorPaletteType.ORANGE}
            iconElement={
              <CircledIcon className="bg-accentOrange-main p-15">
                <Note1 variant={'Bold'} color={colors.white} size={wp(30)} />
              </CircledIcon>
            }
            customElements={
              <BaseText fontSize={22} lineHeight={hp(27)} classes="mt-10 leading-[30px]" type="heading">
                Delivery Summary
              </BaseText>
            }
          />
          <Container className={'mt-20'}>
            <InfoBadge text={"Here's a summary of the information you provided"} />
            <AccordionGroup>
              {section.map(item => (
                <Accordion
                  key={item.title}
                  anchorElement={status => (
                    <AccordionAnchor iconElement={item.iconElement} title={item.title} isOpened={status} />
                  )}
                  initiallyOpened>
                  {item?.content ?? (
                    <View>
                      <BaseText>Items Information</BaseText>
                    </View>
                  )}
                </Accordion>
              ))}
            </AccordionGroup>
          </Container>
        </ScrollView>
      </AvoidKeyboard>
    </Animatable.View>
  );
};

const ItemsSection = ({ items }: { items: IDeliveryItem[] }) => {
  const width = (Dimensions.get('screen').width - 95) / 2;
  return (
    <Row className="my-20 mx-15 flex-wrap" style={{ gap: wp(10) }}>
      {items.map((item, index) => (
        <Row key={index} style={{ gap: wp(10), width }}>
          <CustomImage className="w-25 h-25 rounded-5" imageProps={{ source: item.image }} />
          <View className="flex-1">
            <BaseText fontSize={12} classes="text-black-muted">
              {item.name}
            </BaseText>
          </View>
        </Row>
      ))}
    </Row>
  );
};

const AddressSection = ({ address }: { address: DeliveryFormAddress }) => {
  return (
    <View className="my-20 mx-15">
      <Row>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            NAME
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {address.name}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            PHONE
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {typeof address.phone === 'object' ? phoneObjectToString(address.phone) : address.phone}
          </BaseText>
        </View>
      </Row>
      <View className="flex-1 mt-15">
        <BaseText fontSize={10} classes="text-black-muted">
          ADDRESS
        </BaseText>
        <BaseText fontSize={12} weight="medium" classes="mt-5">
          {address?.address}
        </BaseText>
      </View>
    </View>
  );
};

const ExtraInformationSection = ({ deliveryData }: { deliveryData: CreateDeliveryFormParams }) => {
  return (
    <View className="my-20 mx-15">
      <Row>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            PACKAGE DIMENSION
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {deliveryData?.package_dimensions?.width}cm x {deliveryData?.package_dimensions?.length}cm x{' '}
            {deliveryData?.package_dimensions?.height}cm
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            DELIVERY DATE
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {dayjs(deliveryData.courier?.delivery_eta_time).format('ddd MMM D YYYY')}
          </BaseText>
        </View>
      </Row>
      {deliveryData.delivery_notes && (
        <View className="flex-1 mt-15">
          <BaseText fontSize={10} classes="text-black-muted">
            DELIVERY NOTE
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {deliveryData?.delivery_notes}
          </BaseText>
        </View>
      )}
    </View>
  );
};

const CourierInformationSection = ({ courier }: { courier: CreateDeliveryFormParams['courier'] }) => {
  return (
    <View className="my-20 mx-15">
      <Row className="items-start">
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            COURIER NAME
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {courier?.courier_name}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            Delivery Price
          </BaseText>

          <Row className="justify-start flex-1 mt-5">
            <BaseText fontSize={12} weight="medium">
              {toCurrency(courier?.total ?? 0, courier?.currency)}
            </BaseText>
            {courier?.discount && (
              <View
                className={cx(
                  'flex flex-row py-3 px-6 ml-5 items-center justify-center rounded-40',
                  'bg-accentGreen-main',
                )}>
                <BaseText fontSize={8} weight={'semiBold'} classes="text-white">
                  {`${courier.discount.percentage}${courier.discount.symbol} OFF`}
                </BaseText>
              </View>
            )}
          </Row>
        </View>
      </Row>
      <Row className="mt-15 items-start">
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            PICK UP
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {courier?.pickup_eta}
          </BaseText>
        </View>
        <View className="flex-1">
          <BaseText fontSize={10} classes="text-black-muted">
            Delivery
          </BaseText>
          <BaseText fontSize={12} weight="medium" classes="mt-5">
            {courier?.delivery_eta}
          </BaseText>
        </View>
      </Row>
    </View>
  );
};

export default DeliverySummary;
