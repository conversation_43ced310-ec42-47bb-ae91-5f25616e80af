import { getColorAlternates, getFieldvalues, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { ArrowRight, Search } from '@/components/ui/icons';
import colors from '@/theme/colors';
import Accordion from '@/components/ui/others/accordion';
import Input from '@/components/ui/inputs/input';
import { View } from 'react-native';
import useModals from 'src/hooks/use-modals';
import { Add, AddCircle, Location } from 'iconsax-react-native/src';
import Button from '@/components/ui/buttons/button';
import { ApiData, ResponseWithoutPagination, ResponseWithPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { useMemo, useRef, useState } from 'react';
import ListItemCard from '@/components/ui/cards/list-item-card';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import { colorAlternates } from 'src/constant/static-data';
import Separator from '@/components/ui/others/separator';
import AddAddressModal from '../add-address-modal';
import { SharedInitiateSectionsProps } from './initiate-delivery-form';
import {
  GET_STORE_ADDRESSES,
  GetStoreAddressesParams,
  phoneObjectFromString,
  DeliveryFormAddress,
  IDeliveryAddress,
  phoneObjectToString,
} from 'catlog-shared';

interface CustomerInformationSectionsProps extends SharedInitiateSectionsProps {
  isPickup: boolean;
}

const AddressSection = ({
  form,
  accordionRef,
  isPickup,
  isCompleted,
  toggleStep,
  isOpened,
  isLoading,
  onPressSave,
}: CustomerInformationSectionsProps) => {
  const [triggerRefetch, setTriggerRefetch] = useState(0);
  const fieldName = isPickup ? 'pickup_address' : 'dropoff_address';
  const addressTitle = isPickup ? 'Pick-up Address' : 'Drop-off Address';
  const addressValue = form.values[fieldName];
  const dropDownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['addAddressModal']);

  const getAddressesRequest = useApi<
    GetStoreAddressesParams & { triggerRefetch: number },
    ResponseWithoutPagination<IDeliveryAddress[]>
  >(
    {
      key: 'get-customers',
      apiFunction: GET_STORE_ADDRESSES,
      method: 'GET',
      onSuccess: response => {},
    },
    { include_customers: !isPickup, triggerRefetch },
  );

  const addressMapped = useMemo(
    () =>
      getAddressesRequest?.response?.data?.map((item, index) => {
        const color = getColorAlternates(index);

        return {
          value: item.id!,
          label: item.formatted_address ?? '',
          subTitle: item.phone,
          leftElement: (
            <CircledIcon style={{ backgroundColor: color.bgColor }}>
              <Location variant={'Bold'} color={color.iconColor} size={wp(16)} />
            </CircledIcon>
          ),
        };
      }),
    [getAddressesRequest?.response],
  );

  const openAddAddressModal = () => {
    setTriggerRefetch(Math.random() * 1000);
    dropDownRef.current?.close();
    setTimeout(() => {
      toggleModal('addAddressModal');
    }, 600);
  };

  const handleAddAddress = () => {
    toggleModal('addAddressModal', false);
    setTimeout(() => {
      dropDownRef.current?.open();
    }, 600);
  };

  const handleSelectAddress = (id: string) => {
    // form.handleBlur(fieldName);
    const addresses = getAddressesRequest?.response?.data ?? [];
    const selectedAddress = addresses.find(a => a.id === id);
    const { name, phone, email, formatted_address } = selectedAddress!;
    const addressItem = {
      id,
      address: formatted_address,
      name,
      phone: phoneObjectFromString(phone),
      email,
    };
    form.setFieldValue(fieldName, addressItem);
  };

  return (
    <View>
      <Accordion
        onPressAnchor={toggleStep}
        useOutsideTrigger
        isOpened={isOpened}
        anchorElement={status => (
          <AccordionAnchor
            title={addressTitle}
            isOpened={status}
            isSaved={status === false && isCompleted}
            isError={status === false && !isCompleted}
          />
        )}
        ref={accordionRef}>
        <View>
          {addressValue?.id && (
            <AddressCard address={addressValue} onPressChangeAddress={() => form.setFieldValue(fieldName, undefined)} />
          )}
          {!addressValue?.id && (
            <SelectDropdown
              ref={dropDownRef}
              // showAnchor={false}
              selectedItem={addressValue?.id}
              onPressItem={handleSelectAddress}
              showLabel
              error={getFieldvalues(`${fieldName}.name`, form).error}
              hasError={getFieldvalues(`${fieldName}.name`, form).hasError}
              isLoading={getAddressesRequest.isLoading}
              leftAccessory={
                addressValue?.id ? (
                  <CircledIcon className="bg-accentGreen-pastel">
                    <Location variant={'Bold'} color={colors.accentGreen.main} size={wp(16)} />
                  </CircledIcon>
                ) : undefined
              }
              label={'Select Address'}
              items={addressMapped}
              descriptionProps={{ classes: 'mt-5' }}
              containerClasses="my-15"
              headerComponent={
                <Input
                  // containerClasses="mt-10"
                  label={'Search Address'}
                  leftPadding={wp(35)}
                  leftAccessory={<Search size={wp(16)} primaryColor={colors?.black.muted} />}
                />
              }
              listAddOns={
                <ListItemCard
                  showBorder={false}
                  title={'Add Address'}
                  className="border-t border-t-grey-border"
                  titleProps={{ weight: 'medium' }}
                  onPress={openAddAddressModal}
                  leftElement={
                    <CircledIcon className="p-10 bg-white">
                      <AddCircle variant={'Bold'} size={wp(24)} color={colors.primary.main} />
                    </CircledIcon>
                  }
                  rightElement={
                    <CircledIcon className="p-10 bg-white">
                      <ArrowRight size={wp(16)} strokeWidth={1.32} currentColor={colors.primary.main} />
                    </CircledIcon>
                  }
                />
              }
            />
          )}
        </View>
        <Button
          className="self-end py-10"
          style={{ width: 'auto' }}
          text={'Save'}
          onPress={onPressSave}
          isLoading={isLoading}
          loadingText={'Saving...'}
          disabled={addressValue?.id === undefined}
        />
      </Accordion>
      <AddAddressModal
        isVisible={modals.addAddressModal}
        closeModal={() => toggleModal('addAddressModal', false)}
        // onPressButton={handleAddAddress}
        callBack={handleAddAddress}
        isPickup={isPickup}
      />
    </View>
  );
};

const AddressCard = ({
  address,
  onPressChangeAddress,
}: {
  address: DeliveryFormAddress;
  onPressChangeAddress: VoidFunction;
}) => {
  return (
    <View className="px-15 pb-15 mt-15 bg-grey-bgOne rounded-12 border border-grey-border">
      <ListItemCard
        leftElement={
          <CircledIcon className="bg-accentOrange-pastel">
            <Location variant={'Bold'} color={colors.accentOrange.main} size={wp(16)} />
          </CircledIcon>
        }
        title={address?.address!}
        titleProps={{ weight: 'medium' }}
        description={`${address.name} . ${typeof address.phone === 'string' ? address.phone : phoneObjectToString(address.phone)}`}
        descriptionProps={{ weight: 'regular' }}
        descriptionClasses="text-black-muted mt-5"
      />
      <Separator className="mx-0 my-0 mb-15" />
      <Row className="justify-end">
        <WhiteCardBtn onPress={onPressChangeAddress}>Change Address</WhiteCardBtn>
      </Row>
    </View>
  );
};

export default AddressSection;
