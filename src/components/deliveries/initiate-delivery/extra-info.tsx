import Accordion, { AccordionMethod } from '@/components/ui/others/accordion';
import { View } from 'react-native';
import Button from '@/components/ui/buttons/button';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import { useEffect, useMemo, useRef } from 'react';
import AccordionAnchor from '@/components/ui/others/accordion/accordion-anchor';
import { colorAlternates } from 'src/constant/static-data';
import CalendarModal from '@/components/ui/modals/calendar-modal';
import useModals from 'src/hooks/use-modals';
import Pressable from '@/components/ui/base/pressable';
import Input from '@/components/ui/inputs/input';
import colors from 'src/theme/colors';
import { formatDate, getColorAlternates, getFieldvalues, wp } from 'src/assets/utils/js';
import { Calendar } from 'iconsax-react-native/src';
import { SharedInitiateSectionsProps } from './initiate-delivery-form';
import { GET_PACKAGE_CATEGORIES, GET_PACKAGE_DIMENSIONS, CustomerInterface, IPackageDimension } from 'catlog-shared';

interface CustomerInformationSectionsProps extends SharedInitiateSectionsProps {}

const ExtraInfo = ({
  form,
  accordionRef,
  isOpened,
  isLoading,
  isCompleted,
  toggleStep,
  onPressSave,
}: CustomerInformationSectionsProps) => {
  const dropDownRef = useRef<DropDownMethods>(null);
  const { modals, toggleModal } = useModals(['calender']);
  const { pickup_date, package_dimensions, package_category, items } = form.values ?? {};

  const getPackageCategoriesRequest = useApi<
    void,
    ResponseWithoutPagination<{ category_id: string; category: string }[]>
  >({
    key: 'get-package-categories',
    apiFunction: GET_PACKAGE_CATEGORIES,
    method: 'GET',
    onSuccess: response => {},
  });

  const getPackageDimensionRequest = useApi<void, ResponseWithoutPagination<IPackageDimension[]>>({
    key: 'get-package-dimension',
    apiFunction: GET_PACKAGE_DIMENSIONS,
    method: 'GET',
    onSuccess: response => {},
  });

  const categoriesMapped = useMemo(
    () =>
      getPackageCategoriesRequest?.response?.data?.map((item, index) => {
      const color = getColorAlternates(index);

        return {
          value: String(item.category_id)!,
          label: item.category ?? '',
        };
      }),
    [getPackageCategoriesRequest?.response],
  );

  const categories =
    getPackageCategoriesRequest?.response?.data?.map(c => ({ value: c.category_id, text: c.category })) ?? [];

  const sortedDims = useMemo(() => {
    return ((getPackageDimensionRequest.response?.data ?? []) as IPackageDimension[]).sort(
      (a, b) => a.max_weight - b.max_weight, //todo: @silas take a look at this
    );
  }, [getPackageDimensionRequest.response]);

  useEffect(() => {
    if (items && items.length > 0) {
      const totalWeight = items.reduce((sum, item) => sum + item.unit_weight, 0);
      for (let i = 0; i < sortedDims.length; i++) {
        const { max_weight, height, width, length } = sortedDims[i];
        if (max_weight > totalWeight) {
          form.setFieldValue('package_dimensions', { length, width, height });
          break;
        }
      }
    }
  }, [items, sortedDims]);

  return (
    <View>
      <Accordion
        useOutsideTrigger
        isOpened={isOpened}
        onPressAnchor={toggleStep}
        anchorElement={status => (
          <AccordionAnchor
            title={'Extra Info'}
            isOpened={status}
            isSaved={!status && isCompleted}
            isError={!status && !isCompleted}
          />
        )}
        ref={accordionRef}>
        <View>
          <SelectDropdown
            ref={dropDownRef}
            // showAnchor={false}
            selectedItem={String(form.values.package_category)}
            onPressItem={v => {
              form.setFieldValue('package_category', v);
            }}
            showLabel
            // error={getFieldvalues(`${fieldName}.name`, form).error}
            // hasError={getFieldvalues(`${fieldName}.name`, form).hasError}
            isLoading={getPackageCategoriesRequest.isLoading}
            label={'Package Category'}
            items={categoriesMapped}
            descriptionProps={{ classes: 'mt-5' }}
            containerClasses="my-15"
          />
        </View>
        <Pressable onPress={() => toggleModal('calender')}>
          <Input
            label={'Pickup Date'}
            editable={false}
            value={form.values.pickup_date ? formatDate(form.values.pickup_date) : null}
            onPressIn={() => toggleModal('calender')}
            rightAccessory={
              <View className="border-grey-bgOne rounded-full">
                <Calendar size={wp(16)} color={colors.grey.muted} />
              </View>
            }
          />
        </Pressable>
        <Input
          label={'Delivery Instruction (Optional)'}
          multiline
          className="min-h-[80px]"
          containerClasses="my-15"
          {...getFieldvalues('delivery_notes', form)}
          value={form.values?.delivery_notes}
        />
        <Button
          className="self-end py-10"
          style={{ width: 'auto' }}
          text={'Save'}
          onPress={onPressSave}
          isLoading={isLoading}
          disabled={!Boolean(package_dimensions && pickup_date && package_category)}
        />
      </Accordion>
      <CalendarModal
        isVisible={modals.calender}
        closeModal={() => toggleModal('calender', false)}
        startDate={form.values.pickup_date}
        singleSelectMode
        onDateChange={(date, type) => {
          form.setFieldValue('pickup_date', date);
        }}
      />
    </View>
  );
};

export default ExtraInfo;
