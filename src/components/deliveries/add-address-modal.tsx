import * as Yup from 'yup';
import { ScrollView, View } from 'react-native';
import { useFormik } from 'formik';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { getFieldvalues } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import Toast from 'react-native-toast-message';
import { Fragment, useEffect, useState } from 'react';
import SelectCustomer from '../customer/select-customer';
import useKeyboard from 'src/hooks/use-keyboard';
import { BaseText, Row, SelectionPill } from '../ui';
import GooglePlaceInput from '../ui/inputs/google-place-input';
import Input from '../ui/inputs/input';
import CustomSwitch from '../ui/inputs/custom-switch';
import Separator from '../ui/others/separator';
import {
  CustomerInterface,
  phoneObjectFromString,
  VERIFY_AND_CREATE_ADDRESS,
  VerifyAndCreateAddressParams,
} from 'catlog-shared';

interface AddAddressModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  callBack?: (updateData?: CustomerInterface) => void;
  btnTitle?: string;
  isPickup?: boolean;
}

export interface AddressFormParams extends Omit<VerifyAndCreateAddressParams, 'phone'> {
  phone: { code: string; digits: string };
  selectedCustomer: CustomerInterface | undefined;
  id?: string;
}

enum CUSTOMER_TYPE {
  NEW,
  EXISTING,
}

const AddAddressModal = ({ closeModal, callBack, isPickup, btnTitle, ...props }: AddAddressModalProps) => {
  const [customerType, setCustomerType] = useState<CUSTOMER_TYPE>(CUSTOMER_TYPE.EXISTING);
  const isKeyboardVisible = useKeyboard();

  const addAddressRequest = useApi<VerifyAndCreateAddressParams>({
    apiFunction: VERIFY_AND_CREATE_ADDRESS,
    method: 'POST',
    key: 'create-address',
  });

  const form = useFormik<AddressFormParams>({
    initialValues: {
      phone: {
        digits: '',
        code: '',
      },
      name: '',
      email: '',
      address: '',
      customer: '',
      selectedCustomer: undefined,
      save_as_customer: isPickup ? false : true,
    },
    onSubmit: async values => {
      const phone = `${values.phone.code}-${values.phone.digits}`;

      const reqData = {
        name: values.name,
        phone,
        address: values.address,
        email: values.email,
        customer: values.customer,
        save_as_customer: values.save_as_customer,
      };

      const [response, error] = await addAddressRequest.makeRequest(reqData);
      if (response) {
        form.resetForm();
        callBack?.();
        Toast.show({ type: 'success', text1: 'Address added successfully' });
      }
      if (error) {
        Toast.show({ type: 'error', text1: error.body.body.message });
      }
    },
    validationSchema: addAddressValidationSchema,
    enableReinitialize: true,
  });

  const onSelectCustomer = (customer: CustomerInterface) => {
    form.setFieldValue('selectedCustomer', customer);
    form.setFieldValue('customer', customer.id);
    form.setFieldValue('email', customer.email);
    form.setFieldValue('name', customer.name);
    form.setFieldValue('phone', phoneObjectFromString(customer.phone));
  };

  const onPressCustomerType = (value: CUSTOMER_TYPE) => {
    if (value == CUSTOMER_TYPE.NEW) {
      form.setFieldValue('customer', '');
      form.setFieldValue('selectedCustomer', {});
      setCustomerType(value);
      return;
    }

    setCustomerType(value);
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Add Address'}
      showButton={!isKeyboardVisible}
      buttons={[
        {
          text: btnTitle ? btnTitle : 'Done',
          onPress: () => form.handleSubmit(),
          isLoading: addAddressRequest.isLoading,
        },
      ]}>
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <View className="mb-20 px-20">
          <GooglePlaceInput
            containerClasses="mt-15"
            onPressItem={placeDetail => form.setFieldValue('address', placeDetail.name)}
            {...getFieldvalues('address', form)}
          />
          <Separator className="mx-0" />
          {!isPickup && (
            <View className="mb-15">
              <Row className="justify-start">
                <SelectionPill
                  title="Existing Customer"
                  selected={customerType === CUSTOMER_TYPE.EXISTING}
                  onPress={() => onPressCustomerType(CUSTOMER_TYPE.EXISTING)}
                />
                <SelectionPill
                  title="New Customer"
                  selected={customerType === CUSTOMER_TYPE.NEW}
                  onPress={() => onPressCustomerType(CUSTOMER_TYPE.NEW)}
                />
              </Row>
              {customerType === CUSTOMER_TYPE.EXISTING && (
                <View className="mt-15">
                  <SelectCustomer
                    label="Select Customer"
                    onSelectCustomer={onSelectCustomer}
                    selectedCustomer={form.values.selectedCustomer.id}
                  />
                </View>
              )}
            </View>
          )}
          {isPickup || customerType === CUSTOMER_TYPE.NEW ? (
            <Fragment>
              <Input label={'Full name'} {...getFieldvalues('name', form)} />
              <PhoneNumberInput
                label={'Phone Number'}
                containerClasses="mt-15"
                {...getFieldvalues('phone', form)}
                onChange={value => form.setFieldValue('phone', value)}
              />
              <Input label={'Email Address'} containerClasses="mt-15" {...getFieldvalues('email', form)} />
            </Fragment>
          ) : null}
          {!isPickup && (
            <Row className="justify-start mt-15">
              <CustomSwitch
                value={form.values.save_as_customer}
                onValueChange={value => form.setFieldValue('save_as_customer', value)}
              />
              <BaseText classes="ml-5">Save as Customer</BaseText>
            </Row>
          )}
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export const addAddressValidationSchema = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default AddAddressModal;
