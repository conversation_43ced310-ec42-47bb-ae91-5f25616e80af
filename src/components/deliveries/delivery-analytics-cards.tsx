import { useNavigation } from '@react-navigation/native';
import { Box, BoxRemove, BoxTick, BoxTime, Money, MoneyRemove, MoneyTick, Receipt1 } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo } from 'react';
import { Dimensions, View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify } from '@/assets/utils/js/functions';
import { hp, wp } from '@/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import HomeAnalyticsSkeletonLoader from '../home/<USER>';
import AnalyticsCard from '../ui/cards/analytics-card';
import { DeliveryStat } from 'src/screens/deliveries/deliveries-analytics';
import cx from 'classnames';
import Shimmer from '../ui/shimmer';

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const dummyRow = new Array(2).fill(0);

interface DeliveryAnalyticsCardsProps {
  statistics: DeliveryStat;
  isLoading?: boolean;
}

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

const DeliveryAnalyticsCards = ({ isLoading = false, statistics }: DeliveryAnalyticsCardsProps) => {
  const analyticsCardsInfo: AnalyticsCardInfo[] = useMemo(
    () => [
      {
        title: 'Total Shipments',
        cardBg: 'bg-accentOrange-pastel',
        icon: <Box variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentOrange-main',
        value: statistics?.total_shipments ?? 0,
        change: 0,
      },
      {
        title: 'Processing',
        icon: <BoxTime variant="Bold" size={wp(18)} color={colors?.white} />,
        cardBg: 'bg-accentYellow-pastel',
        iconBg: 'bg-accentYellow-main',
        value: statistics?.ongoing_shipments ?? 0,
        change: 0,
      },
      {
        title: 'Fulfilled Shipments',
        cardBg: 'bg-accentGreen-pastel',
        icon: <BoxTick variant="Bold" size={wp(18)} color={colors?.white} />,
        iconBg: 'bg-accentGreen-main',
        value: statistics?.confirmed_shipments ?? 0,
        change: 0,
      },
      {
        title: 'Cancelled',
        cardBg: 'bg-accentRed-pastel',
        iconBg: 'bg-accentRed-main',
        icon: <BoxRemove variant="Bold" size={wp(18)} color={colors?.white} />,
        value: statistics?.cancelled_shipments ?? 0,
        change: 0,
      },
    ],
    [statistics],
  );

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  const loading = isLoading;

  return (
    <View className="border border-grey-border rounded-[15px]">
      {loading && <AnalyticsSkeletonLoader />}
      {!loading && (
        <>
          {splitCards.map((group, index) => (
            <Fragment key={index}>
              <View className="flex-row last:mb-0" key={index}>
                {group.map((info, index) => (
                  <Fragment key={index}>
                    <AnalyticsCard
                      className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                      title={info.title}
                      value={info.value}
                      iconBg={info.iconBg}
                      change={info.change}
                      showChange={false}
                      icon={info.icon}
                      addon={info.addon}
                      theme="white"
                    />
                    {index === 0 && <View className="w-1 bg-grey-border" />}
                  </Fragment>
                ))}
              </View>
              {index === 0 && <View className="h-1 bg-grey-border" />}
            </Fragment>
          ))}
        </>
      )}
    </View>
  );
};

export const AnalyticsSkeletonLoader: React.FC<{}> = () => {
  return (
    <View className="overflow-hidden rounded-[15px]">
      {dummyRow.map((_, index) => (
        <Fragment key={index}>
          <View className={cx('flex-row justify-between')} key={index}>
            <View className="flex-1 p-15">
              <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
              <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(90)} marginTop={hp(10)} />
              <Shimmer borderRadius={hp(40)} height={hp(15)} width={wp(30)} marginTop={hp(10)} />
            </View>
            <View className="w-1 bg-grey-border" />
            <View className="flex-1 p-15">
              <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
              <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(90)} marginTop={hp(10)} />
              <Shimmer borderRadius={hp(40)} height={hp(18)} width={wp(30)} marginTop={hp(10)} />
            </View>
          </View>
          {index === 0 && <View className="h-1 bg-grey-border" />}
        </Fragment>
      ))}
    </View>
  );
};

export default DeliveryAnalyticsCards;
