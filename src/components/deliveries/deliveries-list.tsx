import React from 'react';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import colors from '@/theme/colors';
import { copyToClipboard, openLinkInBrowser, wp } from '@/assets/utils/js';
import { useNavigation } from '@react-navigation/native';
import { Bag, CloseCircle, Copy, Edit2, ExportSquare, RepeatCircle, Trash } from 'iconsax-react-native/src';
import { Alert, Linking, RefreshControl, View } from 'react-native';
import EmptyState from '../ui/empty-states/empty-state';
import { CircledIcon, Row } from '../ui';
import useDeliveriesApi from 'src/hooks/use-deliveries-api';
import DeliveryCard from './delivery-card';
import { OptionWithIcon } from '../ui/more-options';
import Shimmer from '../ui/shimmer';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import Toast from 'react-native-toast-message';
import {
  DELIVERY_STATUSES,
  IDelivery,
  CANCEL_DELIVERY,
  DELETE_DELIVERY_DRAFT,
  DeleteDeliveryDraftParams,
  GET_DELIVERY,
  GetDeliveryParams,
} from 'catlog-shared';
import CustomImage from '../ui/others/custom-image';

interface DeliveriesListProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  deliveryStatus: DELIVERY_STATUSES;
}

const DeliveriesList = ({ scrollHandler, deliveryStatus }: DeliveriesListProps) => {
  const { deliveries, getDeliveriesRequest, handleOnEndReach, handlePullToRefresh, setDeliveries, goNext } =
    useDeliveriesApi(deliveryStatus);
  const navigation = useNavigation();

  const getDeliveryRequest = useApi<GetDeliveryParams, ResponseWithoutPagination<IDelivery>>({
    apiFunction: GET_DELIVERY,
    method: 'GET',
    key: 'get-delivery',
    autoRequest: false,
  });

  const deleteDeliveryDraftRequest = useApi<DeleteDeliveryDraftParams>({
    apiFunction: DELETE_DELIVERY_DRAFT,
    method: 'DELETE',
    key: 'delete-draft',
  });

  const cancelDeliveryDraftRequest = useApi<GetDeliveryParams>({
    apiFunction: CANCEL_DELIVERY,
    method: 'POST',
    key: 'cancel-delivery',
  });

  const handleDeleteDraft = async (id: string, index: number) => {
    const delFunc = async () => {
      if (id) {
        const [response, error] = await deleteDeliveryDraftRequest.makeRequest({
          id: id!,
        });
        if (response) {
          Toast.show({ type: 'success', text1: 'Delivery draft deleted successfully' });
          deliveries.splice(index, 1);
          setDeliveries(deliveries);
        }
        if (error) {
          Toast.show({ type: 'error', text1: error.body.message });
        }
        return;
      }
    };

    Alert.alert(
      'Delete Customer',
      "Are you sure you want to delete this Delivery ? You won't see this delivery again.",
      [
        { text: 'Cancel', onPress: () => {} },
        { text: 'Delete', onPress: delFunc, style: 'destructive' },
      ],
    );
  };

  const handleCancelDelivery = async (delivery: IDelivery, index: number) => {
    const delFunc = async () => {
      if (delivery?.id!) {
        const [response, error] = await cancelDeliveryDraftRequest.makeRequest({
          id: delivery?.id!,
        });
        if (response) {
          Toast.show({ type: 'success', text1: 'Delivery cancel successfully' });
          deliveries.splice(index, 1);
          setDeliveries(deliveries);
        }
        if (error) {
          Toast.show({ type: 'error', text1: error.body.message });
        }
        return;
      }
    };

    if (new Date(delivery?.pickup_date!) <= new Date()) {
      Toast.show({ type: 'error', text1: 'Cannot cancel delivery, Please contact support to cancel this delivery' });
      return;
    }

    Alert.alert(
      'Confirm Update',
      'Are you sure you want to cancel this delivery? \nYou will be charged NGN 200 for cancelling this delivery',
      [
        { text: 'Cancel', onPress: () => {} },
        { text: 'Proceed', onPress: delFunc, style: 'destructive' },
      ],
    );
  };

  const routeToInitiateDelivery = async (id: string) => {
    const [res, err] = await getDeliveryRequest.makeRequest({ id });

    if (res) {
      navigation.navigate('InitiateDelivery', { deliveryData: res?.data });
    }
  };

  const moreOptionsFunc = (deliveryData: IDelivery, index: number) => [
    ...(deliveryData?.status === DELIVERY_STATUSES.DRAFT
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />}
                label={'Finalize Delivery'}
              />
            ),
            onPress: () => routeToInitiateDelivery(deliveryData?.id!),
          },
        ]
      : []),
    ...(deliveryData?.status !== DELIVERY_STATUSES.DRAFT
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<RepeatCircle size={wp(15)} color={colors.black.placeholder} />}
                label={'Duplicate Delivery'}
              />
            ),
            onPress: () => routeToInitiateDelivery(deliveryData?.id!),
          },
          {
            optionElement: (
              <OptionWithIcon
                icon={<Copy size={wp(15)} color={colors.black.placeholder} />}
                label={'Copy Tracking Code'}
              />
            ),
            onPress: () => copyToClipboard(deliveryData?.tracking_code ?? ''),
          },
          {
            optionElement: (
              <OptionWithIcon
                icon={<ExportSquare size={wp(15)} color={colors.black.placeholder} />}
                label={'Track Delivery'}
              />
            ),
            onPress: () => openLinkInBrowser(deliveryData?.tracking_url!),
          },
        ]
      : []),
    ...(deliveryData.status === DELIVERY_STATUSES.DRAFT
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<Trash size={wp(15)} color={colors.accentRed.main} />}
                label={'Delete Draft'}
                labelClasses="text-accentRed-main"
              />
            ),
            onPress: () => handleDeleteDraft(deliveryData?.id!, index),
          },
        ]
      : []),
    ...([DELIVERY_STATUSES.PENDING].includes(deliveryData.status)
      ? [
          {
            optionElement: (
              <OptionWithIcon
                icon={<CloseCircle size={wp(15)} color={colors.accentRed.main} />}
                label={'Cancel Delivery'}
                labelClasses="text-accentRed-main"
              />
            ),
            onPress: () => handleCancelDelivery(deliveryData, index),
          },
        ]
      : []),
  ];

  return (
    <Animated.FlatList
      data={deliveries ?? []}
      onScroll={scrollHandler}
      refreshControl={<RefreshControl refreshing={false} onRefresh={() => handlePullToRefresh()} />}
      ListEmptyComponent={() => (
        <View className='flex-1'>
          {getDeliveriesRequest?.isLoading || getDeliveriesRequest?.isReLoading ? (
            <DeliveriesSkeletalLoader />
          ) : (
            <EmptyState
              customIcon={
                <CustomImage
                  imageProps={{ source: require('@/assets/images/delivery.png'), contentFit: 'cover' }}
                  className="w-80 h-80"
                />
              }
              btnText={'Initiate Delivery'}
              onPressBtn={() => navigation.navigate('InitiateDelivery')}
              text={"You don't have any delivery yet"}
            />
          )}
        </View>
      )}
      onEndReached={handleOnEndReach}
      className="flex-1 px-20"
      contentContainerStyle={{ flexGrow: 1 }}
      renderItem={({ item, index }) => (
        <DeliveryCard
          item={item}
          index={index}
          showBorder={index !== deliveries.length - 1}
          onPress={() => navigation.navigate('DeliveryDetails', { id: item.id })}
          moreOptions={moreOptionsFunc(item, index)}
        />
      )}
      ListFooterComponent={
        <View style={{ marginBottom: 80 }}>{getDeliveriesRequest?.isLoading && <DeliveriesSkeletalLoader />}</View>
      }
    />
  );
};

interface SkeletonProps {
  showBorder?: boolean;
}

export const DeliveriesSkeletalLoader = ({ showBorder = true }: SkeletonProps) => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <View key={i} className={`flex-row items-center ${showBorder && 'border-b border-b-grey-border py-15'}`}>
          <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
          <View className={'mx-12'}>
            <Row className="mr-12 mb-10">
              <Shimmer {...{ height: 15, width: 90, borderRadius: 50 }} className="mr-2" />
              <Shimmer {...{ height: 15, width: 30, borderRadius: 50 }} />
            </Row>
            <Shimmer {...{ height: 10, width: 150, borderRadius: 50 }} />
          </View>
        </View>
      ))}
    </View>
  );
};

export default DeliveriesList;
