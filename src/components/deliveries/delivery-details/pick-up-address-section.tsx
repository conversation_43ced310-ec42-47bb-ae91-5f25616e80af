import { Linking, View } from 'react-native';
import colors from '@/theme/colors';
import { ArrowUpRight, CheckCircle, ChevronDown, ChevronUp } from '@/components/ui/icons';
import { capitalizeWords, copyToClipboard, normalizeEnums, toCurrency, wp } from '@/assets/utils/js';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import {
  Clock,
  Edit2,
  Link21,
  Location,
  MoneyForbidden,
  Moneys,
  MoneyTick,
  Profile,
  ReceiptSearch,
  ShoppingBag,
  Tag2,
  TruckFast,
} from 'iconsax-react-native/src';
import SectionContainer from '@/components/ui/section-container';
import ProductCard from '@/components/products/storefront-products/product-card';
import MoreOptions from '@/components/ui/more-options';
import Separator from '@/components/ui/others/separator';
import InfoRow from '@/components/ui/others/info-row';
import cx from 'classnames';
import Toast from 'react-native-toast-message';
import { useApi } from 'src/hooks/use-api';
import { EXPO_PUBLIC_PUBLIC_URL } from '@env';
import ListItemCard from '@/components/ui/cards/list-item-card';
import CustomImage from '@/components/ui/others/custom-image';
import Accordion from '@/components/ui/others/accordion';
import dayjs from 'dayjs';
import { ReactNode } from 'react';
import { DeliveryInfo } from './product-detail-section';
import { ORDER_FEES, OrderInterface, toAppUrl, IDelivery } from 'catlog-shared';

const PickUpAddressSection = ({ delivery, callBack }: { delivery: IDelivery; callBack?: VoidFunction }) => {
  const moreOptions = [
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Edit2 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Edit Order
          </BaseText>
        </Row>
      ),
      title: 'Edit Order',
      onPress: () => {},
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <MoneyTick size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Mark as Paid
          </BaseText>
        </Row>
      ),
      title: 'Mark as Paid',
      onPress: () => {},
    },
    {
      optionElement: (
        <Row>
          <CircledIcon iconBg="bg-grey-bgOne" className="p-7">
            <Link21 size={wp(15)} color={colors.black.placeholder} />
          </CircledIcon>
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Copy Order Link
          </BaseText>
        </Row>
      ),
      title: 'Copy Order Link',
      onPress: () => copyToClipboard(toAppUrl(`deliveries/${delivery.id}`, true, EXPO_PUBLIC_PUBLIC_URL)),
    },
  ];

  return (
    <View className="mx-20">
      <Accordion
        initiallyOpened
        anchorElement={status => (
          <Row className="flex flex-row">
            <BaseText type="heading" fontSize={15}>
              Pick-up Address
            </BaseText>
            <CircledIcon className="bg-grey-bgOne p-7">
              {status ? (
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              ) : (
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              )}
            </CircledIcon>
          </Row>
        )}>
        <SectionContainer className="pb-15">
          {delivery?.sender_address?.name && (
            <DeliveryInfo
              icon={<Profile size={wp(15)} color={colors.black.placeholder} />}
              title={delivery?.sender_address?.name}
              subTitle={delivery?.sender_address?.phone}
            />
          )}
          {delivery?.sender_address?.formatted_address && (
            <DeliveryInfo
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              title={'Pick-up Address'}
              subTitle={delivery?.sender_address?.formatted_address}
            />
          )}
        </SectionContainer>
      </Accordion>
    </View>
  );
};

export default PickUpAddressSection;
