import { View } from 'react-native';
import colors from '@/theme/colors';
import { ChevronDown, ChevronUp } from '@/components/ui/icons';
import { wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import { Location, Profile } from 'iconsax-react-native/src';
import SectionContainer from '@/components/ui/section-container';
import { DeliveryInfo } from './product-detail-section';
import { IDelivery } from 'catlog-shared';
import Accordion from '@/components/ui/others/accordion';

const DropOffAddressSection = ({ delivery, callBack }: { delivery: IDelivery; callBack?: VoidFunction }) => {
  return (
    <View className="mx-20">
      <Accordion
        initiallyOpened
        anchorElement={status => (
          <Row className="flex flex-row">
            <BaseText type="heading" fontSize={15}>
              Pick-up Address
            </BaseText>
            <CircledIcon className="bg-grey-bgOne p-7">
              {status ? (
                <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              ) : (
                <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
              )}
            </CircledIcon>
          </Row>
        )}>
        <SectionContainer className="pb-15">
          {delivery?.receiver_address?.name && (
            <DeliveryInfo
              icon={<Profile size={wp(15)} color={colors.black.placeholder} />}
              title={delivery?.receiver_address?.name}
              subTitle={delivery?.receiver_address?.phone}
            />
          )}
          {delivery?.receiver_address?.formatted_address && (
            <DeliveryInfo
              icon={<Location size={wp(15)} color={colors.black.placeholder} />}
              title={'Drop-oo Address'}
              subTitle={delivery?.receiver_address?.formatted_address}
            />
          )}
        </SectionContainer>
      </Accordion>
    </View>
  );
};

export default DropOffAddressSection;
