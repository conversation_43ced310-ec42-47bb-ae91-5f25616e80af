import { View } from 'react-native';
import { BaseText, CircledIcon } from '../ui';
import { Box } from 'iconsax-react-native/src';
import { getColorAlternates, toCurrency, wp } from '@/assets/utils/js';
import MoreOptions, { MoreOptionElementProps } from '../ui/more-options';
import Row from '../ui/row';
import colors from '@/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import CustomImage from '../ui/others/custom-image';
import Pressable, { PressableProps } from '../ui/base/pressable';
import { DELIVERY_STATUSES, IDelivery } from 'catlog-shared';

export interface DeliveryCardProps extends Partial<PressableProps> {
  onPress?: () => void;
  showBorder?: boolean;
  item: IDelivery;
  index?: number;
  moreOptions?: MoreOptionElementProps[];
}

const DeliveryCard = ({ onPress, item, showBorder = false, moreOptions, index, ...props }: DeliveryCardProps) => {
  const colorAlternate = getColorAlternates(index);

  const { store } = useAuthContext();

  // console.log({ item });

  return (
    <Pressable
      className={`flex-row items-center py-15 ${showBorder && 'border-b border-b-grey-border'}`}
      activeOpacity={0.8}
      onPress={onPress}
      {...props}>
      {item?.courier?.courier_image ? (
        <CustomImage
          imageProps={{ source: { uri: item?.courier?.courier_image }, contentFit: 'cover' }}
          className="h-40 w-40 rounded-full"
        />
      ) : (
        <CircledIcon style={{ backgroundColor: colorAlternate.bgColor }}>
          <Box variant={'Bold'} size={wp(20)} color={colorAlternate.iconColor} />
        </CircledIcon>
      )}
      <View className={'mx-12'}>
        <Row className="mr-12">
          <BaseText fontSize={12} weight="medium" classes="text-black-muted">
            {item.status === DELIVERY_STATUSES.DRAFT
              ? `Delivery for ${item.items[0]?.name}`
              : `Delivery to ${item?.receiver_address?.name}`}
          </BaseText>
          <View className="flex-row px-6 py-4 bg-grey-bgOne rounded-full self-start ml-5">
            <BaseText fontSize={10} weight={'medium'} classes="leading-[16px] text-black-placeholder">
              {item?.items?.length} ITEMS
            </BaseText>
          </View>
        </Row>
        <BaseText
          fontSize={12}
          weight={'semiBold'}
          classes="mr-4 text-black-secondary max-w-[125] mt-6"
          numberOfLines={1}>
          {toCurrency(item?.delivery_amount ?? 0, store?.currencies?.default)}
        </BaseText>
      </View>
      <View className={'flex-1 ml-12 items-end self-stretch justify-between'}>
        <MoreOptions options={moreOptions ?? []} />
      </View>
    </Pressable>
  );
};

interface SkeletonProps {
  showBorder?: boolean;
}

export default DeliveryCard;
// export default styled(DeliveryCard, { props: { titleClasses: true, descriptionClasses: true } });
