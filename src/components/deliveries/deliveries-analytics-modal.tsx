import * as Yup from 'yup';
import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import DeliveryAnalyticsCards from './delivery-analytics-cards';
import { DeliveryStat } from 'src/screens/deliveries/deliveries-analytics';

interface DeliveriesAnalyticsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  stats?: DeliveryStat;
  isLoading?: boolean;
}

const DeliveriesAnalyticsModal = ({ closeModal, stats, isLoading, ...props }: DeliveriesAnalyticsModalProps) => {
  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Performance Overview'}
      showButton={false}
      buttons={[
        {
          text: 'Close',
          onPress: () => closeModal,
        },
      ]}>
      <View className='mx-20 pb-10'>
        <DeliveryAnalyticsCards statistics={stats} isLoading={isLoading} />
      </View>
    </BottomModal>
  );
};

export const addAddressValidationSchema = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default DeliveriesAnalyticsModal;
