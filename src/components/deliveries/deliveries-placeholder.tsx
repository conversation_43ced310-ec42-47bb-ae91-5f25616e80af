import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import useAuthContext from 'src/contexts/auth/auth-context';
import EmptyState from '../ui/empty-states/empty-state';
import { Car, Tag2, Truck, Warning2 } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import { useNavigation } from '@react-navigation/native';
import { View } from 'node_modules/react-native-animatable/typings/react-native-animatable';
import { BaseText } from '../ui';

interface Props {}
const DeliveriesPlaceholder: React.FC<Props> = ({}) => {
  const { store, subscription } = useAuthContext();
  const navigator = useNavigation()

  const deliveryEnabledInCountry = actionIsAllowed({
    countryPermission: SCOPES.DELIVERIES.CAN_BOOK_DELIVERIES,
    country: store?.country?.code,
  });

  const deliveryEnabledForPlan = actionIsAllowed({
    plan: subscription?.plan?.type,
    planPermission: SCOPES.DELIVERIES.CAN_BOOK_DELIVERIES,
  });

  if (!deliveryEnabledInCountry) {
    return (
      <EmptyState
        showBtn={false}
        icon={<Truck variant={'Bold'} color={colors.accentOrange.main} size={wp(40)} />}
        text={
            ` Deliveries Coming Soon\nto ${countryPlaceholders[store?.country?.code]
              ? countryPlaceholders[store?.country?.code].country
              : "your country"}`
        }
        onPressBtn={()=> {
          navigator?.navigate('StoreSettings')
        }}
        ></EmptyState>
    );
  }

  if (!deliveryEnabledForPlan) {
    return (
      <EmptyState
        showBtn
        title='Upgrade required'
        icon={<Warning2 variant={'Bold'} color={colors.accentRed.main} size={wp(40)} />}
        btnText="Upgrade Subscription"
        text="Upgrade to the basic or business plus plan to book deliveries"
        onPressBtn={()=> {
          navigator?.navigate('StoreSettings')
        }}
        ></EmptyState>
    );
  }

  return <></>;
};
export default DeliveriesPlaceholder;

const countryPlaceholders = {
    GH: {
      emoji: "🇬🇭",
      country: "Ghana",
    },
    ZA: {
      emoji: "🇿🇦",
      country: "South Africa",
    },
    KE: {
      emoji: "🇰🇪",
      country: "Kenya",
    },
  };