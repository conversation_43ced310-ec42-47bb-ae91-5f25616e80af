import { OrderItem } from 'catlog-shared';
import { View } from 'react-native';

import { ProductCardProps } from './storefront-products/product-card';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';

interface CouponOrdersModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  orders: OrderItem[];
  isLoading?: boolean;
  productCardProps?: Partial<ProductCardProps>;
}

const CouponOrdersModal = ({ closeModal, productCardProps, isLoading, orders, ...props }: CouponOrdersModalProps) => {
  return (
    <BottomModal {...props} closeModal={closeModal} showButton={false} title="Coupon Orders">
      <View className="flex-1 mx-20">
        <SectionContainer />
      </View>
    </BottomModal>
  );
};

export default CouponOrdersModal;
