import { View } from 'react-native';
import { memo } from 'react';
import cx from 'classnames';
import { TouchableHighlightProps } from 'react-native';
import { BaseText, CircledIcon } from '../../ui';
import { Box1 } from 'iconsax-react-native/src';
import { toCurrency, wp } from '@/assets/utils/js';
import Shimmer from '../../ui/shimmer';
import { ProductItemInterface } from 'catlog-shared';
import Pressable from 'src/components/ui/base/pressable';
import { DraggableIcon } from 'src/components/ui/icons';
import colors from 'src/theme/colors';
import CustomImage from 'src/components/ui/others/custom-image';
import CheckBox from 'src/components/ui/buttons/check-box';

export interface SortProductCardProps extends Partial<TouchableHighlightProps> {
  item?: Partial<ProductItemInterface>;
  index?: number;
  onPress?: VoidFunction;
  onLongPress?: VoidFunction;
  currency?: string;
  isSelected?: boolean;
  isActive?: boolean;
  onPressCheckbox?: VoidFunction;
}

const SortProductCard = ({
  onPress,
  index,
  currency,
  isSelected,
  item,
  onLongPress,
  isActive,
  onPressCheckbox,
  ...props
}: SortProductCardProps) => {
  return (
    <Pressable
      className={cx(`flex-row items-center bg-grey-bgOne rounded-12 mx-20 my-5`)}
      style={{ transform: [{ rotate: isActive ? '3deg' : '0deg' }] }}
      onPress={onPress}
      onLongPress={onLongPress}
      {...props}>
      <View className={cx('mx-5')}>
        <DraggableIcon size={wp(16)} primaryColor={colors.black.placeholder} />
      </View>
      <View className={cx('flex-1 flex-row items-center bg-white rounded-12 border border-grey-border p-10 pr-12')}>
        <View className="w-48 h-48 ">
          {item?.images?.[item?.thumbnail ?? 0] ? (
            <CustomImage
              imageProps={{ source: item?.images?.[item?.thumbnail ?? 0], contentFit: 'cover' }}
              className="w-48 h-48 rounded-[6px]"
              style={{ backgroundColor: 'transparent' }}
            />
          ) : (
            <View className="w-full h-full bg-accentYellow-pastel justify-center items-center rounded-[6px]">
              <Box1 color={colors.accentYellow.main} variant="Bulk" size={wp(20)} />
            </View>
          )}
        </View>
        <View className="flex-1 mx-10">
          <BaseText fontSize={13} type="heading" classes=" text-black-muted" numberOfLines={1}>
            {item?.name}
          </BaseText>
          <BaseText fontSize={11} weight="semiBold" classes="mt-5">
            {toCurrency(item?.price, currency)}
          </BaseText>
        </View>
        <Pressable onPress={onPressCheckbox}>
          <CheckBox checked={isSelected} />
        </Pressable>
      </View>
    </Pressable>
  );
};

export default memo(SortProductCard);
// export default styled(SortProductCard, { props: { titleClasses: true, descriptionClasses: true } });

export const SortProductCardSkeleton = () => {
  return (
    <View className="flex-row items-center bg-grey-bgOne rounded-12 mx-20 mt-10">
      <View className="mx-5">
        <Shimmer height={wp(16)} width={wp(16)} borderRadius={4} />
      </View>
      <View className="flex-1 flex-row items-center bg-white rounded-12 border border-grey-border p-10 pr-12">
        <Shimmer height={48} width={48} borderRadius={6} />
        <View className="flex-1 mx-10">
          <Shimmer height={13} width={120} borderRadius={4} />
          <Shimmer height={11} width={80} borderRadius={4} marginTop={5} />
        </View>
        <Shimmer height={20} width={20} borderRadius={2} />
      </View>
    </View>
  );
};

export const SortProductsSkeletonLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => (
        <SortProductCardSkeleton key={i} />
      ))}
    </View>
  );
};
