import { DocumentUpload, PlayCircle, Trash } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import Separator from 'src/components/ui/others/separator';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { IMedia, IVideo } from 'src/@types/utils';
import Pressable from 'src/components/ui/base/pressable';
import CustomImage from 'src/components/ui/others/custom-image';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { useEffect } from 'react';
import { ProcessingStep } from 'src/hooks/use-video-processing-queue';

  const stepDescription = {
    [ProcessingStep.TRIM_VIDEO]: 'Trimming and compressing Video',
    [ProcessingStep.UPLOAD_VIDEO]: 'Uploading Video',
    [ProcessingStep.UPDATE_VIDEO_URL]: 'Updating Video Url',
    [ProcessingStep.UPLOAD_THUMBNAIL]: 'Uploading Thumbnail',
    [ProcessingStep.UPDATE_THUMBNAIL_URL]: 'Updating Thumbnail Url',
  }

interface VideoCardProps {
  video: IVideo;
  onPressViewVideo?: (video: IVideo) => void;
  onPressRetry?: VoidFunction;
  onPressUploadButton?: VoidFunction;
  removeVideoProgress?: VoidFunction;
}

const VideoCard = ({
  video,
  onPressRetry,
  onPressViewVideo,
  onPressUploadButton,
  removeVideoProgress,
}: VideoCardProps) => {
  const uploaded = video?.url ? true : false;

  const progressWidth = useSharedValue(0);
  useEffect(() => {
    console.log('video?.uploadProgress: ', video?.currentStep);
    progressWidth.value = withTiming(video?.uploadProgress, { duration: 200 });
  }, [video?.uploadProgress]);

  const isUploading = video?.isUploading;

  // console.log(video);

  const descriptionText = `${video.fileSize > 0 ? (video.fileSize > 1024 * 1024 ? (video.fileSize / (1024 * 1024)).toFixed(2) + ' MB ' : (video.fileSize / 1024).toFixed(2) + ' KB' + ` • ${formatTime(video?.duration)}`) : ''}`;

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });
  const colorProgress = useSharedValue(0);

  useEffect(() => {
    if (isUploading) {
      colorProgress.value = withRepeat(withTiming(1, { duration: 500 }), -1, true);
    }
  }, [isUploading]);

  const color1 = colors.white;
  const color2 = colors.accentGreen.pastel2;

  const progressBackgroundStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      colorProgress.value,
      [0, 1],
      [color1, color2], // From coral red to teal
    );

    return {
      backgroundColor,
      width: '100%',
    };
  });

  const showFoot = !uploaded;

  const showUploadStatus = uploaded || isUploading

  return (
    <View className="bg-grey-bgOne rounded-12 px-12 py-8">
      <Row style={{ gap: wp(10) }}>
        {video?.meta?.thumbnail?.url ? (
          <CustomImage imageProps={{ source: { uri: video?.meta.thumbnail?.url } }} className="h-50 w-50  rounded-5" />
        ) : (
          <CircledIcon className="h-50 w-50 rounded-[6px] p-10 bg-grey-extraLight">
            <DocumentUpload size={wp(26)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
        )}
        <View className="flex-1">
          <Row style={{ gap: wp(10) }}>
            <View className="flex-1">
              <BaseText weight="medium" classes="text-black-secondary" numberOfLines={1}>
                {video?.name ?? ''}
              </BaseText>
            </View>
            <Pressable onPress={() => onPressViewVideo(video)}>
              <CircledIcon className="p-6 bg-white">
                <PlayCircle size={wp(18)} color={colors.black.muted} />
              </CircledIcon>
            </Pressable>
            <Pressable onPress={removeVideoProgress}>
              <CircledIcon className="p-6 bg-white">
                <Trash strokeWidth={1.5} size={wp(18)} color={colors.accentRed.main} />
              </CircledIcon>
            </Pressable>
          </Row>
          <Row disableSpread>
            {descriptionText && (
              <>
                <BaseText fontSize={12} classes="text-black-placeholder">
                  {descriptionText}
                </BaseText>
                {showUploadStatus && <View className="w-3 h-3 rounded-full bg-black-muted mx-6" />}
              </>
            )}
            {showUploadStatus && (
              <>
                <BaseText fontSize={12} classes="text-accentGreen-main">
                  {video?.isUploading ? stepDescription[video?.currentStep] : 'Upload Complete'}
                </BaseText>
              </>
            )}
          </Row>
          {isUploading && (
            <Animated.View style={progressBackgroundStyle} className="rounded-full overflow-hidden  mt-5">
              <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-full" />
            </Animated.View>
          )}
        </View>
      </Row>
      {showFoot && !isUploading && (
        <>
          <Separator className="my-10 mx-0" />
          <Row>
            {!uploaded && video?.status !== 'failed' && (
              <Button
                text="Upload Video"
                size={ButtonSize.SMALL}
                disabled={isUploading}
                onPress={onPressUploadButton}
              />
            )}
            {video?.status === 'failed' && <Button text="Retry" size={ButtonSize.SMALL} onPress={onPressRetry} />}
          </Row>
        </>
      )}
    </View>
  );
};

export default VideoCard;

// Utility function to format time from milliseconds
const formatTime = (milliseconds: number): string => {
  const seconds = milliseconds / 1000;
  if (isNaN(seconds)) {
    return '';
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes} min ${secs < 10 ? '0' : ''}${secs.toFixed(0)} secs`;
};
