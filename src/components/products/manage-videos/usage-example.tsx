import React, { useRef } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import ManageProductVideos, { ManageProductVideosRef } from './manage-product-videos';
import { IVideo } from 'src/@types/utils';
import { AppMediaType, Image } from 'src/@types/utils';

// Example usage of the updated ManageProductVideos component with forwardRef
const ExampleUsage: React.FC = () => {
  const manageVideosRef = useRef<ManageProductVideosRef>(null);
  
  // Example state
  const [videos, setVideos] = React.useState<IVideo[]>([]);
  const [images, setImages] = React.useState<Image[]>([]);

  const handleSaveVideos = (newVideos: IVideo[]) => {
    setVideos(newVideos);
  };

  const handleSaveMedias = (medias: AppMediaType[]) => {
    // Handle saving medias
    console.log('Saving medias:', medias);
  };

  const handleRemoveVideo = (index: number) => {
    setVideos(prev => prev.filter((_, i) => i !== index));
  };

  // Function to trigger video picking from parent component
  const triggerVideoPicker = () => {
    manageVideosRef.current?.handlePickVideo();
  };

  return (
    <View style={{ flex: 1, padding: 20 }}>
      {/* Custom button to trigger video picking */}
      <TouchableOpacity
        onPress={triggerVideoPicker}
        style={{
          backgroundColor: '#007AFF',
          padding: 15,
          borderRadius: 8,
          marginBottom: 20,
          alignItems: 'center',
        }}
      >
        <Text style={{ color: 'white', fontWeight: 'bold' }}>
          Pick Videos (Custom Button)
        </Text>
      </TouchableOpacity>

      {/* ManageProductVideos component with hidden AddVideo button */}
      <ManageProductVideos
        ref={manageVideosRef}
        videos={videos}
        images={images}
        saveVideos={handleSaveVideos}
        saveMedias={handleSaveMedias}
        removeProductVideo={handleRemoveVideo}
        hideAddButton={true} // Hide the default AddVideo button
      />
    </View>
  );
};

export default ExampleUsage;
