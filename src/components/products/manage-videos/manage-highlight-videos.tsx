import { View } from 'react-native';
import { showLoader, wp } from 'src/assets/utils/js';
import AddVideo from '../create-products/add-video';
import VideoCard from './video-card';
import { memo, useState, useCallback, useMemo, useEffect, forwardRef, useImperativeHandle } from 'react';
import { AppMediaType, Image, IVideo } from 'src/@types/utils';
import useModals from 'src/hooks/use-modals';
import ViewVideoModal from './view-video-modal';
import VideoEditorModal, { SelectedTrimData } from './video-editor-modal';
import { useVideoProcessingQueue, QueueItem } from 'src/hooks/use-video-processing-queue';
import { HighlightInterface, MediaType } from 'catlog-shared';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { pickMultipleMedia, pickMultipleVideos } from 'src/assets/utils/js/pick-multiple-images';
import * as ImagePicker from 'expo-image-picker';
import HighlightVideoCard from './highlight-video-card';
import But<PERSON>, { ButtonVariant } from 'src/components/ui/buttons/button';
import { AddProductHighlightFormProps, FormHighlightVideo } from '../product-hghlights/add-product-highlight-modal';
import useStorefrontItems from 'src/hooks/use-storefront-items';
import useAuthContext from 'src/contexts/auth/auth-context';
import SelectSpecificProductsModal from '../storefront-products/select-specific-product-modal';

export interface ManageProductVideosRef {
  handlePickVideo: () => void;
}

interface Props {
  // product: Product;
  highlights: AddProductHighlightFormProps['videos'];
  updateHighlights: (highlights: AddProductHighlightFormProps['videos']) => void;
  videos: IVideo[];
  thumbnail?: number;
  saveVideos: (videos: IVideo[]) => void;
  removeProductVideo: (index: number) => void;
  isEdit?: boolean;
}

const ManageProductVideos = forwardRef<ManageProductVideosRef, Props>(
  ({ videos, isEdit, saveVideos, removeProductVideo, updateHighlights, highlights }, ref) => {
    const { modals, toggleModal } = useModals(['viewVideo', 'editVideo', 'selectProducts']);
    const [video, setVideo] = useState<IVideo>(null);
    const [activeHighlight, setActiveHighlight] = useState<FormHighlightVideo>(null);
    const [activeIndex, setActiveIndex] = useState<number>(null);

    const [infoMessage, setInfoMessage] = useState(null);

    const { store } = useAuthContext();

    const { items, fetchItemsReq, getItem } = useStorefrontItems(store.id);

    useEffect(() => {
      if (infoMessage) {
        showLoader(infoMessage, false, true);
      }
    }, [infoMessage]);

    // Callback to update video in your local state when processing completes
    const updateVideoInQueue = useCallback(
      (queueItem: QueueItem) => {
        // Update the video in your product.videos array
        // const updatedVideos = videos.map(v => {
        //   if (v.name === queueItem.name || v.key === queueItem.key) {
        //     return {
        //       ...v,
        //       url: queueItem.processedVideoUrl,
        //       src: queueItem.src,
        //       thumbnail: queueItem.processedThumbnailUrl,
        //       isUploading: queueItem.isUploading,
        //       uploadProgress: queueItem.uploadProgress,
        //       error: queueItem.status === 'failed',
        //       fileSize: queueItem.fileSize,
        //       meta: {
        //         ...v.meta,
        //         thumbnail: {
        //           ...v.meta?.thumbnail,
        //           url: queueItem.processedThumbnailUrl,
        //           src: v.meta?.thumbnail?.src,
        //           isUploading: queueItem.status === 'processing',
        //           uploadProgress: queueItem.overallProgress,
        //           error: queueItem.status === 'failed',
        //         },
        //       },
        //     };
        //   }
        //   return v;
        // });

        const updatedHighlights = highlights.map(h => {
          if (h.video.name === queueItem.name || h.video.key === queueItem.key) {
            return {
              ...h,
              video: {
                ...h.video,
                url: queueItem.processedVideoUrl,
                src: queueItem.src,
                status: queueItem.status,
                thumbnail: queueItem.processedThumbnailUrl,
                isUploading: queueItem.isUploading,
                queueId: queueItem.queueId,
                stepProgress: queueItem.stepProgress,
                overallProgress: queueItem.overallProgress,
                currentStep: queueItem.currentStep,
                uploadProgress: queueItem.uploadProgress,
                error: queueItem.status === 'failed',
                fileSize: queueItem.fileSize,
                meta: {
                  ...h.video.meta,
                  thumbnail: {
                    ...h.video.meta?.thumbnail,
                    url: queueItem.processedThumbnailUrl,
                    src: h.video.meta?.thumbnail?.src,
                    isUploading: queueItem.status === 'processing',
                    uploadProgress: queueItem.overallProgress,
                    error: queueItem.status === 'failed',
                  },
                },
              },
            };
          }
          return h;
        });

        // saveVideos(updatedVideos);
        updateHighlights(updatedHighlights);
      },
      [videos, saveVideos, updateHighlights],
    );

    const { addToQueue, removeFromQueue, retryItem } = useVideoProcessingQueue(updateVideoInQueue);

    const removeVideoProgress = (index: number, taskId?: string) => {
      if (taskId) {
        removeFromQueue(taskId);
      }
      removeProductVideo?.(index);
    };

    const handleViewVideo = (video: IVideo) => {
      setVideo(video);
      toggleModal('viewVideo', true);
    };

    const onPressUploadVideo = (video: IVideo) => {
      setVideo(video);
      toggleModal('editVideo', true);
    };

    const onPressAddProducts = (h: FormHighlightVideo, idx: number) => {
      console.log('idx: ', idx);
      setActiveHighlight(h);
      setActiveIndex(idx);
      toggleModal('selectProducts', true);
    };

    const handleToggleActive = (index: number, v: boolean) => {
      const updatedHighlights = highlights.map((h, i) => {
        if (i === index) {
          return {
            ...h,
            active: v,
          };
        }
        return h;
      });

      updateHighlights(updatedHighlights);
    };

    // Complete the editing process by adding to processing queue
    const onCompleteEditing = async (result: {
      video: IVideo;
      thumbnailTimestamp: number;
      trimData?: SelectedTrimData;
    }) => {
      const { video: editedVideo, trimData } = result;

      const generateThumbnail = async () => {
        try {
          const thumbnail = await VideoThumbnails.getThumbnailAsync(video.src, {
            time: Math.ceil(result.thumbnailTimestamp),
            quality: 0.7,
          });

          return thumbnail.uri;
        } catch (error) {
          console.error('Error generating thumbnails:', error);
        }
      };

      const thumbnailUrl = await generateThumbnail();

      // Create queue item for processing
      const queueItem: Omit<
        QueueItem,
        'queueId' | 'status' | 'currentStep' | 'stepProgress' | 'overallProgress' | 'retryCount'
      > = {
        type: MediaType.VIDEO,
        src: editedVideo.src,
        name: editedVideo.name,
        lastModified: Date.now(),
        file: null,
        key: editedVideo.key || editedVideo.name,

        // Trimming parameters
        startTimeMs: trimData?.startTime || 0,
        endTimeMs: trimData?.endTime || 0,

        // Video metadata
        meta: {
          id: editedVideo.mediaHash || editedVideo.name,
          thumbnail: {
            src: thumbnailUrl,
            name: `${editedVideo.name}_thumbnail`,
            lastModified: Date.now(),
            file: null,
            isUploading: false,
            uploadProgress: 0,
            url: '',
            error: false,
            key: `${editedVideo.name}_thumbnail`,
          },
        },
      };

      console.log(JSON.stringify(queueItem));

      // Add to processing queue
      addToQueue(queueItem);

      // Close modal
      toggleModal('editVideo', false);

      // Optional: Show success message or update UI to indicate processing started
      console.log('Video added to processing queue:', editedVideo.name);
    };

    const handlePickVideo = () => {
      pickMultipleVideos(videos, saveVideos, false, {}, setInfoMessage, true);
    };

    const handleRemoveProduct = (productId: string, highLightIndex: number) => {
      const updatedHighlights = highlights.map((h, i) => {
        if (i === highLightIndex) {
          return {
            ...h,
            products: h.products.filter(p => p !== productId),
          };
        }
        return h;
      });

      updateHighlights(updatedHighlights);
    };

    const handleAddProducts = (products: string[], highLightIndex: number) => {
      const updatedHighlights = highlights.map((h, i) => {
        if (i === highLightIndex) {
          return {
            ...h,
            products: [...products],
          };
        }
        return h;
      });

      updateHighlights(updatedHighlights);
    };

    const onSelectProducts = (products: string[]) => {
      console.log('products: ', products);
      handleAddProducts(products, activeIndex);
      // toggleModal('selectProducts', false);
    };

    // Expose handlePickVideo function through ref
    useImperativeHandle(
      ref,
      () => ({
        handlePickVideo,
      }),
      [handlePickVideo],
    );

    return (
      <View className="mt-20">
        <View style={{ gap: wp(10) }}>
          {highlights?.map((h, idx) => (
            <HighlightVideoCard
              key={idx}
              index={idx}
              highlight={h}
              isEdit={isEdit}
              onPressViewVideo={handleViewVideo}
              onToggleActive={v => handleToggleActive(idx, v)}
              onPressUploadButton={() => onPressUploadVideo(h.video)}
              onPressAddProducts={() => onPressAddProducts(h, idx)}
              onPressRetry={() => retryItem(h.video.queueId)}
              getItem={getItem}
              items={items}
              removeProduct={id => handleRemoveProduct(id, idx)}
              removeVideoProgress={() => removeVideoProgress(idx, h.video?.key)}
            />
          ))}
        </View>

        {modals.viewVideo && (
          <ViewVideoModal
            isVisible={modals.viewVideo}
            closeModal={() => toggleModal('viewVideo', false)}
            video={video}
          />
        )}
        {modals.editVideo && video && (
          <VideoEditorModal
            isVisible={modals.editVideo}
            onModalHide={() => setVideo(null)}
            closeModal={() => toggleModal('editVideo', false)}
            video={video}
            onComplete={onCompleteEditing}
          />
        )}
        {modals.selectProducts && typeof activeIndex === 'number' && (
          <SelectSpecificProductsModal
            products={items}
            isVisible={modals.selectProducts}
            closeModal={() => toggleModal('selectProducts', false)}
            getProductsRequest={fetchItemsReq}
            selectedProducts={highlights[activeIndex]?.products || []}
            setSelectedProducts={onSelectProducts}
            onPressContinue={() => toggleModal('selectProducts', false)}
          />
        )}
      </View>
    );
  },
);

export default memo(ManageProductVideos);
