import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react';
import { View } from 'react-native';
import { BaseText, Row } from 'src/components/ui';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import useSteps from 'src/hooks/use-steps';
import { IVideo, TrimData } from 'src/@types/utils';
import TrimVideoModal from './trim-video-modal';
import SelectThumbnailStep from './select-thumbnail-step';
import { useVideoTrimmer } from 'src/hooks/use-video-trimmer';
import { showError, showSuccess } from 'src/assets/utils/js';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import TrimVideo, { TrimVideoRef } from './trim-video';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import * as VideoThumbnails from 'expo-video-thumbnails';

enum VIDEO_EDITOR_STEPS {
  TRIM_VIDEO = 'TRIM_VIDEO',
  SELECT_THUMBNAIL = 'SELECT_THUMBNAIL',
}

export type SelectedTrimData = Pick<TrimData, 'startTime' | 'endTime' | 'videoDuration'>;

interface VideoEditorModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  video: IVideo;
  onComplete?: (result: { video: IVideo; thumbnailTimestamp: number; trimData?: SelectedTrimData }) => void;
}

const VideoEditorModal = ({ closeModal, video, onComplete, ...props }: VideoEditorModalProps) => {
  const [processing, setProcessing] = useState(false);
  const [trimData, setTrimData] = useState<SelectedTrimData | null>(null);

  const [trimStartTime, setTrimStartTime] = useState(0);
  const [trimEndTime, setTrimEndTime] = useState(0);

  const [currentTime, setCurrentTime] = useState(0);

  const trimVideoRef = useRef<TrimVideoRef>(null);

  const { trimVideo, isProcessing, progress } = useVideoTrimmer();

  // Initialize steps
  const { step, next, previous, changeStep } = useSteps(Object.values(VIDEO_EDITOR_STEPS), 0);

  const disableTrimButton = useMemo(() => {
    return trimStartTime === 0 && trimEndTime === 0;
  }, [trimStartTime, trimEndTime]);

  // Reset state when modal opens with a new video
  // useEffect(() => {
  //   if (props.isVisible && video) {
  //     setTrimmedVideo(null);
  //     setSelectedThumbnail(null);
  //     setTrimData(null);
  //     changeStep(VIDEO_EDITOR_STEPS.TRIM_VIDEO);
  //   }
  // }, [props.isVisible, video]);

  // const generateThumbnail = async () => {
  //   try {
  //     const duration = video.duration || 0;
  //     const thumbnailCount = Math.max(6, Math.min(12, Math.floor(duration / 1000)));
  //     const thumbnailPromises = [];

  //     for (let i = 0; i < thumbnailCount; i++) {
  //       const time = (i * duration) / thumbnailCount;
  //       const thumbnailPromise = VideoThumbnails.getThumbnailAsync(video.src, {
  //         time: Math.ceil(time),
  //         quality: 0.7,
  //       });
  //       thumbnailPromises.push(thumbnailPromise);
  //     }

  //     const thumbnailResults = await Promise.all(thumbnailPromises);
  //     const thumbnailData = thumbnailResults.map((result, index) => ({
  //       uri: result.uri,
  //       time: (index * duration) / thumbnailCount,
  //     }));

  //     setThumbnails(thumbnailData);
  //   } catch (error) {
  //     console.error('Error generating thumbnails:', error);
  //   }
  // };

  // Handle completion
  const handleComplete = () => {
    if (!currentTime) {
      showError('Please select a thumbnail');
      return;
    }

    console.log({ currentTime });
    console.log('trimData', trimData);

    const videoToReturn = video;

    onComplete?.({
      video: videoToReturn,
      thumbnailTimestamp: currentTime,
      trimData: trimData,
    });

    closeModal();
  };

  // Get button text based on current step
  const getButtonText = () => {
    switch (step) {
      case VIDEO_EDITOR_STEPS.TRIM_VIDEO:
        return 'Continue';
      case VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL:
        return 'Save Video';
      default:
        return 'Continue';
    }
  };

  const getTrimData = async () => {
    if (!trimVideoRef.current) return;
    const trimData = trimVideoRef.current.getTrimData();
    console.log('trimData: ', trimData);
    setTrimData(trimData);
    next();
  };

  console.log({ trimStartTime });

  // Handle button press based on current step
  const handleButtonPress = () => {
    console.log({ step });
    switch (step) {
      case VIDEO_EDITOR_STEPS.TRIM_VIDEO:
        getTrimData();
        break;
      case VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL:
        handleComplete();
        break;
    }
  };

  console.log(step);

  const selectThumbActive = step === VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL;
  const trimVideoActive = step === VIDEO_EDITOR_STEPS.TRIM_VIDEO;

  return (
    <BottomModal
      closeModal={closeModal}
      title={step === VIDEO_EDITOR_STEPS.TRIM_VIDEO ? 'Trim Video' : 'Select Thumbnail'}
      useChildrenAsDirectChild
      customSnapPoints={[100]}
      buttons={[
        ...(step === VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL
          ? [
              {
                text: 'Go Back',
                variant: ButtonVariant.LIGHT,
                onPress: () => {
                  if (step === VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL) {
                    previous();
                  } else {
                    closeModal();
                  }
                },
              },
            ]
          : []),
        {
          text: getButtonText(),
          onPress: handleButtonPress,
          // isLoading: processing,
          disabled:
            step === VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL ? selectThumbActive && !currentTime : disableTrimButton,
        },
      ]}
      {...props}>
      {trimVideoActive && (
        <TrimVideo
          video={video}
          closeModal={() => {}}
          ref={trimVideoRef}
          trimStartTime={trimStartTime}
          trimEndTime={trimEndTime}
          setTrimStartTime={setTrimStartTime}
          setTrimEndTime={setTrimEndTime}
        />
      )}

      {step === VIDEO_EDITOR_STEPS.SELECT_THUMBNAIL && (
        <SelectThumbnailStep video={video} currentTime={currentTime} setCurrentTime={setCurrentTime} />
      )}
    </BottomModal>
  );
};

export default VideoEditorModal;
