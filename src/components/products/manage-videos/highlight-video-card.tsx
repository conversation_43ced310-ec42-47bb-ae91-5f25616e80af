import { DocumentUpload, PlayCircle, TickCircle, Trash } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from 'src/components/ui';
import colors from 'src/theme/colors';
import { cx, toCurrency, wp } from 'src/assets/utils/js';
import Separator from 'src/components/ui/others/separator';
import Button, { ButtonSize, ButtonVariant } from 'src/components/ui/buttons/button';
import { IMedia, IVideo } from 'src/@types/utils';
import Pressable from 'src/components/ui/base/pressable';
import CustomImage from 'src/components/ui/others/custom-image';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { useEffect, useMemo } from 'react';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import { useVideoMeta } from 'src/hooks/use-video-metadata';
import { getItemThumbnail, HighlightInterface, ProductItemInterface } from 'catlog-shared';
import { AddProductHighlightFormProps, FormHighlightVideo } from '../product-hghlights/add-product-highlight-modal';
import useAuthContext from 'src/contexts/auth/auth-context';
import { Close } from 'src/components/ui/icons';
import Accordion from 'src/components/ui/others/accordion';
import AccordionAnchor from 'src/components/ui/others/accordion/accordion-anchor';
import { ProcessingStep } from 'src/hooks/use-video-processing-queue';

  const stepDescription = {
    [ProcessingStep.TRIM_VIDEO]: 'Trimming and compressing Video',
    [ProcessingStep.UPLOAD_VIDEO]: 'Uploading Video',
    [ProcessingStep.UPDATE_VIDEO_URL]: 'Updating Video Url',
    [ProcessingStep.UPLOAD_THUMBNAIL]: 'Uploading Thumbnail',
    [ProcessingStep.UPDATE_THUMBNAIL_URL]: 'Updating Thumbnail Url',
  }

interface HighlightVideoCardProps {
  highlight?: FormHighlightVideo;
  onPressViewVideo?: (video: IVideo) => void;
  onPressUploadButton?: VoidFunction;
  removeVideoProgress?: VoidFunction;
  isEdit?: boolean;
  getItem: (id: string) => ProductItemInterface;
  onToggleActive?: (v: boolean) => void;
  onPressAddProducts?: VoidFunction;
  removeProduct: (id: string) => void;
  onPressRetry?: VoidFunction;
  items?: ProductItemInterface[];
  index
}

const HighlightVideoCard = ({
  highlight,
  isEdit,
  onPressViewVideo,
  onPressUploadButton,
  removeVideoProgress,
  onToggleActive,
  getItem,
  index,
  removeProduct,
  onPressAddProducts,
  onPressRetry,
  items,
}: HighlightVideoCardProps) => {
  const uploaded = highlight?.video?.url ? true : false;

  const progressWidth = useSharedValue(0);
  useEffect(() => {
    progressWidth.value = withTiming(highlight?.video?.uploadProgress, { duration: 200 });
  }, [highlight?.video?.uploadProgress]);

  const isUploading = highlight?.video?.isUploading;
  const { store } = useAuthContext();

  const storeProductCurrency = store?.currencies?.products;

  const descriptionText = `${highlight?.video?.fileSize > 0 ? (highlight?.video?.fileSize > 1024 * 1024 ? (highlight?.video?.fileSize / (1024 * 1024)).toFixed(2) + ' MB ' : (highlight?.video?.fileSize / 1024).toFixed(2) + ' KB' + ` • ${formatTime(highlight?.video?.duration)}`) : ''}`;

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });
  const colorProgress = useSharedValue(0);

  useEffect(() => {
    if (isUploading) {
      colorProgress.value = withRepeat(withTiming(1, { duration: 500 }), -1, true);
    }
  }, [isUploading]);

  const color1 = colors.white;
  const color2 = colors.accentGreen.pastel2;

  const progressBackgroundStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      colorProgress.value,
      [0, 1],
      [color1, color2], // From coral red to teal
    );

    return {
      backgroundColor,
      width: '100%',
    };
  });

  const highlightProducts = useMemo(() => {
    if (items && items.length > 0) {
      return highlight?.products?.map(i => getItem(i));
    }

    return [];
  }, [highlight?.products, getItem, items]);

  return (
    <View className="bg-grey-bgOne border border-grey-border rounded-12">
      <Row className="px-10 py-10">
        <View className="flex-1">
          <BaseText classes="text-black-secondary" weight="medium">
            Video {index+1}
          </BaseText>
        </View>
        {isEdit && (
          <Row disableSpread>
            <CustomSwitch value={highlight?.active} onValueChange={v => onToggleActive(v)} />
          </Row>
        )}
        {!highlight.active && (
          <Pressable onPress={removeVideoProgress} className="ml-10">
            <CircledIcon className="p-6 bg-white">
              <Trash size={wp(14)} color={colors.accentRed.main} />
            </CircledIcon>
          </Pressable>
        )}
      </Row>
      <View className={cx('bg-white rounded-12 p-10')}>
        <View className={cx('bg-grey-bgOne rounded-12 px-12 py-8', { 'opacity-50': !highlight?.active })}>
          <Row style={{ gap: wp(10) }}>
            {highlight?.video?.meta?.thumbnail?.url ? (
              <CustomImage
                imageProps={{ source: { uri: highlight?.video?.meta.thumbnail?.url } }}
                className="h-50 w-50  rounded-5"
              />
            ) : (
              <CircledIcon className="h-50 w-50 rounded-[6px] p-10 bg-grey-extraLight">
                <DocumentUpload size={wp(26)} variant="Bold" color={colors.black.muted} />
              </CircledIcon>
            )}
            <View className="flex-1">
              <Row style={{ gap: wp(10) }}>
                <View className="flex-1">
                  <BaseText weight="medium" classes="text-black-secondary" numberOfLines={1}>
                    {highlight?.video?.name ?? ''}
                  </BaseText>
                </View>
                <Pressable onPress={() => onPressViewVideo(highlight?.video)}>
                  <CircledIcon className="p-6 bg-white">
                    <PlayCircle size={wp(18)} color={colors.black.muted} />
                  </CircledIcon>
                </Pressable>
                <Pressable onPress={removeVideoProgress}>
                  <CircledIcon className="p-6 bg-white">
                    <Trash size={wp(18)} color={colors.accentRed.main} />
                  </CircledIcon>
                </Pressable>
              </Row>
              {descriptionText && (
                <BaseText fontSize={12} classes="text-black-placeholder">
                  {descriptionText}
                </BaseText>
              )}
              {(uploaded || isUploading) && (
                <Row disableSpread>
                  <BaseText fontSize={12} classes="text-accentGreen-main mr-4">
                    {!uploaded ? stepDescription[highlight?.video?.currentStep] : 'Uploaded'}

                  </BaseText>
                  {uploaded && <TickCircle size={wp(12)} variant="Bold" color={colors.accentGreen.main} />}
                </Row>
              )}
              {isUploading && (
                <Animated.View style={progressBackgroundStyle} className="rounded-full overflow-hidden  mt-5">
                  <Animated.View style={progressStyle} className="h-5 bg-accentGreen-main rounded-full" />
                </Animated.View>
              )}
            </View>
          </Row>
          {!uploaded && (
            <View className="justify-end">
              <Separator className="my-10 mx-0" />
              <View className="self-end">
                {/* <WhiteCardBtn
                  // text="Upload Video"
                  // variant={ButtonVariant.LIGHT}
                  // size={ButtonSize.SMALL}
                  onPress={onPressUploadButton}>
                  Upload Video
                </WhiteCardBtn> */}
                <Button text="Upload Video" size={ButtonSize.SMALL} onPress={onPressUploadButton} />
              </View>
            </View>
          )}
          {highlight?.video?.status === 'failed' && (
            <Button text="Retry" size={ButtonSize.SMALL} onPress={onPressRetry} />
          )}
        </View>
        {highlight?.active && (
          <View>
            {highlight?.products?.length > 0 && (
              <Accordion
                title={'Products'}
                anchorElement={status => (
                  <AccordionAnchor title={'Product List'} isOpened={status} titleProps={{ fontSize: 12 }} />
                )}>
                <View className="mt-10" style={{ gap: wp(10) }}>
                  {highlightProducts?.map((product, index) => (
                    <Row key={index} className="bg-grey-bgOne rounded-8 p-5" style={{ gap: wp(10), flex: 1 }}>
                      {product?.thumbnail_type && (
                        <CustomImage
                          imageProps={{ source: { uri: getItemThumbnail(product) } }}
                          className="h-35 w-35 rounded-5"
                        />
                      )}
                      <View className="flex-1">
                        <BaseText fontSize={10} numberOfLines={1} classes="text-black-secondary">
                          {product?.name}
                        </BaseText>
                        <BaseText fontSize={10} weight="semiBold" numberOfLines={1} classes="mt-4">
                          {toCurrency(product?.price, storeProductCurrency)}
                        </BaseText>
                      </View>
                      <Pressable onPress={() => removeProduct(product?.id)} className="justify-end">
                        <CircledIcon className="p-6">
                          <Close size={wp(12)} currentColor={colors.accentRed.main} />
                        </CircledIcon>
                      </Pressable>
                    </Row>
                  ))}
                </View>
              </Accordion>
            )}
            <Row style={{ gap: wp(10), flex: 1 }}>
              <Pressable onPress={onPressAddProducts} className="pt-15">
                <BaseText fontSize={12} weight={'medium'} classes="text-primary-main">
                  Add Product
                </BaseText>
              </Pressable>
            </Row>
          </View>
        )}
      </View>
    </View>
  );
};

export default HighlightVideoCard;

// Utility function to format time from milliseconds
const formatTime = (seconds: number): string => {
  if (isNaN(seconds)) {
    return '';
  }
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes} min ${secs < 10 ? '0' : ''}${secs.toFixed(0)} secs`;
};
