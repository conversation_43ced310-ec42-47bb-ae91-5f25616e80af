import { IVideo } from 'src/@types/utils';
import { View } from 'react-native';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import { useVideoPlayer, VideoView } from 'node_modules/expo-video/build';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import CircledIcon from 'src/components/ui/circled-icon';
import { Pause, Play } from 'node_modules/iconsax-react-native/src';
import { useEvent } from 'expo';
import Pressable from 'src/components/ui/base/pressable';

interface ViewVideoModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  video: IVideo;
}

const ViewVideoModal = ({ closeModal, video, ...props }: ViewVideoModalProps) => {
  console.log('video?.src: ', video?.src);
  const player = useVideoPlayer(video?.src ?? video?.url);

  const { isPlaying } = useEvent(player, 'playingChange', { isPlaying: player.playing });

  return (
    <BottomModal
      useChildrenAsDirectChild
      closeModal={closeModal}
      title={video?.name}
      {...props}
      customSnapPoints={[90]}>
      <View className="flex-1">
        <View className="flex-1 m-20 border border-grey-border rounded-12">
          <View className="flex-1 rounded-12 overflow-hidden bg-grey-bgOne">
            <VideoView
              player={player}
              style={{ flex: 1 }}
              contentFit={'cover'}
              nativeControls={true}
              allowsFullscreen={false}
              allowsPictureInPicture={false}
            />
          </View>
        </View>
        <View className="items-center justify-center pb-[34px]">
          <Pressable
            onPress={() => {
              if (isPlaying) {
                player.pause();
                return;
              }
              player.play();
            }}>
            <CircledIcon className="p-16 bg-grey-bgOne">
              {isPlaying ? (
                <Pause size={wp(24)} variant='Bold' color={colors.black.placeholder} />
              ) : (
                <Play size={wp(24)} variant='Bold' color={colors.black.placeholder} />
              )}
            </CircledIcon>
          </Pressable>
        </View>
        {/* <View className="h-" /> */}
      </View>
    </BottomModal>
  );
};

export default ViewVideoModal;
