import { useEffect, useState } from 'react';
import { View } from 'react-native';
import Modal from 'react-native-modal';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { hp } from 'src/assets/utils/js';
import { BaseText } from 'src/components/ui';


interface Props {
  isEmbedded?: boolean;
  doneSteps: number;
  stepCount: number;
  show?: boolean;
}

const InstagramImportProgress: React.FC<Props> = ({
  doneSteps = 0,
  stepCount = 1,
  show,
  isEmbedded = false,
}) => {
  const [progress, setProgress] = useState(0);
  const progressWidth = useSharedValue(0);
  const insertTop = useSafeAreaInsets().top + hp(12);

  useEffect(() => {
    progressWidth.value = progress === 100 ? 100 : withSpring(progress);
  }, [progress]);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
    };
  });

  // Calculate progress based on doneSteps and stepCount
  useEffect(() => {
    if (stepCount === 0) {
      setProgress(0);
      return;
    }

    // Calculate the progress from completed steps
    let calculatedProgress = (doneSteps / stepCount) * 100;

    // Ensure progress doesn't exceed 100%
    calculatedProgress = Math.min(calculatedProgress, 100);

    setProgress(calculatedProgress);
  }, [doneSteps, stepCount]);

  const Main = () => (
    <View className="px-20 py-20 w-full ">
      <BaseText fontSize={15} type="heading">
        Import Progress
      </BaseText>
      <View className="flex-row w-full justify-between items-center mt-5">
        <BaseText fontSize={14} className="text-black-placeholder">
          Downloading instagram videos
        </BaseText>
        <BaseText type="heading" fontSize={15} className="text-center">{`${Math.floor(progress)}%`}</BaseText>
      </View>
      <Animated.View className={`rounded-full h-6 py-1 bg-primary-main mt-15`} style={progressStyle}></Animated.View>
    </View>
  );

  if (isEmbedded) return <Main />;

  return (
    <Modal
      avoidKeyboard={true}
      isVisible={show}
      onBackdropPress={() => {}}
      backdropColor={'#1E1E1E80'}
      useNativeDriverForBackdrop={true}
      onBackButtonPress={() => {}}
      style={[{ flex: 1, justifyContent: 'flex-start', margin: 0 }]}>
      <View style={[{ justifyContent: 'flex-end', marginTop: insertTop }]} className="bg-white m-2.5 rounded-lg">
        <Main />
      </View>
    </Modal>
  );
};

export default InstagramImportProgress;
