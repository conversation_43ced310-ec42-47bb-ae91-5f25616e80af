import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { BaseText } from 'src/components/ui';
import InstagramImportProgress from './instagram-import-progress';
import { ProgressStep } from 'src/hooks/useProgess';

// Example usage of the rewritten InstagramImportProgress component
const InstagramImportProgressUsage: React.FC = () => {
  const [doneSteps, setDoneSteps] = useState(0);
  const [showProgress, setShowProgress] = useState(false);

  // Example steps with different states
  const steps: ProgressStep[] = [
    {
      id: '1',
      title: 'Connecting to Instagram',
      isLoading: false,
      complete: true,
      progress: 100,
    },
    {
      id: '2', 
      title: 'Fetching media list',
      isLoading: true,
      complete: false,
      progress: 75, // Specific progress for current step
    },
    {
      id: '3',
      title: 'Downloading videos',
      isLoading: false,
      complete: false,
      progress: undefined,
    },
    {
      id: '4',
      title: 'Processing media',
      isLoading: false,
      complete: false,
      progress: undefined,
    },
    {
      id: '5',
      title: 'Finalizing import',
      isLoading: false,
      complete: false,
      progress: undefined,
    },
  ];

  const stepCount = steps.length;

  // Simulate progress advancement
  const simulateProgress = () => {
    setShowProgress(true);
    setDoneSteps(0);

    // Simulate step progression
    const interval = setInterval(() => {
      setDoneSteps(prev => {
        if (prev >= stepCount - 1) {
          clearInterval(interval);
          setTimeout(() => setShowProgress(false), 2000); // Hide after completion
          return stepCount;
        }
        return prev + 1;
      });
    }, 2000);
  };

  const resetProgress = () => {
    setDoneSteps(0);
    setShowProgress(false);
  };

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <BaseText fontSize={18} weight="bold" className="mb-20">
        Instagram Import Progress Examples
      </BaseText>

      {/* Control Buttons */}
      <View className="mb-30">
        <TouchableOpacity
          onPress={simulateProgress}
          style={{
            backgroundColor: '#007AFF',
            padding: 15,
            borderRadius: 8,
            marginBottom: 10,
            alignItems: 'center',
          }}
        >
          <BaseText style={{ color: 'white', fontWeight: 'bold' }}>
            Start Import Simulation
          </BaseText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={resetProgress}
          style={{
            backgroundColor: '#FF3B30',
            padding: 15,
            borderRadius: 8,
            alignItems: 'center',
          }}
        >
          <BaseText style={{ color: 'white', fontWeight: 'bold' }}>
            Reset Progress
          </BaseText>
        </TouchableOpacity>
      </View>

      {/* Progress Info */}
      <View className="mb-20 p-15 bg-gray-100 rounded-lg">
        <BaseText fontSize={14} weight="semiBold" className="mb-5">
          Current State:
        </BaseText>
        <BaseText fontSize={12} className="mb-2">
          Done Steps: {doneSteps} / {stepCount}
        </BaseText>
        <BaseText fontSize={12} className="mb-2">
          Progress: {Math.round((doneSteps / stepCount) * 100)}%
        </BaseText>
        <BaseText fontSize={12}>
          Show Modal: {showProgress ? 'Yes' : 'No'}
        </BaseText>
      </View>

      {/* Embedded Progress Example */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          Embedded Progress:
        </BaseText>
        <View className="border border-gray-300 rounded-lg">
          <InstagramImportProgress
            isEmbedded={true}
            steps={steps}
            doneSteps={doneSteps}
            stepCount={stepCount}
            show={true}
          />
        </View>
      </View>

      {/* Modal Progress */}
      <InstagramImportProgress
        isEmbedded={false}
        steps={steps}
        doneSteps={doneSteps}
        stepCount={stepCount}
        show={showProgress}
      />

      {/* Step Details */}
      <View>
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          Step Details:
        </BaseText>
        {steps.map((step, index) => (
          <View 
            key={step.id} 
            className={`p-10 mb-5 rounded-lg ${
              index < doneSteps ? 'bg-green-100' : 
              index === doneSteps ? 'bg-blue-100' : 'bg-gray-100'
            }`}
          >
            <BaseText fontSize={12} weight="medium">
              {index + 1}. {step.title}
            </BaseText>
            <BaseText fontSize={10} className="text-gray-600">
              Status: {
                index < doneSteps ? 'Complete' :
                index === doneSteps ? 'In Progress' : 'Pending'
              }
              {step.progress !== undefined && ` (${step.progress}%)`}
            </BaseText>
          </View>
        ))}
      </View>
    </View>
  );
};

export default InstagramImportProgressUsage;

/*
Key improvements in the rewritten component:

1. **Proper State Management**: Uses doneSteps and stepCount to calculate progress
2. **Deterministic Progress**: Progress is calculated based on actual completion state
3. **No Intervals**: Removed complex interval logic that could cause memory leaks
4. **Cleaner Logic**: Simple calculation: (doneSteps / stepCount) * 100
5. **Step Progress Support**: Handles individual step progress when available
6. **Boundary Checks**: Ensures progress never exceeds 100%

Usage patterns:
- doneSteps: Number of completed steps (0 to stepCount)
- stepCount: Total number of steps
- steps[doneSteps]: Current step being processed
- Progress calculation: Base progress + current step progress
*/
