// Example showing how the new progress calculation works

interface ProgressExample {
  doneSteps: number;
  stepCount: number;
  currentStepProgress?: number;
  expectedProgress: number;
}

const progressExamples: ProgressExample[] = [
  // Basic examples
  {
    doneSteps: 0,
    stepCount: 5,
    expectedProgress: 0, // 0/5 = 0%
  },
  {
    doneSteps: 1,
    stepCount: 5,
    expectedProgress: 20, // 1/5 = 20%
  },
  {
    doneSteps: 3,
    stepCount: 5,
    expectedProgress: 60, // 3/5 = 60%
  },
  {
    doneSteps: 5,
    stepCount: 5,
    expectedProgress: 100, // 5/5 = 100%
  },

  // Examples with current step progress
  {
    doneSteps: 2,
    stepCount: 4,
    currentStepProgress: 50, // Current step is 50% done
    expectedProgress: 62.5, // (2/4 * 100) + (0.5/4 * 100) = 50% + 12.5% = 62.5%
  },
  {
    doneSteps: 1,
    stepCount: 3,
    currentStepProgress: 75, // Current step is 75% done
    expectedProgress: 58.33, // (1/3 * 100) + (0.75/3 * 100) = 33.33% + 25% = 58.33%
  },
];

// Function that matches the new component logic
function calculateProgress(doneSteps: number, stepCount: number, currentStepProgress?: number): number {
  if (stepCount === 0) return 0;

  const unitStepPercentage = 100.0 / stepCount;
  let calculatedProgress = (doneSteps / stepCount) * 100;

  if (currentStepProgress !== undefined) {
    const currentStepProgressContribution = (currentStepProgress / 100.0) * unitStepPercentage;
    calculatedProgress = (doneSteps * unitStepPercentage) + currentStepProgressContribution;
  }

  return Math.min(calculatedProgress, 100);
}

// Test the examples
console.log('Progress Calculation Examples:');
progressExamples.forEach((example, index) => {
  const calculated = calculateProgress(
    example.doneSteps, 
    example.stepCount, 
    example.currentStepProgress
  );
  
  console.log(`Example ${index + 1}:`);
  console.log(`  Done Steps: ${example.doneSteps}/${example.stepCount}`);
  console.log(`  Current Step Progress: ${example.currentStepProgress ?? 'N/A'}%`);
  console.log(`  Expected: ${example.expectedProgress}%`);
  console.log(`  Calculated: ${calculated.toFixed(2)}%`);
  console.log(`  Match: ${Math.abs(calculated - example.expectedProgress) < 0.01 ? '✅' : '❌'}`);
  console.log('');
});

/*
Key Changes Made to the Component:

OLD APPROACH:
- Used undefined variables (currentStep, intervalId, unitStepPercentage)
- Complex interval-based progress simulation
- Relied on step.isLoading and step.complete states
- Could cause memory leaks with intervals
- Progress calculation was inconsistent

NEW APPROACH:
- Uses doneSteps and stepCount props directly
- Simple, deterministic progress calculation
- No intervals or timers needed
- Progress = (doneSteps / stepCount) * 100
- Supports individual step progress when available
- Always bounded between 0-100%

USAGE:
// Before (broken):
<InstagramImportProgress steps={steps} />

// After (working):
<InstagramImportProgress 
  steps={steps}
  doneSteps={2}        // 2 steps completed
  stepCount={5}        // 5 total steps
  show={true}
/>

PROGRESS CALCULATION:
- Base progress: (doneSteps / stepCount) * 100
- If current step has progress: add (currentStepProgress / 100) * (100 / stepCount)
- Always Math.min(result, 100) to prevent overflow
*/

export { calculateProgress, progressExamples };
