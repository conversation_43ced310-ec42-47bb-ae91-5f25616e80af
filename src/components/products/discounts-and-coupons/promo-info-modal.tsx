import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import { PromoCardType } from './discounts-and-coupons/promo-card';
import { Box1, Calendar, DiscountShape, PercentageCircle, Status } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../ui';
import ProductInfoRow from './product-info-row';
import { ReactNode } from 'react';
import { ArrowUpRight } from '../ui/icons';

interface PromoInfoModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  type: PromoCardType;
}

const OtherInfo = ({
  icon,
  title,
  value,
  valueElement,
}: {
  icon: ReactNode;
  title: string;
  value?: string;
  valueElement?: ReactNode;
}) => {
  return (
    <Row className="pb-15">
      <CircledIcon iconBg="bg-grey-bgOne" className="p-6">
        {icon}
      </CircledIcon>
      <BaseText numberOfLines={1} fontSize={12} classes="flex-1 text-black-main mx-8">
        {title}
      </BaseText>
      {valueElement ? (
        valueElement
      ) : (
        <BaseText fontSize={13} weight={'medium'}>
          {value}
        </BaseText>
      )}
    </Row>
  );
};

const PromoInfoModal = ({ closeModal, type = PromoCardType.COUPONS, ...props }: PromoInfoModalProps) => {
  const variantsProperty = {
    [PromoCardType.COUPONS]: {
      modalTitle: 'Coupon Details',
      caption: 'Coupon Code (Tap to copy)',
      icon: (
        <CircledIcon iconBg={'bg-accentRed-main'}>
          <PercentageCircle variant={'Bold'} size={wp(20)} color={colors.white} />
        </CircledIcon>
      ),
    },
    [PromoCardType.DISCOUNTS]: {
      modalTitle: 'Discount Details',
      caption: 'Discount Name',
      icon: (
        <CircledIcon iconBg={'bg-accentGreen-main'}>
          <DiscountShape variant={'Bold'} size={wp(20)} color={colors.white} />
        </CircledIcon>
      ),
    },
  };

  return (
    <BottomModal {...props} closeModal={closeModal} buttons={[{ text: 'Load More' }]} title={'Referred Friends'}>
      <View className="px-20">
        <Row className=" py-15">
          <View>
            <BaseText classes="text-black-muted">{variantsProperty[type].caption}</BaseText>
            <BaseText fontSize={15} weight={'bold'} type={'heading'}>
              Back To School
            </BaseText>
          </View>
          {variantsProperty[type].icon}
        </Row>
        <ProductInfoRow
          className="border-y border-grey-border mt-15 py-15"
          leftItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                <PercentageCircle variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
              </CircledIcon>
            ),
            value: '15%',
            title: 'Discount Percent',
          }}
          rightItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentOrange-pastel'}>
                <PercentageCircle variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
              </CircledIcon>
            ),
            value: 'NGN 2,000',
            title: 'Cap Amount',
          }}
        />
        <View className="mt-15">
          <OtherInfo
            title={'Status'}
            icon={<Status size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <View className="flex-row mt-5 px-10 py-4 bg-grey-bgOne rounded-full self-start">
                <BaseText fontSize={10} classes="text-accentGreen-main font-interSemiBold">
                  ACTIVE
                </BaseText>
              </View>
            }
          />
          <OtherInfo
            title={'Start & End Date'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={'16 Aug - 30 Aug'}
          />
          <OtherInfo
            title={'Expiry Date'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={'30 Aug'}
          />
          <OtherInfo
            title={'Min. Purchase Amount'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={'NGN 50,000.00'}
          />
          <OtherInfo
            title={'Quantity Left'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={'4 left'}
          />
          <OtherInfo
            title={'Number of products'}
            icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
            value={'16 Aug - 30 Aug'}
            valueElement={
              <WhiteCardBtn
                className="py-0 px-0"
                icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}>
                10 Products
              </WhiteCardBtn>
            }
          />
        </View>
      </View>
    </BottomModal>
  );
};

export default PromoInfoModal;
