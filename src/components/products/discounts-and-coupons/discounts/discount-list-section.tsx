import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { Alert, RefreshControl, View } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import ProductCard from '@/components/products/storefront-products/product-card';
import PromoCard, { PromoCardType, PromoSkeletalLoader } from '@/components/products/discounts-and-coupons/promo-card';
import FAB from '@/components/ui/buttons/fab';
import { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { mockProducts } from '@/constant/mock-data';
import { Edit2, PercentageCircle, Trash } from 'iconsax-react-native/src';
import {
  dateDuration,
  delay,
  ensureUniqueItems,
  hideLoader,
  showLoader,
  showSuccess,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import colors from '@/theme/colors';
import useModals from '@/hooks/use-modals';
import Row from '@/components/ui/row';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { BaseText } from '@/components/ui';
import useAuthContext from '@/contexts/auth/auth-context';
import usePagination from '@/hooks/use-pagination';
import { ResponseWithoutPagination, ResponseWithPagination, useApi } from '@/hooks/use-api';
import { format } from 'date-fns';
import Toast from 'react-native-toast-message';
import DiscountDetailsModal from './discount-details-modal';
import EditDiscountModal from './edit-discount-modal';
import {
  DELETE_DISCOUNT,
  GET_DISCOUNTS,
  UPDATE_DISCOUNT,
  PaginateSearchParams,
  UpdateDiscountParams,
  DiscountItemInterface,
  StoreInterface,
} from 'catlog-shared';

interface DiscountListSectionProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  discounts: DiscountItemInterface[];
  setDiscounts: React.Dispatch<React.SetStateAction<DiscountItemInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading: boolean;
  isLoading: boolean;
  total_pages: number;
  currentPage: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  fromFiltersPage?: boolean;
}

export interface DiscountsResponse
  extends ResponseWithPagination<{
    store: StoreInterface;
    items: DiscountItemInterface[];
    total_pages: number;
    total: number;
    page: number;
  }> {}

const PER_PAGE = 10;
const DiscountListSection: React.FC<DiscountListSectionProps> = ({
  scrollHandler,
  discounts,
  pullToRefreshFunc,
  setDiscounts,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  fromFiltersPage,
}) => {
  const [activeDiscount, setActiveDiscount] = useState<DiscountItemInterface>({} as DiscountItemInterface);
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['edit_discount', 'discount']);

  const updateDiscountMutation = useApi<UpdateDiscountParams, any>({
    key: 'update-discount',
    apiFunction: UPDATE_DISCOUNT,
    method: 'PUT',
  });
  const deleteDiscountMutation = useApi({ key: 'delete-discount', apiFunction: DELETE_DISCOUNT, method: 'DELETE' });

  const deleteDiscount = async (id: string) => {
    if (modals.discount) {
      toggleModal('discount', false);
    }

    Alert.alert(
      'Delete Discount',
      "You won't see this discount again if you delete it, set it to inactive instead if it is out of use.",
      [
        { text: 'Delete', onPress: () => deleteFunction(), style: 'destructive' },
        { text: 'Cancel', onPress: () => {} },
      ],
    );

    const deleteFunction = async () => {
      const [response, error] = await deleteDiscountMutation.makeRequest({
        id: id,
      });

      if (response && discounts) {
        setDiscounts(updateOrDeleteItemFromList(discounts, 'id', id, null));
        Toast.show({ type: 'success', text1: 'Discount deleted' });
      }
    };
  };

  const openDiscountInfo = (item: DiscountItemInterface) => {
    setActiveDiscount(item);
    toggleModal('discount');
  };

  const openEditDiscount = (item: DiscountItemInterface) => {
    toggleModal('discount', false);
    setActiveDiscount(item);
    setTimeout(() => {
      toggleModal('edit_discount');
    }, 600);
  };

  const handleToggleDiscountActive = async (item: DiscountItemInterface, value: boolean) => {
    showLoader('Updating discount availability status');
    const [response, error] = await updateDiscountMutation.makeRequest({ id: item.id, active: value });
    hideLoader();
    await delay(600);
    if (response && discounts) {
      setDiscounts(updateOrDeleteItemFromList(discounts, 'id', item.id, { ...item, active: value }));
      showSuccess('Discount availability updated successfully');
    }

    if (error) {
      Toast.show({ type: 'error', text1: error?.body?.message });
    }
  };

  const moreOptionFunc = (item: DiscountItemInterface) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Discount'} />
      ),
      title: 'Edit Discount',
      onPress: () => openEditDiscount(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label={'Delete Discount'} />
      ),
      title: 'Delete Discount',
      onPress: () => deleteDiscount(item?.id),
    },
    {
      optionElement: (
        <Row spread={false}>
          <CustomSwitch value={item.active} onValueChange={value => handleToggleDiscountActive(item, value)} />
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Active
          </BaseText>
        </Row>
      ),
      title: 'Active',
      onPress: () => {},
    },
  ];

  return (
    <View style={{ flex: 1 }}>
      <Animated.FlatList
        data={discounts}
        onScroll={scrollHandler}
        ListEmptyComponent={() => (
          <View className="flex-1">
            {discounts?.length === 0 && isLoading ? (
              <PromoSkeletalLoader />
            ) : (
              <EmptyState
                showBtn={!fromFiltersPage}
                icon={<PercentageCircle variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                btnText={'Create Discount'}
                text={'No Discounts to show'}
                onPressBtn={() => navigation.navigate('CreateDiscount')}
              />
            )}
          </View>
        )}
        refreshControl={
          pullToRefreshFunc ? <RefreshControl refreshing={false} onRefresh={pullToRefreshFunc} /> : undefined
        }
        className="flex-1 px-20 pb-40"
        contentContainerStyle={{ flexGrow: 1 }}
        ItemSeparatorComponent={() => <View className="border-b border-b-grey-border my-15" />}
        renderItem={({ item, index }) => (
          <PromoCard
            title={item?.label}
            description={`${item?.percentage}% OFF`}
            leftText={dateDuration(item?.start_date, item?.end_date)}
            type={PromoCardType.DISCOUNTS}
            moreOptions={moreOptionFunc(item)}
            onPress={() => openDiscountInfo(item)}
            index={index}
            key={item.id}
          />
        )}
        ListFooterComponent={
          <View style={{ marginBottom: 120 }}>
            {discounts?.length! > 0 && isLoading && (
              <View className="mt-20">
                <PromoSkeletalLoader />
              </View>
            )}
          </View>
        }
        onEndReached={() => {
          if (!isLoading && discounts?.length! > 0 && currentPage < total_pages) {
            goNext(total_pages);
          }
        }}
      />
      <FAB onPress={() => navigation.navigate('CreateDiscount')} />
      <DiscountDetailsModal
        isVisible={modals.discount}
        activeDiscount={activeDiscount}
        deleteDiscount={deleteDiscount}
        closeModal={() => toggleModal('discount', false)}
        editDiscount={item => openEditDiscount(item)}
      />
      <EditDiscountModal
        isVisible={modals.edit_discount}
        activeDiscount={activeDiscount}
        closeModal={() => toggleModal('edit_discount', false)}
        onPressSave={() => {}}
        setDiscounts={setDiscounts}
      />
    </View>
  );
};

export default DiscountListSection;
