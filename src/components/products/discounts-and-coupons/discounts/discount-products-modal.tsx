import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../../../ui/modals/bottom-modal';
import { ProductCardScreenType } from '../../storefront-products/products-list';
import { BaseText } from '../../../ui';
import ProductsList from '../../storefront-products/products-list';
import { ProductItemInterface } from 'catlog-shared';


interface DiscountsProductsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  products: ProductItemInterface[];
  isLoading?: boolean;
}

const DiscountProductsModal = ({ closeModal, isLoading, products, ...props }: DiscountsProductsModalProps) => {
  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      // buttons={[{ text: 'Edit Discount' }]}
      title={'Discount Products'}
      containerStyle={{ flex: 1 }}
      modalStyle={{flex: 1}}
      contentContainerClass="flex-1">
      <View className="flex-1 px-10">
        <ProductsList
          products={products}
          screen={ProductCardScreenType.DISCOUNT}
          isLoading={isLoading}
          productCardProps={{ disabled: true, showRightElement: false }}
        />
      </View>
    </BottomModal>
  );
};

export default DiscountProductsModal;
