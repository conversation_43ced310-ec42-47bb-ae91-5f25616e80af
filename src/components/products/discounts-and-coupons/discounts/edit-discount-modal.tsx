import { ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../../../ui/modals/bottom-modal';
import DiscountForm, { DiscountFormData, DiscountFormScreen, discountValidationSchema } from './discount-form';
import { BaseText, Row } from '../../../ui';
import CustomSwitch from '../../../ui/inputs/custom-switch';
import { useFormik } from 'formik';
import { useEffect } from 'react';
import { useApi } from '@/hooks/use-api';
import { runOnJS, runOnUI } from 'react-native-reanimated';
import { delay, hp, showError, showSuccess, updateOrDeleteItemFromList, wp } from 'src/assets/utils/js';
import { DiscountItemInterface, UPDATE_DISCOUNT, UpdateDiscountParams } from 'catlog-shared';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import { BottomSheetView } from '@gorhom/bottom-sheet';

interface EditDiscountModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  onPressSave?: VoidFunction;
  activeDiscount?: DiscountItemInterface;
  setDiscounts?: React.Dispatch<React.SetStateAction<DiscountItemInterface[]>>;
}

const EditDiscountModal = ({
  closeModal,
  onPressSave,
  setDiscounts,
  activeDiscount,
  ...props
}: EditDiscountModalProps) => {
  const updateDiscountRequest = useApi({ key: 'update-discount', apiFunction: UPDATE_DISCOUNT, method: 'PUT' });

  const form = useFormik<DiscountFormData>({
    initialValues: {
      id: activeDiscount?.id!,
      label: activeDiscount?.label ?? '',
      active: activeDiscount?.active ?? false,
      percentage: activeDiscount?.percentage ?? 0,
      start_date: activeDiscount?.start_date ? new Date(activeDiscount?.start_date) : null,
      end_date: activeDiscount?.end_date ? new Date(activeDiscount?.end_date) : null,
      items: activeDiscount?.items!,
      discount_cap: activeDiscount?.discount_cap,
      duration: activeDiscount?.end_date ? 'date' : 'till-deleted',
    },
    validationSchema: discountValidationSchema,
    enableReinitialize: true, //todo: this would automatically refresh the initial data
    onSubmit: async values => {
      const { duration, discount_cap, ...rest } = values;

      const requestData = {
        ...rest,
        id: rest.id!,
        percentage: Number(values.percentage),
        start_date: values.start_date?.toISOString() ?? undefined,
        end_date: values.end_date?.toISOString() ?? undefined,
        ...(values?.discount_cap && String(values?.discount_cap).length > 0
          ? { discount_cap: Number(values?.discount_cap) }
          : { discount_cap: null }),
      };

      const [response, error] = await updateDiscountRequest.makeRequest(requestData);
      if (response) {
        eventEmitter.emit('discountEdit', { discount: response.data, id: values.id });
        setDiscounts?.(prev => updateOrDeleteItemFromList(prev, 'id', activeDiscount?.id, requestData));
        closeModal();
        await delay(700);
        showSuccess('Discount updated successfully');
      }
      if (error) {
        showError(error);
        // console.log(error.message);
      }
      return [response, error];
    },
  });

  const handleSaveUpdates = () => {
    form.handleSubmit();
  };

  return (
    <BottomModal
      {...props}
      onModalShow={() => form.resetForm()}
      closeModal={closeModal}
      enableDynamicSizing
      useChildrenAsDirectChild
      enableSnapPoints={false}
      buttons={[
        {
          text: 'Save Updates',
          onPress: handleSaveUpdates,
          isLoading: updateDiscountRequest?.isLoading,
          loadingText: 'Updating Discount...',
        },
      ]}
      title="Edit Discount"
      headerAddon={
        <Row className="justify-start">
          <CustomSwitch value={form.values.active} onValueChange={value => form.setFieldValue('active', value)} />
          <BaseText fontSize={12} classes="ml-8">
            Active
          </BaseText>
        </Row>
      }>
      <BottomSheetView
        // className="mt-20 px-20"
        style={{ paddingHorizontal: wp(20), marginTop: hp(10) }}
        >
        <DiscountForm form={form} useBottomSheetInput />
      </BottomSheetView>
    </BottomModal>
  );
};

export default EditDiscountModal;
