import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../../../ui/modals/bottom-modal';
import SectionContainer from '../../../ui/section-container';
import { PromoCardType } from '../promo-card';
import { Box1, Calendar, DiscountShape, DollarCircle, PercentageCircle, Status } from 'iconsax-react-native/src';
import { dateDuration, toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../../../ui';
import ProductInfoRow from '../../product-info-row';
import { ReactNode } from 'react';
import { ArrowUpRight } from '../../../ui/icons';
import InfoRow from '../../../ui/others/info-row';
import { ButtonVariant, TextColor } from '../../../ui/buttons/button';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import DiscountProductsModal from './discount-products-modal';
import { mockProducts } from '@/constant/mock-data';
import useModals from '@/hooks/use-modals';
import { useApi } from '@/hooks/use-api';
import { DiscountItemInterface, GET_DISCOUNT_ITEMS } from 'catlog-shared';

interface DiscountDetailsModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  editDiscount: (item: DiscountItemInterface) => void;
  deleteDiscount: (id: string) => Promise<void>;
  activeDiscount?: DiscountItemInterface;
}

const DiscountDetailsModal = ({
  activeDiscount,
  deleteDiscount,
  closeModal,
  editDiscount,
  ...props
}: DiscountDetailsModalProps) => {
  const { modals, toggleModal } = useModals(['products']);

  const getDiscountItemsRequest = useApi(
    {
      key: 'get-discount-items',
      apiFunction: GET_DISCOUNT_ITEMS,
      method: 'GET',
      autoRequest: false,
      onSuccess(response) {
        // console.log(response.data[0]);
      },
    },
    {
      id: activeDiscount?.id!,
    },
  );

  const getDiscountItems = async () => {
    await getDiscountItemsRequest.makeRequest({
      id: activeDiscount?.id!,
    });
  };

  const active = checkIfExpired(activeDiscount?.end_date) === false ? activeDiscount.active : false;
  const inActiveText = checkIfExpired(activeDiscount?.end_date) === false ? 'INACTIVE' : 'EXPIRED';

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={[
        {
          text: 'Delete Discount',
          variant: ButtonVariant.LIGHT,
          textColor: TextColor.NEGATIVE,
          onPress: () => deleteDiscount(activeDiscount?.id!),
        },
        { text: 'Edit Discount', onPress: () => editDiscount(activeDiscount!) },
      ]}
      title={'Discount Details'}>
      <View className="px-20 border-t border-grey-border mt-15">
        <Row className="py-20">
          <View>
            <BaseText classes="text-black-muted" fontSize={12}>
              Discount Label
            </BaseText>
            <BaseText type={'heading'} textTransform="capitalize" fontSize={16}>
              {activeDiscount?.label}
            </BaseText>
          </View>
          <CircledIcon iconBg={'bg-accentGreen-main'}>
            <DiscountShape variant={'Bold'} size={wp(20)} color={colors.white} />
          </CircledIcon>
        </Row>
        <ProductInfoRow
          className="border-y border-grey-border py-15"
          leftItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                <PercentageCircle variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />
              </CircledIcon>
            ),
            value: activeDiscount?.percentage ? `${activeDiscount?.percentage}%` : '-',
            title: 'Percentage',
          }}
          rightItem={{
            icon: (
              <CircledIcon iconBg={'bg-accentOrange-pastel'}>
                <DollarCircle variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
              </CircledIcon>
            ),
            value: activeDiscount?.discount_cap ? `${toCurrency(activeDiscount?.discount_cap)}` : '-',
            title: 'Cap Amount',
          }}
        />
        <View className="mt-15">
          <InfoRow
            title={'Status'}
            icon={<Status size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <StatusPill
                className="bg-grey-bgOne"
                statusType={active ? StatusType.SUCCESS : StatusType.DANGER}
                title={active ? 'ACTIVE' : inActiveText}
              />
            }
          />
          <InfoRow
            title={'Start & End Date'}
            icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
            value={dateDuration(activeDiscount?.start_date!, activeDiscount?.end_date)}
          />
          <InfoRow
            title={'Number of products applied'}
            icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
            valueElement={
              <WhiteCardBtn
                className="py-0 px-0 self-center"
                onPress={() => toggleModal('products')}
                icon={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}>
                {activeDiscount?.items?.length} Products
              </WhiteCardBtn>
            }
          />
        </View>
      </View>
      <DiscountProductsModal
        products={getDiscountItemsRequest?.response?.data}
        isLoading={getDiscountItemsRequest?.isLoading}
        onModalShow={getDiscountItems}
        isVisible={modals.products}
        closeModal={() => toggleModal('products', false)}
      />
    </BottomModal>
  );
};

export default DiscountDetailsModal;

const checkIfExpired = (date?: string) => {
  if (!date) return false;

  const today = new Date().getTime();

  return today - new Date(date).getTime() > 24 * 60 * 60 * 1000;
};
