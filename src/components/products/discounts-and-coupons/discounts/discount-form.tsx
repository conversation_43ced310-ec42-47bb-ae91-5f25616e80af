import React from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import Pressable from '../../../ui/base/pressable';
import { BaseText, CircledIcon } from '../../../ui';
import { Add, Edit2 } from 'iconsax-react-native/src';
import { formatDate, getFieldvalues, hp, wp } from '@/assets/utils/js';
import Input from '../../../ui/inputs/input';
import SelectDropdown from '../../../ui/inputs/select-dropdown';
import { useMemo } from 'react';
import { ArrowRight } from '../../../ui/icons';
import CalendarModal from '../../../ui/modals/calendar-modal';
import useModals from '@/hooks/use-modals';
import { FormikProps } from 'formik';
import * as Yup from 'yup';
import { useApi } from '@/hooks/use-api';
import { ProductsResponse } from '@/screens/products/storefront';
import useAuthContext from '@/contexts/auth/auth-context';
import Accordion from '@/components/ui/others/accordion';
import { GET_ITEMS, GetItemsParams } from 'catlog-shared';
import SelectSpecificProductsModal from '../../storefront-products/select-specific-product-modal';
import MoneyInput from 'src/components/ui/inputs/money-input';

export interface DiscountFormMethod {
  submit: VoidFunction;
}

export interface DiscountFormData {
  id?: string;
  label: string;
  percentage: number;
  discount_cap?: number;
  duration: string;
  items: string[];
  end_date: Date | null;
  start_date: Date | null;
  active: boolean;
}

interface DiscountFormProps {
  // screen: DiscountFormScreen;
  form: FormikProps<DiscountFormData>;
  useBottomSheetInput?: boolean;
}

export enum DiscountFormScreen {
  CREATE_SCREEN = 'Create Screen',
  EDIT_SCREEN = 'Edit Screen',
}

const DiscountForm = ({useBottomSheetInput, ...props}: DiscountFormProps) => {
  const { form, ...rest } = props;
  const { store } = useAuthContext();
  const selectedProducts = form.values.items ?? [];
  const selectDurationOption = form?.values?.duration ?? null;

  const { modals, toggleModal } = useModals(['selectProducts', 'calender']);

  const getProductsRequest = useApi<GetItemsParams, ProductsResponse>(
    {
      key: 'fetch-products',
      apiFunction: GET_ITEMS,
      method: 'GET',
    },
    {
      filter: { store: store?.id, search: '' },
      page: 1,
      per_page: Number.MAX_SAFE_INTEGER,
      sort: 'asc',
    },
  );

  const products = getProductsRequest?.response?.data?.items ?? [];

  const firstSelectedProduct = useMemo(() => {
    if (selectedProducts.length > 0 && products.length > 0) {
      return products.find(({ id }) => id === selectedProducts[0]);
    }
    return null;
  }, [selectedProducts[0], products]);

  const onSelectDurationOption = (value: string) => {
    form.setFieldValue('duration', value);
    if (value === 'date') {
      setTimeout(() => {
        toggleModal('calender');
      }, 600);
      return;
    }
    form.setFieldValue('start_date', new Date());
    form.setFieldValue('end_date', null);
  };

  return (
    <View>
      <View>
        <Pressable
          className={'flex flex-row py-10 px-15 items-center justify-between rounded-12 bg-grey-bgOne'}
          onPress={() => toggleModal('selectProducts')}>
          {selectedProducts.length > 0 ? (
            <BaseText fontSize={12} weight="medium">
              <BaseText fontSize={12} classes="text-black-placeholder" weight="medium">
                Apply to
              </BaseText>{' '}
              {firstSelectedProduct && (
                <>
                  {firstSelectedProduct.name}
                  {selectedProducts.length > 1 &&
                    ` & ${selectedProducts.length - 1} other${selectedProducts.length > 2 ? 's' : ''}`}
                </>
              )}
              {!firstSelectedProduct && <>{selectedProducts.length} products</>}
            </BaseText>
          ) : (
            <BaseText fontSize={12} classes="text-primary-main" weight="medium">
              Add Products
            </BaseText>
          )}
          <View className="rounded-full bg-white p-6 flex items-center justify-center">
            {selectedProducts.length > 0 ? (
              <Edit2 size={wp(18)} strokeWidth={2} color={colors.primary.main} />
            ) : (
              <Add size={wp(18)} strokeWidth={2} color={colors.primary.main} />
            )}
          </View>
        </Pressable>
        {form.errors.items && (
          <View className="mt-5">
            <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
              {form.errors.items}
            </BaseText>
          </View>
        )}
        <Input label={'Discount Label'} containerClasses="mt-20" useBottomSheetInput={useBottomSheetInput} {...getFieldvalues('label', form)} />
        <Input
          label={'Discount Percent (No more than 100)'}
          containerClasses="mt-15"
          useBottomSheetInput={useBottomSheetInput}
          {...getFieldvalues('percentage', form, "number",true)}
          keyboardType="numeric"
        />
        <Accordion title={'Maximum amount allowed for this discount'}>
          <MoneyInput label={'Discount Cap Amount (Optional)'} useBottomSheetInput={useBottomSheetInput} {...getFieldvalues('discount_cap', form)} />
        </Accordion>
        <View className="mt-15">
          <SelectDropdown
            selectedItem={selectDurationOption!}
            onPressItem={onSelectDurationOption}
            items={discountDurationOptions}
            label={'Discount Duration'}
          />
          {selectDurationOption !== null && form.values?.start_date && form.values?.end_date && (
            <BaseText fontSize={12} classes="text-black-placeholder mt-10">
              {formatDate(form.values?.start_date)} - {formatDate(form.values.end_date)}
            </BaseText>
          )}
          {form.errors.start_date && (
            <View className="mt-5">
              <BaseText fontSize={10} weight={'medium'} classes="text-accentRed-main">
                {form.errors.start_date as string}
              </BaseText>
            </View>
          )}
        </View>
        <CalendarModal
          isVisible={modals.calender}
          closeModal={() => toggleModal('calender', false)}
          startDate={form.values.start_date ? new Date(form.values.start_date) : undefined}
          endDate={form.values.end_date ? new Date(form.values.end_date) : undefined}
          onDateChange={(date, type) => {
            if (type === 'START_DATE') {
              form.setFieldValue('start_date', date);
            }
            if (type === 'END_DATE') {
              form.setFieldValue('end_date', date);
            }
          }}
        />
      </View>
      <SelectSpecificProductsModal
        products={products}
        isVisible={modals.selectProducts}
        closeModal={() => toggleModal('selectProducts', false)}
        onPressContinue={() => toggleModal('selectProducts', false)}
        loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
        getProductsRequest={getProductsRequest}
        selectedProducts={selectedProducts}
        setSelectedProducts={(products: string[]) => form.setFieldValue('items', products)} //todo: @silas take a look at this
      />
    </View>
  );
};

const discountDurationOptions = [
  {
    value: 'till-deleted',
    label: 'Run until deleted',
  },
  {
    value: 'date',
    label: 'Select specific time',
    rightElement: (
      <CircledIcon iconBg="bg-white" className="p-5">
        <ArrowRight size={wp(16)} strokeWidth={2} currentColor={colors.primary.main} />
      </CircledIcon>
    ),
  },
];

export const discountValidationSchema = Yup.object().shape({
  label: Yup.string().required('Label is required'),
  percentage: Yup.number()
    .required('Percentage is required')
    .min(1, 'Percentage must be greater than 0')
    .integer('Percentage must be an integer')
    .max(100),
  discount_cap: Yup.number().min(1, 'Price must be greater than 0').integer('Price limit must be an integer'),
  duration: Yup.string().required('Duration is required'),
  items: Yup.array()
    .of(Yup.string())
    .min(1, 'Please select at least one product')
    .required('Please select at least one product'),
  end_date: Yup.date().when('duration', {
    is: 'date',
    then: schema => schema.required('End date is required'),
    otherwise: schema => schema.nullable(),
  }),
  start_date: Yup.date().required('Start date is required'),
});

export default DiscountForm;
