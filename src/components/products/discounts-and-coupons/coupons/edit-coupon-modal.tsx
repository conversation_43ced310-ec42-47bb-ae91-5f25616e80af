import { ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../../../ui/modals/bottom-modal';
import { BaseText, Row } from '../../../ui';
import CustomSwitch from '../../../ui/inputs/custom-switch';
import CouponForm, { CouponFormScreen, couponValidationSchema } from './coupon-form';
import AvoidKeyboard from '../../../ui/layouts/avoid-keyboard';
import useKeyboard from '@/hooks/use-keyboard';
import { useFormik } from 'formik';
import Toast from 'react-native-toast-message';
import { useApi } from '@/hooks/use-api';
import { runOnJS } from 'react-native-reanimated';
import { updateOrDeleteItemFromList } from 'src/assets/utils/js';
import { UPDATE_COUPON, UpdateCouponParams, CouponItemInterface } from 'catlog-shared';

interface EditCouponModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  activeCoupon: CouponItemInterface;
  setCoupons: React.Dispatch<React.SetStateAction<CouponItemInterface[]>>;
}

const EditCouponModal = ({ closeModal, activeCoupon, setCoupons, ...props }: EditCouponModalProps) => {
  const keyboardIsVisible = useKeyboard();
  const updateCouponRequest = useApi({ apiFunction: UPDATE_COUPON, method: 'PUT', key: 'update-coupon' });

  const form = useFormik<Omit<UpdateCouponParams, 'id'>>({
    initialValues: {
      coupon_code: activeCoupon?.coupon_code,
      type: activeCoupon?.type,
      active: activeCoupon?.active,
      end_date: activeCoupon?.end_date,
      percentage: activeCoupon?.percentage,
      discount_cap: activeCoupon?.discount_cap, //todo: @silas take a look at this
      quantity: activeCoupon?.quantity,
      discount_amount: activeCoupon?.discount_amount,
      minimum_order_amount: activeCoupon?.minimum_order_amount,
    },
    validationSchema: couponValidationSchema,
    enableReinitialize: true,
    onSubmit: async values => {
      const requestData = {
        ...values,
        id: activeCoupon.id,
        percentage: Number(values.percentage),
        quantity: Number(values.quantity),
        ...(values?.discount_amount && { discount_amount: Number(values?.discount_amount) }),
        ...(values?.minimum_order_amount && { minimum_order_amount: Number(values?.minimum_order_amount) }),
        ...(values?.discount_cap && String(values?.discount_cap).length > 0
          ? { discount_cap: Number(values?.discount_cap) }
          : { discount_cap: null }),
      };

      // const [response, error] = await login(values);
      const [response, error] = await updateCouponRequest.makeRequest(requestData);

      if (response) {
        closeModal();
        form.resetForm();
        Toast.show({ type: 'success', text1: response.message });
        setCoupons(prev => updateOrDeleteItemFromList(prev, 'id', activeCoupon.id, requestData));
      }

      if (error) {
        // setErrorText(error.message);
        Toast.show({ type: 'error', text1: error.body.message });
      }
    },
  });

  // useEffect(() => {
  //   form.setValues(activeCoupon);
  // }, [activeCoupon]);

  const handleSetFormData = () => {
    runOnJS(() => {
      form.setFieldValue('coupon_code', activeCoupon?.coupon_code);
      form.setFieldValue('active', activeCoupon?.active);
      form.setFieldValue('type', activeCoupon?.type);
      form.setFieldValue('percentage', activeCoupon?.percentage?.toString());
      form.setFieldValue('quantity', activeCoupon?.quantity?.toString());
      form.setFieldValue('discount_amount', activeCoupon?.discount_amount?.toString());
      form.setFieldValue('minimum_order_amount', activeCoupon?.minimum_order_amount?.toString());
      form.setFieldValue('end_date', activeCoupon?.end_date);
    })();
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      // onModalShow={handleSetFormData}
      containerStyle={keyboardIsVisible ? { flex: 1 } : undefined}
      contentContainerClass={keyboardIsVisible ? 'flex-1' : undefined}
      buttons={[{ text: 'Save Updates', onPress: () => form.handleSubmit(), isLoading: updateCouponRequest.isLoading }]}
      showButton={!keyboardIsVisible}
      title="Edit Coupon"
      headerAddon={
        <Row className="justify-start">
          <CustomSwitch value={form.values.active} onValueChange={value => form.setFieldValue('active', value)} />
          <BaseText fontSize={12} classes="ml-8">
            Availability
          </BaseText>
        </Row>
      }>
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <View className="px-20 pb-20">
          <CouponForm screen={CouponFormScreen.CREATE_SCREEN} form={form} />
        </View>
      </ScrollView>
    </BottomModal>
  );
};

export default EditCouponModal;
