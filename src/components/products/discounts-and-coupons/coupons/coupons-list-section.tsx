import { useNavigation } from '@react-navigation/native';
import { DELETE_COUPON, UPDATE_COUPON, CouponItemInterface, StoreInterface } from 'catlog-shared';
import { Edit2, TicketDiscount, Trash } from 'iconsax-react-native/src';
import React, { useState } from 'react';
import { Alert, View, RefreshControl } from 'react-native';
import Animated, { ScrollHandlerProcessed } from 'react-native-reanimated';
import Toast from 'react-native-toast-message';

import {
  delay,
  formatDate,
  hideLoader,
  hp,
  showLoader,
  showSuccess,
  toCurrency,
  updateOrDeleteItemFromList,
  wp,
} from '@/assets/utils/js';
import CouponDetailsModal from '@/components/products/discounts-and-coupons/coupons/coupon-details-modal';
import EditCouponModal from '@/components/products/discounts-and-coupons/coupons/edit-coupon-modal';
import PromoCard, { PromoCardType, PromoSkeletalLoader } from '@/components/products/discounts-and-coupons/promo-card';
import { BaseText } from '@/components/ui';
import FAB from '@/components/ui/buttons/fab';
import EmptyState from '@/components/ui/empty-states/empty-state';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import { OptionWithIcon } from '@/components/ui/more-options';
import Row from '@/components/ui/row';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';

interface CouponsListSectionProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  coupons: CouponItemInterface[];
  setCoupons: React.Dispatch<React.SetStateAction<CouponItemInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading: boolean;
  isLoading: boolean;
  total_pages: number;
  currentPage: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
}

export interface CouponResponse
  extends ResponseWithPagination<{
    store: StoreInterface;
    items: CouponItemInterface[];
    total_pages: number;
    total: number;
    page: number;
  }> {}

const CouponsListSection: React.FC<CouponsListSectionProps> = ({
  scrollHandler,
  coupons,
  pullToRefreshFunc,
  setCoupons,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
}) => {
  const [activeCoupon, setActiveCoupon] = useState<CouponItemInterface>({} as CouponItemInterface);
  const navigation = useNavigation();
  const { modals, toggleModal } = useModals(['edit_coupon', 'coupon']);

  const updateCouponRequest = useApi({ apiFunction: UPDATE_COUPON, method: 'PUT', key: 'update-coupon' });
  const deleteCouponRequest = useApi({ apiFunction: DELETE_COUPON, method: 'DELETE', key: 'delete-coupon' });

  const handleOpenEditCoupon = (item?: CouponItemInterface) => {
    if (item) {
      setActiveCoupon(item);
    }
    toggleModal('coupon', false);
    setTimeout(() => {
      toggleModal('edit_coupon');
    }, 600);
  };

  const handleCheckCouponInfo = (item: CouponItemInterface) => {
    setActiveCoupon(item);
    toggleModal('coupon');
  };

  const promptDelete = (id: string) => {
    Alert.alert(
      'Do you want to delete this Coupon?',
      'You won’t see this Coupon again if you delete it, set it to inactive instead if it is out of use.',
      [
        {
          text: 'Delete',
          onPress: () => handleDelete(id),
          style: 'destructive',
        },
        {
          text: 'Cancel',
          onPress: () => {},
          isPreferred: true,
        },
      ],
    );
  };

  const handleDelete = async (id: string) => {
    showLoader('Deleting Coupon');
    const [response, error] = await deleteCouponRequest.makeRequest({
      id,
    });
    hideLoader();
    await delay(600);

    if (response) {
      setCoupons(updateOrDeleteItemFromList(coupons, 'id', id, null));
      Toast.show({ type: 'success', text1: 'Coupon deleted successfully' });
    }
    if (error) {
      Toast.show({ type: 'error', text1: error?.message });
    }
  };

  const handleToggleCouponActive = async (item: CouponItemInterface, value: boolean) => {
    showLoader('Updating coupon availability status');
    const [response, error] = await updateCouponRequest.makeRequest({ ...item, active: value });
    hideLoader();
    await delay(600);

    if (response) {
      setCoupons(updateOrDeleteItemFromList(coupons, 'id', item.id, { ...item, active: value }));
      showSuccess('Coupon availability updated');
    }

    if (error) {
      Toast.show({ type: 'error', text1: error?.body?.message });
    }
  };

  const moreOptions = (item: CouponItemInterface) => [
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label="Edit Coupon" />
      ),
      title: 'Edit Coupon',
      onPress: () => handleOpenEditCoupon(item),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.black.placeholder} />} label="Delete Coupon" />
      ),
      title: 'Delete Coupon',
      onPress: () => promptDelete(item.id),
    },
    {
      optionElement: (
        <Row spread={false}>
          <CustomSwitch value={item.active} onValueChange={value => handleToggleCouponActive(item, value)} />
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Available
          </BaseText>
        </Row>
      ),
      title: 'Available',
      onPress: () => {},
    },
  ];

  return (
    <View style={{ flex: 1 }}>
      <Animated.FlatList
        data={coupons}
        refreshControl={
          pullToRefreshFunc ? <RefreshControl refreshing={false} onRefresh={pullToRefreshFunc} /> : undefined
        }
        ListEmptyComponent={() =>
          isLoading ? (
            <PromoSkeletalLoader />
          ) : (
            <View className="py-30">
              <EmptyState
                btnText="Create Coupons"
                icon={<TicketDiscount variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                text="No coupon to show"
                onPressBtn={() => navigation.navigate('CreateCoupon')}
              />
            </View>
          )
        }
        onScroll={scrollHandler}
        className="flex-1 px-20"
        contentContainerStyle={{ flexGrow: 1, paddingBottom: hp(100) }}
        ItemSeparatorComponent={() => <View className="border-b border-b-grey-border my-15" />}
        renderItem={({ item, index }) => (
          <PromoCard
            title={item?.coupon_code}
            description={item?.discount_amount ? toCurrency(item?.discount_amount) : `${item?.percentage}%`}
            leftText={item.end_date ? formatDate(item.end_date) : '-'}
            moreOptions={moreOptions(item)}
            type={PromoCardType.COUPONS}
            onPress={() => handleCheckCouponInfo(item)}
            index={index}
            key={item.id}
          /> //Todo: Figure out how to merge promocard & listitemcard because they're essentially the same
        )}
        onEndReached={() => {
          if (!isLoading && coupons?.length > 0 && currentPage < total_pages) {
            goNext(total_pages);
          }
        }}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {coupons.length > 0 && isLoading && (
              <View className="mt-20">
                <PromoSkeletalLoader />
              </View>
            )}
          </View>
        }
      />
      <FAB onPress={() => navigation.navigate('CreateCoupon')} />
      <CouponDetailsModal
        isVisible={modals.coupon}
        activeCoupon={activeCoupon}
        closeModal={() => toggleModal('coupon', false)}
        editCoupon={() => handleOpenEditCoupon()}
        deleteCoupon={() => {
          toggleModal('coupon', false);
          promptDelete(activeCoupon?.id);
        }}
      />
      <EditCouponModal
        isVisible={modals.edit_coupon}
        closeModal={() => toggleModal('edit_coupon', false)}
        onPressButton={() => {}}
        activeCoupon={activeCoupon}
        setCoupons={setCoupons}
      />
    </View>
  );
};

export default CouponsListSection;
