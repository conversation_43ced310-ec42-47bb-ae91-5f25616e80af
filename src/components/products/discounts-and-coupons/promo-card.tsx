import { Text, TouchableOpacity, View } from 'react-native';
import { ReactNode, useState } from 'react';
import { styled } from 'nativewind';
import { TextProps } from 'react-native';
import cx from 'classnames';
import { TouchableHighlightProps } from 'react-native';
import { BaseText, CircledIcon } from '../../ui';
import { BaseTextProps } from '../../ui/base/base-text';
import { Edit2, PercentageCircle, TicketDiscount, Trash } from 'iconsax-react-native/src';
import { getColorAlternates, wp } from '@/assets/utils/js';
import MoreOptions, { MoreOptionElementProps } from '../../ui/more-options';
import Row from '../../ui/row';
import Shimmer from '../../ui/shimmer';

export interface PromoCardProps extends Partial<TouchableHighlightProps> {
  onPress?: () => void;
  showBorder?: boolean;
  title: string;
  leftText?: string;
  type: PromoCardType;
  description?: string;
  moreOptions: MoreOptionElementProps[];
  index?: number;
}

export enum PromoCardType {
  COUPONS = 'Coupons',
  DISCOUNTS = 'Discounts',
}

const PromoCard = ({
  onPress,
  type,
  showBorder = false,
  moreOptions = [],
  title,
  leftText,
  description,
  index,
  ...props
}: PromoCardProps) => {
    const colorAlternate = getColorAlternates(index);

  const iconVariants = {
    [PromoCardType.COUPONS]: <TicketDiscount variant={'Bold'} size={wp(20)} color={colorAlternate.iconColor} />,
    [PromoCardType.DISCOUNTS]: <PercentageCircle variant={'Bold'} size={wp(20)} color={colorAlternate.iconColor} />,
  };

  return (
    <View>
      <TouchableOpacity
        className={`flex-row items-center ${showBorder && 'border-b border-b-grey-border py-15'}`}
        activeOpacity={0.8}
        onPress={onPress}
        {...props}>
        <CircledIcon style={{ backgroundColor: colorAlternate.bgColor }}>{iconVariants[type]}</CircledIcon>
        <View className={'mx-12'}>
          <Row className="mr-12">
            {title && (
              <BaseText fontSize={13} type="heading" classes="mr-4 text-black-secondary max-w-[125]" numberOfLines={1}>
                {title}
              </BaseText>
            )}
            {description && (
              <View className="flex-row px-6 py-4 bg-grey-bgOne rounded-full self-start">
                <BaseText fontSize={10} weight={'medium'} classes="leading-[16px] text-accentOrange-main">
                  {description}
                </BaseText>
              </View>
            )}
          </Row>
          {leftText && (
            <BaseText fontSize={11} weight="medium" classes="text-black-muted mt-5">
              {leftText}
            </BaseText>
          )}
        </View>
        <View className={'flex-1 ml-12 items-end self-stretch justify-between'}>
          <MoreOptions options={moreOptions} />
        </View>
      </TouchableOpacity>
      {/* <EditDiscountModal isVisible={editDiscountModalVisible} closeModal={() => setEditDiscountModalVisible(false)} /> */}
    </View>
  );
};

interface SkeletonProps {
  showBorder?: boolean;
}

export const PromoCardSkeleton = ({ showBorder = true }: SkeletonProps) => {
  return (
    <View className={`flex-row items-center ${showBorder && 'border-b border-b-grey-border py-15'}`}>
      <Shimmer {...{ height: 50, width: 50, borderRadius: 100 }} />
      <View className={'mx-12'}>
        <Row className="mr-12 mb-10">
          <Shimmer {...{ height: 15, width: 90, borderRadius: 50 }} className="mr-2" />
          <Shimmer {...{ height: 15, width: 30, borderRadius: 50 }} />
        </Row>
        <Shimmer {...{ height: 10, width: 150, borderRadius: 50 }} />
      </View>
    </View>
  );
};

export const PromoSkeletalLoader = () => {
  return (
    <View>
      {Array.from({ length: 10 }, (_, i) => i).map(i => (
        <PromoCardSkeleton key={i} />
      ))}
    </View>
  );
};

export default PromoCard;
// export default styled(PromoCard, { props: { titleClasses: true, descriptionClasses: true } });
