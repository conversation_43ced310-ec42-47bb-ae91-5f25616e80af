import { View } from 'react-native';
import { BaseText, Row } from '../ui';
import { styled } from 'nativewind';
import { ReactNode } from 'react';
import { RowProps } from '../ui/row';
import React from 'react';

export interface ProductInfoCardProps extends Partial<RowProps> {
  title: string;
  value: string;
  icon: ReactNode;
}

const ProductInfoCard = ({ icon, title, value, ...props }: ProductInfoCardProps) => {
  return (
    <Row className="justify-start flex-1" {...props}>
      {icon}
      <View className="ml-8 items-stretch">
        <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
          {title}
        </BaseText>
        <BaseText fontSize={14} type={'heading'} weight={'bold'}>
          {value}
        </BaseText>
      </View>
    </Row>
  );
};

export default styled(ProductInfoCard);
