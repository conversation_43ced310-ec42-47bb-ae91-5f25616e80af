import { Dimensions, ScrollView } from 'react-native';
import DashboardLayout from '@/components/ui/layouts/dashboard-layout';
import { HeaderProps, HeaderVariants } from '@/components/ui/layouts/header';
import ScreenInfoHeader, { ColorPaletteType, ScreenInfoHeaderProps } from '@/components/ui/screen-info-header';
import { View } from 'react-native';
import Button from '@/components/ui/buttons/button';
import { useNavigation } from '@react-navigation/native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import { Edit, Instagram, Shop } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import AuthQuestion from '@/components/auth/auth-question';
import Pressable from '@/components/ui/base/pressable';
import { ArrowRight, ArrowUpRight, SelectImageIcon } from '@/components/ui/icons';
import SelectStoreModal from '@/components/products/select-store-modal';
import { useRef, useState } from 'react';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import SelectProductModal from '@/components//products/storefront-products/select-products-modal';
import { mockProducts } from '@/constant/mock-data';
import InfoModal from '@/components/ui/modals/info-modal';
import SectionContainer from '@/components/ui/section-container';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { Image } from 'react-native';
import { Add } from 'iconsax-react-native/src';
import { FlatList } from 'react-native';
import { FillProductsInfoActionType } from '@/screens/products/storefront/create-products/fill-products-info';
import { ProductAddingScreenType } from './product-adding-method-controller';

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const ProductEmpty = ({ onPress }: { onPress: VoidFunction }) => {
  return (
    <Pressable className="items-center mt-40" onPress={onPress}>
      <SelectImageIcon
        width={wp(233)}
        height={hp(133)}
        primaryColor={colors.black.placeholder}
        secondaryColor={colors.grey.borderTwo}
      />
    </Pressable>
  );
};

interface SelectImageControllerProps {
  screen: ProductAddingScreenType;
  ScreenInfoHeaderProps: ScreenInfoHeaderProps;
}

const SelectImageController = ({
  screen = ProductAddingScreenType.ADD_PRODUCT,
  ScreenInfoHeaderProps,
}: SelectImageControllerProps) => {
  const [images, setImages] = useState<string[]>([]);
  const navigation = useNavigation();

  const handleSelectImages = async (allowsMultipleSelection?: boolean) => {
    Haptics.selectionAsync();
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      aspect: [4, 3],
      quality: 1,
      selectionLimit: 10 - images.length,
      allowsMultipleSelection: allowsMultipleSelection,
    });

    if (result.assets) {
      const imageUri = result.assets.map(item => item.uri);
      setImages(prev => [...prev, ...imageUri]);
    }

    if (!result.canceled) {
      // console.log(result.assets[0].uri);
    }
  };

  const renderItem = ({ item }: { item: string }) => {
    return (
      <ListItemCard
        className="flex-1 mx-10 my-[12.5px] py-0"
        style={{ maxWidth: cardWidth }}
        leftElement={
          <Image
            className="w-[45px] h-[45px] rounded-[10px]"
            source={{
              uri: item,
            }}
            resizeMode={'cover'}
          />
        }
        bottomElement={
          <View>
            <View className="h-[6px] bg-grey-bgOne mt-10 rounded-full" />
            <View className="h-[6px] w-3/4 bg-grey-bgOne mt-5 rounded-full" />
          </View>
        }
        title="Product 1"
        titleProps={{ type: 'heading', classes: 'text-black-muted' }}
      />
    );
  };

  const Footer = () => {
    return (
      <Pressable
        className="bg-grey-bgOne self-start rounded-[10px] py-10 px-15 ml-10"
        onPress={() => handleSelectImages(false)}>
        <Row className="justify-start">
          <BaseText fontSize={12} classes="text-primary-main font-interMedium mr-12">
            Add new image
          </BaseText>
          <CircledIcon className="p-5 bg-white">
            <Add size={wp(18)} color={colors.primary.main} />
          </CircledIcon>
        </Row>
      </Pressable>
    );
  };

  return (
    <View className="flex-1">
      <ScreenInfoHeader
        pageTitleTop={'Add Images'}
        pageTitleBottom={'For your Products'}
        colorPalette={ColorPaletteType.YELLOW}
      />
      <Container className="flex-1 px-0 mt-20">
        <SectionContainer className="py-12 px-15 mx-20">
          <StatusPill title={'IMPORTANT'} className="self-start" statusType={StatusType.DANGER} />
          <BaseText fontSize={11} classes="text-black-secondary mt-5 leading-[15px]">
            Select one image for every product you want to upload. Maximum of 10 images allowed.
          </BaseText>
        </SectionContainer>

        <FlatList
          data={images}
          numColumns={2}
          ListEmptyComponent={() => <ProductEmpty onPress={() => handleSelectImages(true)} />}
          className="px-10 mt-25"
          contentContainerStyle={{ flexGrow: 1 }}
          renderItem={renderItem}
          ListFooterComponent={images.length > 0 ? <Footer /> : undefined}
        />
        {/* <Row>
          <ListItemCard
            className="flex-1"
            leftElement={
              <Image
                className="w-[45px] h-[45px] rounded-[10px]"
                source={{
                  uri: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScm3h5fAKg9F9ATCMTobLQngMc_ZoigQooSw&usqp=CAU',
                }}
                resizeMode={'cover'}
              />
            }
            title="Product 1"
            titleProps={{ type: 'heading', classes: 'text-black-muted' }}
          />
          <ListItemCard
            className="flex-1"
            leftElement={
              <Image
                className="w-[45px] h-[45px] rounded-[10px]"
                source={{
                  uri: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScm3h5fAKg9F9ATCMTobLQngMc_ZoigQooSw&usqp=CAU',
                }}
                resizeMode={'cover'}
              />
            }
            title="Product 2"
            titleProps={{ type: 'heading', classes: 'text-black-muted' }}
          />
        </Row> */}
      </Container>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Next Step',
            onPress: () => navigation.navigate('FillProductsInfo', { action: FillProductsInfoActionType.CREATE }),
          },
        ]}
      />
    </View>
  );
};

export default SelectImageController;
