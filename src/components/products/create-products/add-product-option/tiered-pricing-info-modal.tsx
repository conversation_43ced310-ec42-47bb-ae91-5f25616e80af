import { ButtonVariant } from 'src/components/ui/buttons/button';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, Row } from 'src/components/ui';
import { Dimensions, View } from 'react-native';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { CountryInterface, GET_PLANS, Plan, PLAN_TYPE, PlanOption } from 'catlog-shared';
import { useMemo } from 'react';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import CustomImage from 'src/components/ui/others/custom-image';
import SectionContainer from 'src/components/ui/section-container';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const width = Dimensions.get('window').width;

const TieredPricingInfoModal = ({ closeModal, ...props }: Props) => {
  const { store } = useAuthContext();
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
    },
    {
      country: typeof store?.country === 'string' ? store?.country : store?.country?.code,
    },
  );

  return (
    <BottomModal
      {...props}
      enableDynamicSizing
      closeModal={closeModal}
      title="What is Tiered Pricing?"
      buttons={[
        {
          text: 'Continue',
          onPress: closeModal,
        },
      ]}>
      <Container className="pb-70">
        <View className="mt-15 rounded-[15px] overflow-hidden h-[156px]">
          <CustomImage
            imageProps={{ source: require('@/assets/images/tiered-pricing-info.png'), contentFit: 'cover' }}
            className="h-full w-full"
          />
        </View>
        <View>
          <BaseText fontSize={14} classes="text-black-secondary mt-10" lineHeight={22}>
            <BaseText fontSize={14} type="heading" classes="text-black-secondary">
              Tiered Pricing
            </BaseText>{' '}
            lets you offer discounts when customers buy in bulk, without removing the option of retail.
          </BaseText>

          <BaseText fontSize={14} type="heading" classes="text-black-secondary mt-15" lineHeight={22}>
            How it works:
          </BaseText>

          <View className="mt-8">
            <Row className="items-start mb-8">
              <BaseText fontSize={14} classes="text-black-secondary flex-1" lineHeight={22}>
                <BaseText fontSize={14} type="heading" classes="text-black-secondary">
                  1. Your products display their regular price by default.
                </BaseText>
              </BaseText>
            </Row>

            <Row className="items-start mb-8">
              <BaseText fontSize={14} classes="text-black-secondary flex-1" lineHeight={22}>
                <BaseText fontSize={14} type="heading" classes="text-black-secondary">
                  2. The discount applies automatically
                </BaseText>{' '}
                when a buyer selects a qualifying quantity.
              </BaseText>
            </Row>

            <Row className="items-start mb-8">
              <BaseText fontSize={14} classes="text-black-secondary flex-1" lineHeight={22}>
                3. Customers can still buy a single unit at the full price.
              </BaseText>
            </Row>

            <Row className="items-start mt-10">
              <BaseText fontSize={14} classes="text-black-secondary flex-1" lineHeight={22}>
                You can create{' '}
                <BaseText fontSize={14} type="heading" classes="text-black-secondary">
                  multiple tiers
                </BaseText>{' '}
                (e.g., Buy 5+ = 10% off, Buy 10+ = 15% off) to give buyers more reason to purchase in bulk.
              </BaseText>
            </Row>
          </View>
        </View>
      </Container>
    </BottomModal>
  );
};

export default TieredPricingInfoModal;
