import { CloseCircle, Edit2 } from 'iconsax-react-native/src';
import React, { useRef, useState } from 'react';
import { TextInput, View } from 'react-native';
import { hp, removeUnderscores, toCurrency, wp } from 'src/assets/utils/js';
import { BaseText, Row } from '@/components/ui';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import colors from 'src/theme/colors';
import classNames from 'classnames';
import Pressable from '@/components/ui/base/pressable';
import { VariantItem } from 'catlog-shared';

interface VariantSummaryCardProps {
  option: VariantItem;
  optionIndex: number;
  // setOptions: (options: VariantItem[]) => void;
  updateOptions: (value: VariantItem, index?: number) => void;
  type?: 'images' | 'custom';
  containerClasses?: string;
  canEditQuantity?: boolean;
}

const VariantSummaryCard = ({
  option,
  optionIndex,
  type = 'custom',
  updateOptions,
  canEditQuantity,
  containerClasses,
}: VariantSummaryCardProps) => {
  const values = Object.entries(option?.values ?? {});
  const [editing, setEditing] = useState<null | number>(null);
  const [editingQty, setEditingQty] = useState<null | number>(null);

  const pricingInputRefs = useRef<TextInput[]>([]);
  const qtyInputRefs = useRef<TextInput[]>([]);

  const togglePriceEdit = () => {
    if (optionIndex === editing) {
      setEditing(null);
      return;
    }

    setEditing(optionIndex);
    setTimeout(() => {
      if (pricingInputRefs.current[optionIndex]) {
        pricingInputRefs.current[optionIndex].focus();
      }
    }, 10);
  };

  const toggleQtyEdit = () => {
    if (optionIndex === editingQty) {
      setEditingQty(null);
      return;
    }

    setEditingQty(optionIndex);
    setTimeout(() => {
      if (qtyInputRefs.current[optionIndex]) {
        qtyInputRefs.current[optionIndex].focus();
      }
    }, 10);
  };

  const getInputPrice = (price: string) => {
    const priceNum = Number(price);
    return priceNum === 0 ? null : price;
  };

  return (
    <View className={classNames('rounded-12 bg-grey-bgOne mt-15', containerClasses)}>
      {values.map((item, index) => (
        <Row
          key={index}
          className={classNames('p-12', { 'border-b border-b-grey-border': index !== values.length - 1 })}>
          <BaseText classes="text-black-placeholder">
            {removeUnderscores(item[0])}:{' '}
            <BaseText weight="medium" classes="text-black-placeholder">
              {item[1]}
            </BaseText>
          </BaseText>
        </Row>
      ))}
      <View className="rounded-12 border border-grey-border bg-white">
        <Row
          className={classNames('flex-1', {
            'p-15': optionIndex !== editing,
            'px-15 py-5': optionIndex === editing,
          })}>
          <BaseText classes="text-black-placeholder">Price: </BaseText>
          {optionIndex !== editing && (
            <Pressable onPress={togglePriceEdit} className="flex-1">
              <Row>
                <View className="flex-1 justify-center">
                  <BaseText weight="medium" classes="text-black-placeholder">
                    {toCurrency(option.price)}
                  </BaseText>
                </View>
                <Edit2 size={wp(16)} color={colors.black.placeholder} />
              </Row>
            </Pressable>
          )}
          {optionIndex === editing && (
            <>
              <View className={classNames('mr-8 flex-1 -mt-4 py-[10.5]')}>
                <TextInput
                  value={option.price ? String(option.price) : ''}
                  keyboardType="number-pad"
                  onSubmitEditing={() => setEditing(null)}
                  ref={ref => {
                    pricingInputRefs.current[optionIndex] = ref as TextInput;
                  }}
                  placeholder="0"
                  onChangeText={e => updateOptions({ ...option, price: Number(getInputPrice(e)) })} //todo: @silas take a look at this
                  className={classNames('text-black-placeholder font-interMedium')}
                  onBlur={() => setEditing(null)}
                  style={{
                    lineHeight: hp(18),
                    fontSize: wp(13),
                    fontFamily: 'interMedium',
                    marginVertical: 0,
                    paddingVertical: 0,
                    paddingLeft: 0,
                  }}
                />
              </View>
              <Pressable onPress={togglePriceEdit}>
                <CloseCircle size={wp(16)} color={colors.black.placeholder} />
              </Pressable>
            </>
          )}
        </Row>

        {/* Quantity section */}
        {canEditQuantity && (
          <Row
            className={classNames('flex-1 border-t border-t-grey-border', {
              'p-15': optionIndex !== editing,
              'px-15 py-5': optionIndex === editing,
            })}>
            <BaseText classes="text-black-placeholder mr-10">QTY: </BaseText>
            {optionIndex !== editingQty && (
              <Pressable onPress={toggleQtyEdit} className="flex-1" disabled={!canEditQuantity}>
                <Row>
                  <View className="flex-1 justify-center">
                    <BaseText weight="medium" classes="text-black-placeholder">
                      {option.quantity ?? 0}
                    </BaseText>
                  </View>
                  {canEditQuantity && <Edit2 size={wp(16)} color={colors.black.placeholder} />}
                </Row>
              </Pressable>
            )}
            {optionIndex === editingQty && (
              <>
                <View className={classNames('mr-8 flex-1 -mt-4')}>
                  <TextInput
                    value={option.quantity ? String(option.quantity) : ''}
                    keyboardType="number-pad"
                    onSubmitEditing={() => setEditingQty(null)}
                    ref={ref => {
                      qtyInputRefs.current[optionIndex] = ref as TextInput;
                    }}
                    placeholder="0"
                    onChangeText={e => updateOptions({ ...option, quantity: Number(e) })}
                    className={classNames('text-black-placeholder font-interMedium')}
                    onBlur={() => setEditingQty(null)}
                    style={{
                      lineHeight: hp(18),
                      fontSize: wp(13),
                      fontFamily: 'inter-medium',
                      marginVertical: 0,
                      paddingVertical: 0,
                      paddingLeft: 0,
                      paddingRight: 0,
                      paddingTop: 0,
                      paddingBottom: 0,
                      paddingHorizontal: 0,
                    }}
                  />
                </View>
                <Pressable onPress={toggleQtyEdit}>
                  <CloseCircle size={wp(16)} color={colors.black.placeholder} />
                </Pressable>
              </>
            )}
          </Row>
        )}
        <Row style={{ gap: wp(10) }} className="p-15 border-t border-t-grey-border">
          <View className="flex-1">
            <BaseText weight="medium" classes="text-black-placeholder">
              Show to Buyers
            </BaseText>
          </View>
          <CustomSwitch
            value={option.is_available}
            onValueChange={v => updateOptions({ ...option, is_available: v })}
          />
        </Row>
      </View>
    </View>
  );
};

export default VariantSummaryCard;

//Improved to handle lower case
export const hasDuplicates = (array: string[]): boolean => {
  const normalizedValues = array.map(item => item.toLowerCase());
  return new Set(normalizedValues).size < array.length;
};
