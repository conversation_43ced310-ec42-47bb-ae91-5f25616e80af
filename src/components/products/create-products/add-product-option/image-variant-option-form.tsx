import { Dimensions, ScrollView, Text, TextInput, View } from 'react-native';
import { Product } from '../types';
import CustomImage from '@/components/ui/others/custom-image';
import { BaseText, Row } from '@/components/ui';
import { Add, CloseCircle, Edit, Edit2, Refresh2 } from 'iconsax-react-native/src';
import { delay, hp, toCurrency, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import Pressable from '@/components/ui/base/pressable';
import React, { Ref, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { FormikProps, useFormik } from 'formik';
import { Image } from 'src/@types/utils';
import useImageUploads from 'src/hooks/use-file-uploads';
import { pickMultipleImages } from 'src/assets/utils/js/pick-multiple-images';
import CircularProgress from 'react-native-circular-progress-indicator';
import classNames from 'classnames';
import Toast from 'react-native-toast-message';
import ImageVariantExtraOptions from './image-variant-extra-option';
import VariantSummaryCard from './variant-summary-card';
import { VariantForm as IVariantForm, VariantItem, getRandString } from 'catlog-shared';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';

interface ImageVariantOptionFormProps {
  product: Product;
  setVariants: (variants: VariantItem[]) => void;
  initialView?: 'images' | 'values' | 'summary';
  variantForm?: FormikProps<IVariantForm>;
  goBack: VoidFunction;
}

export interface ExtraOptionsFormType {
  hasExtraOption: boolean;
  extraOption: {
    name: string;
    values: string[];
  };
  options: VariantItem[];
}

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const ImageVariantOptionForm = ({
  product,
  goBack,
  setVariants,
  variantForm,
  initialView,
}: ImageVariantOptionFormProps) => {
  const [imagesUploading, setImagesUploading] = useState(false);
  const imagePicker = useRef<HTMLInputElement>(null);
  const [step, setStep] = useState<'images' | 'values' | 'summary'>('images');
  const [error, setError] = useState('');
  const [editPrice, setEditPrice] = useState<number | null>(null);
  const [removedImages, setRemovedImages] = useState<string[]>([]);
  const variantItems = variantForm.values.options;

  const [images, setImages] = useState<Image[]>(
    (() => {
      let ixs: Image[] = [];

      variantItems.forEach(v => {
        if (ixs.findIndex(i => i.url === v.image) === -1) {
          const [name, lastModified] = [`${getRandString(10)}.JPG`, +new Date()];

          ixs.push({
            url: v.image,
            key: getRandString(5),
            file: null,
            isUploading: false,
            uploadProgress: 100,
            name,
            src: '',
            lastModified,
          });
        }
      });

      return ixs;
    })(),
  );

  const inputRefs = useRef<TextInput[]>([]);

  const form = useFormik<ExtraOptionsFormType>({
    initialValues: {
      hasExtraOption: false,
      extraOption: {
        name: '',
        values: ['', ''],
      },
      options: [],
    },
    onSubmit: async values => {
      const handleSubmit = async () => {
        const newVariants = images
          .filter(i => !removedImages.includes(i?.url!))
          .map(i => {
            const variant = variantItems.find(v => v.image === i.url);

            if (variant) {
              return variant;
            }

            return {
              image: i.url,
              price: Number(product.price),
              is_available: true,
            };
          });

        setVariants(newVariants);
        await delay(100);
        variantForm.submitForm();
      };

      switch (step) {
        case 'images':
          if (!hasExtraOption) {
            handleSubmit();
            return;
          }
          setStep('values');
          break;
        case 'values':
          const actualValues = values.extraOption.values.filter(v => v !== '');

          if (actualValues.length < 2) {
            setError('You should have at least two option values');
            Toast.show({ text1: 'You should have at least two option values', type: 'error' });
          } else {
            setStep('summary');
          }
          break;
        case 'summary':
          await delay(100);
          variantForm.submitForm();
        default:
          //do nothing
          break;
      }
    },
  });

  const hasExtraOption = form.values.hasExtraOption;

  // useImageUploads(images, FILE_TYPES.ITEMS, setImages);
  useImageUploads(images, setImages);

  //handle updating the extra options state from saved data
  useEffect(() => {
    const extraOptionText = variantItems[0]?.values ? Object.keys(variantItems[0]?.values)[0] : null;

    if (extraOptionText) {
      const optionValues = Array.from(new Set(variantItems?.map(v => Object.values(v?.values!)[0])));
      const extraOption = {
        name: extraOptionText,
        values: optionValues,
      };

      form.setValues({
        hasExtraOption: true,
        extraOption,
        options: variantItems,
      });
    }
  }, []);

  useEffect(() => {
    setVariants(form.values.options);
  }, [form.values.options]);

  //loading is used to delay this running until the extra variants have been calculated from stored data
  //if this runs before the extra variants, the extra varaints options will be lost
  useEffect(() => {
    setImagesUploading(images.some(i => i.isUploading));
    const newVariants = images
      .filter(i => !removedImages.includes(i?.url!))
      .map(i => {
        const variant = variantItems.find(v => v.image === i.url);

        if (variant) {
          return variant;
        }

        return {
          image: i.url,
          price: Number(product.price),
          is_available: true,
        };
      });

    setVariants(newVariants);
  }, [images]);

  useEffect(() => {
    if (!form.values.hasExtraOption) {
      setStep('images');
    }
  }, [form.values.hasExtraOption]);

  const removeVariant = (id: number) => {
    if (images.length < 3) {
      setError('You must have at least 2 options, add new images, then remove this variant');
      Toast.show({
        text1: 'You must have at least 2 options, add new images, then remove this variant',
        type: 'error',
      });
      return;
    }

    const imagesCopy = [...images];
    const removed = imagesCopy.splice(id, 1);

    setRemovedImages([...removedImages, removed[0]?.url ?? '']);
    setImages(imagesCopy);
  };

  const togglePriceEdit = (index: number) => {
    if (index === editPrice) {
      setEditPrice(null);
      return;
    }

    setEditPrice(index);
    setTimeout(() => {
      if (inputRefs.current[index]) {
        inputRefs.current[index].focus();
      }
    }, 10);
  };

  const updateVariantPrice = (price: string) => {
    const variantsCopy = [...variantItems];

    if (editPrice === null) return;

    const priceNum = Number(price);
    variantsCopy[editPrice].price = priceNum === 0 ? null : priceNum;
    setVariants(variantsCopy);
  };

  const updateVariantsFromImages = () => {
    const newImages = [...images];

    product.images.forEach(image => {
      const imageExists = images.findIndex(i => i.url === image.url) !== -1;

      if (!imageExists) {
        newImages.push({ ...image });
      }
    });

    setRemovedImages([]);
    setImages(newImages);
  };

  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     handleContinue: callback => handleContinue(callback),
  //   }),
  //   [step, form],
  // );

  return (
    <AvoidKeyboard className="flex-1" keyboardVerticalOffset={60}>
      <ScrollView>
        <View className="flex- 1 mx-20 pt-15">
          {(step === 'values' || step === 'summary') && (
            <ImageVariantExtraOptions {...{ images, form, product }} setFieldValue={form.setFieldValue} step={step} />
          )}
          {step === 'images' && (
            <View>
              <Row className="flex-wrap mt-15 items-start" style={{ gap: wp(16) }}>
                {images?.map((item, index) => (
                  <View style={{ width: cardWidth }} key={index}>
                    <View style={{ width: cardWidth, height: cardWidth }}>
                      <CustomImage imageProps={{ source: item.url ?? item.src }} className="h-full w-full rounded-12" />
                      <Pressable className="absolute top-8 right-8" onPress={() => removeVariant(index)}>
                        <CloseCircle size={wp(24)} variant="Bold" color={colors.white} />
                      </Pressable>
                      {item.isUploading && (
                        <View
                          className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
                          style={{ backgroundColor: '#292D321A' }}>
                          <CircularProgress
                            value={item.uploadProgress ?? 0}
                            radius={wp(16)}
                            duration={500}
                            delay={600}
                            activeStrokeWidth={wp(4)}
                            inActiveStrokeWidth={wp(4)}
                            strokeLinecap={'round'}
                            activeStrokeColor={colors.accentGreen.main}
                            inActiveStrokeColor={colors.white}
                            maxValue={100}
                            valueSuffix={'%'}
                            progressValueStyle={{
                              fontSize: wp(8),
                              fontFamily: 'Inter-Bold',
                              color: colors.white,
                            }}
                          />
                        </View>
                      )}
                    </View>
                    {/* <View className="h-3 w-full bg-accentGreen-main rounded-full mt-5" /> */}
                    {!hasExtraOption && (
                      <Row className="bg-grey-bgTwo rounded-8 mt-5 py-12 px-12">
                        {editPrice !== index && (
                          <BaseText fontSize={14} weight="medium" classes="text-black-placeholder">
                            {toCurrency(variantItems[index]?.price)}
                          </BaseText>
                        )}
                        {editPrice === index && (
                          <View className="flex-1">
                            <TextInput
                              value={variantItems[index].price ? String(variantItems[index].price) : ''}
                              keyboardType="number-pad"
                              onSubmitEditing={() => togglePriceEdit(index)}
                              ref={ref => {
                                inputRefs.current[index] = ref as TextInput;
                              }}
                              onBlur={() => togglePriceEdit(index)}
                              placeholder="0"
                              onChangeText={e => updateVariantPrice(e)}
                              className={classNames('text-black-placeholder')}
                              style={{
                                lineHeight: hp(18),
                                fontSize: wp(13),
                                fontFamily: 'inter-medium',
                                marginVertical: 0,
                                paddingVertical: 0,
                                paddingLeft: 0,
                              }}
                            />
                          </View>
                        )}
                        <Pressable onPress={() => togglePriceEdit(index)}>
                          <Edit2 size={wp(16)} color={colors.primary.main} />
                        </Pressable>
                      </Row>
                    )}
                  </View>
                ))}
                <Pressable
                  className="rounded-[7px] border-grey-border border border-dashed h-[60] w-[60] flex items-center justify-center"
                  onPress={() => pickMultipleImages(images, setImages)}
                  style={{ width: cardWidth, height: cardWidth }}>
                  <Add color={colors.black.placeholder} size={wp(20)} />
                </Pressable>
              </Row>
            </View>
          )}
        </View>
      </ScrollView>
      {step !== 'summary' && (
        <View className="px-20 pb-10 pt-20 bg-white">
          <Row className="mb-15">
            <BaseText fontSize={14} classes="text-black-muted">
              Does this product have extra Options?
            </BaseText>
            <CustomSwitch value={hasExtraOption} onValueChange={v => form.setFieldValue('hasExtraOption', v)} />
          </Row>
          <Pressable
            onPress={updateVariantsFromImages}
            className="bg-grey-bgOne p-15 rounded-12 justify-center items-center mt-5">
            <Row>
              <BaseText weight="medium" classes="text-primary-main mr-10">
                Update options from product images
              </BaseText>
              <Refresh2 size={wp(18)} color={colors.primary.main} />
            </Row>
          </Pressable>
        </View>
      )}
      <FixedBtnFooter
        buttons={[
          {
            text: 'Go back',
            onPress: () => {
              if (step === 'values') setStep('images');
              else if (step === 'summary') setStep('values');
              else goBack();
            },
            variant: ButtonVariant.LIGHT,
          },
          {
            text: 'Continue',
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
    </AvoidKeyboard>
  );
};

export default ImageVariantOptionForm;
