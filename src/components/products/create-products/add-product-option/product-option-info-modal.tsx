import { ButtonVariant } from 'src/components/ui/buttons/button';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText, Row } from 'src/components/ui';
import { Dimensions, View } from 'react-native';
import { hp, toCurrency, wp } from 'src/assets/utils/js';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { CountryInterface, GET_PLANS, Plan, PLAN_TYPE, PlanOption } from 'catlog-shared';
import { useMemo } from 'react';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import CustomImage from 'src/components/ui/others/custom-image';
import SectionContainer from 'src/components/ui/section-container';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
}

const width = Dimensions.get('window').width;

const ProductOptionInfoModal = ({ closeModal, ...props }: Props) => {
  const { store } = useAuthContext();
  const getPlansReq = useApi(
    {
      apiFunction: GET_PLANS,
      key: GET_PLANS.name,
      method: 'GET',
      autoRequest: true,
    },
    {
      country: typeof store?.country === 'string' ? store?.country : store?.country?.code,
    },
  );

  return (
    <BottomModal
      {...props}
      enableDynamicSizing
      closeModal={closeModal}
      title="What are Product Options?"
      buttons={[
        {
          text: 'Continue',
          onPress: closeModal,
        },
      ]}>
      <Container className="pb-70">
        <View className="mt-15 rounded-[15px] overflow-hidden h-[156px]">
          <CustomImage
            imageProps={{ source: require('@/assets/images/product-option-info.png'), contentFit: 'cover' }}
            className="h-full w-full"
          />
        </View>
        <View>
          <BaseText fontSize={14} classes="text-black-secondary mt-10" lineHeight={22}>
            Product Options is a way to show customers the different variations of a product you have available. On
            Catlog, you can add options in two ways:
          </BaseText>
          <BaseText fontSize={14} type="heading" classes="text-black-secondary mt-10" lineHeight={22}>
            1. With Images
          </BaseText>
          <BaseText fontSize={14} classes="text-black-secondary mt-5" lineHeight={22}>
            Best for visual differences — for example, sneakers that come in different designs or prints, so buyers can
            see exactly what they'll get.
          </BaseText>
          <BaseText fontSize={14} type="heading" classes="text-black-secondary mt-10" lineHeight={22}>
            2. With Text (Custom)
          </BaseText>
          <BaseText fontSize={14} classes="text-black-secondary mt-5" lineHeight={22}>
            Best for straightforward choices like size (S, M, L) or colour (Blue, Green). Image options are best for
            products where design matters, while text options work well for simple variations like sizes or colours.
          </BaseText>
        </View>
      </Container>
    </BottomModal>
  );
};

export default ProductOptionInfoModal;
