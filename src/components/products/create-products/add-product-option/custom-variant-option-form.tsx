import React, { useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { delay } from 'src/assets/utils/js';
import { BaseText, Row } from '@/components/ui';
import { Product } from '../types';
import { FormikProps, useFormik } from 'formik';
import VariantForms from './variant-forms';
import Toast from 'react-native-toast-message';
import VariantSummaryCard from './variant-summary-card';
import { VariantForm as IVariantForm, VariantItem } from 'catlog-shared';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import InfoBadge from 'src/components/store-settings/info-badge';

interface CustomVariantsForm {
  types: string[];
  values: { [key: string]: string[] };
  lastValuesCache: { [key: string]: string[] };
  options: VariantItem[];
}

interface CustomVariantOptionFormProps {
  variants: VariantItem[];
  product: Product;
  variantForm?: FormikProps<IVariantForm>;
  setVariants: (variants: VariantItem[]) => void;
  initialView?: 'form' | 'summary';
  goBack: VoidFunction;
}

export interface CustomVariantOptionFormMethod {
  handleContinue: (callback: VoidFunction) => void;
}

const CustomVariantOptionForm = ({
  product,
  setVariants,
  variants,
  goBack,
  variantForm,
}: CustomVariantOptionFormProps) => {
  const [error, setError] = useState('');
  const [step, setStep] = useState<'form' | 'summary'>('form');

  const isAlwaysAvailable = product?.is_always_available;

  const form = useFormik<CustomVariantsForm>({
    initialValues: {
      types: [''],
      values: {},
      options: [],
      lastValuesCache: {},
    },
    onSubmit: async values => {
      switch (step) {
        case 'form':
          const thisValues = values.values;
          const valuesArray = Object.values(thisValues);
          const types = values.types;
          const isEmpty = valuesArray.length < 1;
          const hasEmptyValues = valuesArray.some(vA => vA.includes(''));

          const hasDuplicateTypes = hasDuplicates(types);
          const hasDuplicateValues = valuesArray.map(val => hasDuplicates(val)).some(dup => dup);

          if (isEmpty) {
            setError('You are required to add at least one option to continue');
            Toast.show({ text1: 'You are required to add at least one option to continue', type: 'error' });
            return;
          }

          if (hasEmptyValues) {
            setError('Please fill or delete empty values');
            Toast.show({ text1: 'Please fill or delete empty values', type: 'error' });
            return;
          }

          if (hasDuplicateTypes || hasDuplicateValues) {
            setError('Please check for duplicate types & values');
            Toast.show({ text1: 'Please check for duplicate types & values', type: 'error' });
            return;
          }

          const sameAsPreviousValues = JSON.stringify(thisValues) === JSON.stringify(values.lastValuesCache);

          if (sameAsPreviousValues) {
            setStep('summary');
          } else {
            const defaultOption = { is_available: true, price: Number(product.price) };

            const createOptions = (values: string[][], index: number): VariantItem[] => {
              const reminder = product.quantity % values.flat().length;
              const generatedQuantity = Number((product.quantity / values.flat().length - reminder).toFixed());
              //index is used to track the index of the type

              if (values.length === 1)
                return values[0].map(v => ({
                  ...defaultOption,
                  values: { [types[index]]: v },
                  quantity: generatedQuantity > 0 ? generatedQuantity : 1,
                }));

              const valuesCopy = [...values];
              valuesCopy.splice(0, 1);
              let cumulative: VariantItem[] = [];
              const currentOptions = createOptions(valuesCopy, index + 1);

              values[0].forEach(value => {
                const generatedValues = currentOptions.map(o => ({
                  ...o,
                  values: { ...o.values, [types[index]]: value },
                  quantity: generatedQuantity > 0 ? generatedQuantity : 1,
                }));
                cumulative = [...cumulative, ...generatedValues];
              });

              return cumulative;
            };

            const options = createOptions(valuesArray, 0);

            //cache the previous values so we can compare in the future if the user goes some steps back
            form.setFieldValue('lastValuesCache', JSON.parse(JSON.stringify(thisValues)));

            form.setFieldValue('options', options);
          }

          setStep('summary');
          return;
        case 'summary':
          setVariants(form.values.options);
          await delay(100);
          variantForm.submitForm();

        default:
          //do nothing
          return;
      }
    },
  });

  //update the values data when any type changes
  useEffect(() => {
    const types = form.values.types.filter(t => t !== '');
    const generatedValues: { [key: string]: string[] } = {};
    const valuesArray = Object.values(form.values.values);

    types.forEach((t, index) => {
      generatedValues[t] = valuesArray[index] ?? ['', ''];
    });

    form.setFieldValue('values', generatedValues);
  }, [form.values.types]);

  // const handleContinue = useCallback(
  //   (callback?: VoidFunction) => {
  //     form.handleSubmit();

  //     if (step === 'summary') {
  //       callback?.();
  //     }
  //   },
  //   [step, form],
  // );

  const handleContinue = () => {
    form.handleSubmit();

    if (step === 'summary') {
      variantForm.submitForm();
    }
  };

  //update form values if there's data from the parent
  useEffect(() => {
    if (variants.length > 0) {
      const types = Object.keys(variants[0].values ?? '');
      const values: { [key: string]: string[] } = {};

      variants.forEach(v => {
        types.forEach(t => {
          if (values[t] !== undefined) {
            if (!values[t].includes(v?.values?.[t]!)) {
              values[t].push(v?.values?.[t]!);
            }
          } else {
            values[t] = [v?.values?.[t]!];
          }
        });
      });

      form.setFieldValue('types', types);
      form.setFieldValue('values', values);
      form.setFieldValue('lastValuesCache', JSON.parse(JSON.stringify(values)));
      form.setFieldValue('options', variants);
    }
  }, [variants]);

  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     handleContinue: callback => handleContinue(callback),
  //   }),
  //   [step, form],
  // );

  //this function basically tries to detect if a type has been deleted and removes the corresponding values for the type
  const updateTypes = (newTypes: string[]) => {
    const currentTypes = form.values.types;

    if (newTypes.length < currentTypes.length) {
      const deletedType = currentTypes.find(t => newTypes.indexOf(t) === -1);

      const valuesCopy = { ...form.values.values };
      delete valuesCopy[deletedType!];

      form.setFieldValue('values', valuesCopy);
    }

    form.setFieldValue('types', newTypes);
  };

  const updateOptions = (value: VariantItem, index: number) => {
    const optionsCopy = [...form.values.options];

    optionsCopy[index] = value;
    form.setFieldValue('options', optionsCopy);
    // form.submitForm();
  };

  return (
    <AvoidKeyboard className="flex-1" keyboardVerticalOffset={60}>
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <View className="flex- 1 mx-20">
          {step === 'form' && (
            <VariantForms
              types={form.values?.types}
              values={form.values?.values}
              setValues={(values: { [key: string]: string[] }) => form.setFieldValue('values', values)}
              setTypes={updateTypes}
              errorText={error}
            />
          )}
          {step === 'summary' && (
            <View className="flex-1">
              {isAlwaysAvailable && (
                // <View className="px-12 py-8 rounded-8 mt-8 mb-10">
                //   <BaseText fontSize={11} classes="text-black-muted" lineHeight={16}>
                //     You cannot set individual quantities for the product options because this product has been marked as
                //     "always in stock."
                //   </BaseText>
                // </View>
                <InfoBadge
                  className='mt-15'
                  text={
                    'You cannot set individual quantities for the product options because this product has been marked as"always in stock."'
                  }
                />
              )}
              {form.values?.options &&
                form.values?.options?.map((item, index) => (
                  <VariantSummaryCard
                    key={`${item?.values?.[form.values.types[0]]}-${index}`}
                    option={item!}
                    optionIndex={index}
                    canEditQuantity={!isAlwaysAvailable}
                    updateOptions={(o, i) => updateOptions(o, i ?? index)}
                  />
                ))}
            </View>
          )}
        </View>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Go back',
            onPress: () => {
              if (step === 'summary') setStep('form');
              else goBack();
            },
            variant: ButtonVariant.LIGHT,
          },
          {
            text: 'Continue',
            onPress: () => form.handleSubmit(),
          },
        ]}
      />
    </AvoidKeyboard>
  );
};

export default CustomVariantOptionForm;

//Improved to handle lower case
export const hasDuplicates = (array: string[]): boolean => {
  const normalizedValues = array.map(item => item.toLowerCase());
  return new Set(normalizedValues).size < array.length;
};
