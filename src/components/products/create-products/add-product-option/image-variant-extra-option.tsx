import React, { useMemo } from 'react';
import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { getFieldvalues, wp } from 'src/assets/utils/js';
import { BaseText, Row } from '@/components/ui';
import Button, { ButtonVariant } from '@/components/ui/buttons/button';
import { hasDuplicates } from './custom-variant-option-form';
import VariantOptionCard from './variant-option-card';
import Toast from 'react-native-toast-message';
import { FormikErrors, FormikProps } from 'formik';
import { ExtraOptionsFormType } from './image-variant-option-form';
import { Product } from '../types';
import { Image } from 'src/@types/utils';
import Input from '@/components/ui/inputs/input';
import VariantSummaryCard from './variant-summary-card';
import CustomImage from '@/components/ui/others/custom-image';
import classNames from 'classnames';
import { VariantItem } from 'catlog-shared';

interface ImageVariantExtraOptionsProps {
  // types: string[];
  // values: { [key: string]: string[] };
  // setValues: (values: { [key: string]: string[] }) => void;
  // setTypes: (types: string[]) => void;
  // errorText?: string;

  form: FormikProps<ExtraOptionsFormType>;
  product: Product;
  images: Image[];
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<FormikErrors<ExtraOptionsFormType>> | Promise<void>;
  step: 'summary' | 'values';
}

const ImageVariantExtraOptions = ({ form, product, images, step, setFieldValue }: ImageVariantExtraOptionsProps) => {
  const name = form.values.extraOption.name;
  const values = form.values.extraOption.values;
  const variantOptions = form.values.options;
  const [error, setError] = useState('');
  const [editing, setEditing] = useState<number | null>(null);

  useEffect(() => {
    const actualValues = values.filter(v => v !== '');

    if (actualValues.length < 1) {
      return;
    }

    const variants: VariantItem[] = [];

    actualValues.forEach(v => {
      images.forEach(i => {
        const existingOption = variantOptions.find(o => o.image === i.url && o?.values?.[name] === v);

        if (existingOption) {
          variants.push(existingOption);
        } else {
          variants.push({
            image: i.url,
            price: Number(product.price),
            is_available: true,
            values: { [name]: v },
          });
        }
      });
    });

    setFieldValue('options', variants);
  }, [values]);

  const uniqueImageOptions = useMemo(() => {
    return images.map(i => i.url);
  }, [images]);

  const handleValueChange = (t: string, index: number, value: string) => {
    const valuesCopy = [...values];

    valuesCopy[index] = value;
    setFieldValue('extraOption.values', valuesCopy);
  };

  const removeValue = (index: number) => {
    const valuesCopy = [...values];

    if (valuesCopy.length <= 2) {
      setError('You must have at least two options, edit the value instead');
      Toast.show({ text1: 'You must have at least two options, edit the value instead', type: 'error' });
      return;
    }

    valuesCopy.splice(index, 1);
    setFieldValue('extraOption.values', valuesCopy);
  };

  const togglePriceEdit = (index: number) => {
    if (index === editing) {
      setEditing(null);
      return;
    }

    setEditing(index);
  };

  const updateOptions = (value: VariantItem, index: number) => {
    const optionsCopy = [...variantOptions];

    optionsCopy[index] = value;
    setFieldValue('options', optionsCopy);
  };

  const getInputPrice = (price: any) => {
    price = Number(price);
    return price === 0 ? null : price;
  };

  const isAlwaysAvailable = product?.is_always_available;

  return (
    <View>
      {step === 'values' && (
        <VariantOptionCard
          name={getFieldvalues('extraOption.name', form).value}
          type={form.values.extraOption.name}
          deleteValue={(t, i) => removeValue(i)}
          updateType={text => getFieldvalues('extraOption.name', form).onChangeText(text)}
          isEditing={true}
          handleValueChange={handleValueChange}
          onBlur={() => {}}
          values={values}
          onPressDelete={() => {}}
          onPressEdit={() => {}}
          showCancel={false}
          setEditing={setEditing}
        />
      )}

      {step === 'summary' && (
        <View>
          {isAlwaysAvailable && (
            <View className="px-12 py-8 bg-accentYellow-pastel rounded-8 mt-8 mb-10">
              <BaseText fontSize={11} classes="text-black-muted" lineHeight={16}>
                You cannot set individual quantities for the product options because this product has been marked as
                "always in stock."
              </BaseText>
            </View>
          )}
          {variantOptions &&
            variantOptions?.map((item, index) => (
              <View className={classNames('rounded-12 bg-grey-bgOne', { 'mt-15': index !== 0 })} key={index}>
                <Row className="bg-grey-bgOne rounded-12 px-12 py-8 border-b border-b-grey-border">
                  <CustomImage imageProps={{ source: item.image }} className="h-40 w-40 rounded-5" />
                  <View className="flex-1 mx-8">
                    <BaseText weight="medium" classes="text-black-placeholder">
                      Image Option {uniqueImageOptions.indexOf(item.image) + 1}
                    </BaseText>
                  </View>
                </Row>
                <VariantSummaryCard
                  key={index}
                  option={item!}
                  type="images"
                  optionIndex={index}
                  canEditQuantity={!isAlwaysAvailable}
                  updateOptions={(o, i) => updateOptions(o, i ?? index)}
                  containerClasses="mt-0 rounded-0"
                />
              </View>
            ))}
        </View>
      )}
    </View>
  );
};

export default ImageVariantExtraOptions;
