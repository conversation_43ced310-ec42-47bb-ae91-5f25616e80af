import { Alert, Dimensions, Modal, Platform, Text, View } from 'react-native';
import { FormikProps, useFormik } from 'formik';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import { BaseText, Container, Header, Row } from '@/components/ui';
import { useEffect, useMemo, useRef, useState } from 'react';
import CustomVariantOptionForm, { CustomVariantOptionFormMethod } from './custom-variant-option-form';
import ImageVariantOptionForm from './image-variant-option-form';
import useLayoutHeight from 'src/hooks/use-layout-height';
import useKeyboard from 'src/hooks/use-keyboard';
import { Product } from '../types';
import Toast from 'react-native-toast-message';
import React from 'react';
import { VariantForm, VariantItem } from 'catlog-shared';
import { ScrollView } from 'react-native-gesture-handler';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import colors from 'src/theme/colors';
import Pressable from 'src/components/ui/base/pressable';
import { cx } from 'src/assets/utils/js';
import AvoidKeyboard from 'src/components/ui/layouts/avoid-keyboard';
import useStatusbar from 'src/hooks/use-statusbar';
import { CloseCircle } from 'iconsax-react-native/src';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import { StatusBar } from 'expo-status-bar';
import { toastConfig } from 'src/components/ui/others/toast-notification';

interface AddProductOptionModalProps extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  product: Product;
  saveVariants: (variants: VariantForm) => void;
  variantForm?: FormikProps<VariantForm>;
  initialView?: 'selectType' | 'fillForm';
  isEdit?: boolean;
}

enum VARIANT_OPTION {
  CUSTOM = 'custom',
  IMAGES = 'images',
}

const screenHeight = Dimensions.get('screen').height;

const { width } = Dimensions.get('window');
const cardWidth = (width - 40 - 20) / 2;

const AddProductOptionModal = ({
  closeModal,
  saveVariants,
  initialView,
  variantForm,
  product,
  isEdit,
  ...props
}: AddProductOptionModalProps) => {
  const [step, setStep] = useState<'selectType' | 'fillForm'>('selectType');

  // useEffect(() => {
  //   if (initialView) {
  //     setStep(initialView);
  //   }
  // }, [initialView]);

  useEffect(() => {
    if (product?.variants) {
      form.setValues(product.variants);
    }
  }, [product.variants]);

  const defaultType = product?.variants?.type ?? '';

  const form = useFormik<VariantForm>({
    initialValues: product.variants
      ? { ...product.variants }
      : {
          is_template: false,
          type: '',
          options: [],
        },
    onSubmit: values => {
      const quantityExists = values?.options.some(val => val?.quantity! > -1);

      //@feranmi what happens if quantity is not set?

      if (quantityExists && !isEdit) {
        values.options = values.options.map(o => ({
          ...o,
          quantity: o?.quantity! > -1 ? o.quantity : 0,
        }));

        // Alert.alert('Newly added options have a default quantity of 0, remember to update the quantities');
        Toast.show({
          text1: 'Newly added options have a default quantity of 0, remember to update the quantities',
          type: 'error',
        });
      }

      saveVariants({ ...values, is_template: values.type === 'custom' ? values.is_template : false }); //force is_template to false if type is image template
      closeModal();
    },
  });

  const variant = form?.values;
  // const variant = product?.variants;

  const setVariants = (variants: VariantItem[]) => {
    form.setFieldValue('options', variants);
  };

  const selectVariantType = (type: string) => {
    if (type === 'images') {
      const variants = product.images.map(i => ({
        image: i.url,
        price: Number(product.price),
        is_available: true,
      }));

      form.setFieldValue('options', variants);
    } else {
      form.setFieldValue('options', []);
    }

    form.setFieldValue('type', type);
  };

  const isKeyboardActive = useKeyboard();

  const { setStatusBar } = useStatusbar();

  useEffect(() => {
    if (String(product?.variants?.type)) {
      setStep('fillForm');
    } else {
      setStep('selectType');
    }
  }, [product?.variants?.type]);

  return (
    <Modal
      animationType="slide"
      visible={props.isVisible}
      title={'Add Product Options'}
      presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'pageSheet'}
      onRequestClose={closeModal}
      {...props}>
      <Row className={cx('justify-between px-20 pt-20 pb-10 border-b border-b-grey-border')}>
        <BaseText fontSize={15} type="heading">
          Add Product Option
        </BaseText>
        <Pressable className="self-end" onPress={closeModal}>
          <Row className="rounded-full p-6 pr-12 bg-white border border-grey-border">
            <CloseCircle variant="Bold" size={18} color={colors.black.main} />
            <BaseText classes="ml-4">Close</BaseText>
          </Row>
        </Pressable>
      </Row>
      {/* <AvoidKeyboard className="flex-1 bg-white" keyboardVerticalOffset={30}>
        <ScrollView keyboardShouldPersistTaps="never"> */}
      <View className="flex-1">
        {step === 'selectType' && (
          <ScrollView>
            <View className="mx-20 mt-30">
              <BaseText fontSize={13} type="heading">
                Choose Option Type
              </BaseText>
              <Row className="mt-20">
                {variantOptions.map(i => (
                  <Pressable
                    key={i.value}
                    onPress={() => selectVariantType(i.value)}
                    className={cx('border border-grey-border rounded-8 items-center justify-center', {
                      'border-primary-main': variant?.type === i.value,
                    })}
                    style={{ width: cardWidth, height: cardWidth }}>
                    <BaseText
                      classes={cx({
                        'text-primary-main': variant?.type === i.value,
                      })}>
                      {i.label}
                    </BaseText>
                  </Pressable>
                ))}
              </Row>
            </View>
          </ScrollView>
        )}
        <View className="flex-1" style={{ display: step === 'fillForm' ? 'flex' : 'none' }}>
            {variant?.type === VARIANT_OPTION.CUSTOM && (
              <CustomVariantOptionForm
                product={product}
                variantForm={form}
                goBack={() => setStep('selectType')}
                // initialView={initialView === 'fillForm' ? 'summary' : undefined}
                setVariants={setVariants}
                variants={variant?.options}
              />
            )}
            {variant?.type === VARIANT_OPTION.IMAGES && (
              <ImageVariantOptionForm
                product={product}
                variantForm={form}
                goBack={() => setStep('selectType')}
                setVariants={setVariants}
              />
            )}
          </View>
        {/* {step === 'fillForm' && (
          <>
            {variant?.type === VARIANT_OPTION.CUSTOM && (
              <CustomVariantOptionForm
                product={product}
                variantForm={form}
                goBack={() => setStep('selectType')}
                // initialView={initialView === 'fillForm' ? 'summary' : undefined}
                setVariants={setVariants}
                variants={variant?.options}
              />
            )}
            {variant?.type === VARIANT_OPTION.IMAGES && (
              <ImageVariantOptionForm
                product={product}
                variantForm={form}
                goBack={() => setStep('selectType')}
                setVariants={setVariants}
              />
            )}
          </>
        )} */}
        {step === 'selectType' && (
          <FixedBtnFooter
            buttons={[
              ...(step !== 'selectType'
                ? [
                    {
                      text: 'Go back',
                      onPress: () => setStep('selectType'),
                      variant: ButtonVariant.LIGHT,
                    },
                  ]
                : []),
              {
                text: step === 'selectType' ? 'Next' : 'Continue',
                onPress: () => setStep('fillForm'),
              },
            ]}
          />
        )}
      </View>

      <Toast config={toastConfig} topOffset={20} />
    </Modal>
  );
};

const variantOptions = [
  {
    value: VARIANT_OPTION.IMAGES,
    label: 'Images',
  },
  {
    value: VARIANT_OPTION.CUSTOM,
    label: 'Custom',
  },
];

export default AddProductOptionModal;
