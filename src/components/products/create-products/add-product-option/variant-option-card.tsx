import React, { useCallback, useEffect, useRef } from 'react';
import { Add, Edit2, Layer, Trash } from 'iconsax-react-native/src';
import { ScrollView, Text, TextInput, View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import WhiteCardBtn from '@/components/ui/buttons/white-card-btn';
import Input from '@/components/ui/inputs/input';
import SectionContainer, { ContainerType } from '@/components/ui/section-container';
import colors from 'src/theme/colors';
import ProductVariantCard from '../../product-variant-card';

interface VariantOptionCardProps {
  name: string;
  index?: number;
  handleValueChange: (type: string, index: number, value: string) => void;
  updateType: (value: string, index?: number) => void;
  type: string;
  onBlur?: VoidFunction;
  values?: string[];
  isEditing: boolean;
  showCancel?: boolean;
  showRemove?: boolean;
  onPressDelete: VoidFunction;
  onPressEdit: VoidFunction;
  setEditing: React.Dispatch<React.SetStateAction<number>>;
  deleteValue?: (type: string, index: number) => void;
}

const VariantOptionCard = ({
  name,
  values,
  type,
  index,
  updateType,
  onBlur,
  deleteValue,
  handleValueChange,
  showCancel = true,
  showRemove = false,
  isEditing,
  setEditing,
  onPressEdit,
  onPressDelete,
}: VariantOptionCardProps) => {
  const inputRefs = useRef<TextInput[]>([]);

  const handleSubmitEditing = useCallback(
    index => {
      const inputsLength = inputRefs.current.length;
      if (index < inputsLength - 1) {
        setTimeout(() => {
          if (inputRefs.current[index + 1]) {
            inputRefs.current[index + 1].focus();
          }
        }, 10);
      } else {
        handleValueChange(type, values?.length!, '');
      }
    },
    [inputRefs],
  );

  const handleInputDelete = (type: string, index: number) => {
    deleteValue?.(type, index);

    if (index > 1) {
      inputRefs.current.splice(index, 1); // remove the input ref
    }
  };

  useEffect(() => {
    // console.log('values: ', values)
    if (values) {
      inputRefs.current = values?.map(() => null);
    }
  }, [values]);

  return (
    <View>
      {isEditing && (
        <SectionContainer className="p-14" containerType={ContainerType.OUTLINED}>
          <Row>
            <BaseText>Variant Name</BaseText>
            {showRemove && <Pressable onPress={onPressDelete}>
              <BaseText fontSize={14} weight="medium" classes="text-accentRed-main">
                Remove
              </BaseText>
            </Pressable>}
          </Row>
          <Input
            containerClasses="mt-15"
            placeholder="e.g. Size, Color"
            value={name}
            onChangeText={text => updateType(text, index)}
            onBlur={onBlur}
            autoFocus
          />
          {values?.map((item, index) => (
            <Row className="rounded-12 bg-grey-bgOne mt-15" key={index}>
              <View className="justify-center pl-8 pr-8">
                <Layer variant="Bold" size={wp(18)} color={colors.black.placeholder} />
              </View>
              <View className="flex-1 rounded-12 border border-grey-border p-12 bg-white">
                <BaseText classes="text-black-secondary">Option {index + 1}</BaseText>
                <Row style={{ gap: wp(10) }}>
                  <View className="flex-1">
                    <Input
                      containerClasses="mt-8"
                      placeholder={`Add a ${type}`}
                      value={item}
                      onChangeText={text => handleValueChange(type, index, text)}
                      ref={ref => {
                        inputRefs.current[index] = ref as TextInput;
                      }}
                      onSubmitEditing={() => handleSubmitEditing(index)}
                      autoFocus={index !== 1} // focus on the first input & not the second
                      // label={`value-${name}-${index}`}
                      // onFocus={() => setIsFocused(true)}
                      // onBlur={() => setIsFocused(false)}
                    />
                  </View>
                  <Pressable onPress={() => handleInputDelete(type, index)}>
                    <CircledIcon className="bg-grey-bgOne">
                      <Trash size={wp(16)} color={colors.accentRed.main} />
                    </CircledIcon>
                  </Pressable>
                </Row>
              </View>
            </Row>
          ))}
          <Row className="mt-16">
            <Pressable
              onPress={() => setEditing?.(null)}
              disabled={showCancel === false}
              style={{ opacity: showCancel ? 1 : 0 }}>
              <BaseText fontSize={14} weight="medium" classes="text-accentRed-main">
                Collapse
              </BaseText>
            </Pressable>
            <WhiteCardBtn
              className="p-0"
              leftIcon={<Add size={wp(18)} color={colors.primary.main} />}
              onPress={() => handleValueChange(type, values?.length!, '')}>
              Add New Option
            </WhiteCardBtn>
          </Row>
        </SectionContainer>
      )}
      {!isEditing && (
        <ProductVariantCard name={name} values={values!} onPressDelete={onPressDelete} onPressEdit={onPressEdit} />
      )}
    </View>
  );
};

export default VariantOptionCard;
