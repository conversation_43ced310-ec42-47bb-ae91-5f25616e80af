import { Add, Flag, InfoCircle, Trash } from 'iconsax-react-native/src';
import React, { useMemo } from 'react';
import { View } from 'react-native';
import { alertPromise, showError, wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import { ArrowUpRight } from '@/components/ui/icons';
import colors from 'src/theme/colors';
import AddProductOptionModal from './add-product-option-modal';
import useModals from 'src/hooks/use-modals';
import { Product } from '../types';
import { FormikProps } from 'formik';
import { VariantForm } from 'catlog-shared';
import VariantSectionCard from '../../storefront-products/variant-section-card';
import Button, { ButtonVariant } from 'src/components/ui/buttons/button';
import Pressable from 'src/components/ui/base/pressable';
import ProductOptionInfoModal from './product-option-info-modal';

interface ProductOptionsSectionProps {
  form?: FormikProps<Product>;
  product: Product;
  saveVariants: (variants: VariantForm) => void;
}

const ProductOptionsSection = ({ form, saveVariants }: ProductOptionsSectionProps) => {
  const { modals, toggleModal } = useModals(['addProductOption', 'productOptionInfo']);

  const variant = form?.values?.variants;
  const hasVariant = variant?.options.length > 0;

  const openAddOptionModal = () => {
    if (!form.values.name || !form.values.price) {
      showError(undefined, 'Please add a name and price before adding options');
      return;
    }

    toggleModal('addProductOption');
  };

  const removeAllOptions = async () => {
    const confirmed = await alertPromise(
      'Remove All Options',
      'Are you sure you want to remove all product options? This action cannot be undone.',
      'Yes, Remove All',
      'Cancel',
      true,
    );

    if (confirmed) {
      saveVariants({ options: [], type: '' });
    }
  };

  const variantValues = useMemo(() => {
    const variants = variant?.options!;
    const hasValues = variants?.[0]?.values !== undefined;
    // if (typeof(variants?.[0]?.values) !== 'object') return;
    if (variants?.length > 0) {
      let types: string[] = [];
      let values: { [key: string]: string[] } = {};

      if (hasValues) {
        types = [...types, ...Object.keys(variants?.[0]?.values!)];

        variants.forEach(v => {
          types.forEach(t => {
            if (values[t] !== undefined) {
              if (!values[t].includes(v?.values?.[t]!)) {
                values[t].push(v?.values?.[t]!);
              }
            } else {
              values[t] = [v?.values?.[t]!];
            }
          });
        });
      }

      if (variant?.type === 'images') {
        types = [...types, 'Images'];
        const images: string[] = [];
        variants.forEach(t => {
          if (t.image !== undefined && !images.includes(t.image)) {
            images.push(t.image);
          }
        });
        values = { ...values, images };
      }

      // console.log("types", types);
      // console.log("values", values);
      const variantsData = Object.entries(values).map(item => ({ name: item[0], value: item[1] }));
      return variantsData;
    }
  }, [variant]);

  return (
    <View>
      {hasVariant &&
        variantValues?.map((item, index) => (
          <VariantSectionCard key={item?.name + index} name={item?.name} values={item?.value} />
        ))}

      {hasVariant && (
        <Row className="justify-start flex-wrap mt-15">
          <WhiteCardBtn
            className="bg-grey-bgOne mr-2.5"
            onPress={openAddOptionModal}
            icon={<Add color={colors.primary.main} size={wp(14)} strokeWidth={2} />}>
            Update Options
          </WhiteCardBtn>
          <WhiteCardBtn
            className="bg-grey-bgOne mr-2.5"
            onPress={removeAllOptions}
            icon={<Trash color={colors.accentRed.main} size={wp(14)} strokeWidth={2} />}>
            <BaseText fontSize={12} weight={'medium'} classes="text-accentRed-main">
              Remove all Options
            </BaseText>
          </WhiteCardBtn>
        </Row>
      )}

      {!hasVariant && (
        <Row className="justify-start flex-wrap mt-15" style={{ gap: wp(10) }}>
          <View className="flex-1">
            <Button
              text="Add Product Options"
              onPress={openAddOptionModal}
              variant={ButtonVariant.LIGHT}
              leftAddOn={
                <View className="p-0">
                  <Add size={wp(14)} color={colors.primary.main} strokeWidth={2} />
                </View>
              }
            />
          </View>
          <Pressable className="p-15 h-full bg-grey-bgOne rounded-12" onPress={() => toggleModal('productOptionInfo')}>
            <InfoCircle size={wp(20)} color={colors.black.placeholder} />
          </Pressable>
        </Row>
      )}
      {modals.addProductOption && (
        <AddProductOptionModal
          isVisible={modals.addProductOption}
          // variant={variant}
          product={form?.values!}
          // form={form}
          saveVariants={saveVariants}
          closeModal={() => toggleModal('addProductOption', false)}
        />
      )}
      <ProductOptionInfoModal
        isVisible={modals.productOptionInfo}
        closeModal={() => toggleModal('productOptionInfo', false)}
      />
    </View>
  );
};

export default ProductOptionsSection;
