import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText } from '@/components/ui';
import Button, { ButtonVariant } from '@/components/ui/buttons/button';
import { hasDuplicates } from './custom-variant-option-form';
import VariantOptionCard from './variant-option-card';
import Toast from 'react-native-toast-message';

interface VariantFormsProps {
  types: string[];
  values: { [key: string]: string[] };
  setValues: (values: { [key: string]: string[] }) => void;
  setTypes: (types: string[]) => void;
  errorText?: string;
}

const VariantForms = ({ types, values, setValues, setTypes, errorText }: VariantFormsProps) => {
  // const [editing, setEditing] = useState(null);

  const [error, setError] = useState('');
  const [typesArray, setTypesArray] = useState<string[]>([]);
  const [editing, setEditing] = useState<null | number>(Object.entries(values).length < 1 ? 0 : null);
  const valuesArray = Object.values(values);

  useEffect(() => {
    setTypesArray([...types]);
  }, [types]);

  const updateType = (value: string, index?: number) => {
    const typesArrayCopy = [...typesArray];

    if (editing === null) {
      return;
    }

    //editing will correspond to the current index of the input being edited
    typesArrayCopy[index ?? editing] = value;
    setTypesArray(typesArrayCopy);
  };

  const removeType = (index: number) => {
    const typesCopy = [...types];

    if (typesCopy.length > 1) {
      typesCopy.splice(index, 1);
      setTypesArray(typesCopy);
      setTypes(typesCopy);
    } else {
      setError('You need to have at least 1 type, edit the type instead');
      Toast.show({ text1: 'You need to have at least 1 type, edit the type instead', type: 'error' });
      return;
    }
  };

  const handleTypeInputBlur = () => {
    if (hasDuplicates(typesArray)) {
      setError('Please check types for duplicates');
      Toast.show({ text1: 'Please check types for duplicates', type: 'error' });
      return;
    }

    // setEditing(null);
    setTypes(typesArray);
  };

  const handleValueChange = (type: string, index: number, value: string) => {
    const valuesCopy = { ...values };

    if (type === '') {
      Toast.show({ text1: 'Please enter option name to add option', type: 'error' });
      return;
    }

    valuesCopy[type][index] = value;

    setValues(valuesCopy);
  };

  const deleteValue = (type: string, index: number) => {
    const valuesCopy = { ...values };
    const thisValues = [...values[type]];

    if (thisValues.length > 2) {
      thisValues.splice(index, 1);
      valuesCopy[type] = thisValues;

      setValues(valuesCopy);
    } else {
      setError('Each type should have at least 2 values');
      Toast.show({ text1: 'Each type should have at least 2 values', type: 'error' });
      return;
    }
  };

  const addNewOption = () => {
    setTypes([...types, '']);
    setEditing(types.length);
  };

  return (
    <View>
      {/* {error && (
        <View>
          <BaseText>{error}</BaseText>
        </View>
      )} */}
      {types.map((type, index) => (
        <VariantOptionCard
          name={typesArray[index]}
          type={type}
          key={index}
          index={index}
          showRemove={types.length > 1}
          deleteValue={deleteValue}
          updateType={updateType}
          isEditing={editing === index}
          handleValueChange={handleValueChange}
          onBlur={handleTypeInputBlur}
          values={valuesArray[index]}
          onPressDelete={() => removeType(index)}
          setEditing={setEditing}
          onPressEdit={() => setEditing(index)}
        />
      ))}

      <Button className="mt-20" text="Add New Variant" variant={ButtonVariant.LIGHT} onPress={addNewOption} />
    </View>
  );
};

export default VariantForms;
