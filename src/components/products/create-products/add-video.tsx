import Pressable from '@/components/ui/base/pressable';
import { DocumentUpload } from 'iconsax-react-native/src';
import { View } from 'react-native';
import { wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row } from 'src/components/ui';
import colors from 'src/theme/colors';

interface Props {
  handlePickVideo: VoidFunction;
}

const AddVideo: React.FC<Props> = ({ handlePickVideo } : Props) => {

  return (
    <View>
      <Pressable
        className="border border-grey-border rounded-8 items-center justify-center p-12 mt-10"
        onPress={handlePickVideo}>
        <Row disableSpread>
          <CircledIcon className="p-6 bg-grey-bgOne">
            <DocumentUpload size={wp(20)} variant="Bold" color={colors.black.muted} />
          </CircledIcon>
          <BaseText weight="medium" classes="text-black-placeholder ml-5">
            Upload Video
          </BaseText>
        </Row>
        <BaseText fontSize={12} classes="text-black-placeholder mt-5">
          MP4, MOV, HEIC (Max size - 15mb)
        </BaseText>
      </Pressable>
    </View>
  );
};

export default AddVideo;
