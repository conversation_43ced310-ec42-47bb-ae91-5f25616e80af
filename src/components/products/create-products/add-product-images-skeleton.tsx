import React from 'react';
import { View } from 'react-native';
import classNames from 'classnames';
import { wp } from 'src/assets/utils/js';
import Shimmer from '@/components/ui/shimmer';
import { Row } from 'src/components/ui';

interface AddProductImageSkeletonProps {
  index: number;
  cardWidth: number;
}

interface AddProductImagesSkeletonLoaderProps {
  count?: number;
  cardWidth: number;
  showUploadProgress?: boolean;
  showCloseButton?: boolean;
}

export const AddProductImagesSkeletonLoader: React.FC<AddProductImagesSkeletonLoaderProps> = ({
  count = 6,
  cardWidth,
  showUploadProgress = true,
  showCloseButton = true
}) => {
  return (
    <Row className='flex-wrap'>
      {Array.from({ length: count }, (_, index) => (
        <AddProductImageSkeletonCard
          key={index}
          index={index}
          cardWidth={cardWidth}
          showUploadProgress={showUploadProgress}
          showCloseButton={showCloseButton}
        />
      ))}
    </Row>
  );
};

// Alternative skeleton that matches the exact structure of the original component
interface AddProductImageSkeletonCardProps extends AddProductImageSkeletonProps {
  showUploadProgress?: boolean;
  showCloseButton?: boolean;
}

export const AddProductImageSkeletonCard: React.FC<AddProductImageSkeletonCardProps> = ({
  index,
  cardWidth,
  showUploadProgress = true,
  showCloseButton = true
}) => {
  return (
    <View
      className={classNames('flex-shrink-0 mb-25 flex-row', { 'mr-10': index % 2 === 0, 'ml-10': index % 2 !== 0 })}
      style={{ maxWidth: cardWidth }}
    >
      {/* Left Element - Image Skeleton */}
      <View className="w-[50px] h-[50px] rounded-[10px] relative">
        <Shimmer
          height={50}
          width={50}
          borderRadius={10}
        />
      </View>

      {/* Content Area */}
      <View className="flex-1 ml-12">
        {/* Title Row */}
        <View className="flex-row items-center justify-between">
          <Shimmer
            height={16}
            width={80}
            borderRadius={4}
          />
        </View>

        {showUploadProgress && (
          <View>
            <Shimmer
              height={6}
              width={cardWidth * 0.6}
              borderRadius={3}
              marginTop={5}
            />
            <Shimmer
              height={6}
              width={cardWidth * 0.4}
              borderRadius={3}
              marginTop={5}
            />
          </View>
        )}
      </View>
    </View>
  );
};

