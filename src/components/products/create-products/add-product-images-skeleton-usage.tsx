import React from 'react';
import { View, ScrollView } from 'react-native';
import { BaseText } from '@/components/ui';
import { AddProductImagesSkeletonLoader, AddProductImageSkeletonCard } from './add-product-images-skeleton';
import { wp } from 'src/assets/utils/js';

// Example usage of the AddProductImages skeleton loaders
const AddProductImagesSkeletonUsage: React.FC = () => {
  const cardWidth = (wp(375) - wp(60)) / 2; // Example card width calculation

  return (
    <ScrollView style={{ flex: 1, padding: 20 }}>
      <BaseText fontSize={18} weight="bold" className="mb-20">
        Product Images Skeleton Examples
      </BaseText>

      {/* Example 1: Full skeleton loader with all features */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          1. Full Skeleton Loader (6 items with upload progress)
        </BaseText>
        <AddProductImagesSkeletonLoader 
          count={6}
          cardWidth={cardWidth}
          showUploadProgress={true}
          showCloseButton={true}
        />
      </View>

      {/* Example 2: Skeleton without upload progress */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          2. Skeleton Without Upload Progress
        </BaseText>
        <AddProductImagesSkeletonLoader 
          count={4}
          cardWidth={cardWidth}
          showUploadProgress={false}
          showCloseButton={true}
        />
      </View>

      {/* Example 3: Skeleton without close button */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          3. Skeleton Without Close Button
        </BaseText>
        <AddProductImagesSkeletonLoader 
          count={3}
          cardWidth={cardWidth}
          showUploadProgress={true}
          showCloseButton={false}
        />
      </View>

      {/* Example 4: Minimal skeleton */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          4. Minimal Skeleton (No progress, no close button)
        </BaseText>
        <AddProductImagesSkeletonLoader 
          count={2}
          cardWidth={cardWidth}
          showUploadProgress={false}
          showCloseButton={false}
        />
      </View>

      {/* Example 5: Individual skeleton cards */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          5. Individual Skeleton Cards
        </BaseText>
        <AddProductImageSkeletonCard 
          index={0}
          cardWidth={cardWidth}
          showUploadProgress={true}
          showCloseButton={true}
        />
        <AddProductImageSkeletonCard 
          index={1}
          cardWidth={cardWidth}
          showUploadProgress={false}
          showCloseButton={true}
        />
      </View>

      {/* Example 6: Grid layout simulation */}
      <View className="mb-30">
        <BaseText fontSize={16} weight="semiBold" className="mb-15">
          6. Grid Layout Simulation
        </BaseText>
        <View className="flex-row flex-wrap">
          {Array.from({ length: 4 }, (_, index) => (
            <AddProductImageSkeletonCard 
              key={index}
              index={index}
              cardWidth={cardWidth}
              showUploadProgress={true}
              showCloseButton={true}
            />
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

export default AddProductImagesSkeletonUsage;

/*
Usage in your actual component:

import { AddProductImagesSkeletonLoader } from './add-product-images-skeleton';

// In your component's render method:
{isLoading ? (
  <AddProductImagesSkeletonLoader 
    count={6}
    cardWidth={cardWidth}
    showUploadProgress={true}
    showCloseButton={true}
  />
) : (
  // Your actual product images
  images.map((image, index) => (
    <ListItemCard key={index} ... />
  ))
)}
*/
