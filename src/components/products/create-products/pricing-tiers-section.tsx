import React, { useState } from 'react';
import { View } from 'react-native';
import { GET_TIERED_PRICING, GetTieredPricingParams, PricingTierInterface, ProductItemInterface } from 'catlog-shared';
import { CircledIcon, Row, WhiteCardBtn } from '@/components/ui';
import useModals from '@/hooks/use-modals';
import BottomModal from '@/components/ui/modals/bottom-modal';
import PricingTiersList from '../pricing-tiers/pricing-tiers-list';
import { BaseText } from '@/components/ui';
import { Add, Edit2, Eye, InfoCircle, Layer } from 'iconsax-react-native/src';
import { ensureUniqueItems, hp, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { ResponseWithPagination, useApi } from '@/hooks/use-api';
import usePagination from '@/hooks/use-pagination';
import PricingTierCard from '../pricing-tiers/pricing-tier-card';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import useStorefrontItems from 'src/hooks/use-storefront-items';
import PricingTierSkeletonLoader from '../pricing-tiers/pricing-tier-skeleton-loader';
import Button, { ButtonVariant } from 'src/components/ui/buttons/button';
import Separator from 'src/components/ui/others/separator';
import AddPricingTierModal from '../pricing-tiers/add-pricing-tier-modal';
import { Product } from './types';
import { FormikProps } from 'formik';
import Pressable from 'src/components/ui/base/pressable';
import PricingTierDetailsModal from '../pricing-tiers/pricing-tier-detail-modal';
import TieredPricingInfoModal from './add-product-option/tiered-pricing-info-modal';

interface PricingTiersSectionProps {
  className?: string;
  form?: FormikProps<Product>;
}

interface PricingTiersResponse extends ResponseWithPagination<PricingTierInterface[]> {}

const PER_PAGE = 10;

const PricingTiersSection: React.FC<PricingTiersSectionProps> = ({ className, form }) => {
  const [tieredPricing, setTieredPricing] = useState<PricingTierInterface[]>([]);
  const [isEdit, setIsEdit] = useState(false);
  const { modals, toggleModal } = useModals(['pricingTiers', 'addTierModal', 'tierDetails', 'tieredPricingInfo']);

  const { getItem, items, fetchItemsReq } = useStorefrontItems();

  // API request to get pricing tiers
  const getPricingTiersRequest = useApi<GetTieredPricingParams, PricingTiersResponse>(
    {
      apiFunction: GET_TIERED_PRICING,
      method: 'GET',
      key: GET_TIERED_PRICING.name,
      onSuccess: response => {
        setTieredPricing(prev => ensureUniqueItems([...prev, ...response?.data]));
      },
    },
    {
      filter: {},
      page: 1,
      per_page: Number.MAX_SAFE_INTEGER,
    },
  );

  const createTierCallback = (data?: PricingTierInterface) => {
    if (data) {
      setTieredPricing(prev => [data, ...prev]);
    }
  };

  const selectTier = (tier: PricingTierInterface) => {
    if (form.values?.tiered_pricing?.id === tier.id) {
      removeTier();
      return;
    }
    form.setFieldValue('tiered_pricing', tier);
    // toggleModal('pricingTiers', false);
  };

  const removeTier = () => {
    form.setFieldValue('tiered_pricing', null);
  };

  const handleEditTier = () => {
    setIsEdit(true);
    toggleModal('tierDetails', false);
    setTimeout(() => {
      toggleModal('addTierModal');
    }, 600);
  };

  return (
    <View className={className}>
      {!form.values?.tiered_pricing && (
        // <WhiteCardBtn className="bg-grey-bgOne rounded-8 mt-10" onPress={() => toggleModal('pricingTiers')}>
        //   Add Bulk Purchase Discount
        // </WhiteCardBtn>
        <Row className="justify-start flex-wrap mt-15" style={{ gap: wp(10) }}>
          <View className="flex-1">
            <Button
              text="Add bulk discount"
              onPress={() => toggleModal('pricingTiers')}
              variant={ButtonVariant.LIGHT}
              leftAddOn={
                <View className="p-0">
                  <Add size={wp(14)} color={colors.primary.main} strokeWidth={2} />
                </View>
              }
            />
          </View>
          <Pressable className="p-15 h-full bg-grey-bgOne rounded-12" onPress={() => toggleModal('tieredPricingInfo')}>
            <InfoCircle size={wp(20)} color={colors.black.placeholder} />
          </Pressable>
        </Row>
      )}

      {form.values?.tiered_pricing && (
        <Row className="mt-10 bg-grey-bgOne py-8 px-10 rounded-8">
          <CircledIcon className="p-6">
            <Layer variant="Bold" size={wp(15)} color={colors.grey.muted} />
          </CircledIcon>
          <View className="flex-1 mx-10">
            <BaseText classes="text-black-main">{form.values?.tiered_pricing?.label}</BaseText>
          </View>
          <Pressable onPress={() => toggleModal('tierDetails')}>
            <CircledIcon className="p-6">
              <Eye size={wp(15)} color={colors.black.muted} strokeWidth={1.5} />
            </CircledIcon>
          </Pressable>
          <Pressable className="ml-10" onPress={() => toggleModal('pricingTiers')}>
            <CircledIcon className="p-6">
              <Edit2 size={wp(15)} color={colors.black.muted} strokeWidth={1.5} />
            </CircledIcon>
          </Pressable>
        </Row>
      )}

      {/* Pricing Tiers Modal */}
      <BottomModal
        isVisible={modals.pricingTiers}
        closeModal={() => toggleModal('pricingTiers', false)}
        title="Add Tiered Pricing"
        size="lg"
        buttons={[
          {
            text: 'Proceed',
            onPress: () => toggleModal('pricingTiers', false),
          },
        ]}
        useChildrenAsDirectChild>
        <BottomSheetScrollView>
          <View className="mx-20">
            <BaseText classes="text-black-muted">Select existing tiered pricing or create a new one</BaseText>
            <View className="mt-15">
              <Button
                text="Create New Tiered Pricing"
                variant={ButtonVariant.LIGHT}
                onPress={() => toggleModal('addTierModal')}
              />
            </View>
            <Separator className="mt-20 mb-15 mx-0" />
            {getPricingTiersRequest.isLoading && <PricingTierSkeletonLoader count={3} />}
            {getPricingTiersRequest.response?.data?.length > 0 && !getPricingTiersRequest.isLoading && (
              <View className="MT-10" style={{ gap: hp(15) }}>
                {tieredPricing.map(tier => (
                  <PricingTierCard
                    key={tier.id}
                    tierItem={tier}
                    isSelect
                    isSelected={form.values?.tiered_pricing?.id === tier.id}
                    onPress={() => selectTier(tier)}
                    firstItem={tier.items?.length > 0 ? getItem(tier.items[0]) : null}
                  />
                ))}
              </View>
            )}
          </View>
          <View className="h-80" />
        </BottomSheetScrollView>
      </BottomModal>
      <AddPricingTierModal
        isVisible={modals.addTierModal}
        isEdit={isEdit}
        activePricingTier={isEdit ? form.values?.tiered_pricing : undefined}
        closeModal={() => toggleModal('addTierModal', false)}
        callBack={createTierCallback}
        getItem={getItem}
        storeItems={items}
      />
      <TieredPricingInfoModal
        isVisible={modals.tieredPricingInfo}
        closeModal={() => toggleModal('tieredPricingInfo', false)}
      />
      {form.values?.tiered_pricing && modals.tierDetails && (
        <PricingTierDetailsModal
          isVisible={modals.tierDetails}
          activePricingTier={form.values?.tiered_pricing}
          closeModal={() => toggleModal('tierDetails', false)}
          editPricingTier={handleEditTier}
          deletePricingTier={() => removeTier()}
          firstItem={
            form.values?.tiered_pricing.items?.length > 0 ? getItem(form.values?.tiered_pricing.items[0]) : null
          }
          fromProductForm
        />
      )}
    </View>
  );
};

export default PricingTiersSection;
