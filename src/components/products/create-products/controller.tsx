import React, { useCallback, useEffect, useRef, useState } from 'react';
import useAuthContext from 'src/contexts/auth/auth-context';
import { Product, ProductCreateMethod, ProductForm, ProductUploadStep } from './types';
import useModals from 'src/hooks/use-modals';
import SelectProductCreateMethod from './select-create-method';
import HideComponent from '@/components/ui/hide-component';
import AddProductImages from './add-product-images';
import { AppMediaType, Image } from 'src/@types/utils';
import ProductsForm, { ProductFormRef } from './form';
import { useApi } from 'src/hooks/use-api';
import { useNavigation } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import InstagramImportManager from '../add-product-controller/instagram-import/instagram-import-manager';
import StoreImportManager from '../add-product-controller/store-import/store-import-manager';
import { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import {
  Category,
  StoreInterface,
  CREATE_ITEMS,
  CreateItemsParams,
  GET_VARIANT_TEMPLATES,
  UPDATE_STORE_DETAILS,
  getRandString,
  Media,
} from 'catlog-shared';
import { View } from 'react-native';
import {
  alertPromise,
  delay,
  hideLoader,
  removeEmptyAndUndefined,
  showLoader,
  showSuccess,
  sluggify,
} from 'src/assets/utils/js';
import { StatusBar } from 'expo-status-bar';
import {
  saveProductCreationState,
  getProductCreationState,
  clearProductCreationState,
} from 'src/assets/utils/js/product-creation-storage';
import eventEmitter from 'src/assets/utils/js/event-emitter';
import { mixpanelTrack } from 'src/assets/utils/js/mixpanel';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant, TextColor } from 'src/components/ui/buttons/button';

interface Props {
  maxUploadable: number;
  success: {
    label: string;
    route: string;
  };
  isSetup?: boolean;
  currentStep: ProductUploadStep;
  setCurrentStep: React.Dispatch<React.SetStateAction<ProductUploadStep>>;
  currentProduct: number;
  setCurrentProduct: React.Dispatch<React.SetStateAction<number>>;
  setIsImport: React.Dispatch<React.SetStateAction<boolean>>;
  footerBackBtn?: boolean;
}

const CreateProductsController = (props: Props) => {
  const {
    maxUploadable,
    isSetup = false,
    currentStep,
    setCurrentStep,
    currentProduct,
    setCurrentProduct,
    setIsImport,
    footerBackBtn,
  } = props;
  const [savedStateExist, setSavedStateExist] = useState(false);
  const { stores, store, user, subscription, categories, updateStore, decideNextRoute } = useAuthContext();
  const navigation = useNavigation();

  // const [images, setImages] = useState<Image[]>([]);
  const [medias, setMedias] = useState<AppMediaType[]>([]);
  const [uploadMethod, setUploadMethod] = useState<ProductCreateMethod | null>(null);
  const [form, setForm] = useState<ProductForm>({
    products: [],
  });
  const [pastProgress, setPastProgress] = useState<ProductForm>({ products: [] });
  const dropDownRef = useRef<DropDownMethods>(null); //Todo: @kayode: I think this should be moved to the StoreImportManager component

  // Store refs for all product forms
  const formRefs = useRef<(ProductFormRef | null)[]>([]);

  const { modals, toggleModal, switchModals } = useModals([
    'import',
    'products',
    'tutorial',
    'chowdeck',
    'instagram',
    'progress',
    'count',
    'menu',
    'import_progress',
  ]);
  const [loadModals, setLoadModals] = useState({ chowdeck: false, instagram: false });

  const { isLoading, makeRequest, error, response } = useApi<CreateItemsParams>({
    method: 'POST',
    key: 'create-products',
    apiFunction: CREATE_ITEMS,
  });

  const getvariantTemplatesReq = useApi({
    method: 'GET',
    key: 'get-variant-templates',
    apiFunction: GET_VARIANT_TEMPLATES,
  });

  const updateStoreRequest = useApi(
    {
      apiFunction: UPDATE_STORE_DETAILS,
      key: UPDATE_STORE_DETAILS.name,
      method: 'PUT',
    },
    { id: store?.id },
  );

  // Update form refs when products change
  useEffect(() => {
    formRefs.current = formRefs.current.slice(0, form.products.length);
  }, [form.products.length]);

  const loadSavedState = async () => {
    const savedState = await getProductCreationState();
    if (savedState) {
      setMedias(savedState.medias);
      setForm(savedState.form);
    }
  };
  const saveState = useCallback(
    async (formToSave?: ProductForm, mediasToSave?: AppMediaType[]) => {
      await saveProductCreationState({
        form: formToSave ?? form,
        medias: mediasToSave ?? (medias as AppMediaType[]),
      });
    },
    [form, medias],
  );

  useEffect(() => {
    const loadSavedStateStatus = async () => {
      const savedState = await getProductCreationState();
      if (savedState?.medias?.length > 0) {
        setSavedStateExist(Boolean(savedState?.medias.length > 0));
      }
    };

    loadSavedStateStatus();
  }, []);

  const templates: Product['variants'][] = getvariantTemplatesReq.response?.data?.templates || [];
  const userStores = stores?.filter?.(s => s.owner === user?.id);
  const allowVariants = true;

  const handleSelectMethod = (method: ProductCreateMethod) => {
    setUploadMethod(method);
    switch (method) {
      case ProductCreateMethod.INSTAGRAM:
        toggleModal('instagram', true);
        break;
      case ProductCreateMethod.CHOWDECK:
        setLoadModals({ ...loadModals, chowdeck: true });
        toggleModal('chowdeck');
        break;
      case ProductCreateMethod.MANUAL:
        setCurrentStep('images');
        break;
      case ProductCreateMethod.STORE:
        dropDownRef.current?.open();
        toggleModal('import');
        break;
      case ProductCreateMethod.MENU_IMAGE:
        toggleModal('menu');
        break;
    }
  };

  const changeView = (dir: string) => {
    if (dir === 'forward') {
      setCurrentProduct(currentProduct + 1);
    } else if (dir === 'backwards' && currentProduct === 0) {
      if (uploadMethod !== ProductCreateMethod.MANUAL) setCurrentStep('method');
      else setCurrentStep('images');
    } else {
      setCurrentProduct(currentProduct - 1);
    }
  };

  const submitForm = async () => {
    const products = form.products.map(p => {
      return {
        ...p,
        images: p.images.map(i => i.url!),
        videos: p.videos?.map(v => ({ name: v.name, url: v.url, thumbnail: v.meta?.thumbnail?.url })),
        price: Number(p.price),
        info_blocks: p.info_blocks?.map(b => b.id),
        upload_source:
          uploadMethod === ProductCreateMethod.STORE ? ProductCreateMethod.MANUAL : (p?.upload_source ?? uploadMethod!),
        slug: `${sluggify(p.name)}-${Date.now()}-${getRandString(3)}`,
        is_always_available: p.is_always_available ?? true,
        tiered_pricing: p.tiered_pricing?.id,
        discount_price: p.discount_price ? Number(p.discount_price) : undefined,
        cost_price: p.cost_price ? Number(p.cost_price) : undefined,
        stock_threshold: p.stock_threshold ? Number(p.stock_threshold) : undefined,
      };
    });

    showLoader('Uploading Products');

    const [response, error] = await makeRequest({ store: store!.id, items: products });
    hideLoader();
    await delay(600);

    if (response) {
      await clearProductCreationState();
      // Clear saved state on successful upload

      const storeMeta = store?.meta ?? {};
      const currentIgUploadCount = storeMeta.instagram_item_upload_count ?? 0;
      const instagramProductsCount =
        products?.filter(p => p.upload_source === ProductCreateMethod.INSTAGRAM).length ?? 0;
      const updateData = {
        item_count: (store?.item_count ?? 0) + products.length,
        meta: { ...storeMeta, instagram_item_upload_count: currentIgUploadCount + instagramProductsCount },
        onboarding_steps: { ...(store.onboarding_steps ?? {}), products_added: true },
      };

      if (isSetup) {
        // await updateStoreRequest.makeRequest({
        //   id: store.id,
        //   onboarding_steps: { ...(store.onboarding_steps ?? {}), products_added: true },
        // });
        updateStore(updateData);
        showSuccess('Your products have been uploaded');
        mixpanelTrack('Uploaded First Product', {
          email: user?.email,
          productsCount: products.length,
        });

        const { nextRoute } = await decideNextRoute({ skipProgress: true });
        navigation.navigate(nextRoute as any);
        return;
      }

      setCurrentStep('response');
      eventEmitter.emit('productCreate', response?.data);
      navigation.navigate('Feedback', {
        headerTitle: 'Products Uploaded',
        headerBg: 'bg-accentYellow-pastel',
        feedbackText: `You have successfully \n  added ${products.length} items`,
        btnText: 'Go to products',
        onPressBtn: () => navigation.navigate('HomeTab', { screen: 'Products' } as any),
      });
    } else {
      Toast.show({ type: 'error', text1: error?.message || "Couldn't upload products please try again" });
    }
  };

  const onInstagramImportComplete = (products: Product[]) => {
    setForm({ products });
    setCurrentStep('details');
    setIsImport(true);
  };

  const uploadMethods = {
    [ProductCreateMethod.MANUAL]: true,
    [ProductCreateMethod.CHOWDECK]: false,
    [ProductCreateMethod.STORE]: userStores?.length > 1,
    [ProductCreateMethod.MENU_IMAGE]: false,
    [ProductCreateMethod.INSTAGRAM]: true, //@kayode user needs to be on the basic plan to be able to import from instagram. Check the dashboard implementation for this
  };

  const removeProduct = (index: number) => {
    const formCopyProducts = JSON.parse(JSON.stringify(form.products)); //deep copy
    const imageCopy = [...medias];
    imageCopy.splice(index, 1);
    setMedias(imageCopy);
    formCopyProducts.splice(index, 1);
    setForm({ ...form, products: formCopyProducts });

    setCurrentProduct(currentProduct - 1);

    Toast.show({ type: 'success', text1: 'Product removed' });
  };

  // Add a function to handle manual state clearing
  const clearCreationState = async () => {
    const confirmed = await alertPromise(
      'Clear Progress',
      'Are you sure you want to clear your product creation progress?',
      'Yes, Clear',
      'Cancel',
      true,
    );

    if (confirmed) {
      await clearProductCreationState();
      setMedias([]);
      setForm({ products: [] });
      setUploadMethod(null);
      setCurrentStep('method');
      setCurrentProduct(0);
    }
  };

  // Function to submit current form
  const submitCurrentForm = () => {
    const currentFormRef = formRefs.current[currentProduct];
    if (currentFormRef?.form) {
      currentFormRef.form.handleSubmit();
    }
  };

  const addProductButtons = [
    {
      text: currentProduct > 0 ? 'Previous Product' : 'Back to Images',
      onPress: () => {
        // updateForm(thisForm.values);
        changeView('backwards');
      },
      variant: ButtonVariant.LIGHT,
      textColor: TextColor.PRIMARY,
      isLoading,
      disabled: isLoading,
    },
    {
      text: currentProduct === form.products.length - 1 ? 'Upload Products' : 'Next Product',
      onPress: submitCurrentForm,
      isLoading,
      disabled: isLoading,
    },
  ];

  return (
    <View className="flex-1">
      <StatusBar style={'dark'} />
      {currentStep === 'method' && (
        <SelectProductCreateMethod
          hasMultipleStores={userStores?.length > 1}
          selectMethod={handleSelectMethod}
          uploadMethods={uploadMethods}
          isSetup={isSetup}
        />
      )}
      {uploadMethod === ProductCreateMethod.MANUAL && (
        <HideComponent show={currentStep === 'images'} classNames="flex-1">
          <AddProductImages
            {...{
              setForm,
              setCurrentStep,
              form,
              savedStateExist,
              loadSavedState,
              saveState,
              currentStep,
              maxUploadable,
              pastProgress,
              medias,
              isSetup,
              setMedias,
              showBackBtn: footerBackBtn,
            }}
          />
        </HideComponent>
      )}

      {currentStep === 'details' &&
        form.products.map((product, index) => (
          <HideComponent show={index === currentProduct} classNames="flex-1" key={index}>
            <ProductsForm
              ref={ref => {
                formRefs.current[index] = ref;
              }}
              product={product}
              index={index}
              setForm={setForm}
              form={form}
              changeView={changeView}
              submitForm={submitForm}
              isLoading={isLoading}
              categories={categories ?? []}
              allowVariants={allowVariants}
              isSetup={isSetup}
              currentIndex={currentProduct}
              variantTemplates={templates}
              store={store!}
              removeProduct={removeProduct}
              saveState={saveState}
            />
            <FixedBtnFooter buttons={addProductButtons} />
          </HideComponent>
        ))}
      {uploadMethod === ProductCreateMethod.INSTAGRAM && currentStep === 'method' && (
        <InstagramImportManager
          modals={modals}
          toggleModal={toggleModal}
          onImportComplete={onInstagramImportComplete}
          medias={medias}
          setMedias={setMedias}
        />
      )}
      <StoreImportManager dropDownRef={dropDownRef} />
      {/* Todo: @kayode we probably don't want to mount the instagramImportManager and StoreImportManager components until the user selects the import method */}
    </View>
  );
};

export default CreateProductsController;
