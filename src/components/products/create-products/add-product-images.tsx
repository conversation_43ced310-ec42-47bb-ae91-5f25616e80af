import classNames from 'classnames';
import { Add } from 'iconsax-react-native/src';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Dimensions, Image, ScrollView, View } from 'react-native';
import CircularProgress from 'react-native-circular-progress-indicator';
import { AppMediaType, Image as ImageType, IVideo } from 'src/@types/utils';
import { generateSimpleUUID, hideLoader, showLoader, wp } from 'src/assets/utils/js';
import { pickMultipleImages, pickMultipleMedia } from 'src/assets/utils/js/pick-multiple-images';
import { BaseText, CircledIcon } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import ListItemCard from '@/components/ui/cards/list-item-card';
import Container from '@/components/ui/container';
import { Close, ImageSelectorPlaceholder } from '@/components/ui/icons';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import Row from '@/components/ui/row';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import SectionContainer from '@/components/ui/section-container';
import { ColorPaletteType } from 'src/constant/static-data';
import useImageUploads from 'src/hooks/use-file-uploads';
import colors from 'src/theme/colors';
import { ProductForm, ProductUploadStep } from './types';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import BaseScrollView from 'src/components/ui/base/base-scrollview';
import CustomImage from 'src/components/ui/others/custom-image';
import { AddProductImagesSkeletonLoader } from './add-product-images-skeleton';
import { Media, MediaType } from 'catlog-shared';
import { useVideoPlayer, VideoPlayer, VideoView } from 'expo-video';
import * as VideoThumbnails from 'expo-video-thumbnails';

interface AddProductImagesProps {
  setForm: (form: ProductForm) => void;
  setCurrentStep: (step: ProductUploadStep) => void;
  form: ProductForm;
  currentStep: string;
  maxUploadable: number;
  pastProgress: ProductForm;
  medias: AppMediaType[];
  isSetup: boolean;
  setMedias: React.Dispatch<React.SetStateAction<AppMediaType[]>>;
  showBackBtn?: boolean;
  savedStateExist: boolean;
  loadSavedState: VoidFunction;
  saveState: (form: ProductForm | undefined, medias?: AppMediaType[]) => void;
}

const { width } = Dimensions.get('window');
const cardWidth = (width - 40 - 20) / 2;

const AddProductImages = (props: AddProductImagesProps) => {
  const {
    setForm,
    setCurrentStep,
    form,
    loadSavedState,
    saveState,
    currentStep,
    maxUploadable,
    pastProgress,
    medias,
    setMedias,
    showBackBtn,
    savedStateExist,
    isSetup = false,
  } = props;

  const [imageSelectionLoading, setImageSelectionLoading] = useState(false);
  const [infoMessage, setInfoMessage] = useState(null);

  useImageUploads(medias ?? [], setMedias, [MediaType.VIDEO]);

  useEffect(() => {
      console.log('showing loader: ', infoMessage);

    if (infoMessage) {
      console.log('showing loader: ', infoMessage);
      showLoader(infoMessage);
    } else {
      hideLoader();
    }
  }, [infoMessage, medias]);

  useEffect(() => {
    const imagesWithUrl = medias.filter(i => i.url !== undefined);
    if (imagesWithUrl.length > 0) {
      saveState(undefined, [...imagesWithUrl]);
    }
  }, [medias]);

  const areImagesUploading = medias
    ?.filter(i => i.type === MediaType.IMAGE)
    .some(image => image.isUploading || image.uploadProgress < 100);
  const uploadEnabled = medias?.length < maxUploadable;

  const handlePickImages = async () => {
    setImageSelectionLoading(true);
    if (isSetup) {
      await pickMultipleImages(
        medias,
        setMedias,
        false,
        { selectionLimit: maxUploadable - medias?.length },
        setInfoMessage,
      );
    } else {
      await pickMultipleMedia(
        medias,
        setMedias,
        false,
        { selectionLimit: maxUploadable - medias?.length },
        setInfoMessage,
      );
    }
    setImageSelectionLoading(false);
  };

  const removeImage = (index: number) => {
    const newImages = [...medias];
    newImages.splice(index, 1);

    setMedias(newImages);
  };

  const finalizeSelection = () => {
    const productsWithInititalImages = medias.map(image => {
      const prevData = form.products.find(
        item =>
          item?.images?.findIndex(i => i.src === image.src) !== -1 ||
          item?.videos?.findIndex(v => v.src === image.src) !== -1,
      );

      const productMedia =
        image.type === MediaType.VIDEO ? { videos: [image], images: [] } : { images: [image], videos: [] };

      return prevData
        ? prevData
        : {
            ...productMedia,
            name: '',
            price: '',
            category: '',
            tags: [],
            description: '',
            thumbnail: 0,
            thumbnail_type: image.type === MediaType.VIDEO ? 'video' : 'image',
            price_unit: '',
            variants: {
              type: '',
              options: [],
            },
            id: generateSimpleUUID(),
          };
    });

    console.log('productsWithInititalImages: ', JSON.stringify(productsWithInititalImages));

    setForm({ ...form, products: productsWithInititalImages });

    console.log('productsWithInititalImages: ', JSON.stringify(productsWithInititalImages));

    saveState(form, medias);
    setCurrentStep('details');
  };

  return (
    <>
      <BaseScrollView>
        <View className="flex-1">
          <ScreenInfoHeader
            pageTitleTop={'Add Media'}
            pageTitleBottom={'For your Products'}
            iconElement={
              <CustomImage
                imageProps={{ source: require('@/assets/images/add-product-image.png'), contentFit: 'cover' }}
                className="w-80 h-80"
              />
            }
            colorPalette={ColorPaletteType.YELLOW}
          />
          <Container className="flex-1 mx-0 w-full">
            <SectionContainer className="py-12 px-15 rounded-[10px] flex-none">
              <StatusPill title={'IMPORTANT'} className="self-start" statusType={StatusType.DANGER} />
              <BaseText fontSize={12} classes="text-black-secondary mt-8 leading-[15px]">
                {uploadEnabled
                  ? 'Upload one media for every product you want to upload. Maximum of 10 media allowed.'
                  : "Maximum of 10 media allowed. You've reached the limit of images allowed at this time"}
              </BaseText>
            </SectionContainer>

            {medias?.length === 0 && !imageSelectionLoading && (
              <View className="w-full flex justify-center items-center py-24">
                <Pressable
                  onPress={handlePickImages}
                  disabled={!uploadEnabled}
                  style={{ opacity: uploadEnabled ? 1 : 0.4 }}>
                  <ImageSelectorPlaceholder />
                </Pressable>
              </View>
            )}

            {medias?.length > 0 && (
              <View className="flex flex-row items-start justify-between flex-wrap mt-25">
                {medias.map((image, index) => (
                  <ImageGridItem key={index} {...{ image, index, removeImage: () => removeImage(index) }} />
                ))}
                {uploadEnabled && (
                  <Pressable
                    className="bg-grey-bgOne self-start rounded-[10px] py-10 px-15 flex-shrink-0 mb-25"
                    onPress={handlePickImages}
                    style={{ width: cardWidth }}>
                    <Row className="justify-between">
                      <BaseText fontSize={12} weight="medium" classes="text-primary-main mr-12">
                        Add new image
                      </BaseText>
                      <CircledIcon className="p-5 bg-white">
                        <Add size={wp(20)} strokeWidth={2} variant="Linear" color={colors.primary.main} />
                      </CircledIcon>
                    </Row>
                  </Pressable>
                )}
              </View>
            )}
            {medias?.length < 1 && imageSelectionLoading && (
              <></>
              // <View className="flex-1 justify-center items-center min-h-[100px]">
              // <ActivityIndicator size={'small'} color={colors.grey.mutedDark} />
              //<BaseText fontSize={12} classes="text-black-muted mt-10 text-center">
              //{infoMessage}
              //</BaseText>
              //</View>
            )}

            {medias.length < 1 && imageSelectionLoading && (
              <AddProductImagesSkeletonLoader count={4} cardWidth={cardWidth} />
              // <View className="flex-1 justify-center items-center min-h-[100px]">
              //   <ActivityIndicator size={'small'} color={colors.grey.mutedDark} />
              //   <BaseText fontSize={12} classes="text-black-muted mt-10 text-center">
              //     {infoMessage}
              //   </BaseText>
              // </View>
            )}
          </Container>
        </View>
      </BaseScrollView>
      {savedStateExist && (
        <View className="absolute bottom-[120] w-full items-center">
          <Pressable className="self-center py-10 px-15 rounded-full bg-grey-bgOne" onPress={loadSavedState}>
            <Row classes="justify-start">
              <BaseText weight={'medium'} classes="text-black-secondary">
                Load media from last Progress?
              </BaseText>
              <BaseText weight={'semiBold'} classes={'text-primary-main ml-4'}>
                Click here
              </BaseText>
            </Row>
          </Pressable>
        </View>
      )}
      <FixedBtnFooter
        buttons={[
          showBackBtn
            ? {
                text: 'Back',
                onPress: () => setCurrentStep('method'),
                variant: ButtonVariant.LIGHT,
              }
            : null,
          {
            text: 'Proceed',
            onPress: finalizeSelection,
            disabled: areImagesUploading || medias?.length === 0,
          },
        ].filter(Boolean)}
      />
    </>
  );
};

const ImageGridItem = ({
  image,
  index,
  removeImage,
}: {
  image: AppMediaType;
  index: number;
  removeImage: VoidFunction;
}) => {
  const [thumbnail, setThumbnail] = useState<string>(null);
  const [loading, setLoading] = useState(false);

  // Generate single thumbnail at 2 seconds for video media types
  const generateThumbnails = useCallback(async () => {
    // Only generate thumbnails if the media type is video
    if (image.type !== MediaType.VIDEO) {
      return;
    }

    setLoading(true);
    try {
      const videoSource = image.src;

      // Generate single thumbnail at 2 seconds
      const thumbnailResult = await VideoThumbnails.getThumbnailAsync(videoSource, {
        time: 2000, // 2 seconds in milliseconds
        quality: 0.7,
      });

      setThumbnail(thumbnailResult.uri);
    } catch (error) {
      console.error('Error generating video thumbnail:', error);
    } finally {
      setLoading(false);
    }
  }, [image.type, image.url, image.src]);

  // Effect to generate thumbnail when component mounts and it's a video
  useEffect(() => {
    if (image.type === MediaType.VIDEO) {
      generateThumbnails();
    }
  }, [generateThumbnails, image.type]);

  return (
    <ListItemCard
      className={classNames('flex-shrink-0 mb-25 py-0', { 'mr-10': index % 2 === 0, 'ml-10': index % 2 !== 0 })}
      style={{ maxWidth: cardWidth }}
      leftElement={
        <View className="w-[50px] h-[50px] rounded-[10px] relative">
          {image.type === MediaType.IMAGE && (
            <Image
              className="w-[50px] h-[50px] rounded-[10px]"
              source={{
                uri: image.url ?? image.src,
              }}
              resizeMode={'cover'}
            />
          )}
          {image.type === MediaType.VIDEO && (
            <Image
              className="w-[50px] h-[50px] rounded-[10px]"
              source={{
                uri: thumbnail ?? image.url ?? image.src,
              }}
              resizeMode={'cover'}
              style={{ opacity: loading ? 0.5 : 1 }}
            />
          )}
          {image.isUploading && (
            <View
              className="absolute bottom-0 right-0 h-full w-full flex items-center justify-center rounded-[10px]"
              style={{ backgroundColor: '#292D321A' }}>
              <CircularProgress
                value={image.uploadProgress ?? 0}
                radius={wp(16)}
                duration={500}
                delay={600}
                activeStrokeWidth={wp(4)}
                inActiveStrokeWidth={wp(4)}
                strokeLinecap={'round'}
                activeStrokeColor={colors.accentGreen.main}
                inActiveStrokeColor={colors.white}
                maxValue={100}
                valueSuffix={'%'}
                progressValueStyle={{
                  fontSize: wp(8),
                  fontFamily: 'Inter-Bold',
                  color: colors.white,
                }}
              />
            </View>
          )}
        </View>
      }
      bottomElement={
        <View>
          <View className="h-[6px] bg-grey-bgOne mt-10 rounded-full" />
          <View className="h-[6px] w-3/4 bg-grey-bgOne mt-5 rounded-full" />
        </View>
      }
      title={`Product ${index + 1}`}
      titleProps={{ type: 'heading', classes: 'text-black-muted' }}
      titleAddon={
        !image.isUploading ? (
          <Pressable onPress={removeImage}>
            <View className="h-24 w-24 flex items-center justify-center rounded-full bg-grey-bgOne">
              <Close currentColor={colors.grey.muted} size={wp(10)} />
            </View>
          </Pressable>
        ) : undefined
      }
    />
  );
};

export default AddProductImages;
