import React, { useMemo } from 'react';
import { ProductCreateMethod } from './types';
import { ClipboardText, Edit, Instagram, Shop } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { wp } from 'src/assets/utils/js';
import { View } from 'react-native';
import ScreenInfoHeader from '@/components/ui/screen-info-header';
import { ColorPaletteType } from 'src/constant/static-data';
import Container from '@/components/ui/container';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '@/components/ui/cards/list-item-card';
import { CircledIcon } from '@/components/ui';
import CustomImage from 'src/components/ui/others/custom-image';
import useAuthContext from 'src/contexts/auth/auth-context';

interface Props {
  selectMethod: (method: ProductCreateMethod) => void;
  hasMultipleStores?: boolean;
  uploadMethods: { [key: string]: boolean };
  isSetup?: boolean;
}

const SelectProductCreateMethod = ({ selectMethod, hasMultipleStores, uploadMethods, isSetup }: Props) => {
  const canUploadFromInstagram = uploadMethods[ProductCreateMethod.INSTAGRAM];
  const { userHasSetup, user } = useAuthContext();

  const options = useMemo(() => {
    return [
      {
        title: 'Add Manually',
        description: 'Manually provide product information',
        icon: <Edit size={wp(20)} variant={'Bold'} color={colors.accentOrange.main} />,
        show: true,
        disable: !uploadMethods[ProductCreateMethod.MANUAL],
        key: ProductCreateMethod.MANUAL,
      },
      {
        title: 'Import From Menu Image',
        description: "Extract items from an image of your restaurant's menu",
        icon: <ClipboardText size={wp(20)} variant={'Bold'} color={colors.accentRed.main} />,
        show: uploadMethods[ProductCreateMethod.MENU_IMAGE],
        disable: !uploadMethods[ProductCreateMethod.MENU_IMAGE],
        key: ProductCreateMethod.MENU_IMAGE,
      },
      {
        title: 'Import from Store',
        description: 'Import products from other stores you own',
        icon: <Shop size={wp(20)} variant={'Bold'} color={colors.accentYellow.main} />,
        show: hasMultipleStores,
        disable: !uploadMethods[ProductCreateMethod.STORE],
        key: ProductCreateMethod.STORE,
      },
      {
        title: 'Import from Instagram',
        description: canUploadFromInstagram
          ? 'Import products from your instagram posts'
          : 'Upgrade to a paid plan to import from IG',
        icon: <Instagram size={wp(20)} variant={'Bold'} color={colors.accentRed.main} />,
        disable: !canUploadFromInstagram,
        show: true,
        key: ProductCreateMethod.INSTAGRAM,
        // extraAction: !canUploadFromInstagram && {
        //   label: "Upgrade Subscription",
        //   onClick: () => router.push("/my-store/change-plan"),
        // },
      },
      // {
      //   title: "Import from Chowdeck",
      //   description: "Import items from your Chowdeck account",
      //   icon: (
      //     <>
      //       {/* prettier-ignore */}
      //       <svg className="text-green-500" width="21" height="24" fill="none">
      //         <path d="M12.49 13.226c-.696.363-1.498.458-2.259.268a3.243 3.243 0 0 1-1.877-1.302 3.402 3.402 0 0 1 .2-4.237 3.25 3.25 0 0 1 4.22-.633c1.353.847 2.86 1.41 4.432 1.654 1.251.176 2.52.23 3.794.338C20.848 5.331 17.43.22 11.337.006a9.978 9.978 0 0 0-5.375 1.407 10.152 10.152 0 0 0-3.823 4.076 10.316 10.316 0 0 0 .825 10.777l-.166-.126L0 24l6.44-4.74a9.978 9.978 0 0 0 4.214 1.122c5.662.233 9.74-4.242 10.243-8.435a.704.704 0 0 0-.159-.055c-2.86-.265-5.627 0-8.247 1.335" fill="currentColor" data-darkreader-inline-fill="" >
      //         </path>
      //       </svg>
      //     </>
      //   ),
      //   show: uploadMethods[ProductCreateMethod.CHOWDECK],
      //   disable: !uploadMethods[ProductCreateMethod.CHOWDECK],
      //   key: ProductCreateMethod.CHOWDECK,
      //   color: "text-accent-orange-500",
      // },
    ].filter(o => o.show);
  }, [hasMultipleStores]);

  const titleOptions = {
    setup: [`Great Job, ${user?.name?.split(' ')[0]}!`, 'Now add 2 Products'],
    default: ['Add New', 'Storefront Products'],
  };

  return (
    <View className="flex-1">
      <ScreenInfoHeader
        pageTitleTop={titleOptions[isSetup ? 'setup' : 'default'][0]}
        pageTitleBottom={titleOptions[isSetup ? 'setup' : 'default'][1]}
        colorPalette={ColorPaletteType.YELLOW}
        iconElement={
          <CustomImage
            imageProps={{
              source: userHasSetup
                ? require('@/assets/images/add-product.png')
                : require('@/assets/images/add-product-setup.png'),
              contentFit: 'cover',
            }}
            className="w-80 h-80"
          />
        }
      />
      <Container className="mt-20 flex-none">
        <SectionContainer className="mt-0">
          {options.map((o, index) => (
            <ListItemCard
              leftElement={<CircledIcon iconBg="bg-white">{o.icon}</CircledIcon>}
              titleProps={{ fontSize: wp(13), type: 'heading', style: { marginBottom: wp(3) } }}
              className="py-24"
              title={o.title}
              description={o.description}
              onPress={() => selectMethod(o.key)}
              showBorder={index !== options.length - 1}
              descriptionProps={{ weight: 'regular' }}
              key={index}
            />
          ))}
        </SectionContainer>
      </Container>
    </View>
  );
};

export default SelectProductCreateMethod;

// lowest size
// {"clamped": 10.8, "fontScale": 0.823, "normalSize": 12, "pixelDensity": 3, "screenWidth": 402, "wpSize": 16}

// {"clamped": 10.8, "fontScale": 0.882, "normalSize": 12, "pixelDensity": 3, "screenWidth": 402, "wpSize": 16}

// {"clamped": 10.8, "fontScale": 1, "normalSize": 12, "pixelDensity": 3, "screenWidth": 402, "wpSize": 16}

//{"clamped": 10.8, "fontScale": 1.118, "normalSize": 12, "pixelDensity": 3, "screenWidth": 402, "wpSize": 16}
