import { FlatList, FlatListProps, ListRenderItem, View } from 'react-native';
import { mockProducts } from '@/constant/mock-data';
import ProductCard, { ProductCardProps } from '@/components/products/storefront-products/product-card';
import EmptyState, { EmptyStateProps } from '@/components/ui/empty-states/empty-state';
import StorefrontSkeletonLoader from './storefront-skeleton-loader';
import { ProductItemInterface } from 'catlog-shared';
import { BottomSheetFlashList, BottomSheetFlatList } from '@gorhom/bottom-sheet';
import { FlashList, FlashListProps } from 'node_modules/@shopify/flash-list/dist';
import { memo, useEffect, useState } from 'react';

interface ProductViewProps extends Partial<FlashListProps<any>> {
  products: ProductItemInterface[];
  screen?: ProductCardScreenType;
  productCardProps?: Partial<ProductCardProps>;
  isLoading?: boolean;
  isReLoading?: boolean;
  customRenderItem?: ListRenderItem<ProductItemInterface> | null | undefined;
  emptyStateProps?: EmptyStateProps;
  handleOnEndReached?: VoidFunction;
  extraData?: any;
}

export enum ProductCardScreenType {
  'STOREFRONT' = 'Storefront',
  'COUPON' = 'Coupon',
  'DISCOUNT' = 'Discount',
  'SELECT_PRODUCT' = 'Select Product',
}

const ProductList = memo(
  ({
    products = [],
    isLoading = false,
    isReLoading = false,
    customRenderItem,
    screen = ProductCardScreenType.STOREFRONT,
    productCardProps,
    handleOnEndReached,
    emptyStateProps,
    extraData,
    ...props
  }: ProductViewProps) => {
    

    const renderItem = ({ item }: { item: ProductItemInterface }) => {
      return <ProductCard fadeImage={false} product={item} screen={screen} {...productCardProps} />;
    };

    return (
      <BottomSheetFlashList
        extraData={extraData}
       
        data={products}
        numColumns={2}
        ListEmptyComponent={() =>
          isLoading ? (
            <View className="mt-20">
              <StorefrontSkeletonLoader />
            </View>
          ) : (
            <View className="mt-32">
              <EmptyState showBtn={false} text={'No products to show'} {...emptyStateProps} />
            </View>
          )
        }

        renderItem={customRenderItem ? customRenderItem : renderItem}
        onEndReached={() => {
          if (!isLoading) {
            handleOnEndReached?.();
          }
        }}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {isReLoading && (
              <View className="mt-20">
                <StorefrontSkeletonLoader />
              </View>
            )}
          </View>
        }
        {...props}
      />
    );

    /* return (
      <FlatList
        onScroll={() => {
          setItemsCache(products);
        }}
        initialNumToRender={20}
        data={itemsCache}
        numColumns={2}
        ListEmptyComponent={() =>
          isLoading ? (
            <View className="mt-20">
              <StorefrontSkeletonLoader />
            </View>
          ) : (
            <View className="mt-32">
              <EmptyState showBtn={false} text={'No products to show'} {...emptyStateProps} />
            </View>
          )
        }
        className="px-10"
        contentContainerStyle={{ flexGrow: 1 }}
        renderItem={customRenderItem ? customRenderItem : renderItem}
        onEndReached={() => {
          if (!isLoading) {
            handleOnEndReached?.();
          }
        }}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {isReLoading && (
              <View className="mt-20">
                <StorefrontSkeletonLoader />
              </View>
            )}
          </View>
        }
        {...props}
      />
    ); */
  },
);

export default ProductList;
