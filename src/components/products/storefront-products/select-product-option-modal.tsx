import { ProductItemInterface, VariantForm, VariantItem, CartItem } from 'catlog-shared';
import { useState } from 'react';
import { View } from 'react-native';
import useLayoutHeight from 'src/hooks/use-layout-height';

import CustomVariantProductSelection from './custom-variant-product-selection';
import ImageVariantProductSelection from './image-variant-product-selection';

import { ButtonVariant } from '@/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';

interface SelectProductOptionModalProps extends Partial<BottomModalProps> {
  variant: VariantForm;
  product: ProductItemInterface;
  selectedProduct: CartItem;
  // Compute all selected products to search for the variants
  selectedVariants: VariantItem[];
  selectedProductsId: { id: string; variant_id?: string | null }[];
  closeModal: () => void;
  onPressContinue: (selectedProducts: string[]) => void;
  setSelectedProducts: (selections?: { id: string; variant_id?: string | null; quantity?: number | null }[]) => void;
}

const SelectProductOptionModal = (props: SelectProductOptionModalProps) => {
  const {
    closeModal,
    onPressContinue,
    product,
    selectedProductsId,
    variant,
    selectedVariants,
    selectedProduct,
    setSelectedProducts,
    ...rest
  } = props;
  const [step, setStep] = useState<'selectVariant' | 'selectVariantOption'>('selectVariant');

  const { onLayout, flexStyle } = useLayoutHeight(0);

  const { type } = variant;

  return (
    <BottomModal
      {...props}
      modalStyle={flexStyle}
      containerStyle={flexStyle}
      innerStyle={{ ...flexStyle, paddingBottom: 0 }}
      closeModal={closeModal}
      showButton={false}
      enableDynamicSizing
      enableSnapPoints={false}
      useScrollView={false}
      // useChildrenAsDirectChild
      buttons={[
        {
          text: 'Go Back',
          variant: ButtonVariant.LIGHT,
          onPress: () => {
            // closeModal();
          },
        },
        {
          text: 'Continue',
          onPress: () => {
            setStep('selectVariantOption');
            // closeModal();
          },
        },
      ]}
      title="Select Options">
      {/* <ScrollView> */}
      <View style={flexStyle}>
        {type === 'images' && (
          <ImageVariantProductSelection
            variant={variant}
            product={product}
            selectedVariants={selectedVariants}
            setSelectedProducts={setSelectedProducts}
            selectedProductsId={selectedProductsId}
            closeModal={closeModal}
          />
        )}
        {type === 'custom' && (
          <CustomVariantProductSelection
            variant={variant}
            product={product}
            closeModal={closeModal}
            setSelectedProducts={setSelectedProducts}
            selectedProductsId={selectedProductsId}
            selectedVariants={selectedVariants}
          />
        )}
      </View>
      {/* </ScrollView> */}
    </BottomModal>
  );
};

export default SelectProductOptionModal;
