import { CartItem, Category, GetItemsParams, ProductItemInterface, VariantForm, VariantItem } from 'catlog-shared';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, View } from 'react-native';
import useLayoutHeight from 'src/hooks/use-layout-height';
import useModals from 'src/hooks/use-modals';

import ProductCard from './product-card';
import ProductsList from './products-list';
import SelectProductOptionModal from './select-product-option-modal';

import { delay, wp } from '@/assets/utils/js';
import { Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import useAuthContext from '@/contexts/auth/auth-context';
import { ApiData } from '@/hooks/use-api';
import colors from '@/theme/colors';
import useKeyboard from 'src/hooks/use-keyboard';
import SelectCustomOptionModal from './select-custom-option-modal';
import SelectImageOptionModal from './select-image-option-modal';

interface SelectProductModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue: (selectedProductsId: string[]) => void;
  modalTitle?: string;
  products: ProductItemInterface[];
  getProductsRequest: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
  selectedProducts: CartItem[];
  setSelectedProducts: (selections?: { id: string; variant_id?: string | null; quantity?: number | null }[]) => void;
}

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2 + 20;

const SelectProductsModal = (props: SelectProductModalProps) => {
  const { closeModal, modalTitle, getProductsRequest, products, selectedProducts, setSelectedProducts } = props;
  const { modals, toggleModal } = useModals(['selectProductOptionModal', 'customOptionModal', 'imageOptionModal']);
  const [selectedProduct, setSelectedProduct] = useState<ProductItemInterface>(null);

  const [searchText, setSearchText] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      if (`${searchText}`.trim() !== '') {
        const filtered = products.filter(p => new RegExp(`${searchText}`, 'ig').test(p.name));
        setFilteredProducts(filtered);
      } else setFilteredProducts(products);
    }, 200);
  }, [searchText, products]);

  const selectedProductsId = selectedProducts?.map(p => ({
    id: p.item?.id!,
    variant_id: p?.variant_id!,
    quantity: p.quantity,
  }));

  const handleToggleProductSelection = (itemId: string) => {
    const itemIdInList = selectedProductsId.map(el => el.id).indexOf(itemId);
    const selectedProductsCopy = [...selectedProductsId];

    if (itemIdInList > -1) {
      selectedProductsCopy.splice(itemIdInList, 1);
      setSelectedProducts(selectedProductsCopy);
      return;
    }
    setSelectedProducts([...selectedProductsId, { id: itemId }]);
  };

  // const selectedVariants = selectedProducts
  //   .filter(i => i.item_id === selectedProduct.id)
  //   .map(i => i.variant)
  //   .filter(variant => variant !== null);

  const selectedVariants = useMemo(() => {
    return selectedProducts
      .filter(i => i.item_id === selectedProduct?.id)
      .map(i => i.variant)
      .filter(variant => variant !== null);
  }, [selectedProducts, selectedProduct]);

  const getTagText = (p: ProductItemInterface) => {
    if (!p.available) {
      return 'Unavailable';
    }
    if (p?.variants?.options.length > 0) {
      return `${p?.variants?.options.length} Options`;
    }
    return '';
  };

  return (
    <BottomModal
      {...props}
      size="lg"
      closeModal={closeModal}
      useScrollView={false}
      useChildrenAsDirectChild
      buttons={[
        {
          text: 'Continue',
          onPress: () => {
            // onPressContinue(selectedProductsId);
            closeModal();
          },
        },
      ]}
      title={modalTitle}>
      <View className="h-full px-10">
        <View className="mt-15 px-10 pb-5">
          <Input
            containerClasses="bg-white"
            label="Search Products"
            useBottomSheetInput
            onChangeText={setSearchText}
            value={searchText}
            returnKeyType="search"
            rightAccessory={<Search size={wp(18)} primaryColor={colors?.black.muted} />}
          />
        </View>
        <ProductsList
          extraData={selectedProducts}
          products={filteredProducts ?? []}
          isLoading={getProductsRequest?.isLoading}
          isReLoading={getProductsRequest?.isReLoading}
          estimatedItemSize={cardWidth}
          customRenderItem={({ item }) => {
            return (
              <View>
                <ProductCard
                  product={item}
                  selectionActive
                  disabled={!item?.available}
                  onPress={async () => {
                    if (item.variants?.options?.length! > 0) {
                      setSelectedProduct(item);
                      await delay(300);
                      if (item.variants.type === 'custom') {
                        toggleModal('customOptionModal');
                      } else {
                        toggleModal('imageOptionModal');
                        // toggleModal('selectProductOptionModal');
                      }
                      return;
                    }
                    handleToggleProductSelection(item.id);
                  }}
                  selected={selectedProducts?.findIndex(p => p.item_id == item.id) > -1}
                  showRightElement={false}
                  tagText={getTagText(item)}
                />
              </View>
            );
          }}
          className="px-20"
        />
        {modals.selectProductOptionModal && (
          <SelectProductOptionModal
            isVisible={modals.selectProductOptionModal}
            selectedProductsId={selectedProductsId}
            setSelectedProducts={setSelectedProducts}
            closeModal={() => toggleModal('selectProductOptionModal', false)}
            variant={selectedProduct?.variants ?? ({} as VariantForm)}
            onPressContinue={() => {}}
            product={selectedProduct}
            selectedProduct={selectedProducts.find(i => i.item_id === selectedProduct?.id) ?? ({} as CartItem)}
            selectedVariants={selectedVariants ?? ([] as VariantItem[])}
          />
        )}
        {selectedProduct && modals.customOptionModal && (
          <SelectCustomOptionModal
            isVisible={modals.customOptionModal}
            selectedProductsId={selectedProductsId}
            setSelectedProducts={setSelectedProducts}
            closeModal={() => toggleModal('customOptionModal', false)}
            variant={selectedProduct?.variants ?? ({} as VariantForm)}
            onPressContinue={() => {}}
            product={selectedProduct}
            // selectedProduct={selectedProducts.find(i => i.item_id === selectedProduct.id) ?? ({} as CartItem)}
            selectedVariants={selectedVariants ?? ([] as VariantItem[])}
          />
        )}
        {selectedProduct && (
          <SelectImageOptionModal
            isVisible={modals.imageOptionModal}
            selectedProductsId={selectedProductsId}
            setSelectedProducts={setSelectedProducts}
            closeModal={() => toggleModal('imageOptionModal', false)}
            variant={selectedProduct?.variants ?? ({} as VariantForm)}
            onPressContinue={() => {}}
            product={selectedProduct}
            // selectedProduct={selectedProducts.find(i => i.item_id === selectedProduct.id) ?? ({} as CartItem)}
            selectedVariants={selectedVariants ?? ([] as VariantItem[])}
          />
        )}
      </View>
    </BottomModal>
  );
};

export default SelectProductsModal;
// import { ScrollView, Text, View } from 'react-native';
// import { useNavigation } from '@react-navigation/native';
// import BottomModal, { BottomModalProps } from '../../ui/modals/bottom-modal';
// import { hp, wp } from '@/assets/utils/js';
// import colors from '@/theme/colors';
// import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../../ui';
// import ProductInfoRow from '../product-info-row';
// import { ReactNode, useEffect, useRef, useState } from 'react';
// import { ArrowUpRight, Search } from '../../ui/icons';
// import { mockCategories, mockProducts } from '@/constant/mock-data';
// // import Input from '../../ui/inputs/input';
// import Radio from '../../ui/buttons/radio';
// import SelectDropdown, { DropDownMethods } from '../../ui/inputs/select-dropdown';
// import ProductCard from './product-card';
// import { ApiData, useApi } from '@/hooks/use-api';
// // import useAuthContext from '@/contexts/auth/auth-context';
// import usePagination from '@/hooks/use-pagination';
// import { ProductsResponse } from '@/screens/products/storefront';
// import ProductsList from './products-list';
// import SelectProductOptionModal from './select-product-option-modal';
// import useModals from 'src/hooks/use-modals';
// // // import useLayoutHeight from 'src/hooks/use-layout-height';

// interface SelectProductModalProps extends Partial<BottomModalProps> {
//   closeModal: () => void;
//   modalTitle?: string;
//   products: ProductItemInterface[];
//   onPressContinue: (selectedProductsId: string[]) => void;
//   getProductsRequest: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
//   selectedProducts: OrderItem[];
//   // setSelectedProducts: (selectedProductsId: string[]) => void;
//   setSelectedProducts: (
//     selections?: { id: string; variant_id?: string | null; quantity?: number | null }[],
//   ) => void;
// }

// const SelectProductsModal = (props: SelectProductModalProps) => {
//   const {
//     closeModal,
//     onPressContinue,
//     modalTitle,
//     getProductsRequest,
//     products,
//     selectedProducts,
//     setSelectedProducts,
//     selectedStore,
//     ...rest
//   } = props;
//   const { store, categories, stores } = useAuthContext();
//   const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
//   const [timeoutRefId, setTimeoutRefId] = useState<NodeJS.Timeout>();
//   const [filteredProducts, setFilteredProducts] = useState<ProductItemInterface[]>(products ?? []);
//   const [searchText, setSearchText] = useState<string>();
//   const [selectedProduct, setSelectedProduct] = useState<ProductItemInterface>({} as ProductItemInterface);

//   const { modals, toggleModal } = useModals(['selectProductOptionModal']);

//   const selectedProductsId = selectedProducts?.map(p => ({ id: p?.item_id!, variant_id: p?.variant_id!, quantity: p.quantity }));
//   const selectedProductsVariantsId = [];

//   console.log(selectedProductsId.map(i => i.id));

//   const dropDownRef = useRef<DropDownMethods>(null);
//   const defaultCategories = selectedStore ? stores?.find(s => s.id === selectedStore)?.categories ?? [] : categories;

//   useEffect(() => {
//     setFilteredProducts(products ?? []);
//   }, [products]);

//   const handleToggleProductSelection = (itemId: string, variantId?: string) => {
//     // const itemIdInList = selectedProductsId.indexOf(itemId);
//     const itemIdInList = selectedProductsId.map((el) => el.id).indexOf(itemId);
//     const selectedProductsCopy = [...selectedProductsId];

//     if (itemIdInList > -1 && !variantId) {
//       selectedProductsCopy.splice(itemIdInList, 1);
//       setSelectedProducts(selectedProductsCopy);
//       return;
//     }
//     setSelectedProducts([...selectedProductsId, {id: itemId, variant_id: variantId}]);
//   };

//   const onSelectCategory = (item: string) => {
//     const productsToSelectOrRemove = filteredProducts.filter(product => product.category?.id === item).map(p => p.id);

//   //   if (selectedCategories.includes(item)) {
//   //     setSelectedProducts(selectedProductsId.filter(product => !productsToSelectOrRemove.includes(product.id)));
//   //     setSelectedCategories(selectedProductsId.filter(category => category !== item));
//   //     return;
//   //   }

//   //   setSelectedProducts([...selectedProductsId, ...productsToSelectOrRemove]);
//   //   setSelectedCategories([...selectedProductsId, item]);
//   // };

//   const handleSearch = (text: string) => {
//     setSearchText(text);

//     if (timeoutRefId) {
//       clearTimeout(timeoutRefId);
//     }

//     setTimeoutRefId(
//       setTimeout(() => {
//         if (!text || text === '') {
//           setFilteredProducts(products ?? []);
//           return;
//         }
//         const filtered = products?.filter(product => product.name.toLowerCase().includes(text.toLowerCase()));
//         setFilteredProducts(filtered ?? []);
//       }, 300),
//     );
//   };

//   const categoriesMapped = (defaultCategories ?? []).map((item: Category) => ({
//     value: item.id!,
//     label: item.name + ' ' + item.emoji,
//   }));

//   // const flexStyle = filteredProducts?.length > 4 || getProductsRequest?.isLoading ? { flex: 1 } : undefined;
//   const getVariantProducts = () => {
//     let variant: OrderItem[] = [];

//     selectedProducts?.forEach(i => {
//       if (i.variant_id) {
//         variant.push(i)
//       }
//     });

//     return variant
//   };

//   const selectedVariants = selectedProducts
//     .filter(i => i.item_id === selectedProduct.id)
//     .map(i => i.variant)
//     .filter(variant => variant !== null);

//   // const flexStyle = products?.length > 4 || getProductsRequest?.isLoading ? { flex: 1 } : undefined;
//   const { onLayout, flexStyle } = useLayoutHeight(0);

//   return (
//     <BottomModal
//       {...props}
//       modalStyle={flexStyle}
//       containerStyle={flexStyle}
//       innerStyle={flexStyle}
//       closeModal={closeModal}
//       buttons={[
//         {
//           text: 'Continue',
//           onPress: () => {
//             // onPressContinue(selectedProducts);
//             // onPressContinue(selectedProductsId);
//             closeModal();
//           },
//         },
//       ]}
//       title={modalTitle}>
//       <View style={flexStyle} onLayout={onLayout}>
//         <View className="mt-15 px-20 pb-5">
//           <Input
//             containerClasses="bg-white"
//             // editable={false}
//             label="Search Products"
//             rightAccessory={<Search size={wp(18)} primaryColor={colors?.black.muted} />}
//           />
//           <Row className="mt-15">
//             <Row>
//               <Radio active={false} />
//               <BaseText classes="ml-4 text-black-placeholder">Select All</BaseText>
//             </Row>
//             <WhiteCardBtn
//               className="bg-grey-bgOne rounded-full"
//               onPress={() => dropDownRef.current?.open()}
//               icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}>
//               Select by category
//             </WhiteCardBtn>
//           </Row>
//         </View>
//         <ProductsList
//           products={products ?? []}
//           isLoading={getProductsRequest?.isLoading}
//           productCardProps={{
//             selectionActive: true,
//             onPress: (product: Partial<ProductItemInterface>) =>
//               setSelectedProducts([...selectedProductsId, {id: product.id!}]),
//           }}
//           customRenderItem={({ item }) => {
//             return (
//               <ProductCard
//                 product={item}
//                 selectionActive
//                 onPress={() => {
//                   if (item.variants?.options?.length! > 0) {
//                     setSelectedProduct(item);
//                     toggleModal('selectProductOptionModal');
//                     return;
//                   }
//                   handleToggleProductSelection(item.id);
//                 }}
//                 selected={selectedProductsId.map(i => i.id).includes(item.id)}
//                 showRightElement={false}
//               />
//             );
//           }}
//         />
//         <SelectProductOptionModal
//           isVisible={modals.selectProductOptionModal}
//           selectedProductsId={selectedProductsId}
//           setSelectedProducts={setSelectedProducts}
//           closeModal={() => toggleModal('selectProductOptionModal', false)}
//           variant={selectedProduct?.variants ?? ({} as VariantForm)}
//           onPressContinue={() => {}}
//           product={selectedProduct}
//           selectedProduct={selectedProducts.find(i => i.item_id === selectedProduct.id) ?? ({} as OrderItem)}
//           selectedVariants={selectedVariants ?? ([] as VariantItem[])}
//         />
//         <SelectDropdown
//           ref={dropDownRef}
//           showAnchor={false}
//           selectedItems={selectedCategories}
//           isMultiSelect
//           onPressItem={(id) => {
//             // onSelectCategory
//           }}
//           label={'Categories'}
//           items={categoriesMapped}
//           containerClasses="mt-15"
//           //add selected variants of the product
//         />
//       </View>
//     </BottomModal>
//   );
// };

// export default SelectProductsModal;
