import React from 'react';
import { Dimensions, View } from 'react-native';
import { hp, wp } from '@/assets/utils/js';
import cx from 'classnames';
import Shimmer from '../../ui/shimmer';

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const dummyRow = new Array(2).fill(0);

interface StorefrontSkeletonLoaderProps {}

const ProductsSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return (
    <View className="flex-1 px-10 py-10">
      {dummyRow.map((_, index) => (
        <View className={cx('flex-row justify-between', { 'mt-0': index === 0, 'mt-[60px]': index !== 0 })} key={index}>
          <View style={{ height: cardWidth, width: cardWidth }}>
            <Shimmer borderRadius={wp(12)} height={cardWidth} width={cardWidth} />
            <View className="h-8" />
            <Shimmer borderRadius={12} height={hp(12)} width={0.6 * cardWidth} />
            <View className="h-8" />
            <Shimmer borderRadius={12} height={hp(10)} width={0.8 * cardWidth} />
          </View>
          <View style={{ height: cardWidth, width: cardWidth }}>
            <Shimmer borderRadius={wp(12)} height={cardWidth} width={cardWidth} />
            <View className="h-8" />
            <Shimmer borderRadius={12} height={hp(12)} width={0.6 * cardWidth} />
            <View className="h-8" />
            <Shimmer borderRadius={12} height={hp(10)} width={0.8 * cardWidth} />
          </View>
        </View>
      ))}
    </View>
  );
};

export default ProductsSkeletonLoader;
