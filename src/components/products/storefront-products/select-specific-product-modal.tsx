import { Category, GetItemsParams, ProductItemInterface } from 'catlog-shared';
import { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';

import ProductCard from './product-card';
import ProductsList from './products-list';

import { wp } from '@/assets/utils/js';
import { BaseText, Row, WhiteCardBtn } from '@/components/ui';
import Radio from '@/components/ui/buttons/radio';
import { ArrowUpRight, Search } from '@/components/ui/icons';
import Input from '@/components/ui/inputs/input';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import useAuthContext from '@/contexts/auth/auth-context';
import { ApiData } from '@/hooks/use-api';
import colors from '@/theme/colors';
import Pressable from 'src/components/ui/base/pressable';

interface SelectSpecificProductsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  modalTitle?: string;
  products: ProductItemInterface[];
  // onPressContinue: (selectedProducts: string[]) => void;
  onPressContinue: VoidFunction;
  getProductsRequest?: ApiData<GetItemsParams, { data: { items: ProductItemInterface[] } }>;
  selectedProducts: string[];
  setSelectedProducts: (selectedProducts: string[]) => void;
  loadingStates?: { isLoading: boolean; isReLoading: boolean };
}

const SelectSpecificProductsModal = (props: SelectSpecificProductsModalProps) => {
  const {
    closeModal,
    onPressContinue,
    modalTitle,
    getProductsRequest,
    products,
    selectedProducts,
    setSelectedProducts,
    loadingStates,
  } = props;
  const { categories } = useAuthContext();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const dropDownRef = useRef<DropDownMethods>(null);

  const [searchText, setSearchText] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      if (`${searchText}`.trim() !== '') {
        const filtered = products.filter(p => new RegExp(`${searchText}`, 'ig').test(p.name));
        setFilteredProducts(filtered);
      } else setFilteredProducts(products);
    }, 200);
  }, [searchText, products]);

  const handleToggleProductSelection = (itemId: string) => {
    const itemIdInList = selectedProducts.indexOf(itemId);
    const selectedProductsCopy = [...selectedProducts];

    if (itemIdInList > -1) {
      selectedProductsCopy.splice(itemIdInList, 1);
      setSelectedProducts(selectedProductsCopy);
      return;
    }
    setSelectedProducts([...selectedProducts, itemId]);
  };

  const onSelectCategory = (item: string) => {
    const productsToSelectOrRemove = products.filter(product => product.category?.id === item).map(p => p.id);

    if (selectedCategories.includes(item)) {
      setSelectedProducts(selectedProducts.filter(p => !productsToSelectOrRemove.includes(p)));
      setSelectedCategories(selectedProducts.filter(category => category !== item));
      return;
    }

    setSelectedProducts([...selectedProducts, ...productsToSelectOrRemove]);
    setSelectedCategories([...selectedProducts, item]);
  };

  const toggleSelectAll = () => {
    const selectedAll = selectedProducts.length === products.length;
    if (selectedAll) {
      setSelectedProducts([]);
    } else setSelectedProducts([...(products ?? []).map(p => p.id)]);
  };

  const categoriesMapped = (categories ?? []).map?.((item: Category) => ({
    value: item.id!,
    label: item.name + ' ' + item.emoji,
  }));

  return (
    <BottomModal
      {...props}
      useScrollView={false}
      useChildrenAsDirectChild
      closeModal={closeModal}
      size="lg"
      buttons={[
        {
          text: 'Continue',
          onPress: onPressContinue,
        },
      ]}
      title={modalTitle}>
      <View className="h-full flex-1 px-10">
        <View className="pt-15 px-10 pb-5 bg-white">
          <Input
            containerClasses="bg-white"
            label="Search Products"
            onChangeText={setSearchText}
            value={searchText}
            returnKeyType="search"
            rightAccessory={<Search size={wp(18)} primaryColor={colors?.black.muted} />}
          />
          <Row className="mt-15">
            <Pressable onPress={toggleSelectAll} className="flex-row items-center" style={{ columnGap: 5 }}>
              <Radio active={selectedProducts.length === products.length} />
              <BaseText classes="ml-4 text-black-placeholder">Select All</BaseText>
            </Pressable>
            <WhiteCardBtn
              className="bg-grey-bgOne rounded-full"
              onPress={() => dropDownRef.current?.open()}
              icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}>
              Select by category
            </WhiteCardBtn>
          </Row>
        </View>
        <ProductsList
          products={filteredProducts ?? []}
          isLoading={loadingStates?.isLoading}
          isReLoading={loadingStates?.isReLoading}
          extraData={selectedProducts}
          customRenderItem={({ item }) => {
            return (
              <ProductCard
                product={item}
                selectionActive
                onPress={() => {
                  console.log("clicking")
                  // return
                  handleToggleProductSelection(item.id);
                }}
                selected={selectedProducts?.includes(item.id)}
                showRightElement={false}
              />
            );
          }}
        />
      </View>
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItems={selectedCategories}
        isMultiSelect
        onPressItem={onSelectCategory}
        label="Categories"
        items={categoriesMapped}
        containerClasses="mt-15"
      />
    </BottomModal>
  );
};

export default SelectSpecificProductsModal;
