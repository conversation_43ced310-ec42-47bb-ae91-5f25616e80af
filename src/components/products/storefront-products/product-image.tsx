import { Fragment, ReactNode, useMemo } from 'react';
import { ActivityIndicator, Image, Text, View, ViewProps } from 'react-native';
import BaseText from '@/components/ui/base/base-text';
import colors from '@/theme/colors';
import { styled } from 'nativewind';
import cx from 'classnames';
import { Close } from '../../ui/icons';
import Pressable, { PressableProps } from '../../ui/base/pressable';
import { hp, wp } from '@/assets/utils/js';
import { BlurView } from 'expo-blur';
import Animated, { SharedValue, useAnimatedStyle } from 'react-native-reanimated';
import { SharedTransition, withSpring } from 'react-native-reanimated';
import CustomImage from '../../ui/others/custom-image';

interface ProductImageProps extends Partial<PressableProps> {
  imageUri: string;
  isThumbnail?: boolean;
  showCloseBtn?: boolean;
  isUploading?: boolean;
  sharedTransitionTag?: string;
  progressWidth?: SharedValue<number>;
  onPressDelete?: VoidFunction;
}

const ProductImage = ({
  isThumbnail,
  sharedTransitionTag,
  isUploading,
  showCloseBtn,
  progressWidth,
  imageUri,
  onPressDelete,
  ...props
}: ProductImageProps) => {
  return (
    <Fragment>
      <Pressable
        className={cx('h-[60px] w-[60px] rounded-[12px] overflow-hidden', {
          'border-[3px] border-accentGreen-main': isThumbnail,
          'border-[0px]': !isThumbnail,
        })}
        {...props}>
        <CustomImage className="h-full w-full" imageProps={{ source: { uri: imageUri } }} />
        {showCloseBtn && (
          <Pressable className="absolute right-5 top-5 rounded-full overflow-hidden" onPress={onPressDelete}>
            <BlurView intensity={5} tint={'extraLight'} className="p-[4px] rounded-full bg-[#00000040] bg-opacity-5">
              <Close currentColor={colors.white} size={hp(8)} />
            </BlurView>
          </Pressable>
        )}
      </Pressable>
      {isUploading && <View className='absolute'><ActivityIndicator size={'small'} color={colors.primary.main} /></View>}
    </Fragment>
  );
};

export default styled(ProductImage);
