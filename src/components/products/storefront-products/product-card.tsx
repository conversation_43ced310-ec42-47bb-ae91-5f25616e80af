import React from 'react';
import { <PERSON>, Link21, More, Notepad, Tick<PERSON>ircle, Trash, Send2, Edit2, Box2, Box1 } from 'iconsax-react-native/src';
import { Dimensions, ImageBackground, View } from 'react-native';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import Row from '@/components/ui/row';
import { ArrowUpRight, MoreHorizontal } from '@/components/ui/icons';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import { BaseText } from '@/components/ui/base';
import CircledIcon from '@/components/ui/circled-icon';
import Pressable from '@/components/ui/base/pressable';
import { ReactNode, useMemo, useState } from 'react';
import { Menu, MenuDivider, MenuItem } from 'react-native-material-menu';
import MenuOptions, { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import { Switch } from 'react-native-switch';
import Radio from '@/components/ui/buttons/radio';
import { ProductCardScreenType } from './products-list';
import cx from 'classnames';
import CustomSwitch from '@/components/ui/inputs/custom-switch';
import {
  copyToClipboard,
  delay,
  getProductLink,
  hideLoader,
  showLoader,
  showSuccess,
  toCurrency,
} from '@/assets/utils/js';
import { Image } from 'expo-image';
import CustomImage from '@/components/ui/others/custom-image';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import useProductMutations from '@/hooks/use-product-mutations';
import { getItemThumbnail, ProductItemInterface, StrippedItem } from 'catlog-shared';
import useAuthContext from 'src/contexts/auth/auth-context';

export interface ProductCardProps {
  product: any;
  tag?: string;
  onPress?: (product: Partial<ProductItemInterface>) => void;
  selectionActive?: boolean;
  showRightElement?: boolean;
  listView?: boolean;
  disabled?: boolean;
  selected?: boolean;
  fadeImage?: boolean;
  tagText?: string;
  rightElement?: ReactNode;
  screen?: ProductCardScreenType;
  updateProductCallback?: (activeKey: string, updatedData?: Partial<ProductItemInterface>) => void;
}

interface UpdateResponse extends ResponseWithoutPagination<ProductItemInterface> {}

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const ProductCard = ({
  tag,
  product,
  listView = false,
  showRightElement = true,
  disabled = false,
  selected = false,
  tagText,
  fadeImage,
  rightElement,
  screen = ProductCardScreenType.STOREFRONT,
  selectionActive,
  updateProductCallback,
  onPress,
}: ProductCardProps) => {
  // const [selected, setSelected] = useState(false);
  const { store } = useAuthContext();
  const navigation = useNavigation();

  const { handleOnPressDelete, handleOnPressFeature, handleProductShare, handleEditProduct, duplicateProduct } =
    useProductMutations();

  // const tagText = useMemo(() => {
  //   if (screen === ProductCardScreenType.STOREFRONT) {
  //     if (product?.variants) {
  //       return `${product?.variants?.options?.length} options`;
  //     }
  //   }

  //   if (screen === ProductCardScreenType.DISCOUNT) {
  //     return `${product?.discount_price} OFF`;
  //   }

  //   return undefined;
  // }, [product, screen]);

  const handleOnPress = () => {
    // if (selectionActive) {
    //   if (handleOnSelected) handleOnSelected(product!);
    //   return;
    // }
    if (onPress) {
      onPress(product!);
    }
  };

  const handleToggleProductAvailable = async (value: boolean) => {
    showLoader('Changing Product Availability');
    await handleEditProduct(product, { quantity: product.quantity, available: value }, async () => {
      await delay(200);
      updateProductCallback?.(product?.id!, { available: value });
      await delay(600);
      showSuccess('Product updated successfully');
    });
    hideLoader();
  };

  const moreOptions: MoreOptionElementProps[] = [
    {
      optionElement: (
        <OptionWithIcon
          icon={
            <TickCircle
              size={wp(15)}
              color={product?.is_featured ? colors.accentGreen.main : colors.black.placeholder}
            />
          }
          labelClasses={product?.is_featured ? 'text-accentGreen-main' : undefined}
          label={product?.is_featured ? 'Item is Featured' : 'Feature'}
        />
      ),
      title: 'Feature',
      onPress: () =>
        handleOnPressFeature(product!, () =>
          updateProductCallback?.(product?.id!, { ...product, is_featured: !product.is_featured }),
        ),
    },

    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label={'Edit Product'} />
      ),
      title: 'Edit Product',
      onPress: () => navigation.navigate('EditProduct', { product, isDuplicate: false }),
    },
    {
      optionElement: <OptionWithIcon icon={<Send2 size={wp(15)} color={colors.black.placeholder} />} label="Share" />,
      title: 'Share',
      onPress: () => handleProductShare(product!),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Link21 size={wp(15)} color={colors.black.placeholder} />} label="Copy Link" />
      ),
      title: 'Copy Link',
      onPress: () => copyToClipboard(getProductLink(product)),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Notepad size={wp(15)} color={colors.black.placeholder} />} label="Duplicate" />
      ),
      title: 'Duplicate',
      onPress: () => duplicateProduct(product),
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Trash size={wp(15)} color={colors.accentRed.main} />} label="Delete Item" />
      ),
      title: 'Delete Item',
      onPress: () => handleOnPressDelete(product!, () => updateProductCallback?.(product?.id!)),
    },
    {
      optionElement: (
        <Row spread={false}>
          <CustomSwitch value={product?.available} onValueChange={value => handleToggleProductAvailable(value)} />
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Availability
          </BaseText>
        </Row>
      ),
      title: 'Availability',
      onPress: () => {},
    },
  ];

  const hasImages = getItemThumbnail?.(product) !== undefined;

  return (
    <Pressable
      style={{
        flex: 1,
        // width: cardWidth,
        minWidth: cardWidth,
        maxWidth: listView ? '100%' : cardWidth,
      }}
      className={cx('m-10', { 'flex-row items-center justify-between m-0 py-15': listView })}
      disabled={disabled}
      onPress={handleOnPress}>
      <View
        className={cx('rounded-[15px] overflow-hidden justify-end items-center', {
          'w-40 rounded-[10px]': listView,
          'w-auto rounded-[15px]': !listView,
        })}
        // source={{ uri: product?.images![0] }}
        style={{
          height: listView ? 40 : cardWidth,
        }}>
        <CustomImage
          className={cx('w-full h-full absolute', { 'opacity-50': fadeImage ?? disabled })}
          imageProps={{
            source: {
              uri: hasImages
                ? getItemThumbnail(product)
                : 'https://res.cloudinary.com/catlog/image/upload/w_200,h_200,c_fill/v1754788351/product-placeholder.jpg',
            },
          }}
        />

        {product?.is_featured && !listView && !selectionActive && (
          <CircledIcon className="p-4 bg-accentGreen-main absolute bottom-10 left-10">
            <Flash variant={'Bold'} size={wp(12)} color={colors.white} />
          </CircledIcon>
        )}
        {tagText && !listView && (
          <View className={'bg-white py-4 px-8 rounded-[5px] absolute bottom-10 right-10'}>
            <BaseText fontSize={10} weight="medium" classes={'text-accentOrange-main'}>
              {tagText}
            </BaseText>
          </View>
        )}
        {selectionActive && (
          <View className="absolute top-10 right-10">
            <Radio active={selected} />
          </View>
        )}
      </View>
      <Row className={cx('items-start', { 'flex-1': listView })} style={{ maxWidth: '100%' }}>
        <View className={cx('flex-1', { 'mx-10 mt-0': listView, 'mt-5': !listView })} style={{ maxWidth: '100%' }}>
          <BaseText
            fontSize={13}
            type="heading"
            classes={cx('text-black-muted leading-snug mb-2')}
            numberOfLines={1}
            ellipsizeMode="tail">
            {product?.name}
          </BaseText>
          <BaseText fontSize={11} weight={'semiBold'} classes={'text-black-main'}>
            {toCurrency(product?.price ?? 0)}
          </BaseText>
        </View>
        {showRightElement ? (
          <>
            {rightElement ? (
              rightElement
            ) : (
              <View className="mt-4">
                <MenuOptions options={moreOptions} />
              </View>
            )}
          </>
        ) : null}
      </Row>
    </Pressable>
  );
};

export default ProductCard;
