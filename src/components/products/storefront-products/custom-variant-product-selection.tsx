import { Dimensions, FlatList, LayoutAnimation, LayoutChangeEvent, ScrollView, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../../ui/modals/bottom-modal';
import SectionContainer, { ContainerType } from '../../ui/section-container';
import { PromoCardType } from '../discounts-and-coupons/promo-card';
import { Add, Box1, Calendar, DiscountShape, Minus, PercentageCircle, Status, Trash } from 'iconsax-react-native/src';
import { hp, removeUnderscores, toCurrency, wp, enumToHumanFriendly, delay } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../../ui';
import { ReactNode, useEffect, useRef, useState } from 'react';
import SelectDropdown, { DropDownMethods } from '../../ui/inputs/select-dropdown';
import useLayoutHeight from 'src/hooks/use-layout-height';
import Pressable from '@/components/ui/base/pressable';
import cx from 'classnames';
import CustomImage from '@/components/ui/others/custom-image';
import VariantSummaryCard from '../create-products/add-product-option/variant-summary-card';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant } from '@/components/ui/buttons/button';
import Toast from 'react-native-toast-message';
import QuantityToggle from '@/components/ui/buttons/quantity-toggle';
import { Category, ProductItemInterface, VariantForm, VariantItem, OrderItem, CartItem, getItemThumbnail } from 'catlog-shared';
import { BottomSheetFooter, BottomSheetFooterContainer, BottomSheetScrollView } from '@gorhom/bottom-sheet';

interface CustomVariantProductSelectionProps {
  variant: VariantForm;
  product: ProductItemInterface;
  selectedVariants: VariantItem[];
  setSelectedProducts: (selections?: { id: string; variant_id?: string | null }[]) => void;
  selectedProductsId: { id: string; variant_id?: string | null; quantity?: number | null }[];
  closeModal: VoidFunction;
}

type VariantValues = {
  [key: string]: string;
};

const CustomVariantProductSelection = ({
  variant,
  product,
  selectedVariants,
  setSelectedProducts,
  selectedProductsId,
  closeModal,
  ...rest
}: CustomVariantProductSelectionProps) => {
  const [selectedCustomOptions, setSelectedCustomOptions] = useState<{ [key: string]: string }[]>([]);
  const [selectedCustomVariants, setSelectedCustomVariants] = useState<CartItem[]>([]);
  const [step, setStep] = useState<'selectValues' | 'summary'>('selectValues');
  const persistedSelectedVariants = useRef(selectedVariants);

  useEffect(() => {
    if (JSON.stringify(persistedSelectedVariants.current) !== JSON.stringify(selectedVariants)) {
      persistedSelectedVariants.current = selectedVariants;
    }
  }, [selectedVariants]);

  useEffect(() => {
    if (persistedSelectedVariants?.current?.length > 0) {
      const newOptions = persistedSelectedVariants.current?.map(i => ({
        item_id: product.id,
        item: product,
        variant: i,
        quantity: 1,
        variant_id: i?.id,
      }));
      setSelectedCustomVariants(newOptions);
    } else {
      setSelectedCustomVariants([]);
    }
  }, [persistedSelectedVariants]);

  useEffect(() => {
    if (selectedCustomVariants?.length > 0) {
      const newOptions = selectedCustomVariants.map(i => i?.variant?.values!);
      setSelectedCustomOptions(newOptions!);
    } else {
      setSelectedCustomOptions([{}]);
    }
  }, [selectedCustomVariants]);

  const filterVariants = (variants: VariantItem[], filterCriteria: VariantValues): VariantItem | null => {
    // Get all unique keys from variants
    const allPossibleKeys = Array.from(new Set(variants.flatMap(variant => Object.keys(variant?.values!))));

    // Check if the number of filter criteria matches the total possible keys
    if (Object.keys(filterCriteria).length !== allPossibleKeys.length) {
      return null;
    }

    const filteredVariants = variants.filter(variant =>
      Object.entries(filterCriteria).every(([key, value]) => variant?.values![key] === value),
    );

    // If exactly one variant matches, return its details
    if (filteredVariants.length === 1) {
      const variant = filteredVariants[0];
      return {
        id: variant.id,
        price: variant.price,
        discount_price: variant.discount_price,
        is_available: variant.is_available,
        values: variant.values,
      };
    }

    // Return null if no variants or multiple variants match
    return null;
  };

  const handleContinue = async () => {
    try {
      const expectedLength = transformVariantData(product?.variants?.options!).length;

      if (selectedCustomOptions.length === 0) {
        // alert('Please select all required options before continuing.');
        Toast.show({ text1: 'You have empty selections, please check', type: 'error' });
        return;
      }

      // Check if any custom option is missing values or is an empty object
      const missingOptions = selectedCustomOptions.some(
        customOption =>
          customOption === undefined ||
          Object.keys(customOption).length === 0 ||
          Object.keys(customOption).length !== expectedLength,
      );

      if (missingOptions) {
        Toast.show({ text1: 'You have some empty selections, please check', type: 'error' });
        // alert('Please complete all option selections.');
        return;
      }

      // Use Promise.all for concurrent processing
      const chosenVariant = await Promise.all(
        selectedCustomOptions.map(async customOption => {
          // Skip undefined or empty options
          if (customOption === undefined || Object.keys(customOption).length === 0) {
            return null;
          }

          // Add a small delay to prevent blocking and ensure console logs are in order
          await delay(200);

          const filtered = filterVariants(product?.variants?.options ?? [], customOption);

          if (filtered !== null) {
            return {
              item_id: product.id,
              item: product,
              variant: filtered,
              quantity: 1,
              variant_id: filtered.id,
            };
          }

          return null;
        }),
      );

      // Filter out null results
      const validVariants = chosenVariant.filter(variant => variant !== null);
      setSelectedCustomVariants(validVariants);
      setStep('summary');
    } catch (error) {
      console.error('Error in variant selection:', error);
    }
  };

  const handleSelectOptions = () => {
    const formatOptions = selectedCustomVariants.map(item => ({
      id: item.item_id,
      variant_id: item.variant_id,
      quantity: item.quantity,
    }));
    // remove previous options of the product
    const newSelected = selectedProductsId.filter(i => i.id !== product.id);
    setSelectedProducts?.([...newSelected, ...formatOptions]);
    closeModal();
  };

  const addNew = () => {
    setSelectedCustomOptions(prev => {
      return [...prev, {}];
    });
  };

  const handleToggleQuantity = (index: number, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = [...selectedCustomVariants];
    const selectedItem = selectedItemsCopy[index];
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        setSelectedCustomVariants(selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          selectedItemsCopy.splice(index, 1);
          setSelectedCustomVariants(selectedItemsCopy);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          setSelectedCustomVariants(selectedItemsCopy);
        }
        return;
      }
    }
  };

  return (
    <View className="mx-20">
      <BottomSheetScrollView>
        <View>
          <SectionContainer
            containerType={ContainerType.OUTLINED}
            className="p-12"
            style={{ display: step === 'selectValues' ? 'flex' : 'none' }}>
            <Row className="bg-grey-bgOne rounded-12 px-12 py-8">
              <CustomImage imageProps={{ source: getItemThumbnail(product) }} className="h-40 w-40 rounded-5" />
              <View className="flex-1 mx-8">
                <BaseText weight="medium" classes="text-black-secondary">
                  {product.name}
                </BaseText>
              </View>
            </Row>
            {selectedCustomOptions?.map((item, index) => (
              <Selector
                key={index}
                product={product}
                setOptions={setSelectedCustomOptions}
                selectedCustomOptions={selectedCustomOptions}
                option={item}
                optionIndex={index}
              />
            ))}
            <WhiteCardBtn
              onPress={addNew}
              className="p-0 py-5 mt-12"
              leftIcon={<Add size={wp(14)} color={colors.primary.main} strokeWidth={2} />}>
              Select New
            </WhiteCardBtn>
          </SectionContainer>
          <SectionContainer
            containerType={ContainerType.OUTLINED}
            className="p-12"
            style={{ display: step === 'summary' ? 'flex' : 'none' }}>
            <Row className="bg-grey-bgOne rounded-12 px-12 py-8">
              <CustomImage imageProps={{ source: getItemThumbnail(product) }} className="h-40 w-40 rounded-5" />
              <View className="flex-1 mx-8">
                <BaseText weight="medium" classes="text-black-secondary">
                  {product.name}
                </BaseText>
              </View>
            </Row>
            {selectedCustomVariants?.map((item, index) => (
              <View className="rounded-12 bg-grey-bgOne mt-15" key={index}>
                {Object.entries?.(item?.variant?.values ?? {}).map((v, vIndex) => (
                  <Row
                    key={vIndex}
                    className={cx('p-12', {
                      'border-b border-b-grey-border':
                        vIndex !== Object.entries?.(item?.variant?.values ?? {}).length - 1,
                    })}>
                    <BaseText classes="text-black-placeholder">
                      {enumToHumanFriendly(v[0])}:{' '}
                      <BaseText weight="medium" classes="text-black-placeholder">
                        {v[1]}
                      </BaseText>
                    </BaseText>
                  </Row>
                ))}
                <View className="rounded-12 border border-grey-border bg-white">
                  <Row style={{ gap: wp(10) }} className="p-15 border-t border-t-grey-border">
                    <View className="flex-1">
                      <BaseText weight="semiBold" classes="text-black-muted">
                        {toCurrency(item.variant.price)}
                      </BaseText>
                    </View>
                    <QuantityToggle
                      quantity={item.quantity}
                      onPressAdd={() => handleToggleQuantity(index, 'increase')}
                      onPressMinus={() => handleToggleQuantity(index, 'decrease')}
                    />
                  </Row>
                </View>
              </View>
            ))}
            <WhiteCardBtn
              onPress={() => setStep('selectValues')}
              className="p-0 py-5 mt-12"
              leftIcon={<Add size={wp(12)} color={colors.primary.main} />}>
              Select New
            </WhiteCardBtn>
          </SectionContainer>
        </View>
      </BottomSheetScrollView>
      <BottomSheetFooterContainer footerComponent={() => <FixedBtnFooter
          buttons={[
            {
              text: 'Go Back',
              variant: ButtonVariant.LIGHT,
              onPress: () => {
                setStep('selectValues');
                // closeModal();
              },
            },
            {
              text: step === 'summary' ? 'Select Options' : 'Continue',
              onPress: () => {
                if (step === 'summary') {
                  handleSelectOptions();
                  return;
                }
                handleContinue();
                // setStep('selectVariantOption');
                // closeModal();
              },
            },
          ]}
        />}>
        
      </BottomSheetFooterContainer>
    </View>
  );
};

export default CustomVariantProductSelection;

const Selector = ({
  product,
  setOptions,
  optionIndex,
  selectedCustomOptions,
  option,
}: {
  product: ProductItemInterface;
  option: { [key: string]: string };
  optionIndex: number;
  setOptions: React.Dispatch<
    React.SetStateAction<
      {
        [key: string]: string;
      }[]
    >
  >;
  selectedCustomOptions: { [key: string]: string }[];
}) => {
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const dropDownRef = useRef<DropDownMethods>(null);

  const variantData = [...transformVariantData(product?.variants?.options ?? [])];

  const handleRemove = () => {
    const options = Object.entries(selectedCustomOptions);
    if (options.length < 2) {
      return;
    }
    setOptions(prev => {
      const optionsCopy = [...prev];
      optionsCopy.splice(optionIndex, 1);
      return optionsCopy;
    });
  };

  const updateOption = (k: string, v: string) => {
    const newOption = { ...option, [k]: v };
    setOptions(prev => {
      const optionsCopy = [...prev];
      optionsCopy[optionIndex] = newOption;
      return optionsCopy;
    });
  };

  return (
    <SectionContainer
      containerType={ContainerType.OUTLINED}
      className="p-12"
      onLayout={e => setContainerWidth(e.nativeEvent.layout.width)}>
      <Row className="flex-wrap" style={{ gap: 8 }}>
        {variantData.map((item, index) => (
          <View
            key={index}
            style={
              variantData.length - 1 !== index
                ? { width: containerWidth / 2 - 18 }
                : { width: variantData.length % 2 === 0 ? containerWidth / 2 - 18 : '100%' }
            }>
            <SelectDropdown
              ref={dropDownRef}
              // showAnchor={false}
              selectedItem={option?.[item.name] ?? ''}
              onPressItem={v => updateOption(item.name, v)}
              showLabel
              label={`Select ${enumToHumanFriendly(item.name)}`}
              // containerClasses="my-15"
              items={item.values.map(item => ({ value: item, label: item }))}
            />
          </View>
        ))}
      </Row>
      {selectedCustomOptions.length > 1 && (
        <Pressable className="flex-row pt-10 justify-end self-end" onPress={handleRemove}>
          <Trash size={wp(16)} color={colors.black.placeholder} />
          <BaseText classes="text-black-placeholder ml-8">Remove</BaseText>
        </Pressable>
      )}
    </SectionContainer>
  );
};

const transformVariantData = (data: VariantItem[]) => {
  // Get all unique keys from the values object
  const valueKeys = Array.from(new Set(data.flatMap(item => Object.keys(item?.values!))));

  // Create the result array
  const result = valueKeys.map(key => ({
    name: key,
    values: Array.from(new Set(data.map(item => item?.values![key]))),
  }));

  return result ?? [];
};

const checkItemAvailability = (item: ProductItemInterface, variant?: VariantItem, variantOnly: boolean = false) => {
  let quantityAvailable;

  if (!variant) {
    quantityAvailable =
      item?.is_always_available !== undefined || item?.quantity !== undefined
        ? item?.is_always_available || item?.quantity! > 0
        : true;
  } else {
    quantityAvailable = variant?.is_available && (variant?.quantity !== undefined ? variant?.quantity > 0 : true);

    if (variantOnly) return quantityAvailable;
  }

  return quantityAvailable && item.available;
};
