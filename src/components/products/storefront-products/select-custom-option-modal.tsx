import { ProductItemInterface, VariantForm, VariantItem, CartItem, getItemThumbnail } from 'catlog-shared';
import { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import useLayoutHeight from 'src/hooks/use-layout-height';

import CustomVariantProductSelection from './custom-variant-product-selection';
import ImageVariantProductSelection from './image-variant-product-selection';

import { ButtonVariant } from '@/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { cx, delay, enumToHumanFriendly, showError, toCurrency, wp } from 'src/assets/utils/js';
import { BottomSheetFooterContainer, BottomSheetScrollView, BottomSheetView } from '@gorhom/bottom-sheet';
import SectionContainer, { ContainerType } from 'src/components/ui/section-container';
import { BaseText, Row, WhiteCardBtn } from 'src/components/ui';
import CustomImage from 'src/components/ui/others/custom-image';
import { Add, Trash } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import QuantityToggle from 'src/components/ui/buttons/quantity-toggle';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import SelectDropdown, { DropDownMethods } from 'src/components/ui/inputs/select-dropdown';
import Pressable from 'src/components/ui/base/pressable';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import InfoBadge from 'src/components/store-settings/info-badge';

interface SelectCustomOptionModalProps extends Partial<BottomModalProps> {
  variant: VariantForm;
  product: ProductItemInterface;
  selectedVariants: VariantItem[];
  selectedProductsId: { id: string; variant_id?: string | null }[];
  closeModal: () => void;
  onPressContinue: (selectedProducts: string[]) => void;
  setSelectedProducts: (selections?: { id: string; variant_id?: string | null; quantity?: number | null }[]) => void;
}

type VariantValues = {
  [key: string]: string;
};

const SelectCustomOptionModal = (props: SelectCustomOptionModalProps) => {
  const {
    closeModal,
    onPressContinue,
    product,
    selectedProductsId,
    variant,
    selectedVariants,
    setSelectedProducts,
    ...rest
  } = props;
  // const [selectedCustomOptions, setSelectedCustomOptions] = useState<{ [key: string]: string }[]>([]);
  // const [selectedCustomVariants, setSelectedCustomVariants] = useState<CartItem[]>([]);
  const [step, setStep] = useState<'selectValues' | 'summary'>('selectValues');
  const persistedSelectedVariants = useRef(selectedVariants);

  const [selectedCustomOptions, setSelectedCustomOptions] = useState<{ [key: string]: string }[]>(
    selectedVariants?.length > 0 ? selectedVariants.map(variant => variant.values || {}) : [{}],
  );

  const [selectedCustomVariants, setSelectedCustomVariants] = useState<CartItem[]>(
    selectedVariants?.length > 0
      ? selectedVariants.map(variant => ({
          item_id: product.id,
          item: product,
          variant: variant,
          quantity: 1,
          variant_id: variant.id,
        }))
      : [],
  );

  useEffect(() => {
    // Clear previous selections first
    setSelectedCustomOptions([{}]);
    setSelectedCustomVariants([]);

    // Then initialize with the new selections if any
    if (selectedVariants?.length > 0) {
      // Convert existing variants to cart items
      const variantCartItems = selectedVariants.map(variant => ({
        item_id: product.id,
        item: product,
        variant: variant,
        quantity: 1,
        variant_id: variant.id,
      }));

      setSelectedCustomVariants(variantCartItems);

      // Extract values from variants
      const variantValues = selectedVariants.map(variant => variant.values || {});
      setSelectedCustomOptions(variantValues.length > 0 ? variantValues : [{}]);
    }
  }, [product.id, selectedVariants]);

  const filterVariants = (variants: VariantItem[], filterCriteria: VariantValues): VariantItem | null => {
    // Get all unique keys from variants
    const allPossibleKeys = Array.from(new Set(variants.flatMap(variant => Object.keys(variant?.values!))));

    // Check if the number of filter criteria matches the total possible keys
    if (Object.keys(filterCriteria).length !== allPossibleKeys.length) {
      return null;
    }

    const filteredVariants = variants.filter(variant =>
      Object.entries(filterCriteria).every(([key, value]) => variant?.values![key] === value),
    );

    // If exactly one variant matches, return its details
    if (filteredVariants.length === 1) {
      const variant = filteredVariants[0];
      return {
        id: variant.id,
        price: variant.price,
        discount_price: variant.discount_price,
        is_available: variant.is_available,
        values: variant.values,
      };
    }

    // Return null if no variants or multiple variants match
    return null;
  };

  const handleContinue = async () => {
    try {
      const expectedLength = transformVariantData(product?.variants?.options!).length;

      if (selectedCustomOptions.length === 0) {
        // alert('Please select all required options before continuing.');
        showError(undefined, 'You have empty selections, please check');
        return;
      }

      // Check if any custom option is missing values or is an empty object
      const missingOptions = selectedCustomOptions.some(
        customOption =>
          customOption === undefined ||
          Object.keys(customOption).length === 0 ||
          Object.keys(customOption).length !== expectedLength,
      );

      if (missingOptions) {
        showError(undefined, 'You have some empty selections, please check');
        // alert('Please complete all option selections.');
        return;
      }

      const completeOptions = selectedCustomOptions.filter(option => Object.keys(option).length === expectedLength);

      const hasDuplicates = completeOptions.some((option, index) => {
        return completeOptions.some((otherOption, otherIndex) => {
          if (index === otherIndex) return false;

          return Object.entries(option).every(([key, val]) => otherOption[key] === val);
        });
      });

      if (hasDuplicates) {
        showError(undefined, 'You have duplicate selections. Please make each selection unique.');
        return;
      }

      // Use Promise.all for concurrent processing
      const chosenVariant = await Promise.all(
        selectedCustomOptions.map(async customOption => {
          // Skip undefined or empty options
          if (customOption === undefined || Object.keys(customOption).length === 0) {
            return null;
          }

          // Add a small delay to prevent blocking and ensure // console logs are in order
          await delay(200);

          const filtered = filterVariants(product?.variants?.options ?? [], customOption);

          if (filtered !== null) {
            return {
              item_id: product.id,
              item: product,
              variant: filtered,
              quantity: 1,
              variant_id: filtered.id,
            };
          }

          return null;
        }),
      );

      // Filter out null results
      const validVariants = chosenVariant.filter(variant => variant !== null);
      setSelectedCustomVariants(validVariants);
      setStep('summary');
    } catch (error) {
      console.error('Error in variant selection:', error);
    }
  };

  const handleSelectOptions = () => {
    const formatOptions = selectedCustomVariants.map(item => ({
      id: item.item_id,
      variant_id: item.variant_id,
      quantity: item.quantity,
    }));
    // remove previous options of the product
    const newSelected = selectedProductsId.filter(i => i.id !== product.id);
    setSelectedProducts?.([...newSelected, ...formatOptions]);
    closeModal();
    setStep('selectValues');
  };

  const addNew = () => {
    setSelectedCustomOptions(prev => {
      return [...prev, {}];
    });
  };

  const handleToggleQuantity = (index: number, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = [...selectedCustomVariants];
    const selectedItem = selectedItemsCopy[index];
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        setSelectedCustomVariants(selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          selectedItemsCopy.splice(index, 1);
          setSelectedCustomVariants(selectedItemsCopy);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          setSelectedCustomVariants(selectedItemsCopy);
        }
        return;
      }
    }
  };

  const insets = useSafeAreaInsets();

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      enableDynamicSizing
      enableSnapPoints={false}
      useScrollView={false}
      useChildrenAsDirectChild
      onModalHide={() => setStep('selectValues')}
      title={`Select Options`}
      // title={`Select Options - ${selectedProduct?.item_id}`}
      buttons={[
        {
          text: 'Go Back',
          variant: ButtonVariant.LIGHT,
          onPress: () => {
            setStep('selectValues');
            // closeModal();
          },
        },
        {
          text: step === 'summary' ? 'Select Options' : 'Continue',
          onPress: () => {
            if (step === 'summary') {
              handleSelectOptions();
              return;
            }
            handleContinue();
          },
        },
      ]}>
      <BottomSheetScrollView keyboardShouldPersistTaps={'handled'}>
        <BottomSheetView enableFooterMarginAdjustment style={{ paddingHorizontal: wp(20) }}>
          <SectionContainer
            containerType={ContainerType.OUTLINED}
            className="p-12"
            style={{ display: step === 'selectValues' ? 'flex' : 'none' }}>
            <Row className="bg-grey-bgOne rounded-12 px-12 py-8">
              {getItemThumbnail(product) && (
                <CustomImage
                  imageProps={{ source: getItemThumbnail(product) }}
                  className="h-40 w-40 rounded-5"
                />
              )}
              <View className="flex-1 mx-8">
                <BaseText weight="medium" classes="text-black-secondary">
                  {product.name}
                </BaseText>
              </View>
            </Row>
            {selectedCustomOptions?.map((item, index) => (
              <Selector
                key={index}
                product={product}
                setOptions={setSelectedCustomOptions}
                selectedCustomOptions={selectedCustomOptions}
                option={item}
                optionIndex={index}
              />
            ))}
            <WhiteCardBtn
              onPress={addNew}
              className="p-0 py-5 mt-12"
              leftIcon={<Add size={wp(14)} color={colors.primary.main} strokeWidth={2} />}>
              Select New
            </WhiteCardBtn>
          </SectionContainer>
          <SectionContainer
            containerType={ContainerType.OUTLINED}
            className="p-12"
            style={{ display: step === 'summary' ? 'flex' : 'none' }}>
            <Row className="bg-grey-bgOne rounded-12 px-12 py-8">
              {getItemThumbnail(product) && (
                <CustomImage
                  imageProps={{ source: getItemThumbnail(product) }}
                  className="h-40 w-40 rounded-5"
                />
              )}
              <View className="flex-1 mx-8">
                <BaseText weight="medium" classes="text-black-secondary">
                  {product?.name}
                </BaseText>
              </View>
            </Row>
            {selectedCustomVariants?.map((item, index) => (
              <View className="rounded-12 bg-grey-bgOne mt-15" key={index}>
                {Object.entries?.(item?.variant?.values ?? {}).map((v, vIndex) => (
                  <Row
                    key={vIndex}
                    className={cx('p-12', {
                      'border-b border-b-grey-border':
                        vIndex !== Object.entries?.(item?.variant?.values ?? {}).length - 1,
                    })}>
                    <BaseText classes="text-black-placeholder">
                      {enumToHumanFriendly(v[0])}:{' '}
                      <BaseText weight="medium" classes="text-black-placeholder">
                        {v[1]}
                      </BaseText>
                    </BaseText>
                  </Row>
                ))}
                <View className="rounded-12 border border-grey-border bg-white">
                  <Row style={{ gap: wp(10) }} className="p-15 border-t border-t-grey-border">
                    <View className="flex-1">
                      <BaseText weight="semiBold" classes="text-black-muted">
                        {toCurrency(item.variant.price)}
                      </BaseText>
                    </View>
                    <QuantityToggle
                      quantity={item.quantity}
                      onPressAdd={() => handleToggleQuantity(index, 'increase')}
                      onPressMinus={() => handleToggleQuantity(index, 'decrease')}
                    />
                  </Row>
                </View>
              </View>
            ))}
            <WhiteCardBtn
              onPress={() => setStep('selectValues')}
              className="p-0 py-5 mt-12"
              leftIcon={<Add size={wp(12)} color={colors.primary.main} />}>
              Select New
            </WhiteCardBtn>
          </SectionContainer>
          <View style={{ height: insets.bottom }} />
        </BottomSheetView>
      </BottomSheetScrollView>
    </BottomModal>
  );
};

export default SelectCustomOptionModal;

const Selector = ({
  product,
  setOptions,
  optionIndex,
  selectedCustomOptions,
  option,
}: {
  product: ProductItemInterface;
  option: { [key: string]: string };
  optionIndex: number;
  setOptions: React.Dispatch<
    React.SetStateAction<
      {
        [key: string]: string;
      }[]
    >
  >;
  selectedCustomOptions: { [key: string]: string }[];
}) => {
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const [error, setError] = useState<null | string>(null);

  const dropDownRef = useRef<DropDownMethods>(null);

  const variantData = [...transformVariantData(product?.variants?.options ?? [])];

  const handleRemove = () => {
    const options = Object.entries(selectedCustomOptions);
    if (options.length < 2) {
      return;
    }
    setOptions(prev => {
      const optionsCopy = [...prev];
      optionsCopy.splice(optionIndex, 1);
      return optionsCopy;
    });
  };

  const selectVariantCombination = (options: { [key: string]: string }) => {
    const variantData = transformVariantData(product?.variants?.options ?? []);
    // console.log('selected options')
    console.log(
      'variantData: ',
      variantData.map(d => d.values),
    );
    console.log('selected options: ', options);
  };

  const updateOption = async (k: string, v: string) => {
    setError(null);
    // Create the new option that would result from this update
    const updatedOption = {
      ...option,
      [k]: v,
    };

    // Check if this update would complete the selection
    const variantData = transformVariantData(product?.variants?.options ?? []);
    const isCompleteSelection = Object.keys(updatedOption).length === variantData.length;

    if (isCompleteSelection) {
      // Check if this would create a duplicate
      const otherOptions = selectedCustomOptions.filter((_, index) => index !== optionIndex);
      const isDuplicate = otherOptions.some(existingOption => {
        // Skip incomplete options
        if (Object.keys(existingOption).length !== variantData.length) return false;

        // Check if all key-value pairs match
        return Object.entries(updatedOption).every(([key, val]) => existingOption[key] === val);
      });

      if (isDuplicate) {
        await delay(600);
        showError(undefined, 'This combination is already selected. Please choose a different option.');
        setError('The combination is already selected. Please choose a different option.');
        return;
      }
    }

    selectVariantCombination(updatedOption);

    // If not a duplicate or not a complete selection yet, update the option
    setOptions(prev => {
      const optionsCopy = [...prev];
      optionsCopy[optionIndex] = updatedOption;
      return optionsCopy;
    });
  };

  return (
    <SectionContainer
      containerType={ContainerType.OUTLINED}
      className="p-12"
      onLayout={e => setContainerWidth(e.nativeEvent.layout.width)}>
      <Row className="flex-wrap" style={{ gap: 8 }}>
        {variantData.map((item, index) => (
          <View
            key={index}
            style={
              variantData.length - 1 !== index
                ? { width: containerWidth / 2 - 18 }
                : { width: variantData.length % 2 === 0 ? containerWidth / 2 - 18 : '100%' }
            }>
            <SelectDropdown
              ref={dropDownRef}
              // showAnchor={false}
              selectedItem={option?.[item.name] ?? ''}
              onPressItem={v => updateOption(item.name, v)}
              showLabel
              label={`Select ${enumToHumanFriendly(item.name)}`}
              // containerClasses="my-15"
              items={item.values.map(item => ({ value: item, label: item }))}
            />
          </View>
        ))}
      </Row>
      {selectedCustomOptions.length > 1 && (
        <Row className={cx('pt-10', { 'justify-end': !error })} style={{ gap: wp(10) }}>
          {error && <InfoBadge hideIcon className="flex-1" text={error} />}
          <Pressable className="flex-row justify-end" onPress={handleRemove}>
            <Trash size={wp(16)} color={colors.black.placeholder} />
            <BaseText classes="text-black-placeholder ml-8">Remove</BaseText>
          </Pressable>
        </Row>
      )}
    </SectionContainer>
  );
};

const transformVariantData = (data: VariantItem[]) => {
  // Get all unique keys from the values object
  const valueKeys = Array.from(new Set(data.flatMap(item => Object.keys(item?.values!))));

  // Create the result array
  const result = valueKeys.map(key => ({
    name: key,
    values: Array.from(new Set(data.map(item => item?.values![key]))),
  }));

  return result ?? [];
};
