import React from 'react';
import { Dimensions, FlatList, LayoutAnimation, LayoutChangeEvent, ScrollView, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../../ui/modals/bottom-modal';
import SectionContainer from '../../ui/section-container';
import { PromoCardType } from '../discounts-and-coupons/promo-card';
import { Add, Box1, Calendar, DiscountShape, Minus, PercentageCircle, Status, Trash } from 'iconsax-react-native/src';
import { enumToHumanFriendly, hp, removeUnderscores, toCurrency, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../../ui';
import ProductInfoRow from '../product-info-row';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { ArrowUpRight, ChevronDown, Search } from '../../ui/icons';
import { mockCategories, mockProducts } from '@/constant/mock-data';
import Input from '../../ui/inputs/input';
import Radio from '../../ui/buttons/radio';
import SelectDropdown, { DropDownMethods } from '../../ui/inputs/select-dropdown';
import ProductCard from './product-card';
import { ApiData, useApi } from '@/hooks/use-api';
import useAuthContext from '@/contexts/auth/auth-context';
import usePagination from '@/hooks/use-pagination';
import { ProductsResponse } from '@/screens/products/storefront';
import ProductsList from './products-list';
import useLayoutHeight from 'src/hooks/use-layout-height';
import Pressable from '@/components/ui/base/pressable';
import cx from 'classnames';
import CustomImage from '@/components/ui/others/custom-image';
import VariantSummaryCard from '../create-products/add-product-option/variant-summary-card';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { ButtonVariant } from '@/components/ui/buttons/button';
import QuantityToggle from '@/components/ui/buttons/quantity-toggle';
import {
  Category,
  ProductItemInterface,
  GET_ITEMS,
  GET_STORE_CATEGORIES,
  GetAllItemsParams,
  GetItemsParams,
  VariantForm,
  VariantItem,
} from 'catlog-shared';

interface ImageVariantProductSelectionProps {
  variant: VariantForm;
  product: ProductItemInterface;
  selectedVariants: VariantItem[];
  setSelectedProducts: (selections?: { id: string; variant_id?: string | null }[]) => void;
  selectedProductsId: { id: string; variant_id?: string | null; quantity?: number | null }[];
  closeModal: VoidFunction;
}

const ImageVariantProductSelection = (props: ImageVariantProductSelectionProps) => {
  const { variant, closeModal, product, selectedProductsId, setSelectedProducts, ...rest } = props;
  const [step, setStep] = useState<'selectVariant' | 'selectVariantOption'>('selectVariant');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selected, setSelected] = useState<{ id: string; variant_id: string; quantity: number }[]>([]);

  const variantHasExtraOption =
    product?.variants?.type === 'images' && product?.variants?.options[0]?.values !== undefined;

  const extraOption = variantHasExtraOption ? Object.keys(product?.variants?.options[0]?.values!)[0] : '';

  // const selectedItemVariantsInCart = useMemo(
  //   () => selectedProductsId.filter((c) => c.id === product?.id).map((c) => c.variant_id),
  //   [cart, product]
  // );

  useEffect(() => {
    const selectedItemVariantsInCart = selectedProductsId.filter(i => i.id === product.id);
    setSelected(selectedItemVariantsInCart); //todo: @silas take a look at this

    const getSelectedImages = new Set(
      selectedItemVariantsInCart.map(s => product?.variants?.options?.find(o => o.id === s.variant_id)?.image!),
    );

    if (variantHasExtraOption) {
      setSelectedImages([...getSelectedImages]);
    }
  }, []);

  const toggleVariant = (variant: string) => {
    const variantIndex = selectedImages.indexOf(variant);

    if (variantIndex > -1) {
      const selectedCopy = [...selectedImages];
      selectedCopy.splice(variantIndex, 1);

      setSelectedImages(selectedCopy);
    } else {
      setSelectedImages([...selectedImages, variant]);
    }
  };

  const selectImageVariantWithoutExtraOption = (variant: string) => {
    const variantIndex = selected.map(i => i.variant_id).indexOf(variant);

    if (variantIndex > -1) {
      const selectedCopy = [...selected];
      selectedCopy.splice(variantIndex, 1);

      setSelected(selectedCopy);
    } else {
      setSelected([...selected, { id: product.id, variant_id: variant, quantity: 1 }]);
    }
  };

  const handleToggleQuantity = (variant_id: string, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = [...selected];
    const selectedItem = selectedItemsCopy.find(i => i.variant_id === variant_id);
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        setSelected(selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          // selectedItemsCopy.splice(index, 1);
          // setSelected(selectedItemsCopy);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          setSelected(selectedItemsCopy);
        }
        return;
      }
    }
  };

  const variantIsSelected = (variant: string) => {
    if (variantHasExtraOption) {
      return selectedImages.includes(variant);
    }

    return selected.map(i => i.variant_id).includes(variant);
  };

  const variantData = (variant: string) => {
    return selected.find(i => i.variant_id === variant);
  };

  const getImageVariants = (): VariantItem[] => {
    if (variantHasExtraOption) {
      // const optionValues = Array.from(new Set(product?.variants.options.map((v) => Object.values(v.values)[0])));
      const optionImages: string[] = Array.from(new Set(product?.variants?.options.map(o => o.image!)));

      const optionsAvailablity = product?.variants?.options.map(o => ({
        image: o.image,
        available: checkItemAvailability(product, o, true),
      }));

      return optionImages.map(o => {
        return {
          id: o,
          image: o,
          price: 0,
          is_available: product?.variants?.options.some(v => v.image === o && v.is_available),
          available_to_purchase: optionsAvailablity?.some(v => v.image === o && v.available),
        };
      });
    }

    return product?.variants?.options.map(o => ({
      ...o,
      available_to_purchase: checkItemAvailability(product, o, true),
    }));
  };

  const handleContinue = () => {
    if (variantHasExtraOption && step === 'selectVariant') {
      if (selectedImages.length < 1) {
        const newSelected = selectedProductsId.filter(i => i.id !== product.id);
        setSelectedProducts?.([...newSelected]);
        closeModal();
      } else {
        setStep('selectVariantOption');
      }
      return;
    }
    // remove previous options of the product
    const newSelected = selectedProductsId.filter(i => i.id !== product.id);
    setSelectedProducts?.([...newSelected, ...selected]);
    closeModal();
  };

  return (
    <View>
      <ScrollView>
        <View className="pb-40">
          {step === 'selectVariant' && (
            <Row className="justify-start flex-wrap">
              {getImageVariants().map((item, index) => (
                <ImageOptionSelectionCard
                  item={item}
                  selected={variantIsSelected(item.id!)}
                  selectedQuantity={variantData(item.id!)?.quantity ?? 0}
                  key={item.id}
                  onPress={() =>
                    variantHasExtraOption ? toggleVariant(item.id!) : selectImageVariantWithoutExtraOption(item.id!)
                  }
                  variantHasExtraOption={variantHasExtraOption}
                  toggleQuantity={a => handleToggleQuantity(item.id!, a)}
                />
              ))}
            </Row>
          )}
          {step === 'selectVariantOption' && (
            <>
              <ImageExtraOptionSelection
                {...{
                  selectedImages,
                  productId: product.id,
                  options: product?.variants?.options,
                  extraOption,
                  selected,
                  setSelected,
                }}
              />
            </>
          )}
        </View>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          {
            text: 'Go Back',
            variant: ButtonVariant.LIGHT,
            onPress: () => {
              if (step === 'selectVariant') {
                closeModal();
              } else {
                setStep('selectVariant');
              }
            },
          },
          {
            text: step === 'selectVariant' ? 'Select Options' : 'Continue',
            onPress: () => {
              handleContinue();
            },
          },
        ]}
      />
    </View>
  );
};

export default ImageVariantProductSelection;

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const ImageOptionSelectionCard = ({
  item,
  selected,
  selectedQuantity,
  onPress,
  variantHasExtraOption,
  toggleQuantity,
}: {
  item: VariantItem;
  selected: boolean;
  variantHasExtraOption: boolean;
  selectedQuantity: number;
  onPress: VoidFunction;
  toggleQuantity: (a: 'increase' | 'decrease') => void;
}) => {
  const [expanded, setExpanded] = useState(false);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  return (
    <Pressable
      style={{
        flex: 1,
        minWidth: cardWidth,
        maxWidth: cardWidth,
      }}
      className={cx('m-10')}
      disabled={!item.is_available}
      onPress={onPress}>
      <View
        className={cx('rounded-[15px] overflow-hidden w-auto')}
        style={{
          height: cardWidth,
        }}>
        <CustomImage className="w-full h-full absolute" imageProps={{ source: { uri: item.image } }} />
        {item.available_to_purchase && (
          <View className="absolute top-10 right-10">
            <Radio active={selected} />
          </View>
        )}
        {item.available_to_purchase && item.price > 0 && (
          <View className="absolute bottom-10 right-10 bg-white rounded-5 px-8 py-6">
            <BaseText fontSize={12} weight="medium" classes="text-black-secondary">
              {toCurrency(item.price)}
            </BaseText>
          </View>
        )}
        {!item.available_to_purchase && (
          <View className="absolute bottom-10 right-10 bg-white rounded-5 px-8 py-6">
            <BaseText fontSize={12} weight="medium" classes="text-accentRed-main">
              Unavailable
            </BaseText>
          </View>
        )}

        {selected && !variantHasExtraOption && (
          <>
            <View
              className={cx('absolute', {
                'top-10 left-10': !expanded,
                'right-3 bottom-3 top-3 left-3': expanded,
              })}>
              <View
                className={cx('bg-white', {
                  'rounded-4 p-5 border border-grey-border': !expanded,
                  'rounded-12 h-full w-full overflow-hidden': expanded,
                })}>
                {!expanded && (
                  <Pressable onPress={toggleExpand}>
                    <Row className="justify-start">
                      <BaseText weight="medium" classes="text-black-muted mr-3">
                        + {selectedQuantity}
                      </BaseText>
                      <ChevronDown size={wp(18)} currentColor={colors.black.muted} />
                    </Row>
                  </Pressable>
                )}
                {expanded && (
                  <View className="flex flex-1 justify-between">
                    <View className="px-10">
                      <Pressable className="self-start py-8 px-5" onPress={toggleExpand}>
                        <Row>
                          <ChevronDown size={wp(20)} currentColor={colors.black.main} />
                        </Row>
                      </Pressable>
                      <BaseText fontSize={10} classes="mt-5 text-black-muted">
                        Select Quantity
                      </BaseText>
                      <BaseText fontSize={12} weight="bold" classes="mt-5 text-black-muted">
                        {toCurrency(selectedQuantity * item.price)}
                      </BaseText>
                    </View>
                    <Row style={{ gap: wp(2) }}>
                      <Pressable
                        onPress={() => toggleQuantity('decrease')}
                        className="bg-grey-bgOne h-35 flex-1 items-center justify-center ">
                        <Minus size={wp(20)} color={colors.black.main} />
                      </Pressable>
                      <View className="bg-grey-bgOne h-35 flex-1 items-center justify-center">
                        <BaseText fontSize={14}>{selectedQuantity}</BaseText>
                      </View>
                      <Pressable
                        onPress={() => toggleQuantity('increase')}
                        className="bg-grey-bgOne h-35 flex-1 items-center justify-center">
                        <Add size={wp(20)} color={colors.black.main} />
                      </Pressable>
                    </Row>
                  </View>
                )}
              </View>
            </View>
          </>
        )}
      </View>
    </Pressable>
  );
};

const ImageExtraOptionSelection = ({
  options,
  selectedImages,
  extraOption,
  selected,
  setSelected,
  productId,
}: {
  options: VariantItem[];
  selectedImages: string[];
  extraOption: string;
  productId: string;
  selected: { id: string; variant_id: string; quantity: number }[];
  setSelected: (options: { id: string; variant_id: string; quantity: number }[]) => void;
}) => {
  const selectedWithOptions = selectedImages.map(s => {
    return {
      image: s,
      options: options
        .filter(o => o.image === s && checkItemAvailability(null, o, true))
        .map(o => ({ option: o?.values?.[extraOption], price: o.price, id: o.id })),
    };
  });

  // useEffect(() => {
  //   if (selected.length > 0) {
  //     const prevSelectedImages = selected.map(s => ({
  //       [s.variant_id]: options.find(o => o.id === s.variant_id)?.image,
  //     }));

  //     //create a new selected array which is an array of the ids that match with previously selected images
  //     const newSelected = prevSelectedImages.filter(s => {
  //       const image = Object.values(s)[0];

  //       return selectedImages.includes(image!);
  //     });

  //     setSelected(
  //       newSelected.map(n => ({
  //         variant_id: Object.keys(n)[0],
  //         quantity: selected.find(v => v.variant_id === Object.keys(n)[0])?.quantity!,
  //         id: productId,
  //       })),
  //     );
  //   }
  // }, []);

  const selectOption = (option: string) => {
    const selectedCopy = [...selected];
    const optionIndex = selectedCopy.map(i => i.variant_id).indexOf(option);

    if (optionIndex > -1) {
      selectedCopy.splice(optionIndex, 1);
      setSelected(selectedCopy);
    } else {
      setSelected([...selectedCopy, { variant_id: option, quantity: 1, id: productId }]);
    }
  };

  const handleToggleQuantity = (variant_id: string, action: 'decrease' | 'increase') => {
    const selectedItemsCopy = [...selected];
    const selectedItem = selectedItemsCopy.find(i => i.variant_id === variant_id);
    const selectedItemIndex = selectedItemsCopy.map(i => i.variant_id).indexOf(variant_id);
    if (selectedItem) {
      //handle increase quantity
      if (action === 'increase') {
        selectedItem.quantity = selectedItem.quantity + 1;
        setSelected(selectedItemsCopy);
        return;
      }

      //handle decrease quantity
      if (action === 'decrease') {
        if (selectedItem.quantity === 1) {
          selectedItemsCopy.splice(selectedItemIndex, 1);
          setSelected(selectedItemsCopy);
        } else {
          selectedItem.quantity = selectedItem.quantity - 1;
          setSelected(selectedItemsCopy);
        }
        return;
      }
    } else {
      selectOption(variant_id);
      return;
    }
  };

  return (
    <View>
      {selectedWithOptions.map((s, index) => (
        <View key={index} className={cx('border border-grey-border rounded-12 p-10 mx-20', { 'mt-15': index !== 0 })}>
          <Row className="bg-grey-bgOne rounded-12 px-12 py-8">
            <CustomImage imageProps={{ source: s.image }} className="h-40 w-40 rounded-5" />
            <View className="flex-1 mx-8">
              <BaseText weight="medium" classes="text-black-secondary">
                Product name
              </BaseText>
            </View>
          </Row>

          {s.options.map((o, index) => (
            <View key={index} className="rounded-12 bg-grey-bgOne mt-8">
              <Pressable>
                <Row key={index} className={cx('py-10 px-12')}>
                  <BaseText classes="text-black-placeholder">
                    {enumToHumanFriendly(Object.entries(options[0]?.values!)[0][0])}:{' '}
                    <BaseText weight="medium" classes="text-black-placeholder">
                      {o.option}
                    </BaseText>
                  </BaseText>
                  <CircledIcon className="p-5">
                    <ChevronDown size={wp(20)} currentColor={colors.black.secondary} />
                  </CircledIcon>
                </Row>
              </Pressable>
              <View className="rounded-12 bg-white border border-grey-border px-15 py-12">
                <Row>
                  <BaseText weight="semiBold">{toCurrency(o.price)}</BaseText>
                  <QuantityToggle
                    quantity={selected.find(v => v.variant_id === o.id)?.quantity ?? 0}
                    onPressAdd={() => handleToggleQuantity(o.id!, 'increase')}
                    onPressMinus={() => handleToggleQuantity(o.id!, 'decrease')}
                  />
                </Row>
              </View>
            </View>
          ))}
          {/* <WhiteCardBtn className="p-0 py-5 mt-12" leftIcon={<Add size={wp(12)} color={colors.primary.main} />}>
            Select New
          </WhiteCardBtn> */}
        </View>
      ))}
    </View>
  );
};

const checkItemAvailability = (
  item: ProductItemInterface | null,
  variant?: VariantItem,
  variantOnly: boolean = false,
) => {
  let quantityAvailable;

  if (!variant) {
    quantityAvailable =
      item?.is_always_available !== undefined || item?.quantity !== undefined
        ? item?.is_always_available || item?.quantity! > 0
        : true;
  } else {
    quantityAvailable = variant?.is_available && (variant?.quantity !== undefined ? variant?.quantity > 0 : true);

    if (variantOnly) return quantityAvailable;
  }

  return quantityAvailable && item?.available;
};
