import { Activity, BagTick2, Box, Chart2, Moneys, Profile2User, ShoppingBag, TrendDown } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify, toCurrency } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import AnalyticsCard from '../ui/cards/analytics-card';
import { StoreSummary } from '@/screens/orders/order-analytics';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';
import { ProductAnalyticsInterface } from 'catlog-shared';

export enum AnalyticsVariants {
  'TOTAL_INVENTORY_VALUE' = 'total_inventory_value',
  'TOTAL_OUT_OF_STOCK_ITEMS' = 'total_out_of_stock_items',
  'TOTAL_ITEMS_LISTED' = 'total_items_listed',
  'TOTAL_LOW_STOCK_ITEMS' = 'total_low_stock_items',
}

interface OrderAnalyticsProps {
  analytics: ProductAnalyticsInterface;
  loading?: boolean;
  currency?: string;
}

const ProductAnalyticsCards = ({ analytics = {} as ProductAnalyticsInterface, currency, loading }: OrderAnalyticsProps) => {
  const analyticsCardsInfo: AnalyticsCardInfo[] = [
    {
      title: 'Total inventory value',
      iconBg: 'bg-accentRed-main',
      icon: <Moneys variant="Bold" size={wp(18)} color={colors?.white} />,
      cardBg: 'bg-transparent',
      key: AnalyticsVariants.TOTAL_INVENTORY_VALUE, 
      value: analytics.total_inventory_value ? millify(analytics.total_inventory_value, 2, currency) : '-',
      change:0,
    },
    {
      title: 'Out-of-stock items',
      cardBg: 'bg-transparent',
      icon: <Chart2 variant="Bold" size={wp(18)} color={colors?.white} />,
       iconBg: 'bg-accentGreen-main',
      key: AnalyticsVariants.TOTAL_OUT_OF_STOCK_ITEMS,
      value: analytics.total_out_of_stock_items ? analytics.total_out_of_stock_items : '-',
      change: 0,
    },
    {
      title: 'Total items listed',
      cardBg: 'bg-transparent',
      icon: <Box variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.TOTAL_ITEMS_LISTED,
      value: analytics.total_items_listed
        ? analytics.total_items_listed
        : '-',
      change: 0,
    },
    {
      title: 'Low-stock items',
      cardBg: 'bg-transparent',
      icon: <TrendDown variant="Bold" size={wp(18)} color={colors?.white} />,
    
      iconBg: 'bg-accentOrange-main',
      key: AnalyticsVariants.TOTAL_LOW_STOCK_ITEMS,
      value: analytics.total_low_stock_items ? analytics.total_low_stock_items : '-',
      change: 0,
    },
  ];

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo, analytics]);

  return (
    <View className="border border-grey-border rounded-[15px]">
      {loading && <AnalyticsSkeletonLoader />}
      {!loading && (
        <Fragment>
          {splitCards.map((group, i) => (
            <Fragment key={i}>
              <View className="flex-row last:mb-0">
                {group.map((info, index) => (
                  <Fragment key={index}>
                    <AnalyticsCard
                      className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                      title={info.title}
                      value={info.value}
                      iconBg={info.iconBg}
                      change={info.change}
                      showChange={false}
                      icon={info.icon}
                      addon={info.addon}
                      theme="white"
                    />
                    {index === 0 && <View className="w-1 bg-grey-border" />}
                  </Fragment>
                ))}
              </View>
              {i === 0 && <View className="h-1 bg-grey-border" />}
            </Fragment>
          ))}
        </Fragment>
      )}
    </View>
  );
};

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}


export default ProductAnalyticsCards;
