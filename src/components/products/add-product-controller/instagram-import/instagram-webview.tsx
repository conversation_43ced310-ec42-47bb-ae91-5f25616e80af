import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Dimensions, ScrollView, View } from 'react-native';
import WebView, { WebViewNavigation } from 'react-native-webview';
import { cx } from 'src/assets/utils/js';
import { BaseText } from '@/components/ui';
import BottomModal from '@/components/ui/modals/bottom-modal';
import { paramsFromObject } from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { NativeViewGestureHandler } from 'react-native-gesture-handler';

interface InstagramWebViewProps {
  onComplete: (code: string) => void;
}

const IG_AUTH_URL = 'https://www.instagram.com/oauth/authorize';
const redirectUri = 'https://app.catlog.shop/products/create';

const params = paramsFromObject({
  client_id: '1078947597341793', //@feranmi: This should be an environment variable
  redirect_uri: redirectUri, //@feranmi: This should be an environment variable
  scope: 'instagram_business_basic',
  response_type: 'code',
  enable_fb_login: '0',
  force_authentication: '0',
});

export const InstagramWebView: React.FC<InstagramWebViewProps> = ({ onComplete }) => {
  const url = `${IG_AUTH_URL}?${params}`;
  const [siteLoaded, setSiteLoaded] = useState(false);
  const hasCalledOnComplete = useRef(false);

  useEffect(() => {
    return () => {
      setSiteLoaded(false);
      hasCalledOnComplete.current = false;
    };
  }, []);

  // const authUri = `https://api.instagram.com/oauth/authorize?${params}`;

  const handleNavigationChange = (nav: WebViewNavigation) => {
    if (nav.url.includes('code=') && !hasCalledOnComplete.current) {
      const code = nav.url.split('code=')[1].replace('#_', '');
      onComplete(code);
      hasCalledOnComplete.current = true;
    }
  };

  return (
    <BottomSheetView style={{ flex: 1 }} className="flex-1" enableFooterMarginAdjustment>
      <NativeViewGestureHandler disallowInterruption={true}>
        <View style={{ flex: 1 }}>
          <WebView
            onNavigationStateChange={handleNavigationChange}
            onLoad={() => setSiteLoaded(true)}
            className={cx('flex-1 w-full h-full p-0')}
            source={{ uri: url }}
          />
        </View>
      </NativeViewGestureHandler>
      {!siteLoaded && <ContainerLoader message="Loading Webpage..." />}
    </BottomSheetView>
  );
};

interface ContainerLoaderProps {
  message: string;
}
export const ContainerLoader: React.FC<ContainerLoaderProps> = ({ message }) => {
  return (
    <View className="flex-1 bg-white justify-center items-center absolute h-full w-full">
      <CustomImage imageProps={{ source: require('@/assets/gif/loader.gif') }} className="w-40 h-40 rounded-8" />
    </View>
  );
};
