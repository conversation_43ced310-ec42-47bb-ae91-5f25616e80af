import React from 'react';
import { Dimensions, View } from 'react-native';
import { hp, wp } from '@/assets/utils/js';
import cx from 'classnames';
import Shimmer from '@/components/ui/shimmer';

const { width } = Dimensions.get('window');
const cardWidth = (width - 40) / 2;
const dummyRow = new Array(2).fill(0);

interface StorefrontSkeletonLoaderProps {}

const InstagramSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return (
    <View className="flex-1 px-10 w-full">
      {dummyRow.map((_, index) => (
        <View className={cx('flex-row justify-between', { 'mt-0': index === 0, 'mt-20': index !== 0 })} key={index}>
          <View style={{ height: cardWidth, width: cardWidth }}>
            <Shimmer borderRadius={wp(12)} height={cardWidth} width={cardWidth} />
          </View>
          <View style={{ height: cardWidth, width: cardWidth }}>
            <Shimmer borderRadius={wp(12)} height={cardWidth} width={cardWidth} />
          </View>
        </View>
      ))}
    </View>
  );
};

export default InstagramSkeletonLoader;
