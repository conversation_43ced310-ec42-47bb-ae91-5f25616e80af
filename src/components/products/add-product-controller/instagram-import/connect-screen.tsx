import { Instagram } from 'iconsax-react-native/src';
import { delay, wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon } from '@/components/ui';
import InfoModal from '@/components/ui/modals/info-modal';
import colors from 'src/theme/colors';
import { View } from 'react-native';

interface Props {
  show: boolean;
  toggle: (s?: boolean) => void;
  toggleWebView: (s?: boolean) => void;
}
const ConnectToInstagramScreen: React.FC<Props> = ({ show, toggle, toggleWebView }) => {
  return (
    // <InfoModal
    //   isVisible={show}
    //   buttons={[
    //     {
    //       text: 'Connect',
    //       onPress: async () => {
    //         // toggle(false)
    //         toggleWebView();
    //       },
    //     },
    //   ]}
    //   modalImage={
    //     <CircledIcon className="bg-accentRed-main p-18">
    //       <Instagram size={wp(35)} variant={'Bold'} color={colors.white} />
    //     </CircledIcon>
    //   }
    //   closeModal={() => toggle(false)}>
    <View className="flex flex-col items-center justify-center mt-25">
      <CircledIcon className="bg-accentRed-main p-18 mb-20">
        <Instagram size={wp(35)} variant={'Bold'} color={colors.white} />
      </CircledIcon>
      <BaseText fontSize={20} classes={`mt-10 font-fhOscarLight`} type="heading">
        Connect your
      </BaseText>
      <BaseText fontSize={20} type="heading">
        Instagram Account
      </BaseText>
      <BaseText fontSize={14} classes="text-black-muted text-center mt-10 mb-40">
        Connecting your Instagram account{'\n'}allows you to import your posts as{'\n'}products on Catlog
      </BaseText>
    </View>
    // </InfoModal>
  );
};
export default ConnectToInstagramScreen;
