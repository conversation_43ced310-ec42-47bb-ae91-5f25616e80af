import Pressable from '@/components/ui/base/pressable';
import { FormikProps } from 'formik';
import { useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import Toast from 'react-native-toast-message';
import { cx } from 'src/assets/utils/js';
import { CheckOutlined, Image as ImageIcon, MediaAlbum, Video } from '@/components/ui/icons';
import CustomImage from '@/components/ui/others/custom-image';
import { useApi } from 'src/hooks/use-api';
import colors from 'src/theme/colors';
import InstagramSkeletonLoader from './skeleton-loader';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { BaseText } from '@/components/ui';
import usePagination from 'src/hooks/use-pagination';
import { GET_INSTAGRAM_MEDIA, GetInstagramMediaParams } from 'catlog-shared';
import { BottomSheetFlatList } from '@gorhom/bottom-sheet';

export interface InstagramMedia {
  id: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  media_url: string;
  permalink?: string;
  caption?: string;
  thumbnail_url?: string;
  post_id?: string;
}

export interface InstagramUser {
  account_type: string;
  id: string;
  media_count: number;
  username: string;
}

export interface ImportForm {
  selected_posts: { [key: string]: InstagramMedia };
  autogenerate_details?: boolean;
}

interface SelectPostsProps {
  selectedPosts: Record<string, InstagramMedia>;
  setSelectedPosts: (posts: Record<string, InstagramMedia>) => void;
  maxUploadable: number;
}

const IG_POSTS_PER_PAGE = 6;

export const SelectInstagramPosts: React.FC<SelectPostsProps> = ({
  selectedPosts,
  setSelectedPosts,
  maxUploadable,
}) => {
  const { currentPage, goNext } = usePagination();

  const [posts, setPosts] = useState<InstagramMedia[]>([]);
  const [pagination, setPagination] = useState<any>({ limit: IG_POSTS_PER_PAGE });
  const [initialLoading, setInitialLoading] = useState<boolean>(true);

  const selectedPostsArray = useMemo(() => Object.keys(selectedPosts), [selectedPosts]);

  const getPostsRequest = useApi<GetInstagramMediaParams>({
    apiFunction: GET_INSTAGRAM_MEDIA,
    method: 'GET',
    key: 'get-instagram-media',
    autoRequest: false,
  });

  const shouldFetchNext = !!getPostsRequest?.response?.data?.paging?.cursors?.after;

  useEffect(() => {
    const fn = async () => {
      const [res, err] = await getPostsRequest.makeRequest({ pagination });
      if (res) {
        if (res?.data?.data) {
          setPosts([...posts, ...(res?.data?.data ?? {})]);
        }
      }
      if (err) {
        console.log(err);
      }
      setInitialLoading(false);
    };
    fn();
  }, [pagination]);

  const onScrollEnd = () => {
    const next = getPostsRequest.response?.data?.paging?.cursors?.after;
    if (next) {
      setPagination({ ...pagination, next });
      goNext(); //Just to have an internal counter of how many pages have fetched
    }
  };

  const isSelected = (id: string) => {
    return selectedPosts[id] !== undefined;
  };

  const handleSelectToggle = (data: InstagramMedia) => {
    const selectedPostsCopy = { ...selectedPosts };

    if (isSelected(data.id)) {
      delete selectedPostsCopy[data.id];
    } else if (selectedPostsArray.length >= maxUploadable) {
      Toast.show({
        type: 'error',
        text1: `You can only select ${maxUploadable} posts`,
        text2: 'To select this post, please unselect another post',
      });
    } else {
      selectedPostsCopy[data.id] = data;
    }

    setSelectedPosts(selectedPostsCopy);
  };

  return (
    <>
      {/* {getPostsRequest.isLoading && posts?.length === 0 && <ContainerLoader message="Loading Posts" />} */}
      {/* Todo: @kayode we should show the loading skeleton when the posts are loading. Secondly we should allow the skeleton show even when isLoading is false because you have this weird blank screen when the modal loads */}
      <BottomSheetFlatList
        className="w-full px-2.5"
        contentContainerStyle={{ flexGrow: 1 }}
        data={posts}
        numColumns={2}
        enableFooterMarginAdjustment
        stickyHeaderIndices={[0]}
        ListHeaderComponent={
          <View className="px-20 justify-between flex-row pb-20 border-b border-grey-border bg-white">
            <BaseText className="text-black-main" weight="bold">
              {selectedPostsArray.length}{' '}
              <BaseText weight="regular" classes="text-black-muted">
                posts selected
              </BaseText>{' '}
            </BaseText>
            <BaseText className="text-black-main" weight="bold">
              <BaseText weight="regular" classes="text-black-muted">
                Max to upload:
              </BaseText>{' '}
              {maxUploadable}{' '}
            </BaseText>
          </View>
        }
        //   columnWrapperStyle={{justifyContent: "space-around",flex:1}}
        keyExtractor={(i, idx) => idx.toString()}
        renderItem={({ item }) => (
          <Pressable
            className={cx('w-[50%] p-2.5 items-center justify-center h-[200px] flex-grow-0')}
            onPress={() => handleSelectToggle(item)}>
            <View className={cx('rounded-[15px] overflow-hidden w-full h-full')}>
              <CustomImage
                className="w-full h-full"
                imageProps={{
                  source: item.media_type === 'VIDEO' ? item.thumbnail_url : item.media_url,
                }}
              />
              <View className="absolute top-10 left-10 p-2.5 bg-[#00000020] rounded-full">
                {MediaIcons[item.media_type]}
              </View>
              {isSelected(item.id) && (
                <View className="w-full h-full absolute bg-[#00000050] items-center justify-center">
                  <CheckOutlined className="" currentColor={colors.white} width={50} height={50} />
                </View>
              )}
            </View>
          </Pressable>
        )}
        onEndReached={posts.length >= IG_POSTS_PER_PAGE * currentPage ? onScrollEnd : null} //Todo: @kayode @feranmi doing a check like this prevents the skeleton from showing when there's clearly no more data to fetch
        ListEmptyComponent={
          getPostsRequest.isLoading || initialLoading ? (
            <View style={{ marginBottom: 50 }}>
              <View className="mt-10">
                <InstagramSkeletonLoader />
              </View>
            </View>
          ) : (
            <EmptyState showBtn={false} text="No posts to show" />
          )
        }
        ListFooterComponent={
          <View style={{ marginBottom: 50 }}>
            {getPostsRequest.isLoading && posts?.length !== 0 && (
              <View className="mt-10">
                <InstagramSkeletonLoader />
              </View>
            )}
          </View>
        }
      />
    </>
  );
};

const MediaIcons = {
  CAROUSEL_ALBUM: <MediaAlbum currentColor={colors.white} width={15} height={15} />,
  VIDEO: <Video currentColor={colors.white} width={15} height={15} />,
  IMAGE: <ImageIcon currentColor={colors.white} width={15} height={15} />,
};
