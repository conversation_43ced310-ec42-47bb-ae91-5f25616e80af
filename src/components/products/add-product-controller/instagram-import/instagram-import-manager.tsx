import React, { Fragment, useContext, useEffect, useMemo, useState } from 'react';
import { useApi } from 'src/hooks/use-api';
import { InstagramWebView } from './instagram-webview';
import ConnectToInstagramScreen from './connect-screen';
import BottomModal from '@/components/ui/modals/bottom-modal';
import { ImportForm, InstagramMedia, SelectInstagramPosts } from './select-products';
import { useFormik } from 'formik';
import { Dimensions, View } from 'react-native';
import useProgress from 'src/hooks/useProgess';
import { AppMediaType } from 'src/@types/utils';
import ProgressModal from '@/components/ui/progress-modal';
import { Product } from '../../create-products/types';
import { CHECK_INSTAGRAM_TOKEN, GENERATE_INSTAGRAM_ACCESS_TOKEN, GetInstagramAccessTokenParams } from 'catlog-shared';
import { hp, showError } from 'src/assets/utils/js';
import BaseText from 'src/components/ui/base/base-text';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Modal from 'react-native-modal';
import { useVideoDownload } from 'src/hooks/use-download-video';
import { SelectInstagramMedias } from './select-medias';
import { ModalsState } from 'src/hooks/use-modals';
import useInstagramImport from './use-instagram-import';

const redirect_uri = `https://app.catlog.shop/products/create`;

interface Props {
  modals: ModalsState<string>;
  toggleModal: (key: string | string[], value?: boolean) => void;
  onImportComplete?: (products: Product[]) => void;
  medias?: AppMediaType[];
  setMedias?: React.Dispatch<React.SetStateAction<AppMediaType[]>>;
  type?: 'IMPORT' | 'SELECT';
  selectedMedia?: string[];
  setSelectedMedia?: React.Dispatch<React.SetStateAction<string[]>>;
}

export const InstagramImportManager: React.FC<Props> = ({
  modals,
  toggleModal,
  medias = [],
  setMedias = () => {},
  onImportComplete,
  type = 'IMPORT',
  selectedMedia = [],
  setSelectedMedia = () => {},
}) => {
  const [step, setStep] = useState<'connect' | 'authenticate' | 'select' | 'import'>('connect');
  const [showLoader, setShowLoader] = useState(false);
  const progressSteps = useProgress(instagramProgressSteps);
  const [selectedPosts, setSelectedPosts] = useState<Record<string, InstagramMedia>>({});
  const selectedPostsArray = useMemo(() => Object.keys(selectedPosts), [selectedPosts]);

  const { handleSelectComplete } = useInstagramImport({
    selectedPosts,
    setStep,
    progressSteps,
    medias,
    setMedias,
    step,
    closeModal: () => toggleModal('instagram', false),
    onImportComplete,
    toggleModal,
  });

  const tokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: GENERATE_INSTAGRAM_ACCESS_TOKEN,
    method: 'GET',
    key: 'get-instagram-token',
    autoRequest: false,
  });

  const checkTokenRequest = useApi<GetInstagramAccessTokenParams>({
    apiFunction: CHECK_INSTAGRAM_TOKEN,
    method: 'GET',
    key: 'check-instagram-token',
    onSuccess: () => {
      setStep('select');
    },
    config: {
      retry: false,
    },
  });

  // const isLoading = checkTokenRequest.isLoading || tokenRequest.isLoading;

  const onInstagramAuthComplete = async (access_code: string) => {
    toggleModal('instagram', false);
    setShowLoader(true);
    const [res, err] = await tokenRequest.makeRequest({ access_code, redirect_uri });

    if (res) {
      setStep('select');
      toggleModal('instagram', true);
    }

    if (err) {
      setStep('authenticate');
      toggleModal('instagram', true);

      setTimeout(() => {
        showError(err.message ?? 'Something went wrong, Please try again');
      }, 500);
    }

    setShowLoader(false);
  };

  // Helper function to get modal configuration based on step and type
  const getModalConfig = () => {
    if (step === 'connect') {
      return {
        title: null,
        size: 'md' as any,
        showButton: true,
        wrapChildren: true,
        childrenWrapperStyle: { maxHeight: Dimensions.get('screen').height * 0.6 },
        buttons: [
          {
            text: 'Connect',
            onPress: async () => {
              setStep('authenticate');
            },
          },
        ],
        content: <ConnectToInstagramScreen show={modals.instagram} toggle={() => {}} toggleWebView={() => {}} />,
      };
    }

    if (step === 'authenticate') {
      return {
        title: null,
        size: 'lg' as any,
        showButton: false,
        wrapChildren: true,
        // childrenWrapperStyle: { maxHeight: Dimensions.get('screen').height * 0.6 },
        content: <InstagramWebView onComplete={onInstagramAuthComplete} />,
      };
    }

    if (step === 'select') {
      if (type === 'IMPORT') {
        return {
          title: 'Select posts to upload as products',
          size: 'lg' as any,
          showButton: true,
          wrapChildren: false,
          childrenWrapperStyle: undefined,
          buttons: [
            {
              text: 'Proceed',
              onPress: handleSelectComplete,
              isLoading: false,
              disabled: selectedPostsArray?.length === 0,
            },
          ],
          content: (
            <SelectInstagramPosts
              selectedPosts={selectedPosts}
              setSelectedPosts={setSelectedPosts}
              maxUploadable={10}
            />
          ),
        };
      }

      if (type === 'SELECT') {
        return {
          title: 'Select Instagram media',
          size: 'lg' as any,
          showButton: true,
          wrapChildren: true,
          childrenWrapperStyle: { maxHeight: Dimensions.get('screen').height * 0.6 },
          buttons: [
            {
              text: 'Proceed',
              onPress: () => onImportComplete([]),
              isLoading: false,
              disabled: selectedMedia?.length === 0,
            },
          ],
          content: (
            <SelectInstagramMedias
              selectedMedia={selectedMedia}
              setSelectedMedia={setSelectedMedia}
              maxUploadable={10}
              mediaType="VIDEO"
            />
          ),
        };
      }
    }

    return null;
  };

  const modalConfig = useMemo(() => getModalConfig(), [step, type, selectedMedia, selectedPosts]);

  if (checkTokenRequest.isLoading || showLoader) return <Loader show={checkTokenRequest.isLoading || showLoader} />;

  return (
    <Fragment>
      <BottomModal
        showButton={modalConfig?.showButton}
        isVisible={modals.instagram && !!modalConfig}
        useChildrenAsDirectChild
        size={modalConfig?.size}
        title={modalConfig?.title}
        wrapChildren={modalConfig?.wrapChildren}
        childrenWrapperStyle={modalConfig?.childrenWrapperStyle}
        buttons={modalConfig?.buttons}
        closeModal={() => toggleModal('instagram', false)}>
        {modalConfig?.content}
      </BottomModal>xxxxxx

      <ProgressModal
        show={modals.import_progress}
        toggle={() => toggleModal('import_progress', false)}
        currentStep={progressSteps.currentStep}
        steps={progressSteps.steps}
      />
    </Fragment>
  );
};

const instagramProgressSteps = [
  { key: 'IMPORT', label: 'Importing images from instagram...', isLoading: true, complete: false },
  { key: 'UPLOADING', label: 'Uploading images to our servers...', progress: 0 },
  { key: 'GENERATE', label: 'Generating product details...', progress: 0 },
  { key: 'ASSIGN', label: 'Downloading videos and assigning details...', progress: 0 },
];

const Loader = ({ show }: { show: boolean }) => {
  const insertTop = useSafeAreaInsets().top + hp(12);
  const translateX = useSharedValue(-300);

  useEffect(() => {
    translateX.value = withRepeat(
      withTiming(300, {
        duration: 700,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true,
    );
  }, []);

  // Create the animated style for the loading bar
  const progressStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  return (
    <Modal
      isVisible={show}
      backdropColor={'#1E1E1E80'}
      animationIn={'fadeInDown'}
      animationOut={'fadeOutUp'}
      style={[{ flex: 1, justifyContent: 'flex-start', margin: 0, padding: 0 }]}>
      <View
        style={[{ justifyContent: 'flex-end', marginTop: insertTop }]}
        className="bg-white mx-20 rounded-12 px-20 py-20">
        <BaseText fontSize={15} type="heading">
          Loading...
        </BaseText>
        <View className="w-full bg-grey-bgTwo rounded-full overflow-hidden mt-15">
          <Animated.View className={`rounded-full h-6 py-1 bg-primary-main`} style={progressStyle}></Animated.View>
        </View>
      </View>
    </Modal>
  );
};

export default InstagramImportManager;
