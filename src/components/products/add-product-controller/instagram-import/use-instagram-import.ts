import { FormikProps } from 'formik';
import { useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { cx, generateSimpleUUID, showError } from 'src/assets/utils/js';
import { useApi } from 'src/hooks/use-api';
import {
  GET_INSTAGRAM_MEDIA,
  GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
  GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
  GetInstagramMediaParams,
  GetMultipleInstagramAlbumMediaParams,
  GetProductDetailsFromMultipleCaptionsParams,
  getRandString,
  MediaType,
  ProductCreateMethod,
} from 'catlog-shared';
import { ProgressSteps } from 'src/hooks/useProgess';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { useLocalObject } from 'src/hooks/useLocalState';
import { useVideoDownload } from '@/hooks/use-download-video';
import useImageUploads from '@/hooks/use-file-uploads';
import { AppMediaType, Image } from 'src/@types/utils';
import { Product } from '../../create-products/types';
import { ImportForm, InstagramMedia } from './select-products';

interface UseInstagramImportProps {
  selectedPosts: Record<string, InstagramMedia>;
  setStep: (step: 'connect' | 'authenticate' | 'select' | 'import') => void;
  progressSteps: ProgressSteps;
  medias: AppMediaType[];
  setMedias: React.Dispatch<React.SetStateAction<AppMediaType[]>>;
  step: 'connect' | 'authenticate' | 'select' | 'import';
  closeModal: VoidFunction;
  onImportComplete: (products: Product[]) => void;
  toggleModal: (key: string | string[], value?: boolean) => void;
}

const useInstagramImport = ({
  selectedPosts,
  setStep,
  progressSteps,
  medias,
  setMedias,
  step,
  closeModal,
  onImportComplete,
  toggleModal,
}: UseInstagramImportProps) => {
  const [videosMap, setVideosMap] = useState<{ [key: string]: string[] }>({});
  const [captionMap, setCaptionMap] = useState<{ [key: string]: string }>({});
  const [mediaCache, setMediaCache] = useLocalObject<{ [key: string]: string }>('instagram-media-cache');

  const getAlbumMediaRequest = useApi<GetMultipleInstagramAlbumMediaParams>({
    apiFunction: GET_MULTIPLE_INSTAGRAM_ALBUM_MEDIA,
    method: 'POST',
    key: 'get-multiple-album-media',
    autoRequest: false,
  });

  const getProductDetailsRequest = useApi<GetProductDetailsFromMultipleCaptionsParams>({
    apiFunction: GET_PRODUCT_DETAILS_FROM_MULTIPLE_CAPTIONS,
    method: 'POST',
    key: 'get-product-details-from-captions',
    autoRequest: false,
  });

  useImageUploads(medias, setMedias);
  const { downloadVideo } = useVideoDownload();

  useEffect(() => {
    if (progressSteps.currentKey === 'GENERATE') {
      processCreateProductsFromImagesStep();
    }
  }, [progressSteps.currentKey]);

  useEffect(() => {
    if (progressSteps.currentKey === 'UPLOADING') {
      const imgs = medias.filter(i => i.type === MediaType.IMAGE) ?? [];
      const allImagesUploaded = imgs?.every(i => i.uploadProgress === 100 && !i.isUploading) && imgs.length > 0;
      const hasVideos = Object.keys(videosMap ?? {})?.length > 0;
      const canGoNext = allImagesUploaded || (imgs?.length === 0 && hasVideos);

      if (canGoNext) {
        setMediaCache({
          ...mediaCache,
          ...imgs.reduce((p, c) => {
            p[c.key] = c.url;
            return p;
          }, {} as any),
        });
        progressSteps.nextStep();
      } else {
        const imageUploadProgress =
          imgs.reduce((prev, cur) => {
            return prev + (cur.uploadProgress || 0);
          }, 0) / imgs.length;
        progressSteps.setStepProgress(imageUploadProgress);
      }
    }
  }, [medias, videosMap, progressSteps.currentKey]);

  const handleDownload = async (url: string, filename: string) => {
    try {
      const download = await downloadVideo(url, filename);
      return download;
    } catch (err) {
      throw new Error(err);
    }
  };

  const handleSelectComplete = async () => {
    toggleModal(['instagram', 'import_progress']);
    setStep('import');
    progressSteps.setStepIsLoading();
    await processImportImagesAndCaptionsStep();
    progressSteps.nextStep();
  };

  const processImportImagesAndCaptionsStep = async () => {
    const posts = Object.values(selectedPosts);
    const images: Partial<AppMediaType>[] = [];
    const videos: { [key: string]: string[] } = {};
    const captions = {} as any;
    const carouselPosts: string[] = [];

    for (const post of posts) {
      captions[post.id] = post.caption;
      const meta = { postId: post.id };
      if (post.media_type === 'CAROUSEL_ALBUM') {
        carouselPosts.push(post.id);
      } else {
        if (post.media_type === 'VIDEO') {
          videos[post.id] = [...(videos[post.id] ?? []), post.media_url];
        } else {
          const cachedUrl = mediaCache?.[post.id];
          const mediaImage = await resizeImageAndGetFilePath(
            post.thumbnail_url ?? post.media_url,
            post.id,
            cachedUrl,
            meta,
          );
          images.push({ ...mediaImage, type: MediaType.IMAGE });
        }
      }
    }

    if (carouselPosts.length > 0) {
      const [res, err] = await getAlbumMediaRequest.makeRequest({ media_ids: carouselPosts });
      if (err) {
        Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
      } else {
        const medias = res?.data as InstagramMedia[];

        for (const m of medias) {
          if (m.media_type === 'VIDEO') {
            videos[m.post_id] = [...(videos[m.post_id] ?? []), m.media_url];
          } else {
            const cachedUrl = mediaCache?.[m.id];
            const image = await resizeImageAndGetFilePath(m?.thumbnail_url ?? m.media_url, m.id, cachedUrl, {
              postId: m?.post_id,
            });
            images.push({ ...image, type: MediaType.IMAGE });
          }
        }
      }
    }
    // setImages(images);

    setMedias(images as any);
    setVideosMap(videos);
    setCaptionMap(captions);
  };

  const processCreateProductsFromImagesStep = async () => {
    try {
      const captions: { key: string; caption: string }[] = Object.keys(captionMap).map(key => ({
        key,
        caption: captionMap[key]?.replace(/(\r\n|\n|\r)/gm, '') ?? '',
      }));
      const products: Product[] = [];
      let productData: { price: string; name: string; description: string; variants?: any[]; variants_type?: string };

      if (captions.length > 0) {
        const [res, error] = await getProductDetailsRequest.makeRequest({ captions });
        const data = res?.data;
        const currentStep = progressSteps.nextStep();

        const videoFileMap: {
          [key: string]: {
            type: 'mp4' | 'mov' | 'avi' | 'mkv';
            uri: string;
            size: number;
            mimeType: string;
          }[];
        } = {};
        const videoKeys = Object.keys(videosMap);

        if (videoKeys.length > 0) {
          for (let i = 0; i < videoKeys.length; i++) {
            const k = videoKeys[i];
            const progress = 50.0 * ((i + 1) / videoKeys.length);
            const urls = videosMap[k];

            const files = await Promise.all(urls.map(async url => handleDownload(url, `video-${k}.mp4`)));

            videoFileMap[k] = [...(videoFileMap[k] ?? []), ...files];
            progressSteps.setStepProgress(progress, currentStep);
          }
        }

        if (error) {
          Toast.show({ type: 'error', text1: 'Something went wrong!', text2: "Couldn't load some images" });
        } else if (Array.isArray(data) && data.length > 0) {
          const imgs = medias.filter(i => i.type === MediaType.IMAGE);

          data.forEach((result, i) => {
            const progress = 100.0 * ((i + 1) / data.length);
            productData = result?.data;
            const price = cleanUpNumberString(productData?.price);
            const videoFiles = videoFileMap[result?.key] ?? [];

            const currentTime = new Date().getTime();

            products.push({
              images: [...imgs.filter(i => i.meta?.postId == result?.key)] as Image[],
              name: productData?.name ?? '',
              price: isNaN(price) ? '0' : String(price),
              category: '',
              description: productData?.description ?? '',
              thumbnail: 0,
              price_unit: '',
              variants: {
                type: 'custom',
                is_template: false,
                options: [],
              },
              upload_source: ProductCreateMethod.INSTAGRAM,
              info_blocks: [],
              tiered_pricing: null,
              videos: videoFiles.map(f => ({
                name: currentTime.toString(),
                key: currentTime.toString(),
                lastModified: null,
                type: MediaType.VIDEO,
                file: null,
                url: null,
                src: f.uri,
                fileSize: f.size,
                thumbnail: null,
                isUploading: false,
                meta: {
                  id: generateSimpleUUID(),
                  thumbnail: {
                    src: null,
                    name: '',
                    lastModified: null,
                    file: null,
                    isUploading: false,
                    uploadProgress: 0,
                    url: null,
                    error: false,
                    key: '',
                  },
                },
                uploadProgress: 0,
              })),
            });

            progressSteps.setStepProgress(progress, currentStep);
          });
        }
      }

      setStep('select');
      onImportComplete(products);
      toggleModal(['import_progress']);
      progressSteps.reset();
    } catch (error) {
      showError(error);
    }
  };

  return { processImportImagesAndCaptionsStep, processCreateProductsFromImagesStep, handleSelectComplete };
};

export default useInstagramImport;

async function resizeImageAndGetFilePath(src: string, key: string, url?: string, meta?: any): Promise<Image> {
  const isUploaded = url !== undefined;

  const { uri } = await manipulateAsync(src, [{ resize: { width: 1080 } }], {
    compress: 0.5,
    format: SaveFormat.JPEG,
    base64: true,
  });

  return {
    file: null,
    url: isUploaded ? url : undefined,
    key,
    isUploading: false,
    uploadProgress: isUploaded ? 100 : 0,
    meta,
    name: getRandString(10),
    src: uri,
  };
}

function cleanUpNumberString(number: any) {
  return Number(String(number).replace(',', ''));
}
