import { useNavigation } from '@react-navigation/native';
import {
  GET_STORE_ITEMS,
  ProductItemInterface,
  GetStoreItemsParams,
  IMPORT_ITEMS,
  ImportItemsParams,
} from 'catlog-shared';
import React, { Fragment, useMemo, useState } from 'react';
import Toast from 'react-native-toast-message';
import { colorAlternates } from 'src/constant/static-data';
import useAuthContext from 'src/contexts/auth/auth-context';
import { ResponseWithPagination, useApi } from 'src/hooks/use-api';
import useModals from 'src/hooks/use-modals';
import useProgress from 'src/hooks/useProgess';

import SelectProductModal from '@/components//products/storefront-products/select-products-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import SelectDropdown, { DropDownMethods } from '@/components/ui/inputs/select-dropdown';
import ProgressModal from '@/components/ui/progress-modal';
import SelectSpecificProductsModal from '../../storefront-products/select-specific-product-modal';
import { delay, hideLoader, showError, showLoader, showSuccess } from 'src/assets/utils/js';

interface Props {
  dropDownRef: React.RefObject<DropDownMethods>;
}
export interface PublicProductsResponse
  extends ResponseWithPagination<{
    store: string;
    items: { featured_items: ProductItemInterface[]; other_items: ProductItemInterface[] };
  }> {}

const StoreImportManager: React.FC<Props> = ({ dropDownRef }) => {
  const { store, stores } = useAuthContext();
  const { goBack } = useNavigation();

  const [selectedStore, setSelectedStore] = useState<string>();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [step, setStep] = useState<'select' | 'progress'>('select');

  const progressSteps = useProgress(storeImportProgressSteps);
  const { modals, toggleModal, switchModals } = useModals(['products', 'progress']);

  const filteredStores = stores?.filter(s => s.id !== store?.id);
  const selectedStoreData = stores?.find(s => s.id === selectedStore);

  const getProductsRequest = useApi<GetStoreItemsParams, PublicProductsResponse>(
    {
      apiFunction: GET_STORE_ITEMS,
      method: 'GET',
      key: 'fetch-public-items',
    },
    {
      per_page: 9007199254740991,
      filter: { store: selectedStore! ?? store?.id! },
    },
  );

  const importItemsRequest = useApi<ImportItemsParams>({
    apiFunction: IMPORT_ITEMS,
    method: 'POST',
    key: IMPORT_ITEMS.name,
  });

  const products = useMemo(() => {
    return getProductsRequest.response?.data
      ? [
          ...getProductsRequest?.response?.data?.items?.other_items,
          ...getProductsRequest.response?.data?.items?.featured_items,
        ]
      : [];
  }, [getProductsRequest.response]);

  const openProductsView = () => {
    dropDownRef.current?.close();
    setTimeout(() => {
      toggleModal('products', true);
    }, 500);
  };

  const importSelectedProducts = async () => {
    toggleModal('products', false);
    await delay(700);
    setStep('progress');
    const [res, err] = await importItemsRequest.makeRequest({ store_id: selectedStore!, items: selectedProducts });
    if (res) {
      progressSteps.setStepProgress(100);
      progressSteps.reset();
      setStep('select');
      await delay(500);
      showSuccess('Product Imported successfully');
      goBack();
    }
    if (err) {
      toggleModal('progress', false);
      setStep('select');
      showError(err);
    }
  };

  return (
    <>
      <SelectDropdown
        ref={dropDownRef}
        showAnchor={false}
        selectedItem={selectedStore}
        onPressItem={setSelectedStore}
        showLabel
        label="Select a Store to import products from"
        descriptionProps={{}}
        closeAfterSelection={false}
        showButton
        buttons={[{ text: 'Continue', onPress: openProductsView }]}
        items={
          filteredStores?.map((s, idx) => ({
            label: s.name,
            value: s.id,
            leftElement: (
              <CircledIcon
                className="px-15"
                style={{ backgroundColor: colorAlternates[idx ? idx % colorAlternates.length : 0].iconColor }}>
                <BaseText fontSize={18} type="heading" classes="capt text-white">
                  {s.name[0]}
                </BaseText>
              </CircledIcon>
            ),
          })) ?? []
        }
        containerClasses="mt-15"
      />
      {step === 'select' && (
        <SelectSpecificProductsModal
          products={products}
          isVisible={modals.products}
          modalTitle="Select Products you want to import"
          title="Select Products you want to import"
          buttons={[{ text: 'continue' }]}
          closeModal={() => toggleModal('products', false)}
          onPressContinue={() => importSelectedProducts()}
          selectedProducts={selectedProducts}
          setSelectedProducts={setSelectedProducts}
          loadingStates={{ isLoading: getProductsRequest?.isLoading, isReLoading: getProductsRequest?.isReLoading }}
          // selectedStore={selectedStore}
        />
      )}
      {step === 'progress' && (
        <ProgressModal
          show={step === 'progress'}
          toggle={() => toggleModal('progress')}
          currentStep={progressSteps.currentStep}
          customLabel={`Importing images from ${selectedStoreData?.name}...`}
          steps={progressSteps.steps}
        />
      )}
    </>
  );
};
export default StoreImportManager;

const storeImportProgressSteps = [
  { key: 'IMPORT', label: 'Importing images from store...', isLoading: true, complete: false }, //Todo: @kayode should be "importing products from $storeName"
];
