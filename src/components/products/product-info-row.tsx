import React from 'react';
import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import { PromoCardType } from './discounts-and-coupons/promo-card';
import { DiscountShape, PercentageCircle, Status } from 'iconsax-react-native/src';
import { wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText, CircledIcon, Row } from '../ui';
import ProductInfoCard, { ProductInfoCardProps } from './product-info-card';
import { styled } from 'nativewind';

interface ProductInfoRowProps extends Partial<BottomModalProps> {
  leftItem: ProductInfoCardProps;
  rightItem?: ProductInfoCardProps;
}

const ProductInfoRow = ({ leftItem, rightItem, ...props }: ProductInfoRowProps) => {
  return (
    <Row {...props}>
      <ProductInfoCard {...leftItem} />
      {rightItem && (
        <>
          <View className="h-full border-r border-grey-border mx-20" />
          <ProductInfoCard {...rightItem} />
        </>
      )}
    </Row>
  );
};

export default styled(ProductInfoRow);
