import React from 'react';
import { CloseCircle, Edit2, Layer, Trash } from 'iconsax-react-native/src';
import { useState } from 'react';
import { LayoutAnimation, View } from 'react-native';
import { enumToHumanFriendly, removeUnderscores, wp } from 'src/assets/utils/js';
import { BaseText, CircledIcon, Row } from '@/components/ui';
import Pressable from '@/components/ui/base/pressable';
import { ChevronUp } from '@/components/ui/icons';
import colors from 'src/theme/colors';
import CustomImage from '../ui/others/custom-image';

interface ProductVariantCardProps {
  name: string;
  values: string[];
  showTools?: boolean;
  onPressEdit?: VoidFunction;
  onPressDelete?: VoidFunction;
}

const ProductVariantCard = ({
  showTools = true,
  name,
  values,
  onPressEdit,
  onPressDelete,
}: ProductVariantCardProps) => {
  const [expand, setExpanded] = useState(false);

  const toggleStep = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  // const Image = () => {
  //   return (
  //     <View style={{ width: cardWidth, height: cardWidth }}>
  //                   <CustomImage imageProps={{ source: item.url ?? item.src }} className="h-full w-full rounded-12" />
  //                   <Pressable className="absolute top-8 right-8" onPress={() => removeVariant(index)}>
  //                     <CloseCircle size={wp(24)} variant="Bold" color={colors.white} />
  //                   </Pressable>
  //                 </View>
  //   )
  // }

  const isImage = name === 'image';

  return (
    <Row className="rounded-12 bg-grey-bgOne mt-15">
      <View className="justify-center px-10">
        <Layer variant="Bold" size={wp(18)} color={colors.black.placeholder} />
      </View>
      <View className="flex-1 rounded-12 border border-grey-border p-12 bg-white">
        <Pressable onPress={toggleStep} className="flex-row items-center justify-between" style={{ gap: wp(10) }}>
          <View className={expand ? 'rotate-0' : 'rotate-90'}>
            <ChevronUp size={wp(20)} currentColor={colors.black.muted} strokeWidth={2} />
          </View>
          <View className="flex-1 flex-row items-center">
            <BaseText fontSize={13} weight="medium">
              {name ? enumToHumanFriendly(name ?? '') : 'Option Name'}
            </BaseText>
            <View className="w-4 h-4 rounded-full bg-black-placeholder mx-6" />
            <BaseText fontSize={13} weight="medium" classes="text-black-muted">
              {values?.length ?? 0} Options
            </BaseText>
          </View>
          {showTools && (
            <>
              <Pressable onPress={onPressEdit}>
                <CircledIcon className="bg-grey-bgOne p-8">
                  <Edit2 size={wp(16)} color={colors.black.placeholder} />
                </CircledIcon>
              </Pressable>
              <Pressable onPress={onPressDelete}>
                <CircledIcon className="bg-grey-bgOne p-8">
                  <Trash size={wp(16)} color={colors.accentRed.main} />
                </CircledIcon>
              </Pressable>
            </>
          )}
        </Pressable>
        {expand && (
          <View>
            {values?.map(item => (
              <View className="mt-8" key={item}>
                <View className="p-15 rounded-12 bg-grey-bgOne border border-grey-border">
                  <BaseText fontSize={14} classes="text-black-muted">
                    {item}
                  </BaseText>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    </Row>
  );
};

export default ProductVariantCard;
