import { View } from 'react-native';
import { BaseText, CircledIcon, Container, Row } from '@/components/ui';
import CustomizationCard from '@/components/ui/cards/customization-card';
import Input from '@/components/ui/inputs/input';
import Pressable from '@/components/ui/base/pressable';
import { Add, DiscountCircle, Edit2, Layer, Trash } from 'iconsax-react-native/src';
import { delay, getFieldvalues, hp, showError, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { FormikProps } from 'formik';
import ListItemCard from '@/components/ui/cards/list-item-card';
import * as Animatable from 'react-native-animatable';
import { PricingTierItemInterface } from 'catlog-shared';
import { PricingTierFormParams } from './pricing-tiers-form';
import ProductInfoRow from '../product-info-row';
import { TextInput } from 'react-native';

interface TierOptionSectionProps {
  form: FormikProps<PricingTierFormParams>;
  useBottomSheetInput?: boolean;
}

const TierOptionSection = ({ form, useBottomSheetInput }: TierOptionSectionProps) => {
  const removeTier = (index: number) => {
    const tiers = [...form.values.tiers];
    tiers.splice(index, 1);
    form.setFieldValue('tiers', tiers);
  };

  const addNewTier = () => {
    const lastTier = form.values.tiers[form.values.tiers.length - 1];

    if (lastTier.minimum_quantity < 1 || lastTier.discount_percentage < 1) {
      showError(null, 'Please fill in the previous tier first');
      return;
    }

    // Validate that the last tier has valid progression values
    if (form.values.tiers.length > 1) {
      const previousTier = form.values.tiers[form.values.tiers.length - 2];
      if (lastTier.minimum_quantity <= previousTier.minimum_quantity) {
        showError(null, `Minimum quantity must be greater than ${previousTier.minimum_quantity}`);
        return;
      }
      if (lastTier.discount_percentage <= previousTier.discount_percentage) {
        showError(null, `Discount percentage must be greater than ${previousTier.discount_percentage}%`);
        return;
      }
    }

    const newTiers = [
      ...form.values.tiers,
      {
        minimum_quantity: lastTier ? lastTier?.minimum_quantity + 5 : 5,
        discount_percentage: 0,
      },
    ];
    form.setFieldValue('tiers', newTiers);
  };

  return (
    <View>
      <Animatable.View animation={'fadeIn'} duration={400} className="mt-0" style={{ gap: hp(15) }}>
        <TierCard
          index={-1}
          pricingTierItem={{ minimum_quantity: 0, discount_percentage: 0 }}
          deleteTier={removeTier}
          form={form}
          canModify={false}
          quantity={`1 - ${form.values.tiers[0] ? form.values.tiers[0].minimum_quantity - 1 : 'Unlimited'} Items`}
          discount={'0'}
          useBottomSheetInput={useBottomSheetInput}
        />
        {form.values.tiers.map((tier, index) => (
          <TierCard
            index={index}
            pricingTierItem={tier}
            key={index}
            deleteTier={removeTier}
            form={form}
            quantity={`${tier.minimum_quantity} - ${
              form.values?.tiers[index + 1]?.minimum_quantity > 1
                ? form.values.tiers[index + 1].minimum_quantity - 1
                : 'Unlimited'
            } Items`}
            discount={tier.discount_percentage.toString()}
            useBottomSheetInput={useBottomSheetInput}
          />
        ))}
        <Pressable className="self-start py-8 mt-5" onPress={addNewTier}>
          <Row className=" justify-start">
            <Add size={wp(14)} color={colors.primary.main} />
            <BaseText weight={'medium'} classes="text-primary-main ml-2">
              Add New Tier
            </BaseText>
          </Row>
        </Pressable>
      </Animatable.View>
    </View>
  );
};

interface TierCardProps {
  pricingTierItem: PricingTierItemInterface;
  deleteTier: (index: number) => void;
  index: number;
  form: FormikProps<PricingTierFormParams>;
  canModify?: boolean;
  quantity: string;
  discount: string;
  useBottomSheetInput?: boolean;
}

const TierCard = ({
  pricingTierItem,
  index,
  deleteTier,
  canModify = true,
  quantity,
  discount,
  form,
  useBottomSheetInput = false,
}: TierCardProps) => {
  const [displayMode, setDisplayMode] = useState<'form' | 'card'>(canModify ? 'form' : 'card');
  const qualityInputRef = useRef<TextInput>(null);
  const discountInputRef = useRef<TextInput>(null);

  useEffect(() => {
    toggleDisplayModeToCard();
  }, []);

  const toggleDisplayModeToCard = async () => {
    await delay(500);

    if (qualityInputRef.current?.isFocused() || discountInputRef.current?.isFocused()) {
      return;
    }
    if (pricingTierItem.minimum_quantity && pricingTierItem.discount_percentage) {
      setDisplayMode('card');
    }
  };

  const tier = form.values.tiers[index];

  return (
    <View className="bg-grey-bgOne rounded-12">
      <Row className="p-15">
        <BaseText fontSize={12} classes="text-black-muted">
          Tier {index + 1}
        </BaseText>
        {canModify && (
          <Row disableSpread>
            {displayMode === 'card' && (
              <Pressable onPress={() => setDisplayMode('form')}>
                <Edit2 size={wp(14)} color={colors.primary.main} />
              </Pressable>
            )}
            {displayMode === 'form' && index !== 0 && (
              <Pressable onPress={() => deleteTier(index)}>
                <Trash size={wp(14)} color={colors.accentRed.main} />
              </Pressable>
            )}
          </Row>
        )}
      </Row>
      <View className="bg-white rounded-12 border border-grey-border p-12">
        {displayMode === 'form' && (
          <Row className="gap-x-10 mt-5">
            <View className="flex-1">
              <Input
                useBottomSheetInput={useBottomSheetInput}
                label="Min. Quantity"
                keyboardType="numeric"
                {...getFieldvalues(`tiers.${index}.minimum_quantity`, form)}
                value={form.values.tiers[index].minimum_quantity.toString()}
                onBlur={toggleDisplayModeToCard}
                ref={ref => {
                  qualityInputRef.current = ref as TextInput;
                }}
              />
            </View>
            <View className="flex-1">
              <Input
                useBottomSheetInput={useBottomSheetInput}
                label="Discount"
                keyboardType="numeric"
                {...getFieldvalues(`tiers.${index}.discount_percentage`, form)}
                value={form.values.tiers[index].discount_percentage.toString()}
                onBlur={toggleDisplayModeToCard}
                ref={ref => {
                  discountInputRef.current = ref as TextInput;
                }}
              />
            </View>
          </Row>
        )}
        {displayMode === 'card' && (
          <Row>
            <Row className="justify-start flex-1">
              <CircledIcon iconBg="bg-grey-bgOne">
                <Layer variant="Bold" size={wp(18)} color={colors.grey.muted} />
              </CircledIcon>
              <View className="ml-8 items-stretch">
                <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
                  Quantity
                </BaseText>
                <BaseText fontSize={13} weight={'medium'} numberOfLines={1} adjustsFontSizeToFit classes="mt-4">
                  {quantity}
                </BaseText>
              </View>
            </Row>
            <View className="w-2 h-full bg-grey-border mx-10" />
            <Row className="justify-start flex-1">
              <CircledIcon iconBg="bg-grey-bgOne">
                <DiscountCircle variant="Bold" size={wp(18)} color={colors.grey.muted} />
              </CircledIcon>
              <View className="ml-8 items-stretch">
                <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
                  Discount
                </BaseText>
                <BaseText fontSize={13} weight={'medium'} classes="mt-4">
                  {discount}%
                </BaseText>
              </View>
            </Row>
          </Row>
        )}
      </View>
    </View>
  );
};

export default TierOptionSection;
