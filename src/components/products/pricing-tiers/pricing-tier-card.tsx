import React from 'react';
import { View } from 'react-native';
import cx from 'classnames';
import { BaseText, CircledIcon } from '@/components/ui';
import Row from '@/components/ui/row';
import SectionContainer, { ContainerType } from '@/components/ui/section-container';
import Separator from '@/components/ui/others/separator';
import MoreOptions, { MoreOptionElementProps } from '@/components/ui/more-options';

import { MoreHorizontal } from '@/components/ui/icons';
import colors from '@/theme/colors';
import { wp } from '@/assets/utils/js';
import { Layer } from 'node_modules/iconsax-react-native/src';
import { getItemThumbnail, PricingTierInterface, ProductItemInterface } from 'catlog-shared';
import CustomImage from 'src/components/ui/others/custom-image';
import Pressable from 'src/components/ui/base/pressable';
import Radio from 'src/components/ui/buttons/radio';

interface PricingTierCardProps {
  onPress?: () => void;
  tierItem: PricingTierInterface;
  moreOptions?: MoreOptionElementProps[];
  firstItem?: ProductItemInterface | null;
  isSelect?: boolean;
  isSelected?: boolean;
}

const PricingTierCard: React.FC<PricingTierCardProps> = ({
  onPress,
  isSelect,
  isSelected,
  tierItem,
  moreOptions = [],
  firstItem,
}) => {
  return (
    <Pressable onPress={onPress} className="px-12 border border-grey-border rounded-12">
      <Row className="py-12">
        <CircledIcon className="bg-grey-bgOne">
          <Layer variant="Bold" size={wp(20)} color={colors.grey.muted} />
        </CircledIcon>
        <View className="flex-1 mx-10">
          <BaseText fontSize={14} weight="medium" classes="text-black-main">
            {tierItem.label}
          </BaseText>
          <BaseText fontSize={13} classes="text-black-muted mt-4">
            {tierItem.tiers?.length} Tiers
          </BaseText>
        </View>

        {isSelect && <Radio active={isSelected} />}
        {moreOptions.length > 0 && (
          <MoreOptions
            options={moreOptions}
            customMenuElement={
              <CircledIcon className="bg-grey-bgOne p-6 ml-8">
                <MoreHorizontal strokeWidth={2} currentColor={colors.grey.muted} fill={colors.grey.muted} />
              </CircledIcon>
            }
          />
        )}
      </Row>

      <Separator className="mx-0 my-0" />

      <View className="py-12">
        {tierItem?.items?.length > 0 ? (
          <Row className={cx('items-center')}>
            {firstItem && (
              <CustomImage
                imageProps={{ source: { uri: getItemThumbnail(firstItem) }, contentFit: 'cover' }}
                className="h-30 w-30 rounded-5"
              />
            )}
            <BaseText fontSize={14} classes="text-black-muted flex-1 ml-12">
              {tierItem.items?.length} Items Assigned
            </BaseText>
          </Row>
        ) : (
          <BaseText fontSize={14} classes="text-black-muted">
            No Items Assigned
          </BaseText>
        )}
      </View>
    </Pressable>
  );
};

export default PricingTierCard;
