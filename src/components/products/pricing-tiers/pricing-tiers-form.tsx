import React, { forwardRef, useImperative<PERSON>andle, useEffect, useMemo } from 'react';
import { View } from 'react-native';
import { useFormik, FormikProps } from 'formik';
import * as Yup from 'yup';
import Toast from 'react-native-toast-message';
import { wp, getFieldvalues, delay, cx, showError, showSuccess } from '@/assets/utils/js';
import { BaseText } from '@/components/ui';
import Row from '@/components/ui/row';
import Input from '@/components/ui/inputs/input';
import Button, { ButtonVariant } from '@/components/ui/buttons/button';
import { Add, CloseCircle, Edit2 } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import {
  CreateTieredPricingParams,
  getItemThumbnail,
  PricingTierInterface,
  ProductItemInterface,
  UpdateTieredPricingParams,
} from 'catlog-shared';
import eventEmitter from '@/assets/utils/js/event-emitter';
import Separator from 'src/components/ui/others/separator';
import TierOptionSection from './tier-option-section';
import Pressable from 'src/components/ui/base/pressable';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import CustomImage from 'src/components/ui/others/custom-image';
import { ApiData } from 'src/hooks/use-api';
import SelectSpecificProductsModal from '../storefront-products/select-specific-product-modal';
import useModals from 'src/hooks/use-modals';

// Form ref interface
export interface PricingTiersFormRef {
  submitForm: () => void;
}

// Pricing Tier Form Interface
export interface PricingTierFormParams {
  id?: string;
  label: string;
  active: boolean;
  tiers: {
    minimum_quantity: number;
    discount_percentage: number;
  }[];
  items: string[];
}

interface PricingTiersFormProps {
  isEdit?: boolean;
  showItemsSection?: boolean;
  showActiveToggle?: boolean;
  activePricingTier?: PricingTierInterface;
  closeModal?: () => void;
  callBack?: (updateData?: PricingTierInterface) => void;
  getItem: (id: string) => ProductItemInterface;
  storeItems: ProductItemInterface[];
  createPricingTierRequest: ApiData<CreateTieredPricingParams, any>;
  updatePricingTierRequest: ApiData<UpdateTieredPricingParams, any>;
  useBottomSheetInput?: boolean;
}
const PricingTiersForm = forwardRef<PricingTiersFormRef, PricingTiersFormProps>(
  (
    {
      isEdit = false,
      getItem,
      storeItems,
      showItemsSection = false,
      showActiveToggle = false,
      activePricingTier,
      closeModal,
      callBack,
      createPricingTierRequest,
      updatePricingTierRequest,
      useBottomSheetInput = false,
    },
    ref,
  ) => {
    const { modals, toggleModal } = useModals(['selectProducts']);

    const form = useFormik<PricingTierFormParams>({
      initialValues: {
        id: '',
        label: '',
        active: true,
        tiers: [{ minimum_quantity: 5, discount_percentage: 10 }],
        items: [],
      },
      validationSchema: pricingTierFormValidationSchema,
      onSubmit: async values => {
        try {
          // Additional validation for tier progression
          // const tierValidationError = validateTierProgression(values.tiers);
          // if (tierValidationError) {
          //   showError(tierValidationError);
          //   return;
          // }

          let response, error;

          if (isEdit && activePricingTier) {
            [response, error] = await updatePricingTierRequest.makeRequest({
              id: activePricingTier.id,
              ...values,
              tiers: values.tiers.map(tier => ({
                ...tier,
                minimum_quantity: Number(tier.minimum_quantity),
                discount_percentage: Number(tier.discount_percentage),
              })),
            });
          } else {
            [response, error] = await createPricingTierRequest.makeRequest({
              ...values,
              tiers: values.tiers.map(tier => ({
                ...tier,
                minimum_quantity: Number(tier.minimum_quantity),
                discount_percentage: Number(tier.discount_percentage),
              })),
            });
          }

          if (error) {
            if (error.fields && Object.keys(error.fields).length > 0) {
              form.setErrors(error.fields);
            } else {
              showError(error.message, `Failed to ${isEdit ? 'update' : 'create'} tiered pricing rule`);
            }
          } else {
            // Emit appropriate event based on action
            if (isEdit) {
              eventEmitter.emit('pricingTierEdit', response.data);
            } else {
              eventEmitter.emit('pricingTierCreate', response.data);
            }

            showSuccess(`Tiered pricing rule ${isEdit ? 'updated' : 'created'} successfully!`);
            if (!isEdit) {
              form.resetForm();
            }
            closeModal?.();
            callBack?.(response?.data);
          }
        } catch (err) {
          showError(err);
        }
      },
      enableReinitialize: true,
    });

    const selectedItems = form.values.items;

    console.log('selectedItems: ', selectedItems);

    const handleItemsSelect = (items: string[]) => {
      console.log('items: ', items);
      console.log('form?.values?.items: ', form?.values?.items);
      form.setFieldValue('items', items);
      // if (form.values.tiers.length === 0) {
      //   form.setFieldValue("tiers", [
      //     {
      //       minimum_quantity: 5,
      //       discount_percentage: 10,
      //     },
      //   ]);
      // }
    };

    const firstItem = useMemo(() => {
      if (form?.values?.items?.length > 0) {
        return getItem(form?.values?.items?.[0]);
      }
      return null;
    }, [form.values?.items]);

    // Handle form initialization based on edit mode
    useEffect(() => {
      if (!isEdit) {
        form.resetForm();
      }
      if (isEdit && activePricingTier) {
        form.setFieldValue('id', activePricingTier.id);
        form.setFieldValue('label', activePricingTier.label);
        form.setFieldValue('active', activePricingTier.active ?? true);
        form.setFieldValue('tiers', activePricingTier.tiers || []);
        form.setFieldValue('items', activePricingTier.items || []);
      }
    }, [activePricingTier, isEdit]);

    // Expose form submit function through ref
    useImperativeHandle(ref, () => ({
      submitForm: () => {
        form.handleSubmit();
      },
    }));
    const addTier = () => {
      const newTier = { minimum_quantity: 1, discount_percentage: 0 };
      form.setFieldValue('tiers', [...form.values.tiers, newTier]);
    };

    const removeTier = (index: number) => {
      const updatedTiers = form.values.tiers.filter((_, i) => i !== index);
      form.setFieldValue('tiers', updatedTiers);
    };

    const updateTier = (index: number, field: 'minimum_quantity' | 'discount_percentage', value: number) => {
      const updatedTiers = form.values.tiers.map((tier, i) => (i === index ? { ...tier, [field]: value } : tier));
      form.setFieldValue('tiers', updatedTiers);
    };

    const addItem = () => {
      form.setFieldValue('items', [...form.values.items, '']);
    };

    const removeItem = (index: number) => {
      const updatedItems = form.values.items.filter((_, i) => i !== index);
      form.setFieldValue('items', updatedItems);
    };

    const updateItem = (index: number, value: string) => {
      const updatedItems = form.values.items.map((item, i) => (i === index ? value : item));
      form.setFieldValue('items', updatedItems);
    };

    return (
      <View>
        <Input
          useBottomSheetInput={useBottomSheetInput}
          label="Label e.g Clothing Wholesale Prices"
          {...getFieldvalues('label', form)}
          containerClasses="mt-15"
        />
        {isEdit && (
          <Row className="mt-15">
            <BaseText>Active Status</BaseText>
            <CustomSwitch value={form.values.active} onValueChange={value => form.setFieldValue('active', value)} />
          </Row>
        )}

        {form.values.items?.length > 0 && (
          <View className="bg-grey-bgOne rounded-12 mt-15">
            <Row className="p-12">
              <BaseText fontSize={13} classes="text-black-secondary">
                Assigned Items
              </BaseText>

              <Pressable onPress={() => {}} className="flex flex-row items-center">
                <Edit2 size={wp(14)} color={colors.primary.main} />
                <BaseText fontSize={13} weight="medium" classes="ml-4 text-primary-main">
                  Edit Selection
                </BaseText>
              </Pressable>
            </Row>
            <View className="p-15 bg-white rounded-12 border border-grey-border">
              {firstItem && (
                <Row className={cx('items-center')}>
                  {getItem(form?.values?.items[0]) && (
                    <CustomImage
                      imageProps={{ source: { uri: getItemThumbnail(firstItem) }, contentFit: 'cover' }}
                      className="h-30 w-30 rounded-6"
                      transparentBg={false}
                    />
                  )}
                  {form?.values?.items.length > 1 ? (
                    <BaseText fontSize={14} classes="text-black-main flex-1 ml-12">
                      {firstItem?.name} & {form?.values?.items.length - 1} other items
                    </BaseText>
                  ) : (
                    <BaseText fontSize={14} classes="text-black-main flex-1 ml-12">
                      {firstItem?.name}
                    </BaseText>
                  )}
                </Row>
              )}
            </View>
          </View>
        )}

        {form.values.items?.length === 0 && (
          <Pressable onPress={() => toggleModal('selectProducts')}>
            <Row disableSpread className="my-15 bg-grey-bgOne rounded-8 py-8 px-12">
              <BaseText>Assign Items (Optional)</BaseText>
              <View className="flex flex-row items-center p-6 bg-white rounded-4 ml-10">
                <BaseText fontSize={13} weight="medium" classes="text-primary-main">
                  Click here
                </BaseText>
              </View>
            </Row>
          </Pressable>
        )}

        <Separator className="mx-0 my-0" />
        <View className="mt-20">
          <TierOptionSection form={form} />
        </View>

        <View className="h-40" />
        <SelectSpecificProductsModal
          products={storeItems ?? []}
          isVisible={modals.selectProducts}
          closeModal={() => toggleModal('selectProducts', false)}
          // getProductsRequest={fetchItemsReq}
          selectedProducts={form?.values?.items || []}
          setSelectedProducts={handleItemsSelect}
          onPressContinue={() => toggleModal('selectProducts', false)}
        />
      </View>
    );
  },
);

// Validation Schema
export const pricingTierFormValidationSchema = Yup.object().shape({
  label: Yup.string().required('Pricing tier label is required'),
  tiers: Yup.array()
    .of(
      Yup.object().shape({
        minimum_quantity: Yup.number()
          .min(1, 'Minimum quantity must be at least 1')
          .required('Minimum quantity is required'),
        discount_percentage: Yup.number()
          .min(0, 'Discount percentage cannot be negative')
          .max(100, 'Discount percentage cannot exceed 100%')
          .required('Discount percentage is required'),
      }),
    )
    .min(1, 'At least one discount tier is required'),
});

export default PricingTiersForm;
