import React, { useState, useEffect, useMemo } from 'react';
import { View, FlatList, RefreshControl, Alert, ScrollView } from 'react-native';
import { ScrollHandlerProcessed } from 'react-native-reanimated';
import Animated from 'react-native-reanimated';
import {
  hp,
  wp,
  delay,
  showLoader,
  hideLoader,
  showSuccess,
  showError,
  updateOrDeleteItemFromList,
  alertPromise,
  ensureUniqueItems,
} from '@/assets/utils/js';
import PricingTierCard from './pricing-tier-card';
import { DELETE_TIERED_PRICING, PricingTierInterface } from 'catlog-shared';
import EmptyState from '@/components/ui/empty-states/empty-state';
import { Edit2, Trash, Layer } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { OptionWithIcon } from '@/components/ui/more-options';
import { useApi } from '@/hooks/use-api';
import { BaseText, CircledIcon } from '@/components/ui';
import Row from '@/components/ui/row';
import useModals from '@/hooks/use-modals';
import FAB from '@/components/ui/buttons/fab';
import PricingTierDetailsModal from './pricing-tier-detail-modal';
import PricingTierSkeletonLoader from './pricing-tier-skeleton-loader';
import useStorefrontItems from 'src/hooks/use-storefront-items';
import CustomSwitch from 'src/components/ui/inputs/custom-switch';
import AddPricingTierModal from './add-pricing-tier-modal';
import { useNavigation } from '@react-navigation/native';
import useEventListener from 'src/hooks/use-event-emitter';

interface PricingTiersListProps {
  scrollHandler?: ScrollHandlerProcessed<Record<string, unknown>>;
  tieredPricing: PricingTierInterface[];
  setTieredPricing: React.Dispatch<React.SetStateAction<PricingTierInterface[]>>;
  pullToRefreshFunc?: VoidFunction;
  isReLoading?: boolean;
  isLoading?: boolean;
  total_pages?: number;
  currentPage?: number;
  goNext: (totalPages?: number) => void;
  setPage: React.Dispatch<number>;
  error?: any;
}

const PricingTiersList: React.FC<PricingTiersListProps> = ({
  scrollHandler,
  tieredPricing,
  pullToRefreshFunc,
  setTieredPricing,
  isReLoading,
  isLoading,
  total_pages,
  currentPage,
  goNext,
  setPage,
  error,
}) => {
  const [activeTier, setActiveTier] = useState<PricingTierInterface>({} as PricingTierInterface);
  const [isEdit, setIsEdit] = useState(false);
  const { modals, toggleModal } = useModals(['editTier', 'tierDetails', 'addTierModal']);

  const { getItem, items, fetchItemsReq } = useStorefrontItems();

  const navigation = useNavigation();

  // Listen for new pricing tier creation
  useEventListener('pricingTierCreate', (newTier: PricingTierInterface) => {
    // Add new tier to the beginning of the list
    setTieredPricing(prev => ensureUniqueItems([newTier, ...prev]));
  });

  // Listen for pricing tier edits
  useEventListener('pricingTierEdit', (updatedTier: PricingTierInterface) => {
    // Update the existing tier in the list
    setTieredPricing(prev => prev.map(tier => (tier.id === updatedTier.id ? updatedTier : tier)));
  });

  const deletePricingTierRequest = useApi({
    apiFunction: DELETE_TIERED_PRICING,
    method: 'DELETE',
    key: 'delete-pricing-tier',
  });

  const viewDetails = (tier: PricingTierInterface) => {
    setActiveTier(tier);
    toggleModal('tierDetails');
  };

  const handleEditTier = (tier?: PricingTierInterface) => {
    if (tier) {
      setActiveTier(tier);
    }
    setIsEdit(true);
    toggleModal('tierDetails', false);
    setTimeout(() => {
      toggleModal('addTierModal');
    }, 600);
  };

  const handleAddTier = () => {
    navigation.navigate('CreatePricingTier');
  };

  const createTierCallback = (data?: PricingTierInterface) => {
    if (data) {
      if (isEdit) {
        // Update existing tier
        setTieredPricing(prev => prev.map(tier => (tier.id === data.id ? data : tier)));
      } else {
        // Add new tier
        setTieredPricing(prev => [data, ...prev]);
      }
    }
  };


  const handleDeleteTier = async (id: string) => {
    toggleModal('tierDetails', false);
    const canDelete = await alertPromise(
      'Do you want to delete this pricing tier?',
      'This pricing tier would be deleted and removed from all related items.',
      'Delete',
      'Cancel',
      true,
    );
    if (canDelete) {
      showLoader('Deleting Pricing Tier');
      const [response, error] = await deletePricingTierRequest.makeRequest({
        id: id,
      });
      hideLoader();
      await delay(600);

      if (response) {
        setTieredPricing(updateOrDeleteItemFromList(tieredPricing, 'id', id, null));
        showSuccess('Pricing tier deleted successfully');
      }

      if (error) {
        showError(error);
      }
    }
  };

  const moreOptions = (item: PricingTierInterface) => [
    {
      optionElement: (
        <Row spread={false}>
          <CustomSwitch value={item.active} onValueChange={value => {}} />
          <BaseText fontSize={12} classes="text-black-main ml-10">
            Active
          </BaseText>
        </Row>
      ),
      title: 'Available',
      onPress: () => {},
    },
    {
      optionElement: (
        <OptionWithIcon icon={<Edit2 size={wp(15)} color={colors.black.placeholder} />} label="Edit Pricing Tier" />
      ),
      onPress: () => {
        handleEditTier(item);
      },
    },
    {
      optionElement: (
        <OptionWithIcon
          icon={<Trash size={wp(15)} color={colors.accentRed.main} />}
          label="Delete Pricing Tier"
          labelClasses="text-accentRed-main"
        />
      ),
      onPress: () => {
        setActiveTier(item);
        handleDeleteTier(item?.id);
      },
    },
  ];

  const renderPricingTier = ({ item: tier }: { item: PricingTierInterface }) => (
    <PricingTierCard
      tierItem={tier}
      moreOptions={moreOptions(tier)}
      onPress={() => viewDetails(tier)}
      firstItem={tier.items?.length > 0 ? getItem(tier.items[0]) : null}
    />
  );

  const shouldShowLoader = isLoading && tieredPricing.length === 0 && fetchItemsReq.isLoading;

  return (
    <View style={{ flex: 1 }}>
      <Animated.FlatList
        data={shouldShowLoader ? [] : tieredPricing}
        refreshControl={
          pullToRefreshFunc ? <RefreshControl refreshing={false} onRefresh={pullToRefreshFunc} /> : undefined
        }
        ListEmptyComponent={() =>
          shouldShowLoader ? (
            <PricingTierSkeletonLoader count={5} />
          ) : (
            <View className="py-30">
              <EmptyState
                btnText="Add Pricing Tier"
                icon={<Layer variant={'Bold'} size={wp(40)} color={colors.grey.muted} />}
                text="No pricing tiers to show"
                onPressBtn={() => navigation.navigate('CreatePricingTier')}
              />
            </View>
          )
        }
        onEndReachedThreshold={0.3}
        onScroll={scrollHandler}
        className="flex-1 px-20 pb-40"
        ItemSeparatorComponent={() => <View className="h-15" />}
        renderItem={renderPricingTier}
        keyExtractor={item => item.id}
        onEndReached={() => {
          console.log('End reached');
          console.log('isLoading', isLoading);
          console.log('tieredPricing?.length', tieredPricing?.length);
          console.log('currentPage', currentPage);
          console.log('total_pages', total_pages);
          if (!isLoading && tieredPricing?.length > 0 && currentPage < total_pages) {
            console.log('goNext(total_pages);');
            goNext(total_pages);
          }
        }}
        ListFooterComponent={
          <View style={{ marginBottom: 80 }}>
            {tieredPricing.length > 0 && isLoading && (
              <View className="mt-20">
                <PricingTierSkeletonLoader count={2} />
              </View>
            )}
          </View>
        }
      />

      <FAB onPress={handleAddTier} />

      {/* Pricing Tier Details Modal */}
      {activeTier && modals.tierDetails && (
        <PricingTierDetailsModal
          isVisible={modals.tierDetails}
          activePricingTier={activeTier}
          closeModal={() => toggleModal('tierDetails', false)}
          editPricingTier={() => handleEditTier()}
          deletePricingTier={() => handleDeleteTier(activeTier.id)}
          firstItem={activeTier.items.length > 0 ? getItem(activeTier.items[0]) : null}
        />
      )}

      {/* Add/Edit Pricing Tier Modal */}
      <AddPricingTierModal
        isEdit={isEdit}
        activePricingTier={activeTier}
        isVisible={modals.addTierModal}
        closeModal={() => toggleModal('addTierModal', false)}
        callBack={createTierCallback}
        getItem={getItem}
        storeItems={items}
      />
    </View>
  );
};

export default PricingTiersList;
