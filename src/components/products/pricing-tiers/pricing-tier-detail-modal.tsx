import React, { Fragment, useState } from 'react';
import { View, FlatList, RefreshControl, Alert } from 'react-native';
import { ScrollHandlerProcessed } from 'react-native-reanimated';
import Animated from 'react-native-reanimated';
import {
  hp,
  wp,
  delay,
  showLoader,
  hideLoader,
  showSuccess,
  showError,
  updateOrDeleteItemFromList,
  toCurrency,
  cx,
} from '@/assets/utils/js';
import PricingTierCard from './pricing-tier-card';
import { getItemThumbnail, PricingTierInterface, ProductItemInterface } from 'catlog-shared';
import EmptyState from '@/components/ui/empty-states/empty-state';
import {
  MoneyRecive,
  Edit2,
  Trash,
  Layer,
  PercentageCircle,
  ShoppingCart,
  DiscountCircle,
} from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { MoreOptionElementProps, OptionWithIcon } from '@/components/ui/more-options';
import { useApi } from '@/hooks/use-api';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BaseText, CircledIcon } from '@/components/ui';
import Row from '@/components/ui/row';
import InfoRow from '@/components/ui/others/info-row';
import StatusPill, { StatusType } from '@/components/ui/others/status-pill';
import { ButtonVariant, TextColor } from '@/components/ui/buttons/button';
import useModals from '@/hooks/use-modals';
import SectionContainer from 'src/components/ui/section-container';
import Separator from 'src/components/ui/others/separator';
import CustomImage from 'src/components/ui/others/custom-image';

// Pricing Tier Details Modal Component
interface PricingTierDetailsModalProps extends Partial<BottomModalProps> {
  activePricingTier: PricingTierInterface;
  closeModal: VoidFunction;
  editPricingTier: VoidFunction;
  deletePricingTier: VoidFunction;
  fromProductForm?: boolean;
  itemPrice?: number;
  firstItem?: ProductItemInterface | null;
}

const PricingTierDetailsModal = ({
  activePricingTier,
  deletePricingTier,
  closeModal,
  editPricingTier,
  fromProductForm,
  itemPrice,
  firstItem,
  ...props
}: PricingTierDetailsModalProps) => {
  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      size="lg"
      buttons={[
        ...(!fromProductForm
          ? [
              {
                text: 'Delete Tier',
                variant: ButtonVariant.LIGHT,
                textColor: TextColor.NEGATIVE,
                onPress: deletePricingTier,
              },
            ]
          : []),
        { text: 'Edit Tier', onPress: editPricingTier },
      ]}
      title="Pricing Tier Details">
      <View className="px-20 border-t border-grey-border mt-15">
        <Row className="py-20">
          <CircledIcon iconBg="bg-grey-bgOne">
            <Layer variant="Bold" size={wp(20)} color={colors.grey.muted} />
          </CircledIcon>
          <View className="flex-1 mx-12">
            <BaseText fontSize={14} weight="bold" type="heading">
              {activePricingTier.label}
            </BaseText>
            <BaseText classes="text-black-muted mt-5">{getTierSummary(activePricingTier.tiers)}</BaseText>
          </View>
          <StatusPill
            statusType={activePricingTier?.active ? StatusType.SUCCESS : StatusType.DANGER}
            title={activePricingTier?.active ? 'ACTIVE' : 'INACTIVE'}
            className="bg-grey-bgOne"
          />
        </Row>

        <View className="border-y border-grey-border py-15">
          <BaseText fontSize={14} type="heading" classes="mb-12">
            Pricing Tiers
          </BaseText>
          <SectionContainer className="p-0 mt-0">
            <Row className="justify-between items-center p-12 bg-grey-bgOne rounded-8">
              <CircledIcon iconBg="bg-white">
                <DiscountCircle variant="Bold" size={wp(20)} color={colors.accentGreen.main} />
              </CircledIcon>
              <View className="flex-1 ml-10">
                <BaseText classes="text-black-muted">
                  1 to {activePricingTier.tiers[0].minimum_quantity - 1} items
                </BaseText>
                <BaseText weight="medium" classes="text-black-secondary mt-4">
                  No Discount
                </BaseText>
              </View>
            </Row>

            {activePricingTier.tiers?.map((tier, index) => (
              <Fragment key={index}>
                <Separator className="mx-0 my-0" />
                <Row key={index} className="justify-between items-center p-12 bg-grey-bgOne rounded-8">
                  <CircledIcon iconBg="bg-white">
                    <DiscountCircle variant="Bold" size={wp(20)} color={colors.accentGreen.main} />
                  </CircledIcon>
                  <View className="flex-1 ml-10">
                    <BaseText classes="text-black-muted">
                      {tier.minimum_quantity} -{' '}
                      {activePricingTier.tiers[index + 1]?.minimum_quantity - 1 || 'Unlimited'} items
                    </BaseText>
                    <BaseText weight="medium" classes="text-black-secondary mt-4">
                      {tier.discount_percentage}% Discount{' '}
                      {itemPrice ? `(${toCurrency(itemPrice * (1 - tier.discount_percentage / 100))})` : ''}
                    </BaseText>
                  </View>
                </Row>
                {index !== activePricingTier.tiers.length - 1 && <Separator className="mx-0 my-0" />}
              </Fragment>
            ))}
          </SectionContainer>
        </View>

        {fromProductForm && activePricingTier?.items?.length === 0 ? null : (
          <View className="bg-grey-bgOne rounded-12 mt-15">
            <Row className="p-12">
              <BaseText fontSize={14} type="heading">
                Assigned Items
              </BaseText>
            </Row>
            <View className="p-15 bg-white rounded-12 border border-grey-border">
              {activePricingTier.items && activePricingTier.items.length > 0 && firstItem && (
                <Row className={cx('items-center')}>
                  {firstItem && (
                    <CustomImage
                      imageProps={{ source: { uri: getItemThumbnail(firstItem) }, contentFit: 'cover' }}
                      className="h-30 w-30 rounded-5"
                      transparentBg={false}
                    />
                  )}
                  {activePricingTier.items.length > 1 ? (
                    <BaseText fontSize={14} classes="text-black-main flex-1 ml-12">
                      {firstItem?.name} & {activePricingTier.items.length - 1} other items
                    </BaseText>
                  ) : (
                    <BaseText fontSize={14} classes="text-black-main flex-1 ml-12">
                      {firstItem?.name}
                    </BaseText>
                  )}
                </Row>
              )}
            </View>
          </View>
        )}
      </View>
    </BottomModal>
  );
};

export default PricingTierDetailsModal;

const getTierSummary = (tiers: any[]) => {
  if (!tiers || tiers.length === 0) return 'No tiers';
  const minTier = tiers.reduce((min, tier) => (tier.minimum_quantity < min.minimum_quantity ? tier : min));
  const maxTier = tiers.reduce((max, tier) => (tier.discount_percentage > max.discount_percentage ? tier : max));
  return `${tiers.length} tiers (${minTier.minimum_quantity}+ qty, up to ${maxTier.discount_percentage}% off)`;
};
