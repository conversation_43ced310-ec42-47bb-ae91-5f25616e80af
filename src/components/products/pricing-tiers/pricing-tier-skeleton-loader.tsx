import React from 'react';
import { View } from 'react-native';
import Shimmer from '@/components/ui/shimmer';
import Row from '@/components/ui/row';
import Separator from '@/components/ui/others/separator';

interface PricingTierSkeletonLoaderProps {
  count?: number;
}

const PricingTierCardSkeleton = () => {
  return (
    <View className="px-12 border border-grey-border rounded-12 mb-15">
      {/* Header Row */}
      <Row className="py-12">
        {/* Icon skeleton */}
        <Shimmer width={40} height={40} borderRadius={800} />

        {/* Content skeleton */}
        <View className="flex-1 mx-10">
          <Shimmer width={120} height={14} classes="mb-4" borderRadius={8} />
          <Shimmer width={60} height={13} borderRadius={8} />
        </View>

        {/* More options skeleton */}
        <Shimmer width={32} height={32} borderRadius={8} classes="rounded-full ml-8" />
      </Row>

      <Separator className="mx-0 my-0" />

      {/* Bottom section skeleton */}
      <View className="py-12">
        <Row disableSpread className="items-center">
          {/* Image skeleton */}
          <Shimmer width={30} height={30} borderRadius={6} />

          {/* Items text skeleton */}
          <Shimmer width={100} height={14} borderRadius={8} classes="ml-12" />
        </Row>
      </View>
    </View>
  );
};

const PricingTierSkeletonLoader: React.FC<PricingTierSkeletonLoaderProps> = ({ count = 3 }) => {
  return (
    <View>
      {Array.from({ length: count }, (_, index) => (
        <PricingTierCardSkeleton key={index} />
      ))}
    </View>
  );
};

export default PricingTierSkeletonLoader;
