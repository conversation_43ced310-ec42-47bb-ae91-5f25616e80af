import React, { useMemo, useRef } from 'react';
import { View } from 'react-native';
import { wp } from '@/assets/utils/js';
import { CREATE_TIERED_PRICING, PricingTierInterface, ProductItemInterface, UPDATE_TIERED_PRICING } from 'catlog-shared';
import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import PricingTiersForm, { PricingTiersFormRef } from './pricing-tiers-form';
import { useApi } from 'src/hooks/use-api';

// Add Pricing Tier Modal Component
interface AddPricingTierModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  callBack?: (updateData?: PricingTierInterface) => void;
  activePricingTier?: PricingTierInterface;
  isEdit?: boolean;
  getItem: (id: string) => ProductItemInterface;
  storeItems: ProductItemInterface[];
}

const AddPricingTierModal = ({
  closeModal,
  callBack,
  activePricingTier,
  isEdit,
  getItem,
  storeItems,
  ...props
}: AddPricingTierModalProps) => {
  // Form ref to trigger submit from modal buttons
  const formRef = useRef<PricingTiersFormRef>(null);
  const createPricingTierRequest = useApi({
    apiFunction: CREATE_TIERED_PRICING,
    method: 'POST',
    key: 'create-pricing-tier',
  });

  const updatePricingTierRequest = useApi({
    apiFunction: UPDATE_TIERED_PRICING,
    method: 'PUT',
    key: 'update-pricing-tier',
  });

  return (
    <BottomModal
      enableDynamicSizing
      {...props}
      closeModal={closeModal}
      title={isEdit ? 'Edit Pricing Tier' : 'Add Pricing Tier'}

      useScrollView
      useChildrenAsDirectChild
      buttons={[
        {
          text: isEdit ? 'Update Tier' : 'Create Tier',
          onPress: () => formRef.current?.submitForm(),
          isLoading: createPricingTierRequest.isLoading || updatePricingTierRequest.isLoading,
        },
      ]}>
      <BottomSheetView style={{ paddingHorizontal: wp(20) }} enableFooterMarginAdjustment>
        {/* Pricing Tiers Form */}
        <PricingTiersForm
          ref={formRef}
          isEdit={isEdit}
          activePricingTier={activePricingTier}
          closeModal={closeModal}
          callBack={callBack}
          getItem={getItem}
          storeItems={storeItems}
          createPricingTierRequest={createPricingTierRequest}
          updatePricingTierRequest={updatePricingTierRequest}
        />

        <View className="h-40" />
      </BottomSheetView>
    </BottomModal>
  );
};

export default AddPricingTierModal;


