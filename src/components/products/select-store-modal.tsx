import { Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText } from '../ui';
import { useMemo } from 'react';

interface SelectStoreModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressContinue: () => void;
}

const SelectStoreModal = ({ closeModal, onPressContinue, ...props }: SelectStoreModalProps) => {
  const buttons = useMemo(() => {
    return [
      {
        text: 'Continue',
        onPress: onPressContinue,
      },
    ];
  }, [onPressContinue]);

  return (
    <BottomModal {...props} closeModal={closeModal} buttons={buttons} title={'Select Store'}>
      <View className="flex-1 flex-grow px-20">
        <BaseText fontSize={14} classes="px-20 font-fhOscarBold">
          Select Store
        </BaseText>
      </View>
    </BottomModal>
  );
};

export default SelectStoreModal;
