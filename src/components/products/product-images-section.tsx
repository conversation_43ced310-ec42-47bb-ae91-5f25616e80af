import { ScrollView, View } from 'react-native';
import { BaseText, Row } from '../ui';
import { styled } from 'nativewind';
import { ReactNode, useEffect, useState } from 'react';
import { RowProps } from '../ui/row';
import React from 'react';
import { AppMediaType, Image } from 'src/@types/utils';
import { convertItemToProduct, hideLoader, showLoader, wp } from 'src/assets/utils/js';
import ProductImage from './storefront-products/product-image';
import { useNavigation } from '@react-navigation/native';
import UploadImageBtn from '../ui/buttons/upload-image-btn';
import Pressable from '../ui/base/pressable';
import { Plus } from '../ui/icons';
import colors from 'src/theme/colors';
import useImageUploads from 'src/hooks/use-file-uploads';
import * as ImagePicker from 'expo-image-picker';
import { useApi } from 'src/hooks/use-api';
import useAuthContext from 'src/contexts/auth/auth-context';
import Toast from 'react-native-toast-message';
import { ProductItemInterface, VariantItem, EDIT_ITEM, ProductCreateMethod } from 'catlog-shared';

export interface ProductImagesSectionProps {
  item: ProductItemInterface;
  updateProduct?: React.Dispatch<React.SetStateAction<ProductItemInterface>>;
  showUpdate?: boolean;
  disabledEnlargePicture?: boolean;
}

const ProductImagesSection = ({
  item,
  updateProduct,
  showUpdate = true,
  disabledEnlargePicture = false,
}: ProductImagesSectionProps) => {
  const [product, setProduct] = useState<Product>(null);
  const [images, setImages] = useState<AppMediaType[]>([]);
  const [prevImages, setPrevImages] = useState(product?.images as AppMediaType[]);

  const { categories } = useAuthContext();

  useImageUploads(images, setImages);
  const navigation = useNavigation();

  const editItemRequest = useApi({
    apiFunction: EDIT_ITEM,
    method: 'PUT',
    key: 'edit-product',
  });

  useEffect(() => {
    if (item?.images) {
      setProduct({ ...convertItemToProduct(item) });
    }
  }, [item]);

  useEffect(() => {
    if (product?.images) {
      setImages(product.images as AppMediaType[]);
    }
  }, [product?.images]);

  useEffect(() => {
    if (JSON.stringify(images) === JSON.stringify(prevImages)) return;

    const hasUploadedUpdates = images.some(i => i.newFile && i.url && i.uploadProgress === 100);

    if (hasUploadedUpdates) {
      updateItem(
        {
          images: images.map(i => i.url),
        },
        () => {
          setPrevImages(images);
        },
      );
    }
  }, [images, prevImages]);

  const updateItem = async (data: any, callBack?: VoidFunction, loadingMessage?: string) => {
    showLoader(loadingMessage ?? 'Updating Item');
    const [res, error] = await editItemRequest.makeRequest({ id: item.id, item: data });
    hideLoader();
    if (error) {
      Toast.show({ text1: 'Error updating product Images', type: 'error' });
      return;
    }

    const updatedProduct = { ...res.data, category: categories?.find(c => res.data.category === c.id) };
    updateProduct?.(updatedProduct);
    callBack && callBack();
  };

  const removePickedImage = (index: number) => {
    if (index === product.thumbnail) {
      Toast.show({ text1: 'Cannot delete thumbnail image.', type: 'error' });
      return;
    }

    const productCopy = { ...product };
    productCopy.thumbnail = product.thumbnail > index ? product.thumbnail - 1 : product.thumbnail;

    productCopy.images.splice(index, 1);

    updateItem(
      {
        images: productCopy.images.map(i => i.url),
      },
      () => {
        setPrevImages(productCopy.images as AppMediaType[]);
        setProduct({ ...productCopy });
      },
      'Removing Image',
    );
  };

  const changeThumbnail = (img: Image, index: number) => {
    if (index === product.thumbnail || !img.url || (img?.uploadProgress ?? 0) < 100) return;

    updateItem(
      {
        thumbnail: index,
      },
      () => {
        setProduct({ ...product, thumbnail: index });
      },
    );
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      aspect: [1, 1],
      quality: 0.5,
      allowsMultipleSelection: true,
      selectionLimit: 5,
      // base64: true,
    });

    if (!result.canceled) {
      const newImages = result.assets.map((item, index) => {
        const uriParts = item.uri.split('/');
        const name = uriParts[uriParts.length - 1];
        const currentTime = new Date().getTime();
        return {
          file: null,
          url: '',
          name: item.fileName ?? name,
          src: item.uri,
          key: item.assetId ?? currentTime.toString(),
          isUploading: false,
          uploadProgress: 0,
          error: false,
          newFile: true,
        };
      });

      // console.log([...product.images, ...newImages]);

      setProduct(prev => ({ ...prev, images: [...prev.images, ...newImages] }));
    }
  };

  const imagesUrls = images?.map?.(item => item.url ?? item.src);
  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View className="flex-row px-20 mt-15" style={{ gap: wp(10) }}>
        {images?.map?.((item, index) => (
          <ProductImage
            key={item.src ?? item.url}
            isThumbnail={index === product?.thumbnail}
            disabled={disabledEnlargePicture}
            showCloseBtn={index !== product?.thumbnail && showUpdate}
            imageUri={item.src ?? item.url}
            onPressDelete={() => removePickedImage(index)}
            onPress={() =>
              navigation.navigate('ProductImages', {
                images: imagesUrls ?? [],
                thumbnail: product?.thumbnail,
                activeIndex: index,
                itemId: product.id,
              })
            }
          />
        ))}
        {showUpdate && (
          <Pressable className="rounded-[12px] p-[22px] bg-grey-bgOne" onPress={pickImage}>
            <Plus primaryColor={colors.black.placeholder} height={wp(16)} width={wp(16)} strokeWidth={1.5} />
          </Pressable>
        )}
      </View>
    </ScrollView>
  );
};

export interface Product {
  thumbnail: number;
  images: Image[];
  name: string;
  price: string;
  discount_price?: string;
  category: string;
  price_unit?: string;
  is_always_available?: boolean;
  quantity?: number;
  minimum_order_quantity?: number;
  cost_price?: number;
  description: string;
  variants?: {
    type: string;
    is_template?: boolean;
    options: VariantItem[];
  };
  hasImages?: boolean;
  id?: string;
  upload_source?: ProductCreateMethod;
}

export default ProductImagesSection;
