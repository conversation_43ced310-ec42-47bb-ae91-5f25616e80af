import { Dimensions, ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, CircledIcon, Row } from '../ui';
import { useMemo, useState } from 'react';
import CustomImage from '../ui/others/custom-image';
import { hp, removeEmptyAndUndefined, removeUnderscores, toCurrency, wp } from '@/assets/utils/js';
import { useApi } from '@/hooks/use-api';
import { capitalizeFirstLetter, CartItem, GET_ITEM, GetItemParams, getItemThumbnail, ProductItemInterface } from 'catlog-shared';
import { Category, Flash, Layer, Money, Moneys, Stickynote } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { Ellipse } from '../ui/icons';
import ProductImagesSection from './product-images-section';
import ProductInfoRow from './product-info-row';
import InfoRow from '../ui/others/info-row';
import useLayoutHeight from 'src/hooks/use-layout-height';
import Shimmer from '../ui/shimmer';
import QueryErrorBoundary from '../ui/query-error-boundary';
import React from 'react';

const { width } = Dimensions.get('window');

interface ProductDetailsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  cartItem: CartItem;
}

const ProductDetailsModal = ({ closeModal, cartItem, ...props }: ProductDetailsModalProps) => {
  const [product, setProduct] = useState<ProductItemInterface>({} as ProductItemInterface);
  const [isRetrying, setIsRetrying] = useState(false);

  const getProductRequest = useApi<GetItemParams>(
    {
      apiFunction: GET_ITEM,
      method: 'GET',
      key: 'get-product',
      onSuccess: response => {
        setProduct(response?.data);
      },
    },
    {
      slug: cartItem?.item_id,
    },
  );

  const handleRetry = () => {
    setIsRetrying(true);
    getProductRequest.refetch();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  const hasVariants = Boolean(cartItem.variant);

  const extraProductDetails = removeEmptyAndUndefined({
    expiry_date: product?.expiry_date,
    cost_price: product?.cost_price,
    minimum_order_quantity: product?.minimum_order_quantity,
  });

  const validExtraProductDetails = Object.entries(extraProductDetails);

  const variantData = () => {
    const d = Object.entries(cartItem.variant.values);
    // return d.map(i => `${capitalizeFirstLetter(removeUnderscores(i[0]))} ${i[1]}`);
    return d.map(i => `${i[1]}`);
  };

  const isLastData = (currentIndex: number, data?: any[]) => {
    return Boolean(currentIndex === data.length - 1);
  };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      showButton={false}
      title={'Product Details'}>
      <QueryErrorBoundary
        error={getProductRequest.error}
        isLoading={getProductRequest.isLoading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="fullPage"
        errorTitle="Failed to load product details"
        customErrorMessage="We couldn't load the product details. Please check your connection and try again."
      >
        <ScrollView>
          {getProductRequest.isLoading && <SingleProductSkeletonLoader />}
          {!getProductRequest.isLoading && (
          <View className="flex-1 flex-grow px-20">
            <View
              className="mt-25 rounded-[15px] overflow-hidden justify-end"
              style={{ height: hp((width - 40) * 0.55) }}>
              <CustomImage imageProps={{ source: { uri: getItemThumbnail(product) } }} />
              {product?.is_featured && (
                <CircledIcon className="p-4 bg-accentGreen-main absolute bottom-10 left-10">
                  <Flash variant={'Bold'} size={wp(12)} color={colors.white} />
                </CircledIcon>
              )}
            </View>
            <View className="mt-15">
              <Row spread={false}>
                <BaseText fontSize={13} type="heading">
                  {product.name}
                </BaseText>
                {hasVariants && (
                  <View className="rounded-full py-6 px-10 bg-grey-bgOne ml-10 flex-row items-center">
                    {variantData().map((i, idx) => (
                      <>
                        <BaseText key={idx} fontSize={12} weight="medium" classes="text-black-muted">
                          {i}
                        </BaseText>
                        {}
                        {!isLastData(idx, variantData()) && (
                          <View className="mx-5">
                            <Ellipse size={wp(3)} primaryColor={colors.black.placeholder} />
                          </View>
                        )}
                      </>
                    ))}
                  </View>
                )}
              </Row>
              <BaseText fontSize={12} classes="text-black-muted mt-10" lineHeight={wp(16)}>
                {product?.description}
              </BaseText>
            </View>
            <View className="-mx-20">
              <ProductImagesSection
                item={product}
                updateProduct={setProduct}
                showUpdate={false}
                disabledEnlargePicture
              />
            </View>
            <View className="-mx-20">
              <ProductInfoRow
                className="px-20 pt-25 pb-10"
                leftItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentGreen-pastel'}>
                      <Moneys variant={'Bold'} size={wp(15)} color={colors.accentGreen.main} />
                    </CircledIcon>
                  ),
                  value: toCurrency(product?.price),
                  title: 'Price',
                }}
                rightItem={{
                  icon: (
                    <CircledIcon iconBg={'bg-accentOrange-pastel'}>
                      <Category variant={'Bold'} size={wp(15)} color={colors.accentOrange.main} />
                    </CircledIcon>
                  ),
                  value: product?.category?.name ?? '-',
                  title: 'Category',
                }}
              />
            </View>
            {validExtraProductDetails.length > 0 && (
              <View className="mt-15">
                <BaseText fontSize={15} type="heading">
                  Additional Information
                </BaseText>
                <View className="mt-10">
                  {validExtraProductDetails.map(([key, value]) => {
                    return (
                      <InfoRow
                        key={key}
                        icon={extraProductDetailsConfig[key].icon}
                        title={extraProductDetailsConfig[key].title}
                        value={value}
                        iconBg="white"
                      />
                    );
                  })}
                </View>
              </View>
            )}
          </View>
        )}
        </ScrollView>
      </QueryErrorBoundary>
    </BottomModal>
  );
};

export default ProductDetailsModal;

const extraProductDetailsConfig = {
  expiry_date: {
    title: 'Expiry Date',
    icon: <Stickynote size={wp(16)} color={colors.black.placeholder} />,
  },
  cost_price: {
    title: 'Cost Price',
    icon: <Money size={wp(16)} color={colors.black.placeholder} />,
  },
  minimum_order_quantity: {
    title: 'Minimum Order Quantity',
    icon: <Layer size={wp(16)} color={colors.black.placeholder} />,
  },
};

const SingleProductSkeletonLoader = () => {
  return (
    <View className="flex-1">
      <View className="px-20 mt-10">
        <Shimmer borderRadius={hp(12)} height={hp((width - 40) * 0.55)} width={width - 40} marginTop={hp(10)} />
        <View className="h-1 bg-grey-border my-10" />
        <View>
          <BaseText fontSize={12} classes="text-black-main" type="heading">
            Product description
          </BaseText>
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(280)} marginTop={hp(10)} />
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(300)} marginTop={hp(8)} />
          {/* <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(310)} marginTop={hp(8)} />
          <Shimmer borderRadius={hp(40)} height={hp(10)} width={wp(320)} marginTop={hp(8)} /> */}
          <View className="items-center flex-row mt-25" style={{ columnGap: 10 }}>
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
            <Shimmer borderRadius={hp(12)} height={hp(55)} width={wp(55)} />
          </View>
        </View>
        <View className="h-1 bg-grey-border my-15" />
        <View className="flex-row justify-between">
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(65)} />
          <Shimmer borderRadius={hp(12)} height={hp(15)} width={wp(55)} />
        </View>
      </View>
    </View>
  );
};
