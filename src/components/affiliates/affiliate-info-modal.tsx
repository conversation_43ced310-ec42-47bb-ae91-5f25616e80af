import {
  CustomerInterface,
  humanFriendlyDate,
  GET_CUSTOMER,
  removeCountryCode,
  AffiliateInterface,
  GET_AFFILIATE,
  AFFILIATE_TYPES,
  GET_AFFILIATE_ORDERS,
  GET_AFFILIATE_ANALYTICS,
  AffiliateStatisticsInterface,
} from 'catlog-shared';
import { Box1, Calendar, Call, DollarSquare, Profile, Sms } from 'iconsax-react-native/src';
import { useState } from 'react';
import { View } from 'react-native';

import ProductInfoRow from '../products/product-info-row';
import { BaseText, CircledIcon, Row, WhiteCardBtn } from '../ui';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import InfoRow from '../ui/others/info-row';
import Shimmer from '../ui/shimmer';
import QueryErrorBoundary from '../ui/query-error-boundary';

import { copyToClipboard, hp, toCurrency, wp } from '@/assets/utils/js';
import { ArrowUpRight } from '@/components/ui/icons';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import useModals from '@/hooks/use-modals';
import colors from '@/theme/colors';
import CustomerInitial from '../customer/customer-initial';
import ContactWidget from '../customer/contact-widget';
import useAuthContext from 'src/contexts/auth/auth-context';
import Pressable from '../ui/base/pressable';
import CustomerOrdersModal from '../customer/customer-orders-modal';

interface AffiliateInformationModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  activeAffiliate: Partial<AffiliateInterface>;
  canManageAffiliates: boolean;
}

const SHIMMER_LENGTH = new Array(4).fill(0);

const AffiliateInformationModal = ({
  closeModal,
  activeAffiliate,
  onPressButton,
  canManageAffiliates,
  ...props
}: AffiliateInformationModalProps) => {
  // const [affiliate, setAffiliate] = useState<AffiliateInterface>({} as AffiliateInterface);
  const [loading, setLoading] = useState(true);

  const { storeLink } = useAuthContext();

  const { modals, toggleModal } = useModals(['orders']);

  // const getAffiliate = useApi({
  //   apiFunction: GET_AFFILIATE,
  //   method: 'GET',
  //   autoRequest: false,
  //   key: 'get-affiliate',
  // });
  const [isRetrying, setIsRetrying] = useState(false);


  const handleRetry = () => {
    setIsRetrying(true);
    // handleGetAffiliate();
    setTimeout(() => setIsRetrying(false), 2000);
  };

  // const handleGetAffiliate = async () => {
  //   setLoading(true);
  //   const [response, error] = await getAffiliate.makeRequest({ id: activeAffiliate.id! });
  //   // console.log(JSON.stringify(response));
  //   if (response) {
  //     setAffiliate(response?.data);
  //   }
  //   setLoading(false);
  // };

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      enableDynamicSizing
      onModalShow={() => {
      }}
      onModalHide={() => setLoading(true)}
      buttons={canManageAffiliates ? [{ text: 'Edit Affiliate', onPress: onPressButton }] : []}
      title="Affiliate Information">
      <QueryErrorBoundary
        error={null}
        isLoading={loading}
        refetch={handleRetry}
        isRetrying={isRetrying}
        variant="fullPage"
        errorTitle="Failed to load affiliate details"
        customErrorMessage="We couldn't load the affiliate details. Please check your connection and try again.">
        {/* {loading ? (
          <CustomerSkeletonLoader />
        ) : ( */}
        <View className="px-20">
          <Row className="py-25 border-t border-t-grey-border">
            <CustomerInitial initial={activeAffiliate?.name?.[0]} classes="w-36 h-36" />
            <View className="flex-1 mx-10">
              <BaseText fontSize={15} weight="bold" type="heading">
                {activeAffiliate?.name}
              </BaseText>
              <BaseText
                numberOfLines={1}
                ellipsizeMode="middle"
                classes="text-black-muted mt-5">{`${storeLink}?ref=${activeAffiliate.slug}`}</BaseText>
              <Pressable onPress={() => copyToClipboard(`${storeLink}?ref=${activeAffiliate.slug}`)}>
                <BaseText fontSize={12} weight="semiBold" classes="text-primary-main mt-5">
                  Copy Link
                </BaseText>
              </Pressable>
            </View>
            {activeAffiliate.type === AFFILIATE_TYPES.PERSON && <ContactWidget phone={activeAffiliate?.phone} />}
          </Row>
          <ProductInfoRow
            className="border-y border-grey-border py-15"
            leftItem={{
              icon: (
                <CircledIcon iconBg="bg-accentRed-pastel">
                  <Box1 variant="Bold" size={wp(15)} color={colors.accentRed.main} />
                </CircledIcon>
              ),
              value: activeAffiliate?.total_orders ? toCurrency(activeAffiliate?.total_orders) : '-',
              title: 'Total Orders',
            }}
            rightItem={{
              icon: (
                <CircledIcon iconBg="bg-accentOrange-pastel">
                  <Profile variant="Bold" size={wp(15)} color={colors.accentOrange.main} />
                </CircledIcon>
              ),
              value: activeAffiliate?.total_customers ? toCurrency(activeAffiliate?.total_customers) : '-',
              title: 'Total Customers',
            }}
          />
          <View className="mt-15">
            {activeAffiliate.type === AFFILIATE_TYPES.PERSON && (
              <InfoRow
                title="Phone Number"
                icon={<Call size={wp(15)} color={colors.black.placeholder} />}
                value={removeCountryCode(activeAffiliate?.phone ?? '')}
              />
            )}
            {activeAffiliate.type === AFFILIATE_TYPES.PERSON && (
              <InfoRow
                title="Email Address"
                icon={<Sms size={wp(15)} color={colors.black.placeholder} />}
                value={activeAffiliate?.email}
              />
            )}
            <InfoRow
              title="Date Added"
              icon={<Calendar size={wp(15)} color={colors.black.placeholder} />}
              value={humanFriendlyDate(activeAffiliate?.created_at)}
            />
            <InfoRow
              title="Total Orders"
              icon={<Box1 size={wp(15)} color={colors.black.placeholder} />}
              value="16 Aug - 30 Aug"
              valueElement={
                <WhiteCardBtn
                  className="bg-grey-bgOne rounded-full"
                  onPress={() => toggleModal('orders')}
                  icon={<ArrowUpRight size={wp(14)} strokeWidth={2} currentColor={colors.primary.main} />}>
                  {activeAffiliate?.total_orders} Orders
                </WhiteCardBtn>
              }
            />
          </View>
          <View className="h-40" />
        </View>
        {/* )} */}
      </QueryErrorBoundary>
      {/* <CustomerOrdersModal
        orders={customer?.orders}
        isVisible={modals.orders}
        closeParentModal={closeModal}
        closeModal={() => toggleModal('orders', false)}
        customerName={customer?.name?.split(' ')[0] ?? ''}
      /> */}
    </BottomModal>
  );
};

const CustomerSkeletonLoader = () => {
  return (
    <View>
      <View className="items-start flex-row px-20 mt-30">
        <Shimmer borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
        <View className="flex-1 ml-10">
          {/* <Shimmer borderRadius={hp(40)} height={hp(12)} width={wp(120)} /> */}
          <BaseText classes="text-black-muted">Customer Name</BaseText>
          <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(180)} marginTop={hp(5)} />
        </View>
        <Shimmer borderRadius={hp(40)} height={hp(20)} width={wp(50)} />
      </View>
      <View className="px-20">
        <View className="h-1 bg-grey-border my-15" />
        <Row>
          <Row>
            <Shimmer {...{ height: hp(30), width: wp(30), borderRadius: wp(1000) }} />
            <View className="mx-12 flex-1">
              <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
                Total Orders
              </BaseText>
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10), marginTop: hp(5) }} />
            </View>
            {/* <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} /> */}
          </Row>
          <Row>
            <Shimmer {...{ height: hp(30), width: wp(30), borderRadius: wp(1000) }} />
            <View className="mx-12 flex-1">
              <BaseText classes="text-black-muted" fontSize={12} numberOfLines={1}>
                Total Customers
              </BaseText>
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10), marginTop: hp(5) }} />
            </View>
            {/* <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} /> */}
          </Row>
        </Row>
        <View className="h-1 bg-grey-border my-10" />
      </View>
      <View className="px-20" style={{ columnGap: 10 }}>
        {SHIMMER_LENGTH.map((d, i) => (
          <Row className="my-10" key={i}>
            <Shimmer {...{ height: hp(20), width: wp(20), borderRadius: wp(10) }} />
            <View className="mx-12 flex-1">
              <Shimmer {...{ height: hp(15), width: wp(80), borderRadius: wp(10) }} />
            </View>
            <Shimmer {...{ height: hp(15), width: wp(50), borderRadius: wp(100) }} />
          </Row>
        ))}
      </View>
    </View>
  );
};

export default AffiliateInformationModal;
