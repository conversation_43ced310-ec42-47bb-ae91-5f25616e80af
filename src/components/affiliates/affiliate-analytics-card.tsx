import { Activity, Bag, BagTick2, Box, Chart2, Health, Profile, Profile2User, ProfileAdd, ShoppingBag, StatusUp } from 'iconsax-react-native/src';
import React, { Fragment, ReactNode, useMemo, useState } from 'react';
import { View } from 'react-native';
import colors from '@/theme/colors';
import { isEven, millify, toCurrency, toNaira } from '@/assets/utils/js/functions';
import { wp } from '@/assets/utils/js';
import AnalyticsCard from '../ui/cards/analytics-card';
import { StoreSummary } from '@/screens/orders/order-analytics';
import { AnalyticsSkeletonLoader } from '../deliveries/delivery-analytics-cards';
import { AnalyticsData } from 'src/screens/payments/payment-analytics';
import { AffiliateStatisticsInterface, CURRENCIES, CustomerStatisticsInterface } from 'catlog-shared';
import { BaseText, Row } from '../ui';
import { ChevronDown } from '../ui/icons';
import MoreOptions from '../ui/more-options';
import { TimeRange } from 'src/@types/utils';

export enum AnalyticsVariants {
  'TOTAL_AFFILIATES' = 'total_affiliates',
  'TOTAL_ORDERS' = 'total_orders',
  'TOP_AFFILIATES' = 'top_affiliates',
  'CUSTOMERS' = 'customers',
}

interface OrderAnalyticsProps {
  analytics: AffiliateStatisticsInterface;
  isLoading?: boolean;
  setRange: (range: TimeRange) => void;
  range: TimeRange;
}

const AffiliateAnalyticsCards = ({
  analytics = {} as AffiliateStatisticsInterface,
  isLoading,
  setRange,
  range,
}: OrderAnalyticsProps) => {
  // const [range, setRange] = useState(TimeRange.THIS_YEAR);

  const analyticsCardsInfo: AnalyticsCardInfo[] = [
    {
      title: 'Total Affiliates',
      iconBg: 'bg-accentRed-main',
      icon: <Profile variant="Bold" size={wp(18)} color={colors?.white} />,
      cardBg: 'bg-transparent',
      key: AnalyticsVariants.TOTAL_AFFILIATES,
      value: analytics[AnalyticsVariants.TOTAL_AFFILIATES] ? analytics[AnalyticsVariants.TOTAL_AFFILIATES] : '-',
      change: 0,
    },
    {
      title: 'Total Orders',
      cardBg: 'bg-transparent',
      icon: <ShoppingBag variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentGreen-main',
      key: AnalyticsVariants.TOTAL_ORDERS,
      value: analytics[AnalyticsVariants.TOTAL_ORDERS] || '0',
      change: 0,
    },
    {
      title: 'Customers',
      cardBg: 'bg-transparent',
      icon: <Chart2 variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.CUSTOMERS,
      value: analytics[AnalyticsVariants.CUSTOMERS] || '0',
      change: 0,
    },
    {
      title: 'Top Affiliates',
      cardBg: 'bg-transparent',
      icon: <Profile2User variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
      key: AnalyticsVariants.TOP_AFFILIATES,
      value: analytics[AnalyticsVariants.TOP_AFFILIATES] ? analytics[AnalyticsVariants.TOP_AFFILIATES] : 'N/A',
      change: 0,
    },
  ];

  const splitCards = useMemo(() => {
    let columnOne: AnalyticsCardInfo[] = [],
      columnTwo: AnalyticsCardInfo[] = [];

    analyticsCardsInfo.forEach((i, index) => (isEven(index) ? columnOne.push(i) : columnTwo.push(i)));

    return [columnOne, columnTwo];
  }, [analyticsCardsInfo]);

  const optionElement = [
    {
      title: 'All Time',
      onPress: () => setRange?.(TimeRange.ALL_TIME),
    },
    {
      title: 'This Year',
      onPress: () => setRange?.(TimeRange.THIS_YEAR),
    },
    {
      title: 'Last 30 Days',
      onPress: () => setRange?.(TimeRange.LAST_30_DAYS),
    },
    {
      title: 'Last week',
      onPress: () => setRange?.(TimeRange.LAST_WEEK),
    },
    {
      title: 'This week',
      onPress: () => setRange?.(TimeRange.THIS_WEEK),
    },
    {
      title: 'Today',
      onPress: () => setRange?.(TimeRange.TODAY),
    },
  ];

  return (
    <View>
      <View className="border border-grey-border rounded-[15px]">
        {isLoading && <AnalyticsSkeletonLoader />}
        {!isLoading && (
          <Fragment>
            {splitCards.map((group, i) => (
              <Fragment key={i}>
                <View className="flex-row last:mb-0">
                  {group.map((info, index) => (
                    <Fragment key={index}>
                      <AnalyticsCard
                        className="p-0 py-15 pr-10 pl-20 rounded-[15px]"
                        title={info.title}
                        value={info.value}
                        iconBg={info.iconBg}
                        change={info.change}
                        showChange={false}
                        icon={info.icon}
                        addon={info.addon}
                        theme="white"
                      />
                      {index === 0 && <View className="w-1 bg-grey-border" />}
                    </Fragment>
                  ))}
                </View>
                {i === 0 && <View className="h-1 bg-grey-border" />}
              </Fragment>
            ))}
          </Fragment>
        )}
      </View>
    </View>
  );
};

interface AnalyticsCardInfo {
  title: string;
  cardBg: string;
  icon: ReactNode;
  iconBg: string;
  key: AnalyticsVariants;
  addon?: ReactNode;
  value?: number | string;
  change: number;
}

export default AffiliateAnalyticsCards;
