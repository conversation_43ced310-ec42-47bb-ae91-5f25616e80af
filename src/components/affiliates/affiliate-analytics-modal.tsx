import * as Yup from 'yup';
import { View } from 'react-native';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { DeliveryStat } from 'src/screens/deliveries/deliveries-analytics';
import { InvoiceStatsResponse } from 'src/screens/invoices/dashboard';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import CustomerAnalyticsCards from 'src/components/customer/customer-analytics-cards';
import { AffiliateStatisticsInterface, CustomerStatisticsInterface, GET_CUSTOMERS_STATISTICS } from 'catlog-shared';
import { ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { useMemo, useState } from 'react';
import { TimeRange } from 'src/@types/utils';
import useModals from 'src/hooks/use-modals';
import { getFilter } from 'src/components/ui/graph/area-graph';
import AffiliateAnalyticsCards from './affiliate-analytics-card';

interface AffiliateAnalyticsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  stats?: AffiliateStatisticsInterface;
  isLoading?: boolean;
}

const AffiliateAnalyticsModal = ({ closeModal, stats, isLoading, ...props }: AffiliateAnalyticsModalProps) => {
  const [range, setRange] = useState(TimeRange.THIS_YEAR);
  const analytics = stats ?? {} as AffiliateStatisticsInterface;

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      showButton={false}
      title='Affiliate Overview'
      buttons={[
        {
          text: 'Close',
          onPress: () => closeModal,
        },
      ]}>
      <View className="mx-20 pb-10">
        <AffiliateAnalyticsCards
          range={range}
          setRange={setRange}
          analytics={analytics}
          isLoading={isLoading}
        />
      </View>
    </BottomModal>
  );
};

export const addAddressValidationSchema = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address'),
});

export default AffiliateAnalyticsModal;
