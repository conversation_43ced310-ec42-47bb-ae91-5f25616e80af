import { Text, TouchableOpacity, View } from 'react-native';
import { ReactNode, useState } from 'react';
import { styled } from 'nativewind';
import { TextProps } from 'react-native';
import cx from 'classnames';
import { TouchableHighlightProps } from 'react-native';
import { BaseText, CircledIcon } from '../ui';
import { BaseTextProps } from '../ui/base/base-text';
import { Copy, Edit2, PercentageCircle, Profile, TicketDiscount, Trash, UserOctagon } from 'iconsax-react-native/src';
import { copyToClipboard, getColorAlternates, randomColor, wp } from '@/assets/utils/js';
import MoreOptions, { MoreOptionElementProps } from '../ui/more-options';
import Row from '../ui/row';
import colors from '@/theme/colors';
import Pressable from '../ui/base/pressable';
import Toast from 'react-native-toast-message';
import { AffiliateInterface, removeCountryCode } from 'catlog-shared';
import PillTextButton from '../ui/buttons/pill-text-button';

export interface AffiliateCardProps extends Partial<TouchableHighlightProps> {
  onPress?: () => void;
  showBorder?: boolean;
  affiliate: AffiliateInterface;
  index: number;
  moreOptions: MoreOptionElementProps[];
  bottomComponent?: ReactNode;
  showOptions?: boolean;
}

const AffiliateCard = ({
  onPress,
  showBorder = true,
  moreOptions = [],
  affiliate,
  index = 0,
  bottomComponent,
  showOptions = true,
  ...props
}: AffiliateCardProps) => {
  const color = getColorAlternates(index);

  const copyPhone = async () => {
    const status = await copyToClipboard(affiliate.phone!);
    if (status === true) {
      Toast.show({ type: 'success', text1: 'Copied to clipboard' });
    }
  };

  return (
    <View>
      <Pressable
        className={`flex-row items-center py-15 ${showBorder && 'border-b border-b-grey-border'}`}
        activeOpacity={0.8}
        onPress={onPress}
        {...props}>
        <CircledIcon style={{ backgroundColor: color.bgColor }}>
          <UserOctagon size={wp(20)} variant={'Bold'} color={color.iconColor} />
        </CircledIcon>
        <View className={'mx-12 flex-1'}>
          {affiliate.name && (
            <BaseText fontSize={12} classes={cx('font-fhOscarBold mr-12')} numberOfLines={1}>
              {affiliate.name}
            </BaseText>
          )}
          {affiliate.phone ? (
            <View className="flex-row mt-5 items-center rounded-full self-start">
              <BaseText fontSize={12} weight={'medium'} classes="leading-[16px] text-black-muted">
                {removeCountryCode(affiliate.phone)}
              </BaseText>
              <Pressable onPress={() => copyPhone()}>
                <CircledIcon className={'bg-primary-pastel p-5 ml-8'}>
                  <Copy size={wp(10)} color={colors?.primary.main} />
                </CircledIcon>
              </Pressable>
            </View>
          ) : (
            bottomComponent
          )}
        </View>
        <View>
          <View className={'flex-1 ml-12 items-end self-stretch justify-between'}>
          { showOptions && <MoreOptions options={moreOptions} />}
          </View>

            {affiliate.type && (
              <View className='bg-grey-bgOne py-5 px-12 rounded-full mt-5 self-start'>
                <BaseText fontSize={10} classes={cx('font-fhOscarBold')} numberOfLines={1}>
                  {affiliate.type}
                </BaseText>
              </View>
            )}
        </View>
        
      </Pressable>
    </View>
  );
};

export default AffiliateCard;
