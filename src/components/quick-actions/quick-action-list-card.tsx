import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode } from 'react';
import Row from '../ui/row';
import { StyledProps } from 'nativewind';
import colors from '@/theme/colors';
import { ArrowRight } from '../ui/icons';
import { BaseText } from '../ui/base';
import Pressable from '../ui/base/pressable';

export interface QuickActionListCardProps {
  icon: ReactNode;
  title: string;
  iconBgColor: string;
  description: string;
  actionScreenName?: string;
  onPress?: VoidFunction;
}

const { width } = Dimensions.get('window');

const QuickActionListCard = ({
  icon,
  title,
  description,
  iconBgColor,
  actionScreenName,
  onPress,
}: QuickActionListCardProps) => {
  const navigation = useNavigation();

  return (
    <Pressable
      className="flex-row items-center py-20 border-b border-b-grey-border last:border-b-0 justify-between"
      onPress={onPress}>
      <View className="flex-1 flex flex-row items-center">
        <Row className={`p-10 rounded-full items-center justify-center ${iconBgColor}`}>{icon}</Row>
        <View className="flex-1 ml-12">
          <BaseText type="heading" fontSize={15} classes="text-black-secondary mb-2">
            {title}
          </BaseText>
          <BaseText fontSize={13} classes="text-black-muted">
            {description}
          </BaseText>
        </View>
      </View>
      <Row className="rounded-full p-9 bg-grey-bgOne">
        <ArrowRight currentColor={colors.black.placeholder} size={18} strokeWidth={1.5} />
        {/* <ArrowRight size={16} color={colors.black.placeholder} /> */}
      </Row>
    </Pressable>
  );
};

export default QuickActionListCard;
