import { Arrow<PERSON>ircleR<PERSON>, ArrowCircleRight2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r, Profile } from 'iconsax-react-native/src';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode } from 'react';
import Row from '../ui/row';
import { StyledProps } from 'nativewind';
import colors from '@/theme/colors';
import { BaseText, CircledIcon } from '../ui';
import { CheckActive } from '../ui/icons';
import { getColorAlternates, wp } from 'src/assets/utils/js';

export interface ReferredFriendsCardProps {
  showBorder?: boolean;
  name: string;
  date: string;
  index: number;
  variant: ReferredFriendsCardVariant;
}

export enum ReferredFriendsCardVariant {
  'CLAIMED' = 'Claimed',
  'UNCLAIMED' = 'Unclaimed',
}

const ReferredFriendsCard = ({
  showBorder = true,
  name,
  date,
  index,
  variant = ReferredFriendsCardVariant.CLAIMED,
}: ReferredFriendsCardProps) => {
  const navigation = useNavigation();

  const colorsAlternates = getColorAlternates(index);

  const variantProperty: { [key: string]: { description: string } } = {
    [ReferredFriendsCardVariant.CLAIMED]: {
      description: 'Credit Claimed',
    },
    [ReferredFriendsCardVariant.UNCLAIMED]: {
      description: "User hasn't subscribed",
    },
  };

  return (
    <TouchableOpacity
      className={`flex-row items-center py-15 ${showBorder && 'border-b border-b-grey-border'}`}
      activeOpacity={0.8}>
      {/* <Row className={`p-12 rounded-full items-center justify-center bg-accentGreen-pastel`}>
      </Row> */}
      <CircledIcon iconBg="bg-accentGreen-pastel" style={{ backgroundColor: colorsAlternates.bgColor }}>
        <Profile variant="Bold" color={colorsAlternates.iconColor} size={20} />
      </CircledIcon>
      <View className={'flex-1 ml-12'}>
        <Row>
          <BaseText fontSize={13} classes={'flex-1 font-interSemiBold text-black-primary mr-12'} numberOfLines={1}>
            {name}
          </BaseText>
          <BaseText fontSize={12} classes={'font-interRegular text-black-secondary'}>
            Joined {date}
          </BaseText>
        </Row>
        <Row className="justify-start">
          <BaseText fontSize={12} classes={'font-interMedium text-black-muted mr-4'}>
            {variantProperty[variant].description}
          </BaseText>
          {variant === ReferredFriendsCardVariant.CLAIMED && (
            <CheckActive size={wp(13)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          )}
        </Row>
      </View>
    </TouchableOpacity>
  );
};

export default ReferredFriendsCard;
