import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import ReferredFriendsCard, { ReferredFriendsCardVariant } from './referred-friends-card';
import SectionContainer from '../ui/section-container';
import { GetReferralResponse } from 'src/screens/catlog-credits';
import dayjs from 'dayjs';

interface ReferredFriendsModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  referrals: GetReferralResponse['data']['referrals'];
}

const ReferredFriendsModal = ({ closeModal, referrals = [], ...props }: ReferredFriendsModalProps) => {
  const navigation = useNavigation();

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Referred Friends'}
      showButton={false}
      modalStyle={referrals?.length > 8 ? { flex: 1 } : undefined}
      containerStyle={referrals?.length > 8 ? { flex: 1 } : undefined}
      innerStyle={referrals?.length > 8 ? { flex: 1 } : undefined}>
      <View className="px-20" style={referrals?.length > 8 ? { flex: 1 } : undefined}>
        <SectionContainer>
          {referrals.map((item, index) => (
            <ReferredFriendsCard
              key={`${item?.user}-${index}`}
              index={index}
              name={item?.user?.name}
              showBorder={index !== referrals.length - 1}
              date={dayjs(item.user.date_joined).format('DD/mm/YYYY')}
              variant={item.has_claimed ? ReferredFriendsCardVariant.CLAIMED : ReferredFriendsCardVariant.UNCLAIMED}
            />
          ))}
        </SectionContainer>
      </View>
    </BottomModal>
  );
};

export default ReferredFriendsModal;
