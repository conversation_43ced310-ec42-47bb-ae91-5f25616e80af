import {
  Activity,
  BagHappy,
  Chart,
  Copy,
  ExportCircle,
  Moneys,
  Notification,
  TagUser,
  WalletMoney,
} from 'iconsax-react-native/src';
import { Dimensions, ImageBackground, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { ReactNode, useMemo } from 'react';
import Row from '../ui/row';
import { ArrowUp } from '@/components/ui/icons';
import { hp, wp } from '@/assets/utils/js/responsive-dimension';
import { BaseText } from '../ui/base';

export interface CreditInfoCardProps {
  variant?: CreditInfoCardVariants;
  onPress?: () => void;
}

export enum CreditInfoCardVariants {
  'BALANCE_CARD' = 'balance_card',
  'REFERRAL_CARD' = 'referral_card',
}

const { width } = Dimensions.get('window');

const CreditInfoCard = ({ variant = CreditInfoCardVariants.BALANCE_CARD, onPress }: CreditInfoCardProps) => {
  const navigation = useNavigation();

  const variantProperty: { [key: string]: { title: string; cardBg: any; icon: ReactNode; iconBg: string } } = {
    [CreditInfoCardVariants.BALANCE_CARD]: {
      title: 'Credit Balance',
      cardBg: require('@/assets/images/credit-balance-bg.png'),
      icon: <WalletMoney variant="Bold" width={22} height={22} color={colors?.white} />,
      iconBg: 'bg-accentOrange-main',
    },
    [CreditInfoCardVariants.REFERRAL_CARD]: {
      title: 'Referral Code',
      cardBg: require('@/assets/images/referral-code-bg.png'),
      icon: <TagUser variant="Bold" width={22} height={2} color={colors?.white} />,
      iconBg: 'bg-primary-main',
    },
  };

  return (
    <ImageBackground
      className={`px-15 rounded-[15px] overflow-hidden items-start py-20`}
      source={variantProperty[variant]?.cardBg}
      style={{ width: wp(305) }}>
      <Row className="items-start">
        <Row className={`p-10 rounded-full items-center justify-center ${variantProperty[variant]?.iconBg}`}>
          {variantProperty[variant]?.icon}
        </Row>
        <View className={'flex-1 ml-10'}>
          <BaseText fontSize={12} classes={'text-xs leading-4 text-black-main'}>
            {variantProperty[variant]?.title}
          </BaseText>
          <Row className="flex-1 justify-start">
            <Text className={'text-2xl font-fhOscarBold text-black-main'}>NGN 20,000.00</Text>
            {variant === CreditInfoCardVariants.REFERRAL_CARD && (
              <TouchableOpacity className="rounded-[5px] p-5 bg-white ml-5">
                <Copy variant="Outline" size={wp(5)} color={colors.primary.main} />
              </TouchableOpacity>
            )}
          </Row>
          <TouchableOpacity className="self-start rounded-[5px] mt-10 py-8 px-12 bg-white">
            <Row>
              <Text className="text-xs font-interMedium text-primary-main mr-4">What is Catlog Credits?</Text>
              <ArrowUp primaryColor={colors.primary.main} />
            </Row>
          </TouchableOpacity>
        </View>
      </Row>
    </ImageBackground>
  );
};

export default CreditInfoCard;
