import React, { useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import PaymentBalanceCards, { BalanceCardItem } from '../payments/balance-cards';
import { Copy, TagUser, WalletMoney } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import WhiteCardButton from '../ui/buttons/white-card-btn';
import { ArrowUpRight } from '../ui/icons';
import { copyToClipboard, hp, toCurrency, toNaira, wp } from '@/assets/utils/js';
import { ApiData, useApi } from 'src/hooks/use-api';
import useAuthContext from 'src/contexts/auth/auth-context';
import Pressable from '../ui/base/pressable';
import { GetReferralResponse } from 'src/screens/catlog-credits';
import { GET_STORE_CREDITS, GET_STORE_REFERRALS } from 'catlog-shared';
import useWalletContext from 'src/contexts/wallet/wallet-context';

interface CatlogCreditsInfoCardsProps {
  referralInfo: GetReferralResponse['data'];
  onPressWithdraw: VoidFunction;
  onPressFriends: VoidFunction;
}

const CatlogCreditsInfoCards = ({
  onPressWithdraw,
  onPressFriends,
  referralInfo,
}: CatlogCreditsInfoCardsProps) => {
  // const catlogCreditRequest = useApi({
  //   key: 'get-store-credit',
  //   apiFunction: GET_STORE_CREDITS,
  //   method: 'GET',
  // });

  const { catlogCredits: wallet } = useWalletContext();
  

  const cards: BalanceCardItem[] = [
    {
      title: 'Credit Balance',
      icon: <WalletMoney variant="Bold" size={22} color={colors?.white} />,
      iconBg: colors.accentOrange.main,
      getValue: () => toCurrency(toNaira(wallet?.balance), wallet?.currency),
      bottomAddon: (
        <WhiteCardButton
          icon={<ArrowUpRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />}
          onPress={onPressWithdraw}
          className="mt-12">
          Withdraw Credits
        </WhiteCardButton>
      ),
      cardBg: require('@/assets/images/credit-balance-bg.png'),
    },
    {
      title: 'Referral Code',
      icon: <TagUser variant="Bold" size={22} color={colors?.white} />,
      iconBg: colors.primary.main,
      getValue: () => referralInfo?.referral_code?.toUpperCase(),
      bottomAddon: (
        <WhiteCardButton
          icon={<ArrowUpRight size={wp(15)} strokeWidth={1.5} currentColor={colors.primary.main} />}
          onPress={onPressFriends}
          className="mt-12">
          {referralInfo?.referrals?.length} Friends Invited
        </WhiteCardButton>
      ),
      cardBg: require('@/assets/images/referral-code-bg.png'),
      valueAddon: (
        <Pressable
          className="rounded-[5px] p-5 bg-white ml-5"
          onPress={() => copyToClipboard(referralInfo?.referral_code?.toUpperCase())}>
          <Copy variant="Linear" size={wp(16)} color={colors.primary.main} />
        </Pressable>
      ),
    },
  ];

  return (
    <View className="pt-25">
      <PaymentBalanceCards cards={cards} dotsColor={colors.accentOrange.main} />
    </View>
  );
};

export default CatlogCreditsInfoCards;
