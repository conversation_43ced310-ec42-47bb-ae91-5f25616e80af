import { View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import LeftLabelledInput from '../ui/inputs/left-labelled-input';
import useSteps from '@/hooks/use-steps';
import { BaseText, CircledIcon, Row } from '../ui';
import { TickCircle } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import { getFieldvalues, hp, showError, toCurrency, toKobo, toNaira, wp } from 'src/assets/utils/js';
import { useFormik } from 'formik';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useEffect, useState } from 'react';
import { CONVERT_CREDITS_TO_CASH } from 'catlog-shared';
import { useApi } from 'src/hooks/use-api';
import * as Animatable from 'react-native-animatable';
import Input from '../ui/inputs/input';

interface WithdrawCreditModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
}

const WithdrawCreditModal = ({ closeModal, ...props }: WithdrawCreditModalProps) => {
  const { catlogCredits: wallet, updateBalance } = useWalletContext();
  const [error, setError] = useState<string | null>(null);

  const { stores, getRewards } = useAuthContext();

  const minCashoutAmount = getRewards()?.min_cash_out ?? 5000_00;
  const cashoutDisabled = wallet.balance < minCashoutAmount;

  const creditsToCashReq = useApi({
    apiFunction: CONVERT_CREDITS_TO_CASH,
    key: CONVERT_CREDITS_TO_CASH.name,
    method: 'POST',
  });

  const { step, next, changeStep } = useSteps(['amount', 'success'], 0);

  const form = useFormik({
    initialValues: {
      amount: 0,
    },
    onSubmit: async values => {
      if (toKobo(values.amount) < minCashoutAmount) {
        setError(`You'll need at least ${toCurrency(toNaira(minCashoutAmount), wallet.currency)} to convert to cash`);
        return;
      }

      const [res, err] = await creditsToCashReq.makeRequest({ amount: values.amount });

      if (err) {
        showError(err);
        setError(err?.message);
        return;
      }

      next();
      updateBalance({
        walletId: 'catlogCredit',
        type: 'debit',
        amount: toKobo(values.amount),
      });
    },
  });

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'Convert Credits to Cash'}
      showButton={true}
      useScrollView
      enableDynamicSizing
      onModalHide={() => {
        form.resetForm();
        changeStep('amount');
      }}
      enableSnapPoints={false}
      buttons={
        step === 'amount'
          ? [
              {
                text: 'Continue',
                onPress: () => form.submitForm(),
                disabled: cashoutDisabled,
                isLoading: creditsToCashReq.isLoading,
              },
            ]
          : [{ text: 'Done', onPress: closeModal, disabled: cashoutDisabled }]
      }>
      <View className="px-20 pb-80 mt-15">
        <View className="mt-15" style={{ display: step === 'amount' ? 'flex' : 'none' }}>
          {(cashoutDisabled || error) && (
            <View className="bg-accentRed-pastel rounded-8 py-10 px-10 mb-15">
              <BaseText fontSize={11} classes="text-accentRed-main text-center">
                {cashoutDisabled
                  ? `You'll need at least ${toCurrency(toNaira(minCashoutAmount), wallet.currency)} to convert to cash`
                  : error}
              </BaseText>
            </View>
          )}
          <LeftLabelledInput
            leftText={wallet.currency}
            label="Amount to convert"
            useBottomSheetInput
            keyboardType="number-pad"
            {...getFieldvalues('amount', form)}
          />
          <View className="bg-grey-bgOne rounded-full py-6 px-10 mt-8 self-start">
            <BaseText fontSize={11} classes="text-black-placeholder">
              Balance:{' '}
              <BaseText fontSize={11} weight="medium">
                {toCurrency(toNaira(wallet.balance), wallet.currency)}
              </BaseText>
            </BaseText>
          </View>
          {Boolean(form.values?.amount) && (
            <Animatable.View animation={'fadeIn'} className="bg-grey-bgTwo rounded-12 py-16 px-14 mt-28">
              <Row>
                <BaseText classes="text-black-muted" fontSize={11} weight="medium">
                  You receive in wallet
                </BaseText>
                <BaseText classes="text-black-secondary" fontSize={11} weight="medium">
                  {toCurrency(form.values.amount / 2, wallet.currency)}
                </BaseText>
              </Row>
            </Animatable.View>
          )}
        </View>
        {step === 'success' && (
          <View
            className="items-center justify-center"
            style={{ display: step === 'success' ? 'flex' : 'none', height: hp(200) }}>
            <View className="items-center">
              <Animatable.View className="bg-accentGreen-pastel2 p-10 rounded-full" animation={'zoomIn'} duration={300}>
                <Animatable.View animation={'zoomIn'} delay={75} duration={200}>
                  <CircledIcon className="p-24 bg-accentGreen-main">
                    <Animatable.View animation={'zoomIn'} duration={300} delay={150}>
                      <TickCircle variant="Bold" color={colors.white} size={wp(30)} />
                    </Animatable.View>
                  </CircledIcon>
                </Animatable.View>
              </Animatable.View>
              <BaseText type="heading" weight="light" fontSize={20} classes="text-center text-black-muted mt-12">
                <BaseText type="heading" fontSize={20} classes="text-center text-black-main">
                  {toCurrency(form.values.amount / 2, wallet.currency)}
                  {`\n`}
                </BaseText>
                has been converted to cash
              </BaseText>
            </View>
          </View>
        )}
      </View>
    </BottomModal>
  );
};

export default WithdrawCreditModal;
