import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import SectionContainer from '../ui/section-container';
import { BaseText, Row } from '../ui';
import { hp, wp } from 'src/assets/utils/js';
import { TickCircle } from 'node_modules/iconsax-react-native/src';
import colors from 'src/theme/colors';
import CustomImage from '../ui/others/custom-image';
import { useFeatureFlags } from 'src/contexts/feature-flags/use-feature-flags';

interface CatlogCreditInformationModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
}

const CatlogCreditInformationModal = ({ closeModal, ...props }: CatlogCreditInformationModalProps) => {
  const navigation = useNavigation();
  const { isFeatureEnabled } = useFeatureFlags();

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      title={'What is Catlog credits'}
      showButton={true}
      // modalStyle={{ flex: 1 }}
      // containerStyle={{ flex: 1 }}
      // innerStyle={{ flex: 1 }}
      buttons={[{ text: 'Got it', onPress: closeModal }]}>
      <View
        // style={{ flex: 1 }}
        className="px-20 pb-40">
        <CustomImage
          imageProps={{ source: require('@/assets/images/coin-red.png') }}
          className="w-100 h-[96px] bg-transparentWhite"
        />
        <BaseText type="heading" fontSize={16} classes="mt-20">
          Get more from Catlog with Catlog Credits
        </BaseText>
        <View className="mt-16">
          {creditInfo(isFeatureEnabled?.('subscriptions') ?? false)?.map(i => (
            <SectionContainer className="p-16" key={i.title}>
              <BaseText type="heading" fontSize={14}>
                {i?.title}
              </BaseText>
              <View className="mt-12" style={{ rowGap: hp(12) }}>
                {i?.points.map(p => (
                  <Row key={p} className="justify-start items-start">
                    <TickCircle variant="Bold" color={colors.accentGreen.main} size={wp(12)} />
                    <BaseText fontSize={12} className="text-black-muted ml-6">
                      {p}
                    </BaseText>
                  </Row>
                ))}
              </View>
            </SectionContainer>
          ))}
        </View>
      </View>
    </BottomModal>
  );
};

export default CatlogCreditInformationModal;

const creditInfo = (isSubscriptionEnabled: boolean = false) => [
  {
    title: 'How to earn credits',
    points: [
      'Share your referral link with friends to invite them',
      'You earn NGN 1000 when your friend subscribes',
      'You can also earn credits by collecting payments with Catlog',
    ],
  },
  ...(isSubscriptionEnabled ? [
    {
      title: 'How do I spend my credits',
      points: [
        'You can pay your subscription with credits',
        'You can pay to book deliveries with credits',
        'You can withdraw your credits as cash',
      ],
    },
  ] : []),
];
