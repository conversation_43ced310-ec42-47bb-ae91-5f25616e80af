import React from 'react';
import { Dimensions } from 'react-native';
import { hp, wp } from '@/assets/utils/js';
import cx from 'classnames';
import Shimmer from '../ui/shimmer';
import { View } from 'react-native';

const { width } = Dimensions.get('window');
//determine card width by subtracting the page padding and gap from widow width
const cardWidth = (width - 40 - 20) / 2;

const dummyRow = new Array(2).fill(0);

interface StorefrontSkeletonLoaderProps {}

const HomeAnalyticsSkeletonLoader: React.FC<StorefrontSkeletonLoaderProps> = () => {
  return (
    <View className='flex-1'>
      {dummyRow.map((_, index) => (
        <View className={cx('flex-row justify-between', { 'mt-0': index === 0, 'mt-15 mb-15': index !== 0 })} style={{gap: wp(18)}} key={index}>
            
            <View className="flex-1 p-15 bg-grey-bgTwo rounded-12">
              <Shimmer  borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
              <Shimmer  borderRadius={hp(40)} height={hp(10)} width={wp(90)} marginTop={hp(10)} />
              <Shimmer  borderRadius={hp(40)} height={hp(15)} width={wp(30)} marginTop={hp(10)} />
            </View>
            <View className="flex-1 p-15 bg-grey-bgTwo rounded-12">
              <Shimmer  borderRadius={hp(40)} height={hp(40)} width={hp(40)} />
              <Shimmer  borderRadius={hp(40)} height={hp(10)} width={wp(90)} marginTop={hp(10)} />
              <Shimmer  borderRadius={hp(40)} height={hp(15)} width={wp(30)} marginTop={hp(10)} />
            </View>
        </View>
      ))}
    </View>
  );
};

export default HomeAnalyticsSkeletonLoader;
