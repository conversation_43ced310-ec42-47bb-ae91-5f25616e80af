import { View } from 'react-native';
import { ArrowRight2, Bag, Co<PERSON>, More, Profile2User, Shop } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { hp, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, SelectionPill, WhiteCardBtn } from '@/components/ui';
import { useState } from 'react';
import SectionContainer from '@/components/ui/section-container';
import { ApiData, ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { OrdersResponse } from 'src/screens/orders/list';
import ProductCard from '../products/storefront-products/product-card';
import Pressable from '../ui/base/pressable';
import { useNavigation } from '@react-navigation/native';
import { OrderListCard } from '../orders/list/order-item-card';
import Separator from '../ui/others/separator';
import {
  GetOrdersParams,
  GET_ORDERS,
  GET_STORE_TOP_PRODUCTS,
  GetStoreTopProductsParams,
  ProductItemInterface,
} from 'catlog-shared';
import InfoBadge from '../store-settings/info-badge';
import StatusPill from '../ui/others/status-pill';
import { orderStatusPillTypeMap } from 'src/screens/orders/order-info';

enum SelectionPillType {
  LATEST_ORDERS = 'Latest Orders',
  TOP_PRODUCTS = 'Top Products',
}
export interface TopProductResponse extends ResponseWithoutPagination<ProductItemInterface[]> {}
interface DashboardOrdersAndTopProductsProps {
  getLatestOrdersReq: ApiData<GetOrdersParams, OrdersResponse>;
  getTopProductReq: ApiData<GetStoreTopProductsParams, TopProductResponse>;
}

const DashboardOrdersAndTopProducts = ({
  getLatestOrdersReq,
  getTopProductReq,
}: DashboardOrdersAndTopProductsProps) => {
  const [selectedPill, setSelectedPill] = useState<SelectionPillType>(SelectionPillType.LATEST_ORDERS);

  const navigation = useNavigation();

  const topProduct = getTopProductReq?.response?.data ?? [];
  const latestOrders = getLatestOrdersReq?.response?.data?.data ?? [];

  return (
    <View className="mt-5">
      <View className="flex-row py-20">
        <SelectionPill
          onPress={() => setSelectedPill(SelectionPillType.LATEST_ORDERS)}
          selected={selectedPill === SelectionPillType.LATEST_ORDERS}
          title={SelectionPillType.LATEST_ORDERS}
        />
        <SelectionPill
          onPress={() => setSelectedPill(SelectionPillType.TOP_PRODUCTS)}
          selected={selectedPill === SelectionPillType.TOP_PRODUCTS}
          title={SelectionPillType.TOP_PRODUCTS}
        />
      </View>
      {selectedPill === SelectionPillType.LATEST_ORDERS && (
        <View>
          <SectionContainer
            isEmpty={latestOrders.length < 1 || getLatestOrdersReq.isLoading}
            className="py-12"
            emptyStateProps={{
              text: 'No Orders to show',
              icon: (
                <CircledIcon className="bg-white p-15 mb-12">
                  <Bag variant={'Bulk'} color={colors.grey.muted} size={wp(30)} />
                </CircledIcon>
              ),
            }}
            classes="mt-0">
            {latestOrders.map((item, index) => (
              <View key={item.id}>
                <OrderListCard
                  order={item}
                  disabled={false}
                  onPress={() => navigation.navigate('OrderInfo', { id: item.id })}
                  rightAddon={
                    <StatusPill
                      title={item?.status}
                      className="bg-white"
                      statusType={orderStatusPillTypeMap[item?.status]}
                    />
                  }
                />
                {index !== latestOrders.length - 1 && <Separator className="mx-0" />}
              </View>
            ))}
          </SectionContainer>
          <WhiteCardBtn
            className="bg-grey-bgOne rounded-full mt-10 self-end"
            onPress={() => navigation.navigate('Orders')}
            icon={<ArrowRight2 size={wp(14)} color={colors.primary.main} />}>
            See all
          </WhiteCardBtn>
        </View>
      )}
      {selectedPill === SelectionPillType.TOP_PRODUCTS && (
        <View>
          <SectionContainer
            isEmpty={topProduct.length < 1 || getTopProductReq.isLoading}
            emptyStateProps={{
              text: 'No Orders to show',
              icon: (
                <CircledIcon className="bg-white p-15 mb-12">
                  <Bag variant={'Bulk'} color={colors.grey.muted} size={wp(30)} />
                </CircledIcon>
              ),
            }}
            classes="mt-0">
            {topProduct.slice(0, 5).map((item, index) => (
              <View key={item.id}>
                <ProductCard
                  key={item.id}
                  listView
                  product={item}
                  onPress={() => navigation.navigate('ProductDetails', { id: item.slug! })}
                  rightElement={
                    <BaseText classes="text-black-main opacity-50" fontSize={12}>
                      {item.total_orders} Orders
                    </BaseText>
                  }
                />
                {index !== 4 && <Separator className="mx-0 my-0" />}
              </View>
            ))}
          </SectionContainer>
          <WhiteCardBtn
            className="bg-grey-bgOne rounded-full mt-10 self-end"
            onPress={() => navigation.navigate('Products')}
            icon={<ArrowRight2 size={wp(14)} color={colors.primary.main} />}>
            See all
          </WhiteCardBtn>
        </View>
      )}
    </View>
  );
};

export default DashboardOrdersAndTopProducts;
