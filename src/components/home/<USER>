import { Gift, InfoCircle, Profile, RefreshCircle } from 'iconsax-react-native/src';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import colors from '@/theme/colors';
import { BaseText, CircledIcon } from '../ui';
import { hideLoader, showLoader, wp } from '@/assets/utils/js';
import { ArrowRight } from '../ui/icons';
import CircularProgress from 'react-native-circular-progress-indicator';
import { PAYMENT_TYPES, PLAN_TYPE, StoreInterface, User } from 'catlog-shared';
import { getAccountSetupSteps } from 'src/screens/get-started/get-started';
import useWalletContext from 'src/contexts/wallet/wallet-context';
import PaymentsWidget from '../payments/payments-widget';
import useModals from 'src/hooks/use-modals';

export interface HomeNotificationCardProps {
  user?: User;
  store?: StoreInterface;
  refetchSession: () => Promise<boolean>;
}

enum HomeNotificationCardVariant {
  'SUBSCRIPTION' = 'Subscription',
  'INACTIVE' = 'Inactive',
  'GET_STARTED' = 'Get Started',
}

const HomeNotificationCard = ({ store, user, refetchSession }: HomeNotificationCardProps) => {
  const navigation = useNavigation();
  const { wallets } = useWalletContext();
  const { modals, toggleModal } = useModals(['payment']);

  const [variant, setVariant] = useState(HomeNotificationCardVariant.GET_STARTED);

  const accountSetupSteps = useMemo(() => getAccountSetupSteps(store!, user!, wallets), [store, user]);
  const allTasks = accountSetupSteps.flatMap(step => step.tasks);
  const completedTaskCount = allTasks.filter(task => task.isCompleted).length;

  const setupCompletion = useMemo(() => {
    return (completedTaskCount / allTasks.length) * 100;
  }, [store]);

  useEffect(() => {
    const subscription = store?.subscription;
    if (subscription) {
      if (subscription.status === 'INACTIVE' && subscription.plan.type !== PLAN_TYPE.STARTER) {
        setVariant(HomeNotificationCardVariant.SUBSCRIPTION);
      } else if (subscription.plan.type === PLAN_TYPE.STARTER) {
        setVariant(HomeNotificationCardVariant.INACTIVE);
      }
    }
  }, [store]);

  const handlePaymentSuccess = async () => {
    toggleModal('payment', false);
    showLoader('Updating Account...');
    await refetchSession();
    hideLoader();
  };

  const variantProperty: {
    [key: string]: { leftElement: ReactNode; title: string; description: string; onClick?: () => void };
  } = {
    [HomeNotificationCardVariant.GET_STARTED]: {
      leftElement: (
        <View>
          <CircularProgress
            value={Math.ceil(setupCompletion)}
            radius={wp(17.5)}
            duration={500}
            delay={600}
            activeStrokeWidth={wp(6)}
            inActiveStrokeWidth={wp(6)}
            strokeLinecap={'round'}
            activeStrokeColor={colors.accentGreen.main}
            inActiveStrokeColor={colors.white}
            maxValue={100}
            valueSuffix={'%'}
            progressValueStyle={{ fontSize: wp(8), fontFamily: 'Inter-Bold', color: colors.black.muted }}
          />
        </View>
      ),
      title: 'Get started',
      description: 'Finish setting up & learn more about catlog ',
      onClick: () => navigation.navigate('GetStarted'),
    },
    [HomeNotificationCardVariant.INACTIVE]: {
      leftElement: (
        <CircledIcon iconBg="bg-accentRed-main">
          <InfoCircle size={wp(16)} color={colors.white} variant="Bold" />
        </CircledIcon>
      ),
      title: 'Account Inactive',
      description: 'Your subscription has expired, please renew to continue using catlog',
      onClick: () => navigation.navigate('ChangePlan'),
    },
    [HomeNotificationCardVariant.SUBSCRIPTION]: {
      leftElement: (
        <CircledIcon iconBg="bg-accentOrange-main">
          <RefreshCircle size={wp(16)} color={colors.white} variant="Bold" />
        </CircledIcon>
      ),
      title: 'Renew Subscription',
      description: `Your subscription to the ${store?.subscription?.plan?.name} plan has expired`,
      onClick: () => toggleModal('payment', true),
    },
  };

  if (setupCompletion >= 100 && variant === HomeNotificationCardVariant.GET_STARTED) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        className="flex-row items-center p-20 border-b border-b-grey-border bg-primary-pastel flex"
        activeOpacity={0.8}
        onPress={variantProperty[variant]?.onClick}>
        {variantProperty[variant].leftElement}
        <View className={'flex-1 mx-10'}>
          <BaseText fontSize={15} type="heading" classes={'flex-1 text-black-primary'} numberOfLines={1}>
            {variantProperty[variant].title}
          </BaseText>
          <BaseText fontSize={11} classes={'text-black-secondary'} numberOfLines={1}>
            {variantProperty[variant].description}
          </BaseText>
        </View>
        <CircledIcon iconBg="bg-white">
          <ArrowRight currentColor={colors.primary.main} width={wp(16)} height={wp(16)} strokeWidth={2} />
        </CircledIcon>
      </TouchableOpacity>
      {variant === HomeNotificationCardVariant.SUBSCRIPTION && (
        <PaymentsWidget
          data={{
            paymentType: PAYMENT_TYPES.SUBSCRIPTION,
            plan: {
              ...store?.subscription?.plan,
              plan_option_id: store?.subscription?.plan_option_id ?? store?.subscription?.plan_option?.id,
            },
            upfrontSubscription: false,
          }}
          onComplete={handlePaymentSuccess}
          show={modals.payment}
          toggle={() => toggleModal('payment', false)}
        />
      )}
    </>
  );
};

export default HomeNotificationCard;
