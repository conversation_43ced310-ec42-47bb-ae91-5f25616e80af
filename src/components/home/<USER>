import { Activity, BagHappy, Chart, ExportCircle, Moneys, Notification } from 'iconsax-react-native/src';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '@/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { ReactNode, useMemo } from 'react';
import Row from '../ui/row';
import { ArrowUp, ArrowUpRight } from '@/components/ui/icons';
import BaseText from '../ui/base/base-text';
import { wp } from '@/assets/utils/js';

export interface StoreSummaryCardProps {
  variant?: SummaryCardVariants;
  onPress?: () => void;
}

export enum SummaryCardVariants {
  'STORE_VISIT' = 'store_visits',
  'TOTAL_ORDERS' = 'total_orders',
  'PAYMENTS' = 'payments',
  'CREDIT_BALANCE' = 'credit_balance',
}

const { width } = Dimensions.get('window');

const StoreSummaryCard = ({ variant = SummaryCardVariants.STORE_VISIT, onPress }: StoreSummaryCardProps) => {
  const navigation = useNavigation();

  const variantProperty: { [key: string]: { title: string; cardBg: string; icon: ReactNode; iconBg: string } } = {
    [SummaryCardVariants.STORE_VISIT]: {
      title: 'Store Visits',
      cardBg: 'bg-accentRed-pastel',
      icon: <Chart variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentRed-main',
    },
    [SummaryCardVariants.TOTAL_ORDERS]: {
      title: 'Total Orders',
      cardBg: 'bg-accentYellow-pastel',
      icon: <BagHappy variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentYellow-main',
    },
    [SummaryCardVariants.PAYMENTS]: {
      title: 'Payments Received',
      cardBg: 'bg-accentGreen-pastel',
      icon: <Moneys variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentGreen-main',
    },
    [SummaryCardVariants.CREDIT_BALANCE]: {
      title: 'Credit Balance',
      cardBg: 'bg-accentOrange-pastel',
      icon: <Activity variant="Bold" size={wp(18)} color={colors?.white} />,
      iconBg: 'bg-accentOrange-main',
    },
  };

  return (
    <TouchableOpacity
      className={`px-15 rounded-[15px] items-start py-15 ${variantProperty[variant]?.cardBg}`}
      style={{ width: (width - 40) / 2 - 7.5 }}
      disabled={!onPress}
      onPress={onPress}
      activeOpacity={0.8}>
      <Row className="w-full items-start">
        <Row className={`p-10 rounded-full items-center justify-center ${variantProperty[variant]?.iconBg}`}>
          {variantProperty[variant]?.icon}
        </Row>
        {variant === SummaryCardVariants.CREDIT_BALANCE && (
          <ExportCircle variant="Bold" size={wp(18)} color={colors.accentOrange.light} />
        )}
      </Row>
      <View className={'mt-10'}>
        <BaseText fontSize={12} classes={'text-xs leading-4 font-interRegular text-black-muted'}>
          {variantProperty[variant]?.title}
        </BaseText>
        <Row className="justify-start mt-4">
          <BaseText fontSize={18} classes={'text-black-muted'} type="heading">
            0
          </BaseText>
          <Row className={'flex flex-row items-center justify-center ml-4 rounded-3xl bg-white py-3 px-5'}>
            <Text className={'text-xs leading-none text-accentGreen-main font-interSemiBold'}>0%</Text>
            <ArrowUpRight currentColor={colors.accentGreen.main} size={wp(14)} strokeWidth={2} />
          </Row>
        </Row>
      </View>
    </TouchableOpacity>
  );
};

export default StoreSummaryCard;
