import { View } from 'react-native';
import { ArrowRight2, Bag, <PERSON><PERSON>, Layer, More, Profile2User, Shop } from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { cx, getDummyArray, hp, wp } from '@/assets/utils/js';
import { BaseText, CircledIcon, Row, SelectionPill, WhiteCardBtn } from '@/components/ui';
import SectionContainer from '@/components/ui/section-container';
import ListItemCard from '../ui/cards/list-item-card';
import CustomImage from '../ui/others/custom-image';
import { ArrowRight } from '../ui/icons';
import { useNavigation } from '@react-navigation/native';
import { ApiData, ResponseWithoutPagination, useApi } from 'src/hooks/use-api';
import { GET_PENDING_ACTIONS, ORDER_PAID_TAG, ORDER_STATUSES, OrderInterface } from 'catlog-shared';
import { useEffect, useState } from 'react';
import { StorePendingActions } from 'catlog-shared';
import Shimmer from 'src/components/ui/shimmer';
import Separator from '../ui/others/separator';

interface PendingActionSectionProps {}

interface PendingActionSectionProps {
  pendingActionRequest: ApiData<void, ResponseWithoutPagination<StorePendingActions>>;
}

const PendingActionSection = ({ pendingActionRequest }: PendingActionSectionProps) => {
  const [pendingActionData, setPendingActionData] = useState<StorePendingActions | null>(null);
  const [pendingActions, setPendingActions] = useState([]);
  const navigation = useNavigation();

  const navigate = (type: PENDING_ACTION_TYPE) => {
    if (!pendingActionData || pendingActionData === null) return;
    switch (type) {
      case PENDING_ACTION_TYPE.PRODUCT_OUT_OF_STOCK:
        navigation.navigate('QuantityUpdate', {
          updateType: 'outOfStock',
          lowStockProducts: pendingActionData?.items_out_of_stock.map(i => i.id),
        });
        break;
      case PENDING_ACTION_TYPE.LOW_STOCK:
        navigation.navigate('QuantityUpdate', {
          updateType: 'lowStock',
          lowStockProducts: pendingActionData?.low_stock_items.map(i => i.id),
        });
        break;
      case PENDING_ACTION_TYPE.UNPAID_ORDERS:
        navigation.navigate('BulkUpdateOrders', {
          status: ORDER_STATUSES.PROCESSING,
          payment_status: ORDER_PAID_TAG.UNPAID,
          pageCount: pendingActionData.orders_unpaid.count,
        });
        break;
      case PENDING_ACTION_TYPE.PENDING_ORDERS:
        navigation.navigate('BulkUpdateOrders', {
          status: ORDER_STATUSES.PENDING,
          pageCount: pendingActionData.orders_pending.count,
        });
        break;
      case PENDING_ACTION_TYPE.PROCESSING_ORDERS:
        navigation.navigate('BulkUpdateOrders', {
          status: ORDER_STATUSES.PROCESSING,
          pageCount: pendingActionData.orders_processing.count,
        });
        break;
    }
    // navigation.navigate('BulkUpdateOrders');
    // navigation.navigate('QuantityUpdate', { isLowStockUpdate: true, lowStockProducts: [] });
  };

  useEffect(() => {
    if (pendingActionRequest.response) {
      const data = pendingActionRequest.response.data as StorePendingActions;

      setPendingActionData(data);
      setPendingActions(
        [
          {
            type: PENDING_ACTION_TYPE.PRODUCT_OUT_OF_STOCK,
            count: data.summary.total_out_of_stock,
            description:
              data.summary.total_out_of_stock > 1
                ? `${data.summary.total_out_of_stock} Items/Options are Out of Stock`
                : `${data.summary.total_out_of_stock} Item/Option is Out of Stock`,
            image: data.items_out_of_stock[0]?.images[0],
          },
          {
            type: PENDING_ACTION_TYPE.LOW_STOCK,
            count: data.summary.total_low_stock_items,
            description:
              data.summary.total_low_stock_items > 1
                ? `${data.summary.total_low_stock_items} Items/Options have Low Stocks`
                : `${data.summary.total_low_stock_items} Item/Option has Low Stocks`,
            image: data.low_stock_items[0]?.images[0],
          },
          {
            type: PENDING_ACTION_TYPE.UNPAID_ORDERS,
            count: data.orders_unpaid.count,
            description:
              data.orders_unpaid.count > 1
                ? `${data.orders_unpaid.count} Orders are marked as Unpaid`
                : `${data.orders_unpaid.count} Order is marked as Unpaid`,
            image: data.orders_unpaid.image,
          },
          {
            type: PENDING_ACTION_TYPE.PROCESSING_ORDERS,
            count: data.orders_processing.count,
            description:
              data.orders_processing.count > 1
                ? `${data.orders_processing.count} Orders are Still Processing`
                : `${data.orders_processing.count} Order is Still Processing`,
            image: data.orders_processing.image,
          },

          {
            type: PENDING_ACTION_TYPE.PENDING_ORDERS,
            count: data.orders_pending.count,
            description:
              data.orders_pending.count > 1
                ? `${data.orders_pending.count} Orders are Pending`
                : `${data.orders_pending.count} Order is Pending`,
            image: data.orders_pending.image,
          },
        ].filter(a => a.count > 0),
      );
    }
  }, [pendingActionRequest.response]);

  useEffect(() => {
    pendingActionRequest.makeRequest();
  }, []);

  return (
    <View className="mt-20">
      <BaseText fontSize={15} weight={'bold'} type={'heading'}>
        Pending Actions
      </BaseText>

      {pendingActionRequest.isLoading ? (
        <SectionContainer isEmpty={false} className="px-12 mt-15" classes="mt-0">
          {getDummyArray(5).map((_, index) => (
            <ListItemCardSkeleton key={index} showBorder={index < 4} />
          ))}
        </SectionContainer>
      ) : (
        <SectionContainer
          isEmpty={pendingActions?.length == 0}
          className="px-12 mt-15"
          emptyStateProps={{
            text: 'No Pending actions',
            icon: (
              <CircledIcon className="bg-white p-10 mb-12">
                <Layer variant={'Bulk'} color={colors.grey.muted} size={wp(20)} />
              </CircledIcon>
            ),
            className: 'py-100',
          }}
          classes="mt-0">
          {pendingActions.map((item, index) => (
            <View key={index}>
              <ListItemCard
                description={item.description ?? ''}
                descriptionProps={{ fontSize: 12 }}
                leftElement={<MultipleImages image={item.image} count={item.count} />}
                onPress={() => navigate(item.type)}
                rightElement={
                  <CircledIcon>
                    <ArrowRight size={wp(15)} strokeWidth={2} currentColor={colors.primary.main} />
                  </CircledIcon>
                }
              />
              {index !== pendingActions.length - 1 && <Separator className="mx-0 my-0" />}
            </View>
          ))}
        </SectionContainer>
      )}
    </View>
  );
};

export default PendingActionSection;

const MultipleImages = (props: { image: string; count: number }) => {
  return (
    <Row classes="rounded-30 bg-white p-3">
      {/* {props.images.map((img, index) => ( */}
      <CircledIcon className={cx('p-2 bg-white')}>
        <CustomImage
          className="w-32 h-32 rounded-full"
          imageProps={{ source: { uri: props.image }, contentFit: 'cover' }}
        />
      </CircledIcon>
      {props.count > 1 && (
        <CircledIcon className={cx('p-2 bg-primary-pastel', '-ml-15')}>
          <View className="w-32 h-32 bg-primary-pastel rounded-full flex align-center justify-center">
            <BaseText fontSize={10} classes="text-black-muted text-center" weight="semiBold">
              +{props.count - 1}
            </BaseText>
          </View>
        </CircledIcon>
      )}
      {/* ))} */}
    </Row>
  );
};

// Skeleton loader for ListItemCard component
const ListItemCardSkeleton = ({ showBorder = false }: { showBorder?: boolean }) => {
  return (
    <View className={`flex-row py-15 items-center ${showBorder && 'border-b border-b-grey-border'}`}>
      <Row>
        {Array.from({ length: 2 }, (_, index) => (
          <View key={index} className={cx('p-2 bg-white rounded-full', { '-ml-15': index !== 0 })}>
            <Shimmer width={wp(32)} height={wp(32)} borderRadius={wp(16)} />
          </View>
        ))}
      </Row>
      <View className="flex-1 ml-12">
        <Shimmer width={wp(200)} height={hp(12)} borderRadius={6} classes="mb-5" />
        <Shimmer width={wp(150)} height={hp(10)} borderRadius={6} />
      </View>
      <View className="p-8 bg-grey-bgOne rounded-full">
        <Shimmer width={wp(20)} height={wp(20)} borderRadius={wp(10)} />
      </View>
    </View>
  );
};

enum PENDING_ACTION_TYPE {
  PRODUCT_OUT_OF_STOCK = 'Product Out of Stock',
  LOW_STOCK = 'Low Stock',
  UNPAID_ORDERS = 'Unpaid Orders',
  PENDING_ORDERS = 'Pending Orders',
  PROCESSING_ORDERS = 'Processing Orders',
}
