import BottomModal, { BottomModalProps } from '@/components/ui/modals/bottom-modal';
import { Dimensions, Image, Modal, Platform, ScrollView, Text, View } from 'react-native';
import { shareAsync } from 'expo-sharing';
import { CloseCircle, ExportSquare } from 'iconsax-react-native/src';
import React, { useRef } from 'react';
import ViewShot from 'react-native-view-shot';
import { cx, wp } from 'src/assets/utils/js';
import BaseText from 'src/components/ui/base/base-text';
import FixedBtnFooter from 'src/components/ui/buttons/fixed-btn-footer';
import colors from 'src/theme/colors';
import Confetti from 'src/components/ui/confetti';
import SheetModal from '../ui/modals/sheet-modal';
import { Row } from '../ui';
import Pressable from '../ui/base/pressable';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  count: number;
}
const DownloadCardModal = ({ closeModal, count, ...props }: Props) => {
  const ref = useRef<ViewShot>();
  const share = () => {
    ref?.current?.capture()?.then(uri => {
      shareAsync(uri, { dialogTitle: 'Share Download Card' });
    });
  };

  const computedHeight = (Dimensions.get('window').width - wp(40)) * 1.778;

  return (
    <BottomModal
      title={'Share Card'}
      customSnapPoints={[95]}
      buttons={[
        {
          text: 'Share On Instagram',
          onPress: share,
          rightAddOn: (
            <View className="ml-5">
              <ExportSquare strokeWidth={2} size={wp(15)} color={colors.white} />
            </View>
          ),
        },
      ]}
      closeModal={closeModal}
      {...props}>
      {/* <Row className={cx('justify-between px-20 pt-15 pb-8 border-b border-b-grey-border')}>
        <View className="flex-1">
          <BaseText fontSize={15} type="heading" numberOfLines={2}>
            Share Card
          </BaseText>
        </View>
        <Pressable className="self-end" onPress={closeModal}>
          <Row className="rounded-full p-6 pr-12 bg-white border border-grey-border">
            <CloseCircle variant="Bold" size={18} color={colors.black.main} />
            <BaseText className="ml-4">Close</BaseText>
          </Row>
        </Pressable>
      </Row> */}
      <ScrollView className="flex-1">
        <View className="mx-20 mt-20">
          <BaseText type="body" fontSize={12} classes=" text-black-secondary">
            Share this card on your Instagram story and tag <Text className="font-interSemiBold">@catlogshop</Text> to
            join our ongoing giveaway!
          </BaseText>
        </View>

        <ViewShot
          style={{ margin: wp(20) }}
          ref={ref}
          options={{ fileName: 'Download Card', format: 'jpg', quality: 0.9 }}>
          <View className={cx(`items-center justify-start flex-col relative `)} style={{ height: computedHeight }}>
            <Image
              source={require('@/assets/images/download-card.png')}
              resizeMode={'contain'}
              className="w-full h-full"
            />
            <BaseText fontSize={wp(40)} classes="text-white absolute top-[48%]" type="heading">
              {count < 10 ? `0${count}` : count}
            </BaseText>
          </View>
        </ViewShot>
      </ScrollView>
      <Confetti />
    </BottomModal>
  );
};

// const DownloadCardModal = ({ closeModal, count, ...props }: Props) => {
//   const ref = useRef<ViewShot>();
//   const share = () => {
//     ref?.current?.capture()?.then(uri => {
//       shareAsync(uri, { dialogTitle: 'Share Download Card' });
//     });
//   };

//   const computedHeight = (Dimensions.get('window').width - wp(40)) * 1.778;

//   return (
//     <Modal
//       animationType="slide"
//       visible={props.isVisible}
//       title={'Share Card'}
//       presentationStyle={Platform.OS === 'android' ? 'overFullScreen' : 'pageSheet'}
//       onRequestClose={closeModal}
//       {...props}>
//       <Row className={cx('justify-between px-20 pt-15 pb-8 border-b border-b-grey-border')}>
//         <View className="flex-1">
//           <BaseText fontSize={15} type="heading" numberOfLines={2}>
//             Share Card
//           </BaseText>
//         </View>
//         <Pressable className="self-end" onPress={closeModal}>
//           <Row className="rounded-full p-6 pr-12 bg-white border border-grey-border">
//             <CloseCircle variant="Bold" size={18} color={colors.black.main} />
//             <BaseText className="ml-4">Close</BaseText>
//           </Row>
//         </Pressable>
//       </Row>
//       <ScrollView className="flex-1">
//         <View className="mx-20 mt-20">
//           <BaseText type="body" fontSize={12} classes=" text-black-secondary">
//             Share this card on your Instagram story and tag <Text className="font-interSemiBold">@catlogshop</Text> to
//             join our ongoing giveaway!
//           </BaseText>
//         </View>

//         <ViewShot
//           style={{ margin: wp(20) }}
//           ref={ref}
//           options={{ fileName: 'Download Card', format: 'jpg', quality: 0.9 }}>
//           <View className={cx(`items-center justify-start flex-col relative `)} style={{ height: computedHeight }}>
//             <Image
//               source={require('@/assets/images/download-card.png')}
//               resizeMode={'contain'}
//               className="w-full h-full"
//             />
//             <BaseText fontSize={wp(40)} classes="text-white absolute top-[48%]" type="heading">
//               {count < 10 ? `0${count}` : count}
//             </BaseText>
//           </View>
//         </ViewShot>
//       </ScrollView>
//       <Confetti />
//       <FixedBtnFooter
//         buttons={[
//           {
//             text: 'Share On Instagram',
//             onPress: share,
//             rightAddOn: (
//               <View className="ml-5">
//                 <ExportSquare strokeWidth={2} size={wp(15)} color={colors.white} />
//               </View>
//             ),
//           },
//         ]}
//       />
//     </Modal>
//   );
// };

export default DownloadCardModal;
