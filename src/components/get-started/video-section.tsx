import { Animated, Dimensions, FlatList, View } from 'react-native';
import { VideoCircle } from 'iconsax-react-native/src';
import Row from '@/components/ui/row';
import colors from '@/theme/colors';
import { BaseText, CircledIcon } from '@/components/ui';
import { cx, hexToRgba, hp, wp } from '@/assets/utils/js';
import { useCallback, useMemo, useState } from 'react';
import SectionContainer from '@/components/ui/section-container';
import YoutubePlayer, { PLAYER_STATES } from 'react-native-youtube-iframe';

const windowWidth = Dimensions.get('window').width;
const playerWidth = windowWidth - wp(40) - wp(30);
const playerHeight = (playerWidth * 9.2) / 16;

interface RenderCardProps {
  item: any;
  index: number;
}

const RenderCard = ({ item, index }: RenderCardProps) => {
  const [playing, setPlaying] = useState(false);

  const onStateChange = useCallback((state: PLAYER_STATES) => {
    if (state === 'ended') {
      setPlaying(false);
      console.log('video has finished playing!');
    }
  }, []);

  const togglePlaying = useCallback(() => {
    setPlaying(prev => !prev);
  }, []);

  return (
    <View
      style={{
        width: windowWidth - 40,
        paddingHorizontal: wp(15),
        paddingBottom: hp(15),
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <View className={`rounded-[15px] self-center overflow-hidden bg-white`} style={{ width: '100%' }} key={index}>
        <YoutubePlayer
          height={playerHeight}
          initialPlayerParams={{
            controls: true,
          }}
          play={playing}
          videoId={item.ytLink}
          onChangeState={onStateChange}
        />
      </View>
    </View>
  );
};

interface DotsProps {
  scrollX: Animated.Value;
  colors: { active: string; inactive: string };
  length: number;
}

const Dots = (props: DotsProps) => {
  const { scrollX, colors, length } = props;
  const stepPosition = Animated.divide(scrollX, playerWidth);

  return (
    <View className="flex-row items-center">
      {Array(length)
        .fill(null)
        .map((item, index) => {
          const backgroundColor = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [colors.inactive, colors.active, colors.inactive],
            extrapolate: 'clamp',
          });

          const height = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(4), hp(5), hp(4)],
            extrapolate: 'clamp',
          });

          const width = stepPosition.interpolate({
            inputRange: [index - 1, index, index + 1],
            outputRange: [hp(4), hp(15), hp(4)],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={`step-${index}`}
              className="mx-3 rounded-full"
              style={{ backgroundColor, height, width }}
            />
          );
        })}
    </View>
  );
};

const VideoSection = ({ hideIcon = false }: { hideIcon?: boolean }) => {
  const [carouselIndex, setCarouselIndex] = useState(0);
  const [scrollX] = useState(new Animated.Value(0));

  const onViewCallBack = useCallback((viewableItems: any) => {
    if (viewableItems?.viewableItems) {
      setCarouselIndex(viewableItems?.viewableItems[0]?.index);
    }
  }, []);

  const title = useMemo(() => {
    return ytVideos[carouselIndex].videoTitle;
  }, [carouselIndex]);

  return (
    <View>
      <View className="px-20 pt-24 pb-0">
        <Row className="justify-start">
          {!hideIcon && (
            <CircledIcon iconBg="" classes="p-3" style={{ backgroundColor: hexToRgba(colors.accentOrange.main, 0.1) }}>
              <VideoCircle variant={'Bulk'} size={wp(20)} color={colors.accentOrange.main} />
            </CircledIcon>
          )}
          <BaseText fontSize={14} weight={'bold'} type={'heading'} classes={cx({"ml-6": !hideIcon})}>
            Learn how it works on Catlog
          </BaseText>
        </Row>
      </View>
      <View className="px-20">
        <SectionContainer className="rounded-[20px] px-0 py-15">
          <View>
            <FlatList
              horizontal
              scrollEnabled
              showsHorizontalScrollIndicator={false}
              snapToAlignment={'center'}
              pagingEnabled
              // contentContainerStyle={{ flex: 1, paddingHorizontal: wp(15) }}
              onViewableItemsChanged={onViewCallBack}
              scrollEventThrottle={20}
              data={ytVideos}
              renderItem={props => <RenderCard {...props} />}
              keyExtractor={(item, index) => index.toString()}
              onScroll={Animated.event(
                [
                  {
                    nativeEvent: { contentOffset: { x: scrollX } },
                  },
                ],
                { useNativeDriver: false },
              )}
            />
            <View className="items-center pt-10">
              <Dots
                scrollX={scrollX}
                colors={{ active: colors.accentOrange.main, inactive: colors.grey.border }}
                length={ytVideos.length}
              />
            </View>
          </View>
          <View className="border-t border-t-grey-border p-15 mt-15">
            <BaseText fontSize={14} weight={'bold'} type={'heading'} classes="text-center">
              {title}
            </BaseText>
          </View>
        </SectionContainer>
      </View>
    </View>
  );
};

const ytVideos = [
  {
    ytLink: 'XxPwkbFvTk8',
    videoTitle: 'How customers make orders from your store',
  },
  {
    ytLink: 'moJr7jYJnf4',
    videoTitle: 'How to collect payments with Catlog',
  },
  {
    ytLink: 'Vjmt8IZMgb8',
    videoTitle: 'How to upload your products to catlog',
  },
  {
    ytLink: 'MAWJMK1UIek',
    videoTitle: 'How to manage your orders & customers',
  },
];

export default VideoSection;
