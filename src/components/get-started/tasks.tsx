import React, { useMemo, useRef, useState } from 'react';
import { Linking, TouchableOpacity, View } from 'react-native';
import Row from '../ui/row';
import { CheckActive, ChevronDown, ChevronUp, Tiktok, Twitter, X } from '../ui/icons';
import {
  copyToClipboard,
  getCommunityLink,
  hideLoader,
  openLinkInBrowser,
  showError,
  showLoader,
  toCurrency,
  toNaira,
  wp,
} from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText } from '../ui';
import CircledIcon from '../ui/circled-icon';
import { SetupTask } from '@/screens/get-started/get-started';
import Pressable from '../ui/base/pressable';
import { useApi } from 'src/hooks/use-api';
import { useNavigation } from '@react-navigation/native';
import useAuthContext from 'src/contexts/auth/auth-context';
import { SETUP_TYPE, VERIFICATION_TYPE } from 'src/@types/utils';
import { UPDATE_PROFILE, UPDATE_STORE_DETAILS, CountryInterface } from 'catlog-shared';
import AccordionGroup from '../ui/others/accordion/accordion-group';
import Accordion from '../ui/others/accordion';
import AccordionAnchor from '../ui/others/accordion/accordion-anchor';
import SelectDropdown, { DropDownItem, DropDownMethods } from '../ui/inputs/select-dropdown';
import { Gift, Instagram, TickCircle } from 'iconsax-react-native/src';
import { PaymentSettingsTabs, StoreConfigurationTabs, StoreInformationTabs } from '../store-settings/types';
import UploadLogoModal from './upload-logo-modal';
import useModals from 'src/hooks/use-modals';
import InfoBadge from '../store-settings/info-badge';

interface TasksProps {
  tasks: SetupTask[];
  storeId: string;
  storeCountry: CountryInterface;
  onboardingSteps: {};
  disabled?: boolean;
  openRewardModal?: VoidFunction;
}

const GetStartedTasks: React.FC<TasksProps> = props => {
  const { tasks, storeId, onboardingSteps, storeCountry } = props;
  const { user, store, updateStore, updateUser } = useAuthContext();
  const { modals, toggleModal } = useModals(['uploadLogo']);

  const navigation = useNavigation();

  const { storeLink } = useAuthContext();
  const dropDownRef = useRef<DropDownMethods>(null);

  const sortedTasks = useMemo(() => {
    return tasks.map(task => {
      return {
        ...task,
        tasks: task.tasks.sort((a, b) => (a.isCompleted ? 1 : -1)),
      };
    });
  }, [tasks]);

  const updateStoreRequest = useApi(
    {
      apiFunction: UPDATE_STORE_DETAILS,
      key: 'update-store-request',
      method: 'PUT',
    },
    { id: storeId },
  );

  const updateProfileRequest = useApi({
    apiFunction: UPDATE_PROFILE,
    key: 'update-profile-request',
    method: 'PUT',
  });

  const handleOnPressStep = async (type: SETUP_TYPE, isPrimaryAction: boolean) => {
    switch (type) {
      //Getting started
      case SETUP_TYPE.BOOK_ONBOARDING_CALL:
        if (isPrimaryAction) {
          openLinkInBrowser('https://calendly.com/silas-adedoyin/catlog-onboarding-session');
        } else {
          showLoader('Updating Information...');
          const [res] = await updateProfileRequest.makeRequest({
            onboarding_steps: { ...user.onboarding_steps, onboarding_call_booked: true },
          });

          if (res) {
            updateUser({ ...user, onboarding_steps: { ...user.onboarding_steps, onboarding_call_booked: true } });
          }
          hideLoader();
        }
        break;
      case SETUP_TYPE.JOIN_COMMUNITY:
        if (isPrimaryAction) {
          Linking.openURL(getCommunityLink(storeCountry ?? 'NG'));
        } else {
          showLoader('Updating Information...');
          const [res] = await updateProfileRequest.makeRequest({
            onboarding_steps: { ...user.onboarding_steps, community_joined: true },
          });

          if (res) {
            updateUser({ ...user, onboarding_steps: { ...user.onboarding_steps, community_joined: true } });
          }
          hideLoader();
        }
        break;
      case SETUP_TYPE.FOLLOW_OUR_SOCIALS:
        if (isPrimaryAction) {
          dropDownRef.current?.open();
        } else {
          showLoader('Updating Information...');
          const [res] = await updateProfileRequest.makeRequest({
            onboarding_steps: { ...user.onboarding_steps, has_followed_socials: true },
          });

          if (res) {
            updateUser({ ...user, onboarding_steps: { ...user.onboarding_steps, has_followed_socials: true } });
          }
          hideLoader();
        }
        break;

      //Start Taking orders
      case SETUP_TYPE.GET_VERIFIED:
        navigation.navigate('Verify', {
          type: !user?.email_verified
            ? VERIFICATION_TYPE.EMAIL
            : !user?.phone_verified
              ? VERIFICATION_TYPE.PHONE
              : null,
        });
        break;
      case SETUP_TYPE.UPLOAD_10_PRODUCT:
        if (isPrimaryAction) {
          navigation.navigate('CreateProducts');
        }
        // else {
        //   updateStoreRequest.makeRequest({
        //     id: storeId,
        //     onboarding_steps: { ...onboardingSteps, products_added: true },
        //   });
        // }
        break;
      case SETUP_TYPE.ADD_LINK_TO_SOCIAL:
        if (isPrimaryAction) {
          copyToClipboard(storeLink);
        } else {
          showLoader('Updating store...', false, true);
          const [res, error] = await updateStoreRequest.makeRequest({
            id: storeId,
            onboarding_steps: { ...onboardingSteps, link_added: true },
          });
          if (error) {
            showError(error?.message);
          }
        }
        break;

      //Setting up payment
      case SETUP_TYPE.VERIFY_IDENTITY:
        navigation.navigate('KYC');
        break;
      case SETUP_TYPE.ADD_SECURITY_PIN:
        //@ts-ignore
        navigation.navigate('StoreSettingsStack', {
          screen: 'PaymentSettings',
          params: { tab: PaymentSettingsTabs.SECURITY_PIN },
        });
        break;
      case SETUP_TYPE.ADD_WITHDRAWAL_ACCOUNT:
        //@ts-ignore
        navigation.navigate('StoreSettingsStack', {
          screen: 'PaymentSettings',
        });
        break;

      //Customizing your store
      case SETUP_TYPE.ADD_LOGO_AND_COVER_IMAGE:
        toggleModal('uploadLogo', true);
        break;
      case SETUP_TYPE.CUSTOMIZE_COLOR:
        navigation.navigate('StoreSettingsStack', {
          screen: 'StoreConfigurations',
          params: { tab: StoreConfigurationTabs.CUSTOMIZATION },
        });
        break;
      case SETUP_TYPE.ADD_STORE_LOCATION:
        navigation.navigate('StoreSettingsStack', {
          screen: 'StoreInformation',
          params: { tab: StoreInformationTabs.LOCATION_DETAILS },
        });
        break;
      case SETUP_TYPE.ADD_DELIVERY_TIMELINE:
        navigation.navigate('StoreSettingsStack', {
          screen: 'StoreInformation',
          params: { tab: StoreInformationTabs.EXTRA_INFO },
        });
        break;
      case SETUP_TYPE.ADD_DELIVERY_AREA:
        navigation.navigate('StoreSettingsStack', {
          screen: 'DeliveryAreas',
        });
        break;
      default:
      //do nothing
      // console.log("Invalid action passed:: ", action);
    }
  };

  const handleOnPressAction = (v: string) => {
    const vIndex = socialMediaItems.findIndex(d => d.value === v);
    if (vIndex !== -1) {
      const actionDelay = socialMediaItems[vIndex]?.actionDelay;

      setTimeout(() => {
        socialMediaItems[vIndex].onPress?.();
      }, 500);
    }
  };

  return (
    <View>
      <AccordionGroup openedIndex={0}>
        {sortedTasks.map((item, index) => {
          const completedTasks = item?.tasks?.filter(task => task.isCompleted).length;
          const totalTasks = item?.tasks?.length;

          return (
            <Accordion
              key={index}
              classes="mt-0"
              anchorElement={status => (
                <View className="bg-grey-bgTwo px-20 py-14 border-b border-b-grey-border">
                  <Row className="items-center" style={{ gap: 5 }}>
                    <Row>
                      <BaseText type="heading" fontSize={15}>
                        {item?.title}
                      </BaseText>
                      {!status && (
                        <>
                          {completedTasks === totalTasks ? (
                            <View className="bg-accentGreen-whiteTransparent rounded-full ml-10 p-5">
                              <TickCircle
                                variant="Bold"
                                color={colors.accentGreen.main}
                                size={wp(15)}
                                strokeWidth={2}
                              />
                            </View>
                          ) : (
                            <View className="bg-white rounded-full ml-10 px-2.5 py-1.5">
                              <BaseText
                                fontSize={10}
                                classes={completedTasks === totalTasks ? 'text-accentGreen-main' : 'text-black-muted'}
                                weight="semiBold">
                                {completedTasks === totalTasks ? 'DONE' : `${completedTasks} / ${totalTasks} DONE`}
                              </BaseText>
                            </View>
                          )}
                        </>
                      )}
                    </Row>
                    <CircledIcon className="bg-white p-7">
                      {status ? (
                        <ChevronUp size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                      ) : (
                        <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.black.muted} />
                      )}
                    </CircledIcon>
                  </Row>
                </View>
              )}>
              <View className="px-20">
                {item?.tasks?.map((task, index) => (
                  <Task
                    key={index}
                    rightIcon={task.rightIcon}
                    showBorder={item.tasks.length - 1 !== index}
                    title={task.title}
                    hasReward={task?.hasReward ?? false}
                    reward={task?.reward}
                    openRewardModal={props.openRewardModal}
                    currency={store.currencies.default ?? 'NGN'}
                    isCompleted={task.isCompleted ?? false}
                    onPressIsCompleted={() => props.disabled || handleOnPressStep(task.type, false)}
                    onPressActionBtn={() => props.disabled || handleOnPressStep(task.type, true)}
                  />
                ))}
              </View>
            </Accordion>
          );
        })}
      </AccordionGroup>
      <SelectDropdown
        showAnchor={false}
        showRadio={false}
        ref={dropDownRef}
        selectedItem=""
        onPressItem={handleOnPressAction}
        items={socialMediaItems}
        containerClasses="mb-15"
        label="Follow Our Socials"
        genItemKeysFun={value => value.label}
        // showButton
        showLabel
      />
      <UploadLogoModal isVisible={modals.uploadLogo} closeModal={() => toggleModal('uploadLogo', false)} />
    </View>
  );
};

interface TaskProps {
  title: string;
  rightIcon: React.ReactNode;
  showBorder: boolean;
  isCompleted: boolean;
  hasReward?: boolean;
  reward?: number;
  currency: string;
  onPressIsCompleted?: VoidFunction;
  onPressActionBtn?: VoidFunction;
  openRewardModal?: VoidFunction;
}

const Task = ({
  title,
  rightIcon,
  showBorder,
  hasReward,
  currency,
  reward,
  isCompleted,
  onPressIsCompleted,
  onPressActionBtn,
  openRewardModal,
}: TaskProps) => {
  // const [isCompleted, setIsCompleted] = useState(isCompleted);
  return (
    <Pressable key={title} onPress={onPressActionBtn}>
      <Row className={`py-20 ${showBorder && 'border-b border-b-grey-border'}`}>
        <Pressable activeOpacity={0.8} disabled={isCompleted} onPress={onPressIsCompleted}>
          {isCompleted ? (
            <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          ) : (
            <View className="rounded-full border-[3px] border-grey-border w-20 h-20" />
          )}
        </Pressable>
        <View className="flex-1 flex-row items-center">
          <BaseText fontSize={13} classes="mx-10 text-black-secondary">
            {title}
          </BaseText>
          {hasReward && (
            <Pressable
              style={{ gap: 5 }}
              className="flex-row items-center justify-center bg-grey-bgOne py-6 px-10 rounded-full"
              onPress={openRewardModal}>
              <Gift variant="Bold" size={wp(15)} color={colors.accentOrange.main} />
              <BaseText fontSize={11} className="text-black-secondary">
                {toCurrency(toNaira(reward), currency, false, 0)}
              </BaseText>
            </Pressable>
          )}
        </View>
        {isCompleted ? (
          <CircledIcon iconBg={'bg-accentGreen-pastel'}>
            <CheckActive size={wp(16)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          </CircledIcon>
        ) : (
          <Pressable disabled={onPressActionBtn === undefined} onPress={onPressActionBtn}>
            {rightIcon}
          </Pressable>
        )}
      </Row>
    </Pressable>
  );
};

export default GetStartedTasks;

const socialMediaItems: DropDownItem[] = [
  {
    label: 'Instagram',
    value: 'Instagram',
    // onPress: () => Linking.openURL('instagram://user?username=catlogshop'),
    onPress: () => Linking.openURL('https://www.instagram.com/catlogshop'),
    leftElement: (
      <CircledIcon className="p-8 bg-accentOrange-pastel">
        <Instagram variant="Bold" size={wp(15)} color={colors.accentOrange.main} strokeWidth={2} />
      </CircledIcon>
    ),
  },
  {
    label: 'Tiktok',
    value: 'Tiktok',
    onPress: () => Linking.openURL('https://www.tiktok.com/@catlogshop'),
    actionDelay: 600,
    leftElement: (
      <CircledIcon className="p-8 bg-accentRed-pastel">
        <Tiktok size={wp(15)} primaryColor={colors.accentRed.main} />
      </CircledIcon>
    ),
  },
  {
    label: 'X (Twitter)',
    value: 'X (Twitter)',
    onPress: () => Linking.openURL('https://x.com/CatlogShop'),
    leftElement: (
      <CircledIcon className="p-8 bg-accentYellow-pastel">
        <X size={wp(15)} primaryColor={colors.accentYellow.main} />
      </CircledIcon>
    ),
  },
];
