import { ScrollView } from 'react-native';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import Container from 'src/components/ui/container';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import { BaseText } from 'src/components/ui';

interface Props extends Partial<BottomModalProps> {
  closeModal: VoidFunction;
  continueSetup: VoidFunction;
  rewatch: VoidFunction;
}

const VideoCongratsModal = ({ closeModal, rewatch, continueSetup, ...props }: Props) => {
  return (
    <BottomModal
      {...props}
      enableDynamicSizing
      closeModal={closeModal}
      buttons={[
        {
          text: 'Watch Again',
          onPress: ()=> rewatch(),
          variant: ButtonVariant.LIGHT,
        },
        {
          text: 'Continue Setup',
          onPress: ()=> continueSetup(),
        },
      ]}>
      <ScrollView keyboardShouldPersistTaps={'handled'}>
        <Container className="items-center">
          <SuccessCheckmark />
          <BaseText fontSize={20} weight="medium" classes="text-center mt-15" type="heading">
            {`Well done, you watched the \nfull video`}
          </BaseText>
          <BaseText classes="text-black-muted text-center mt-10">
            We've added NGN 500 to your catlog credits, now let's continue your setup.
          </BaseText>
        </Container>
      </ScrollView>
    </BottomModal>
  );
};

export default VideoCongratsModal;
