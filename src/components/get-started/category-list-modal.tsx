import { ScrollView, Text, View } from 'react-native';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { BaseText, Row, SelectionPill } from '../ui';
import { useMemo } from 'react';
import { FormikProps } from 'formik';
import { hp, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { UpdateBusinessCategoryParams } from 'catlog-shared';

interface CategoryListModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  productType: string[];
  form: FormikProps<UpdateBusinessCategoryParams>;
  handleSelectProductType: (value: string) => void;
}

const CategoryListModal = ({
  productType,
  form,
  handleSelectProductType,
  closeModal,
  ...props
}: CategoryListModalProps) => {
  const buttons = [
    {
      text: 'Continue',
      onPress: closeModal,
    },
  ];

  return (
    <BottomModal
      {...props}
      closeModal={closeModal}
      buttons={buttons}
      title={'Select product type'}
      modalStyle={{ flex: 1 }}
      containerStyle={{ flex: 1 }}
      innerStyle={{ flex: 1 }}>
      <ScrollView className="flex-1 flex-grow px-20" contentContainerStyle={{ paddingBottom: hp(30) }}>
        <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
          {productType?.map(item => (
            <SelectionPill
              key={item}
              title={item}
              className="mr-0"
              selected={form?.values?.business_category?.product_types?.includes(item) ?? false}
              onPress={() => handleSelectProductType(item)}
              // backgroundColor={{ active: 'transparent', inActive: colors.grey.bgOne }}
              // textColor={{ active: colors.accentGreen.main, inActive: colors.black.muted }}
              borderColor={{ active: colors.primary.main, inActive: 'transparent' }}
              showBorder={true}
            />
          ))}
        </Row>
      </ScrollView>
    </BottomModal>
  );
};

export default CategoryListModal;
