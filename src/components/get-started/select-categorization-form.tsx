import React from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import { BaseText, CircledIcon, Row, SelectionPill } from '@/components/ui';
import useAuthContext from 'src/contexts/auth/auth-context';
import SelectDropdown from '@/components/ui/inputs/select-dropdown';
import { FormikProps, useFormik } from 'formik';
import * as Yup from 'yup';
import { hp, showError, wp } from 'src/assets/utils/js';
import colors from 'src/theme/colors';
import { navigationRef } from 'src/navigation';
import { forwardRef, Fragment, Ref, useEffect, useImperativeHandle } from 'react';
import Toast from 'react-native-toast-message';
import Pressable from '../ui/base/pressable';
import Input from '../ui/inputs/input';
import CategoryListModal from './category-list-modal';
import useModals from 'src/hooks/use-modals';
import { ChevronDown } from '../ui/icons';
import {
  CREATE_DISCOUNT,
  GET_BUSINESS_CATEGORIES,
  GET_PRODUCT_TYPE,
  GET_STORE_CATEGORIES,
  GetProductTypesParams,
  UPDATE_BUSINESS_CATEGORIZATION,
  UpdateBusinessCategoryParams,
} from 'catlog-shared';

export interface SelectCategorizationFormMethod {
  submitForm: VoidFunction;
  submitFormLoading: boolean;
}

interface SelectCategorizationFormProps {
  loadingCallBack?: (loadingStatus: boolean) => void;
  useDropdown?: boolean;
  setIsLoading: (state: boolean) => void;
}

const SelectCategorizationForm = forwardRef(
  (
    { loadingCallBack, useDropdown, setIsLoading }: SelectCategorizationFormProps,
    ref: Ref<SelectCategorizationFormMethod>,
  ) => {
    const { store, updateStore } = useAuthContext();
    const navigation = useNavigation();

    const { modals, toggleModal } = useModals(['categoryList']);

    const updateBusinessCategoryRequest = useApi<UpdateBusinessCategoryParams, ResponseWithoutPagination<string[]>>({
      key: 'update-business-categories',
      apiFunction: UPDATE_BUSINESS_CATEGORIZATION,
      method: 'PUT',
    });

    const getBusinessCategoryRequest = useApi<void, ResponseWithoutPagination<string[]>>({
      key: 'get-categories',
      apiFunction: GET_BUSINESS_CATEGORIES,
      method: 'GET',
    });

    useEffect(() => {
      if (store?.business_category?.name) {
        getBusinessProductTypeRequest.makeRequest({ category: store?.business_category?.name });
      }
    }, []);

    const form = useFormik<UpdateBusinessCategoryParams>({
      initialValues: {
        id: store?.id!,
        business_category: {
          name: store?.business_category?.name || '',
          product_types: store?.business_category?.product_types || [],
          monthly_orders: store.business_category.monthly_orders || '',
          type: store.business_category.type || '',
        },
      },
      onSubmit: async values => {
        const [response, error] = await updateBusinessCategoryRequest.makeRequest({
          business_category: values.business_category,
          id: store?.id!,
        });
        if (error) {
          showError(error)
        } else {
          updateStore({ business_category: values.business_category });
          Toast.show({ text1: 'Business category updated successfully' });
          // navigation.goBack();
        }
      },
      validationSchema,
    });

    useEffect(() => {
      setIsLoading(form.isSubmitting);
    }, [form.isSubmitting]);

    const getBusinessProductTypeRequest = useApi<GetProductTypesParams, ResponseWithoutPagination<string[]>>(
      {
        key: 'get-product-type',
        apiFunction: GET_PRODUCT_TYPE,
        autoRequest: false,
        method: 'GET',
      },
      { category: form?.values?.business_category?.name ?? '' },
    );

    useEffect(() => {
      if (getBusinessCategoryRequest.isLoading || getBusinessProductTypeRequest.isLoading) {
        loadingCallBack?.(true);
      } else {
        loadingCallBack?.(false);
      }
    }, [getBusinessCategoryRequest.isLoading, getBusinessProductTypeRequest.isLoading]);

    const businessCategoryMapped = getBusinessCategoryRequest?.response?.data?.map(item => ({
      label: item,
      value: item,
    }));

    const handleSelectProductType = async (value: string) => {
      const selectCatCopy = form?.values?.business_category?.product_types ?? [];
      const valueIndex = selectCatCopy.findIndex(t => t === value);

      if (valueIndex === -1) {
        form.setFieldValue('business_category.product_types', [...selectCatCopy, value]);
        return;
      }

      selectCatCopy.splice(valueIndex, 1);
      form.setFieldValue('business_category.product_types', selectCatCopy);
    };

    const handleSelectBusinessCategory = (value: string) => {
      form.setFieldValue('business_category.name', value);
      form.setFieldValue('business_category.name', value);
      form.setFieldValue('business_category.product_types', [])
      getBusinessProductTypeRequest.makeRequest({ category: value });
    };

    useImperativeHandle(
      ref,
      () => ({
        submitForm: () => form.handleSubmit(),
        submitFormLoading: updateBusinessCategoryRequest?.isLoading,
      }),
      [updateBusinessCategoryRequest.isLoading],
    );

    const label = () => {
      const productTypes = form.values?.business_category?.product_types;
      if (productTypes === undefined) return;

      if (productTypes?.length === 1) {
        return `${productTypes[0]}`;
      }

      if (productTypes?.length > 0) {
        return `${productTypes[0]} and ${productTypes.length - 1} more`;
      }
    };

    return (
      <Fragment>
        <SelectDropdown
          label={'Select business category'}
          items={businessCategoryMapped}
          value={form?.values?.business_category?.name}
          onPressItem={handleSelectBusinessCategory}
        />
        {!useDropdown && (
          <View className="mt-15">
            {!getBusinessProductTypeRequest.isLoading &&
              !getBusinessProductTypeRequest.error &&
              getBusinessProductTypeRequest?.response?.data?.length > 0 && (
                <>
                  <BaseText type={'heading'} fontSize={14}>
                    Which of these products do you sell?
                  </BaseText>
                  <Row className="flex-wrap justify-start mt-12" style={{ rowGap: hp(12), columnGap: wp(8) }}>
                    {getBusinessProductTypeRequest?.response?.data?.map(item => (
                      <SelectionPill
                        key={item}
                        title={item}
                        className="mr-0"
                        selected={form?.values?.business_category?.product_types?.includes(item) ?? false}
                        onPress={() => handleSelectProductType(item)}
                        backgroundColor={{ active: 'transparent', inActive: colors.grey.bgOne }}
                        textColor={{ active: colors.accentGreen.main, inActive: colors.black.muted }}
                        borderColor={{ active: colors.accentGreen.main, inActive: 'transparent' }}
                        showBorder={true}
                      />
                    ))}
                  </Row>
                </>
              )}
          </View>
        )}
        {useDropdown && (
          <View className="mt-15">
            <Pressable onPress={() => toggleModal('categoryList')}>
              <Input
                editable={false}
                value={label()}
                onPressIn={() => toggleModal('categoryList')}
                label={'Select Product Types'}
                rightAccessory={
                  <View className="p-3 my-12 bg-grey-bgOne rounded-full">
                    <ChevronDown size={wp(16)} strokeWidth={2} currentColor={colors.grey.muted} />
                  </View>
                }
                containerClasses={`py-0`}
              />
            </Pressable>
          </View>
        )}
        <CategoryListModal
          isVisible={modals.categoryList}
          closeModal={() => toggleModal('categoryList', false)}
          handleSelectProductType={handleSelectProductType}
          productType={getBusinessProductTypeRequest?.response?.data ?? []}
          form={form}
        />
      </Fragment>
    );
  },
);

const validationSchema = Yup.object().shape({
  business_category: Yup.object().shape({
    name: Yup.string().required('Category name is required'),
    product_types: Yup.array()
      .min(1, 'Add at least one product type')
      .required('Please select at least one item type for this category'),
  }),
});

export default SelectCategorizationForm;
