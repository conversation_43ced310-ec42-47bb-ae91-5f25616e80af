import React from 'react';
import { BaseText } from '@/components/ui';
import Row from '@/components/ui/row';
import Pressable from '../ui/base/pressable';

interface AuthQuestionProps {
  question: string;
  actionText: string;
  onPress?: () => void;
}

const AuthQuestion = ({ question, actionText, onPress }: AuthQuestionProps) => {
  return (
    <Pressable className="self-center mb-12 mt-6 py-10 px-15 rounded-full bg-grey-bgOne" onPress={onPress}>
      <Row classes="justify-start">
        <BaseText weight={'medium'} classes="text-black-secondary">
          {question}
        </BaseText>
        <BaseText weight={'semiBold'} classes={'text-primary-main ml-4'}>
          {actionText}
        </BaseText>
      </Row>
    </Pressable>
  );
};

export default AuthQuestion;
