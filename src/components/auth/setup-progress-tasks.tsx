import React, { useState } from 'react';
import { Linking, TouchableOpacity, View } from 'react-native';
import Row from '../ui/row';
import { CheckActive } from '../ui/icons';
import { getCommunityLink, showError, showLoader, wp } from '@/assets/utils/js';
import colors from '@/theme/colors';
import { BaseText } from '../ui';
import CircledIcon from '../ui/circled-icon';
import Pressable from '../ui/base/pressable';
import { useApi } from 'src/hooks/use-api';
import { useNavigation } from '@react-navigation/native';
import useAuthContext from 'src/contexts/auth/auth-context';
import { VERIFICATION_TYPE } from 'src/@types/utils';
import { UPDATE_PROFILE, UPDATE_STORE_DETAILS, CountryInterface } from 'catlog-shared';
import { AccountSetupStepType } from 'src/screens/setup/setup-progress';

interface TasksProps {
  tasks: AccountSetupStepType[];
  storeId: string;
  storeCountry: CountryInterface;
  onboardingSteps: {};
  disabled?: boolean;
  handleOnPressStep: (index: number) => void;
}

const SetupProgressTasks: React.FC<TasksProps> = props => {
  const { tasks, storeId, onboardingSteps, storeCountry, handleOnPressStep } = props;
  const { user } = useAuthContext();
  const navigation = useNavigation();

  const updateStoreRequest = useApi(
    {
      apiFunction: UPDATE_STORE_DETAILS,
      key: 'update-store-request',
      method: 'PATCH',
    },
    { id: storeId },
  );

  const updateProfileRequest = useApi({
    apiFunction: UPDATE_PROFILE,
    key: 'update-profile-request',
    method: 'PATCH',
  });

  return (
    <View className="px-20">
      {tasks.map((item, index) => (
        <Task
          key={index}
          rightIcon={item?.rightIcon}
          showBorder={tasks.length - 1 !== index}
          title={item.title}
          isCompleted={item?.isCompleted ?? false}
          onPressTask={() => handleOnPressStep(index)}
        />
      ))}
    </View>
  );
};

interface TaskProps {
  title: string;
  rightIcon: React.ReactNode;
  showBorder: boolean;
  isCompleted: boolean;
  onPressTask?: VoidFunction;
}

const Task = ({ title, rightIcon, showBorder, isCompleted, onPressTask }: TaskProps) => {
  // const [isCompleted, setIsCompleted] = useState(isCompleted);
  return (
    <Pressable key={title} disabled={isCompleted}  onPress={onPressTask}>
      <Row className={`py-20 ${showBorder && 'border-b border-b-grey-border'}`}>
        <Pressable activeOpacity={0.8} disabled={isCompleted} onPress={onPressTask}>
          {isCompleted ? (
            <CheckActive size={wp(18)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          ) : (
            <View className="rounded-full border-[3px] border-grey-border w-20 h-20" />
          )}
        </Pressable>
        <BaseText fontSize={13} classes="flex-1 mx-10 text-black-secondary">
          {title}
        </BaseText>
        {isCompleted ? (
          <CircledIcon iconBg={'bg-accentGreen-pastel'}>
            <CheckActive size={wp(16)} primaryColor={colors.accentGreen.main} secondaryColor={colors.white} />
          </CircledIcon>
        ) : (
          rightIcon
        )}
      </Row>
    </Pressable>
  );
};

export default SetupProgressTasks;
