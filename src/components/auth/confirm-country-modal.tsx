import { useNavigation } from '@react-navigation/native';
import {
  COUNTRY_CURRENCY_MAP,
  CountryInterface,
  CREATE_FREE_SUBSCRIPTION,
  CURRENCIES,
  PAYMENT_TYPES,
  Plan,
  PlanOption,
} from 'catlog-shared';
import { Tag2 } from 'iconsax-react-native/src';
import { useMemo, useState } from 'react';
import { Text, View } from 'react-native';
import { delay, showLoader, showSuccess, toCurrency, toNaira } from 'src/assets/utils/js';
import PaymentsWidget from 'src/components/payments/payments-widget';
import { BaseText } from 'src/components/ui';
import { ButtonVariant } from 'src/components/ui/buttons/button';
import Radio from 'src/components/ui/buttons/radio';
import ListItemCard from 'src/components/ui/cards/list-item-card';
import CircledIcon from 'src/components/ui/circled-icon';
import Container from 'src/components/ui/container';
import BottomModal, { BottomModalProps } from 'src/components/ui/modals/bottom-modal';
import StatusPill, { StatusType } from 'src/components/ui/others/status-pill';
import SuccessCheckmark from 'src/components/ui/others/success-checkmark';
import SectionContainer from 'src/components/ui/section-container';
import { DropDownItem } from '../ui/inputs/select-dropdown';
import {
  GhanaFlag,
  NigeriaFlag,
  USAFlag,
  BritainFlag,
  CanadaFlag,
  SouthAfricaFlag,
  KenyaFlag,
  EuropeFlag,
} from '../ui/icons';

interface Props extends Partial<BottomModalProps> {
  closeModal: () => void;
  countries: DropDownItem[];
  onPressContinue: VoidFunction;
  selectedCountry: DropDownItem['value'];
  onselectCountry: (country: DropDownItem['value']) => void;
}
const ConfirmCountryModal: React.FC<Props> = ({ closeModal, onPressContinue, countries, selectedCountry, ...props }) => {
  const selectedCountryData = countries.find(c => c.value === (selectedCountry ?? 'NG'));

  return (
    <BottomModal
      closeModal={closeModal}
      enableDynamicSizing
      {...props}
      buttons={[
        {
          onPress: onPressContinue,
          text: 'Continue',
        },
      ]}>
      <Container className="">
        <View className="items-center flex-col justify-center flex-1 mt-40">
          <View className="w-80 h-80 bg-grey-bgOne rounded-full p-4">{countryDetails[selectedCountry ?? 'NG']()}</View>
          <BaseText fontSize={20} type="heading" classes='text-center mt-10'>
            Is your business based in{'\n'} {selectedCountryData?.label ?? 'Nigeria'}?
          </BaseText>
        </View>
        <SectionContainer className="">
          {countries.map((p, i) => (
            <ListItemCard
              key={i}
              title={p.label}
              showBorder={countries.length !== i + 1}
              // description={p.value}
              leftElement={<View className="w-24 h-24">{p.leftElement}</View>}
              // titleAddon={
              //   p.discount ? (
              //     <StatusPill
              //       statusType={StatusType.SUCCESS_INVERTED}
              //       className="py-[3px]"
              //       title={`${p.discount}% off`}
              //     />
              //   ) : null
              // }
              // titleProps={{
              //   fontSize: 12,
              //   type: 'body',
              //   weight: 'light',
              //   classes: 'text-black-muted',
              // }}
              // descriptionProps={{
              //   fontSize: 14,
              //   type: 'body',
              //   weight: 'medium',
              //   classes: 'text-black-main',
              // }}
              rightElement={<Radio active={selectedCountry === p.value} />}
              onPress={() => props.onselectCountry(p.value)}
              spreadTitleContainer={false}
            />
          ))}
        </SectionContainer>
      </Container>
    </BottomModal>
  );
};
export default ConfirmCountryModal;

export const countryDetails = {
  NG: NigeriaFlag,
  GH: GhanaFlag,
  US: USAFlag,
  CA: CanadaFlag,
  ZA: SouthAfricaFlag,
  KE: KenyaFlag,
};
