import { ActivityIndicator, ScrollView, View } from 'react-native';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import BottomModal, { BottomModalProps } from '../ui/modals/bottom-modal';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { delay, getFieldvalues, hideLoader, showError, showLoader, showSuccess } from '@/assets/utils/js';
import { phoneValidation } from '@/assets/utils/js/common-validations';
import { useApi } from '@/hooks/use-api';
import Toast from 'react-native-toast-message';
import { useEffect, useState } from 'react';
import {
  CreateCustomerParams,
  UpadateCustomerParams,
  CustomerInterface,
  CREATE_CUSTOMER,
  UPDATE_CUSTOMER,
  ACCEPT_INVITE,
  AcceptInviteParams,
  StoreInviteDetails,
} from 'catlog-shared';
import CustomImage from '../ui/others/custom-image';
import { BaseText } from '../ui';
import { ButtonSize, ButtonVariant, TextColor } from '../ui/buttons/button';
import useAuthContext from 'src/contexts/auth/auth-context';
import ScreenModal from '../ui/modals/screen-modal';

interface AcceptInviteModalProps extends Partial<BottomModalProps> {
  closeModal: () => void;
  onPressButton?: () => void;
  callBack?: (updateData?: CustomerInterface) => void;
  inviteDetail?: StoreInviteDetails;
  activeCustomer?: CustomerInterface;
  isEdit?: boolean;
}

const AcceptInviteModal = ({ closeModal, callBack, inviteDetail, ...props }: AcceptInviteModalProps) => {
  const { initialize, switchStore, isSwitchingStore } = useAuthContext();

  const acceptInviteRequest = useApi<AcceptInviteParams>({
    apiFunction: ACCEPT_INVITE,
    method: 'PUT',
    key: 'accept-invite',
  });

  const handleAcceptInvite = async () => {
    const [res, err] = await acceptInviteRequest.makeRequest({ id: inviteDetail.id });

    if (err) {
      showError(err);
      closeModal?.();
      return;
    }

    showSuccess(`You've successfully joined ${inviteDetail?.store?.name}`);
    closeModal?.();
    await delay(600)
    showLoader('Switch store....')
    initialize();
    switchStore(inviteDetail?.store?.id);
    hideLoader()
  };

  return (
    <View>
      <BottomModal
        {...props}
        closeModal={closeModal}
        buttons={[
          {
            text: 'Reject Invite',
            variant: ButtonVariant.LIGHT,
            textColor: TextColor.NEGATIVE,
            onPress: () => closeModal(),
            isLoading: acceptInviteRequest.isLoading,
          },
          {
            text: 'Accept Invite',
            onPress: handleAcceptInvite,
            isLoading: acceptInviteRequest.isLoading,
          },
        ]}>
        <View className="my-20 px-20">
          <View className="items-center mt-20">
            <CustomImage
              transparentBg
              className="w-[70px] h-[50px]"
              imageProps={{ source: require('@/assets/images/storefront.png'), contentFit: 'cover' }}
            />
          </View>
          <View className="mb-20 mt-25">
            <BaseText weight="light" fontSize={20} classes="text-center">
              You've been invited to join {`\n`}{' '}
              <BaseText fontSize={20} weight="bold" classes="mt-15 text-center">
                {inviteDetail?.store?.name}{' '}
              </BaseText>
              on Catlog
            </BaseText>
          </View>
        </View>
      </BottomModal>
      <ScreenModal show={isSwitchingStore} toggleModal={() => null}>
        <View className="min-h-[25%] w-full rounded-xl bg-white justify-center items-center">
          <ActivityIndicator className="text-black-main" size="small" animating />
          <BaseText classes="mt-10">{'Switching Store...'}</BaseText>
        </View>
      </ScreenModal>
    </View>
  );
};

export default AcceptInviteModal;
