import { styled } from 'nativewind';
import React from 'react';
import { View, Image } from 'react-native';
import { BaseText } from '@/components/ui';

interface StoreInviteInfoProps {
  name: string;
}

const StoreInviteInfo = ({ name, ...rest }: StoreInviteInfoProps) => {
  return (
    <View className="rounded-[10px] px-16 pt-16 pb-20 bg-primary-pastel overflow-hidden" {...rest}>
      <BaseText weight={'semiBold'} classes="text-primary-main">
        {name} invited you to Catlog!
      </BaseText>
      <BaseText classes="text-black-muted mt-4 leading-[18px]">
        You’ll get some credits when{'\n'}you setup your store
      </BaseText>
      <Image
        source={require('@/assets/images/referInfoBg.png')}
        resizeMode={'contain'}
        className="absolute h-[72px] w-[96px] bottom-0 right-0"
      />
    </View>
  );
};

export default styled(StoreInviteInfo);
