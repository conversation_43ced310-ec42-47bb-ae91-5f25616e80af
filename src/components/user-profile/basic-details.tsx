import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import SelectDropdown from '../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../ui/icons';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import InfoBadge from '../store-settings/info-badge';
import useAuthContext from 'src/contexts/auth/auth-context';
import { useApi } from 'src/hooks/use-api';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { phoneValidation } from 'src/assets/utils/js/common-validations';
import { getFieldvalues } from 'src/assets/utils/js';
import Toast from 'react-native-toast-message';
import { UPDATE_PROFILE, UpdateProfileParams } from 'catlog-shared';

interface BasicDetailsProps {}

const BasicDetails = ({}: BasicDetailsProps) => {
  const { user, updateUser, stores } = useAuthContext();

  const updateProfileRequest = useApi<UpdateProfileParams>({
    key: 'update-profile',
    apiFunction: UPDATE_PROFILE,
    method: 'PUT',
  });

  const form = useFormik({
    initialValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone: {
        digits: user?.phone.split('-')[1],
        code: user?.phone.split('-')[0],
      },
    },
    validationSchema,
    onSubmit: async values => {
      const phone = `${values.phone.code}-${values.phone.digits}`;
      const [response, error] = await updateProfileRequest.makeRequest({ ...values, phone });

      if (error) {
        if (error.fields && Object.keys(error.fields).length > 0) {
          form.setErrors({ ...error.fields });
        }
        Toast.show({ type: 'error', text1: error?.body?.message });
      } else {
        updateUser({ ...response, stores });
        Toast.show({ text1: 'Profile updates successfully' });
      }
    },
  });
  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Here’s a summary of the information you provided'} />
          <View>
            <Input label={'Full Name'} containerClasses="mt-20" {...getFieldvalues('name', form)} />
            <Input label={'Email Address'} containerClasses="mt-15" {...getFieldvalues('email', form)} />
            <PhoneNumberInput
              label={'Phone Number'}
              containerClasses="mt-15"
              {...getFieldvalues('phone', form)}
              onChange={value => form.setFieldValue('phone', value)}
            />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          { text: 'Update Changes', onPress: () => form.submitForm(), isLoading: updateProfileRequest?.isLoading },
        ]}
      />
    </View>
  );
};

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  phone: phoneValidation(),
  email: Yup.string().email('Invalid email address').required('Email address is required'),
});

export default BasicDetails;
