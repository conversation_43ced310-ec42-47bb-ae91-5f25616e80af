import { <PERSON><PERSON>View } from 'react-native';
import { View } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { Container } from '@/components/ui';
import SelectDropdown from '../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../ui/icons';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import InfoBadge from '../store-settings/info-badge';
import PasswordInput from '../ui/inputs/password-input';
import { useApi } from 'src/hooks/use-api';
import { useFormik } from 'formik';
import { getFieldvalues } from 'src/assets/utils/js';
import * as Yup from 'yup';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';
import { UPDATE_PASSWORD } from 'catlog-shared';

interface UpdatePasswordProps {}

const UpdatePassword = ({}: UpdatePasswordProps) => {
  const navigation = useNavigation();

  const updatePasswordRequest = useApi({
    key: 'update-password',
    apiFunction: UPDATE_PASSWORD,
    method: 'PUT',
  });

  const form = useFormik({
    initialValues: {
      current_password: '',
      new_password: '',
    },
    validationSchema,
    onSubmit: async values => {
      const [response, error] = await updatePasswordRequest.makeRequest(values);

      if (error) {
        Toast.show({ type: 'error', text1: error.body.message });
      } else {
        Toast.show({ text1: 'Password updated successfully' });
        navigation.goBack();
      }
    },
  });

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Update your account password'} />
          <View>
            <PasswordInput
              label={'Current Password'}
              containerClasses="mt-20"
              {...getFieldvalues('current_password', form)}
            />
            <PasswordInput label={'New Password'} containerClasses="mt-15" {...getFieldvalues('new_password', form)} />
          </View>
        </Container>
      </ScrollView>
      <FixedBtnFooter
        buttons={[
          { text: 'Update Password', onPress: () => form.submitForm(), isLoading: updatePasswordRequest.isLoading },
        ]}
      />
    </View>
  );
};

const validationSchema = Yup.object().shape({
  current_password: Yup.string().required('Current password is required'),
  new_password: Yup.string().required('New password is required'),
});

export default UpdatePassword;
