import { <PERSON><PERSON>View } from 'react-native';
import { View, Platform, Linking } from 'react-native';
import FixedBtnFooter from '@/components/ui/buttons/fixed-btn-footer';
import { CircledIcon, Container } from '@/components/ui';
import SelectDropdown from '../ui/inputs/select-dropdown';
import { GhanaFlag, NigeriaFlag } from '../ui/icons';
import Input from '../ui/inputs/input';
import PhoneNumberInput from '../ui/inputs/phone-number-input';
import InfoBadge from '../store-settings/info-badge';
import PasswordInput from '../ui/inputs/password-input';
import { useApi } from 'src/hooks/use-api';
import { useFormik } from 'formik';
import { delay, getFieldvalues, hideLoader, showLoader, showSuccess, wp } from 'src/assets/utils/js';
import * as Yup from 'yup';
import Toast from 'react-native-toast-message';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { UPDATE_PASSWORD, UPDATE_PROFILE } from 'catlog-shared';
import { ListCard } from '../ui/cards/list-item-card';
import { Notification, Trash, Wallet } from 'iconsax-react-native/src';
import colors from 'src/theme/colors';
import CustomSwitch from '../ui/inputs/custom-switch';
import { ContainerType } from '../ui/section-container';
import Separator from '../ui/others/separator';
import { AuthorizationStatus, getMessaging } from '@react-native-firebase/messaging';
import * as Notifications from 'expo-notifications';
import { useState, useEffect, useCallback } from 'react';
import DeleteAccountModal from './delete-account-modal';
import useModal from 'src/hooks/use-modals';
import { useFeatureFlags } from '@/contexts/feature-flags/use-feature-flags';
import useAuthContext from 'src/contexts/auth/auth-context';

interface PreferenceProps {}

const Preference = ({}: PreferenceProps) => {
  const { modals, toggleModal } = useModal(['deleteAccount']);
  const { isFeatureEnabled } = useFeatureFlags();
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'unsupported' | 'pending'>('pending');

  const { user, updateUser } = useAuthContext();

  // Check current permission status on component mount
  // useEffect(() => {
  //   checkNotificationPermissions();
  // }, []);

  useFocusEffect(
    useCallback(() => {
      console.log('focussed');
      checkNotificationPermissions();

      return () => {
        console.log('Screen unfocused');
      };
    }, []),
  );

  const checkNotificationPermissions = async () => {
    try {
      const authStatus = await getMessaging().requestPermission();
      const { status: expoStatus } = await Notifications.getPermissionsAsync();

      const firebaseEnabled =
        authStatus === AuthorizationStatus.AUTHORIZED || authStatus === AuthorizationStatus.PROVISIONAL;
      const expoEnabled = expoStatus === 'granted';

      setPermissionStatus(firebaseEnabled && expoEnabled ? 'granted' : 'denied');
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      setPermissionStatus('denied');
    }
  };

  const handleNotificationToggle = async (newValue: boolean) => {
    if (newValue) {
      // User wants to enable notifications
      await requestNotificationPermissions();
    } else {
      // User wants to disable notifications
      // Direct them to app settings since we can't programmatically revoke permissions
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    }
  };

  const requestNotificationPermissions = async (): Promise<boolean> => {
    try {
      // Check Firebase messaging permissions
      const authStatus = await getMessaging().requestPermission();
      const firebaseEnabled =
        authStatus === AuthorizationStatus.AUTHORIZED || authStatus === AuthorizationStatus.PROVISIONAL;

      // Check Expo notification permissions
      const { status: expoStatus } = await Notifications.requestPermissionsAsync();
      const expoEnabled = expoStatus === 'granted';

      // If either permission is denied, redirect to settings
      if (!firebaseEnabled || !expoEnabled) {
        if (Platform.OS === 'ios') {
          await Linking.openURL('app-settings:');
        } else {
          await Linking.openSettings();
        }
        setPermissionStatus('denied');
        return false;
      }

      // Both permissions granted
      setPermissionStatus('granted');
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Notification enabled!',
          body: 'Here is what your notifications will look like',
          data: {},
        },
        trigger: null, // Show immediately
      });
      return true;
    } catch (error) {
      console.error('Permission request failed:', error);
      setPermissionStatus('denied');
      return false;
    }
  };

  const updateProfileRequest = useApi({
    apiFunction: UPDATE_PROFILE,
    key: 'update-profile-request',
    method: 'PUT',
  });

  const updateAutoDebits = async (state: boolean) => {
    showLoader('Updating Preference...');
    const [res, err] = await updateProfileRequest.makeRequest({ auto_debit_wallets: state });
    hideLoader();
    if (res) {
      console.log(res);
      updateUser(res);
      await delay(700);
      showSuccess('Preference updated successfully');
      return;
    }
  };

  const additionalAction = [
    {
      leftElement: (
        <CircledIcon className="bg-accentRed-pastel p-10">
          <Trash size={wp(16)} color={colors.accentRed.main} variant="Bold" />
        </CircledIcon>
      ),
      title: 'Delete Account',
      description: 'Permanently delete your Catlog account',
      onPress: () => toggleModal('deleteAccount'),
    },
  ];

  const getPreferenceActions = () => {
    let actions = [
      {
        leftElement: (
          <CircledIcon className="bg-accentYellow-pastel p-10">
            <Notification size={wp(16)} color={colors.accentYellow.main} variant="Bold" />
          </CircledIcon>
        ),
        title: 'Enable Notifications',
        description: 'Get notified for updates directly via the app',
        rightElement: <CustomSwitch value={permissionStatus === 'granted'} onValueChange={handleNotificationToggle} />,
        onPress: () => {},
      },
    ];

    // Only add subscription-related preferences if the feature is enabled
    if (isFeatureEnabled('subscriptions')) {
      actions.push({
        leftElement: (
          <CircledIcon className="bg-accentOrange-pastel p-10">
            <Wallet size={wp(16)} color={colors.accentOrange.main} variant="Bold" />
          </CircledIcon>
        ),
        title: 'Wallet Auto-Debits For Subscription',
        description: 'Auto-pay subscription fees from wallet',
        rightElement: <CustomSwitch value={user?.auto_debit_wallets} onValueChange={updateAutoDebits} />,
        onPress: () => {},
      });
    }

    return actions;
  };

  const preferenceActions = getPreferenceActions();

  return (
    <View className="flex-1">
      <ScrollView className="flex-1">
        <Container>
          <InfoBadge text={'Customize how Catlog works for you'} />
          <View>
            <Separator className="mt-20 mb-0 mx-0" />
            <ListCard
              containerType={ContainerType.OUTLINED}
              classes="mt-0 p-0 border-0"
              titleProps={{ type: 'heading', fontSize: 15 }}
              descriptionProps={{ fontSize: 11, classes: 'text-black-secondary', weight: 'regular' }}
              items={[...preferenceActions, ...additionalAction]}
            />
          </View>
        </Container>
      </ScrollView>
      <DeleteAccountModal isVisible={modals.deleteAccount} closeModal={() => toggleModal('deleteAccount', false)} />
    </View>
  );
};

const validationSchema = Yup.object().shape({
  current_password: Yup.string().required('Current password is required'),
  new_password: Yup.string().required('New password is required'),
});

export default Preference;
