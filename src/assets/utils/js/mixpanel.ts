// import { Mixpanel } from 'expo-mixpanel-analytics';
// import MixpanelAnalytics from '@bothrs/expo-mixpanel-analytics';
import { Mixpanel } from 'mixpanel-react-native';
import { StoreInterface, User } from 'catlog-shared';
import { EXPO_PUBLIC_MIXPANEL_TOKEN } from '@env';
import { Platform } from 'react-native';

const trackAutomaticEvents = false; //disable legacy mobile autotrack
const useNative = false; //disable Native Mode, use Javascript Mode
let mixpanel: Mixpanel;

export const initMixpanel = async () => {
  mixpanel = new Mixpanel(EXPO_PUBLIC_MIXPANEL_TOKEN, trackAutomaticEvents, useNative);
  mixpanel.init();
};

// export const mixpanelIdentify = (id: string) => mixpanel.identify(id);
export const mixpanelTrack = (event: string, props?: Record<string, any>) => {
  mixpanel?.track?.(event, { ...props, platform: 'mobile-app', device: Platform.OS });
};
// export const mixpanelPeopleSet = (props: Record<string, any>) => mixpanel.people_set(props);

export const mixpanelRecordUser = (user: User, storeId: string) => {
  try {
    mixpanel.identify(user.id);
    const store = (user.stores as StoreInterface[]).find(s => s.id === storeId);
    const subscription = store?.subscription;
    mixpanel.getPeople().set({
      $name: user.name,
      $email: user.email,
      plan: subscription?.plan?.type,
      'Store Id': store?.id,
      'Store Name': store?.name,
      'Store Link': store?.slug + '.catlog.shop',
      'Store Country': store?.country?.code,
      'Current Plan': subscription?.plan?.type,
      'Last Payment Date': subscription?.last_payment_date,
      'Next Payment Date': subscription?.next_payment_date,
      'Products Count': store?.item_count,
      'Store Visits': store?.total_visits,
      'Business Type': store?.business_category?.type,
      'Business Category': store?.business_category?.name,
      'Plan Interval': subscription?.plan?.interval_text,
      'Signup Date': user.created_at,
      Phone: user?.phone?.split('-').join(''),
    });
    
  } catch (error) {
    console.log("MIX PANEL ERROR", error);
  }
};
