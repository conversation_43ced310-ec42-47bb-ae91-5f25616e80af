type Listener = (...args: any[]) => void;

class EventEmitter {
  private events: Map<string, Listener[]> = new Map();

  // Add an event listener
  on(event: string, listener: Listener): () => void {
    // Create event array if it doesn't exist
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    // Add listener to the event
    const listeners = this.events.get(event)!;
    listeners.push(listener);

    // Return unsubscribe function
    return () => this.off(event, listener);
  }

  // Remove a specific listener
  off(event: string, listenerToRemove: Listener): void {
    const listeners = this.events.get(event);
    if (!listeners) return;

    // Filter out the specific listener
    this.events.set(
      event, 
      listeners.filter(listener => listener !== listenerToRemove)
    );
  }

  // Emit an event
  emit(event: string, ...args: any[]): void {
    const listeners = this.events.get(event);
    if (!listeners) return;

    // Call all listeners for this event
    listeners.forEach(listener => listener(...args));
  }

  // Remove all listeners for a specific event
  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}

// Create a singleton instance
export default new EventEmitter();