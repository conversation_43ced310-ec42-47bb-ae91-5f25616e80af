import * as Yup from 'yup';
import { isValidInstagramUsername } from 'catlog-shared';

const phoneValidation = (fieldName = 'Phone number') =>
  Yup.object().shape({
    code: Yup.string().required('Country code is required Boss'),
    digits: Yup.string()
      .test('minlen', `${fieldName} must be at least 7 digits`, val => val !== undefined && val.length >= 7)
      .test('maxlen', `${fieldName} must be at most 15 digits`, val => val !== undefined && val.length <= 15)
      .test('digits', `${fieldName} should contain only digits`, value => /^\d+$/.test(value ?? ''))
      .required(`${fieldName} is required`),
  });

const optionalPhoneValidation = Yup.object().shape({
  code: Yup.string(),
  digits: Yup.string()
    .notRequired()
    .test('minlen', 'Phone number must be at least 7 digits', val => val === undefined || (val ?? '').length >= 7)
    .test('maxlen', 'Phone number must be at most 15 digits', val => val === undefined || (val ?? '').length <= 15)
    .test('digits', 'Phone number should contain only digits', val => val === undefined || /^\d+$/.test(val ?? '')),
});

const InstagramUsernameValidation = Yup.string()
  .min(3, 'Username should be at least 3 characters')
  .test('valid_username', 'Please enter a valid username', value => isValidInstagramUsername(value));

export { phoneValidation, optionalPhoneValidation, InstagramUsernameValidation };
