import AsyncStorage from '@react-native-async-storage/async-storage';
import { ProductForm, ProductCreateMethod, ProductUploadStep } from '@/components/products/create-products/types';
import { AppMediaType, Image } from '@/@types/utils';

const STORAGE_KEY = '@product_creation_state';

interface ProductCreationState {
  form: ProductForm;
  medias: AppMediaType[];
  // currentStep: ProductUploadStep;
  // uploadMethod: ProductCreateMethod | null;
  // currentProduct: number;
  lastUpdated: number;
}

export const saveProductCreationState = async (state: Omit<ProductCreationState, 'lastUpdated'>) => {
  try {
    const dataToSave = { ...state, lastUpdated: Date.now() };
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  } catch (error) {
    console.error('Error saving product creation state:', error);
  }
};

export const getProductCreationState = async (): Promise<ProductCreationState | null> => {
  try {
    const savedState = await AsyncStorage.getItem(STORAGE_KEY);
    return savedState ? JSON.parse(savedState) : null;
  } catch (error) {
    console.error('Error getting product creation state:', error);
    return null;
  }
};

export const clearProductCreationState = async () => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing product creation state:', error);
  }
};
