import Intercom, { Visibility, LogLevel } from '@intercom/intercom-react-native';
import { StoreInterface, User } from 'catlog-shared';
import { removeUndefinedValues } from './functions';

const intercomInitForLoggedInUser = async (user: User, storeId: string) => {
  try {
    let store: StoreInterface = (user.stores as StoreInterface[]).find(s => s.id === storeId);

    const subscription = store?.subscription;
    // Intercom.setLogLevel(LogLevel.DEBUG);
    await Intercom.setLauncherVisibility(Visibility.GONE);
    await Intercom.loginUserWithUserAttributes({
      userId: user.id,
      name: user.name,
      email: user.email,
      signedUpAt: new Date(user.created_at).getTime(),
      ...removeUndefinedValues({
        'Store Id': store?.id,
        'Store Name': store?.name,
        'Store Link': store?.slug + '.catlog.shop',
        'Store Country': store?.country?.code,
        'Current Plan': subscription?.plan?.type,
        'Last Payment Date': subscription?.last_payment_date,
        'Next Payment Date': subscription?.next_payment_date,
        'Products Count': store?.item_count,
        'Store Visits': store?.total_visits,
        'Business Type': store?.business_category?.type,
        'Business Category': store?.business_category?.name,
        'Plan Interval': subscription?.plan?.interval_text,
      }),
    });
    await Intercom.updateUser({ phone: user?.phone?.split('-').join('') }); //doing this because intercom throws an error if phone is not valid
  } catch (error) {
    //do nothing
  }
};

const intercomLogoutUser = async () => {
  await Intercom.logout();
};

const intercomTrackEvent = (eventName: string, metaData?: Record<string, any>) => {
  Intercom.logEvent(eventName, metaData);
};

const intercomInitForGuest = async () => {
  await Intercom.loginUnidentifiedUser();
  await Intercom.setBottomPadding(100);
  await Intercom.setLauncherVisibility(Visibility.VISIBLE);
};

export { intercomInitForLoggedInUser, intercomLogoutUser, intercomTrackEvent, intercomInitForGuest };
