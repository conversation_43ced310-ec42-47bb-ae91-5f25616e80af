import { MMKV } from 'react-native-mmkv';
import { StateStorage } from 'zustand/middleware';

// Create an MMKV instance
export const storage = new MMKV({
  id: 'zustand-storage',
  // encryptionKey: 'your-encryption-key', // Encryption ensures secure storage
});

export const appStorage = new MMKV({ id: 'app-storage' });

// Configure Zustand to use MMKV for state persistence
export const zustandStorage: StateStorage = {
  setItem: (name, value) => {
    return storage.set(name, value);
  },
  getItem: name => {
    const value = storage.getString(name);
    return value ?? null;
  },
  removeItem: name => {
    return storage.delete(name);
  },
};

export default zustandStorage;

export const mmkvStorage = new MMKV();
