import { Image } from 'src/@types/utils';

interface Args {
  // e: ChangeEvent;
  images: Image[] | Image;
  saveImages: (images: Image[] | Image) => void;
  maxSize?: number; //in dimensions
  maxLength?: number;
  uploadImages?: (images: Image[] | Image) => void;
}

const handleImageSelectionFromFile = async (args: Args) => {
  const { images, saveImages, maxSize = 1500, maxLength, uploadImages } = args;
};
