import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MobileAppConfig } from 'catlog-shared';
import { EXPO_PUBLIC_API_URL } from 'src/configs-files/@env';
import zustandStorage from 'src/assets/utils/js/zustand-mmkv-storage';
import * as Application from 'expo-application';
import { Platform } from 'react-native';
import { resetRoutes } from 'src/navigation';

const base_url = EXPO_PUBLIC_API_URL;

interface FeatureFlagsState {
  flags: MobileAppConfig['feature_flags'];
  current_released_versions: {
    android: string;
    ios: string;
  };
  minimum_required_app_version: {
    android: string;
    ios: string;
  };
  isInitialized: boolean;
  isAppOutdated: boolean;
  forceUpdateApp: boolean;
}

interface FeatureFlagsActions {
  setFlags: (flags: Partial<MobileAppConfig['feature_flags']>) => void;
  setIsInitialized: (isInitialized: boolean) => void;
  initialize: () => Promise<void>;
}

const defaultFlags: MobileAppConfig['feature_flags'] = {
  subscriptions: false,
  deliveries: false,
  welcome_back_promo: true,
  store_reviews: false,
};

const useFeatureFlagsStore = create(
  persist<FeatureFlagsState & FeatureFlagsActions>(
    (set, get) => ({
      flags: defaultFlags,
      current_released_versions: {
        android: '',
        ios: '',
      },
      minimum_required_app_version: {
        android: '',
        ios: '',
      },
      isInitialized: false,
      isAppOutdated: false,
      forceUpdateApp: false,

      setFlags: flags => {
        set(state => ({
          flags: {
            ...state.flags,
            ...flags,
          },
        }));
      },

      setIsInitialized: isInitialized => {
        set({ isInitialized });
      },

      initialize: async () => {
        try {
          const isAndroid = Platform.OS === 'android';
          const appVersion = Application.nativeApplicationVersion;
          const remoteFlags = await fetch(`${base_url}/utils/mobile-app-config`);

          if (!remoteFlags.ok) {
            throw new Error(`HTTP error! status: ${remoteFlags.status}`);
          }

          const response = await remoteFlags.json();
          const responseData = response?.data || {};
          
          const featureFlags = responseData.feature_flags || {};
          const { current_released_versions, minimum_required_app_version, bypass_version_check, ...feature_flags } =
            featureFlags;

          const flags = {
            ...defaultFlags,
            ...feature_flags,
          };

          const expectedVersion = isAndroid ? current_released_versions?.android : current_released_versions?.ios;
          if (!bypass_version_check && feature_flags?.subscriptions) {
            const isCorrectVersion = compareVersions(appVersion, expectedVersion) <= COMPARED_RESULT.EQUAL;
            flags.subscriptions = isCorrectVersion;
          }

          const minimumRequiredAppVersion = isAndroid
            ? minimum_required_app_version?.android
            : minimum_required_app_version?.ios;

          const isAppLessThanMinimumVersion =
            compareVersions(appVersion, minimumRequiredAppVersion) === COMPARED_RESULT.LESS_THAN;
          const isAppOutdated = compareVersions(appVersion, 'expectedVersion') === COMPARED_RESULT.LESS_THAN;

          set({
            flags,
            current_released_versions,
            minimum_required_app_version,
            isInitialized: true,
            isAppOutdated,
            forceUpdateApp: isAppLessThanMinimumVersion,
          });
        } catch (error) {
          console.error('Failed to fetch feature flags:', error);
          set({
            flags: defaultFlags,
            current_released_versions: null,
            isInitialized: true,
            forceUpdateApp: false,
          });
        }
      },
    }),
    {
      name: 'feature-flags-storage',
      storage: createJSONStorage(() => zustandStorage),
      partialize: state => {
        return {
          ...state,
          //Values to overwrite when persisting to storage
          isInitialized: false,
        };
      },
      onRehydrateStorage: () => state => {
        state?.initialize();
      },
    },
  ),
);

export default useFeatureFlagsStore;

enum COMPARED_RESULT {
  LESS_THAN = -1,
  EQUAL = 0,
  GREATER_THAN = 1,
}

function compareVersions(version1, version2) {
  if (!version1 || !version2) return COMPARED_RESULT.EQUAL;

  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  const maxLength = Math.max(v1Parts.length, v2Parts.length);

  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part < v2Part) return COMPARED_RESULT.LESS_THAN;
    if (v1Part > v2Part) return COMPARED_RESULT.GREATER_THAN;
  }

  return COMPARED_RESULT.EQUAL;
}
