import { useEffect } from 'react';
import useFeatureFlagsStore from './store';

export const useFeatureFlags = () => {
  const { flags, isInitialized, initialize } = useFeatureFlagsStore();

  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized]);

  const isFeatureEnabled = (feature: keyof typeof flags) => {
    return flags[feature];
  };

  return {
    flags,
    isInitialized,
    isFeatureEnabled,
  };
};
