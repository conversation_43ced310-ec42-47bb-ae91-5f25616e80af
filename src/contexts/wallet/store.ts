import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { CURRENCIES, StoreInterface, CatlogCredits, Wallet } from 'catlog-shared';
import zustandStorage from 'src/assets/utils/js/zustand-mmkv-storage';

export interface WalletBalances {
  wallets: {
    id: string;
    balance: number;
    currency: CURRENCIES;
  }[];
  catlogCredits: {
    balance: number;
    currency: CURRENCIES;
  };
}

export interface WalletState {
  wallets: Wallet[];
  catlogCredits: CatlogCredits;
  canOwnWallet: boolean;
  canManageWallet: boolean;
  store: StoreInterface;
  storeId: string;
}

export interface WalletActions {
  setWallets: (wallets: Wallet[]) => void;
  setCatlogCredits: (credits: CatlogCredits) => void;
  updateBalance({ walletId, type, amount }: UpdateBalanceParams): void;
  setStore(store: StoreInterface): void;
  setStoreId(storeId: string): void;
  setCanOwnWallet(canOwnWallet: boolean): void;
  setCanManageWallet(canManageWallet: boolean): void;
  clearWallet(): void;
}

interface UpdateBalanceParams {
  walletId: string;
  type: 'credit' | 'debit' | 'replace';
  amount: number;
}

const defaultCreditData = {
  balance: 0,
  owner: null,
  currency: CURRENCIES.NGN,
  id: null,
};

const useWalletStore = create(
  persist<WalletState & WalletActions>(
    (set, get) => {
      return {
        // states
        wallets: [],
        catlogCredits: defaultCreditData,
        canOwnWallet: false,
        canManageWallet: false,
        store: null,
        storeId: null,

        // actions
        setWallets: (wallets: Wallet[]) => {
          set({ wallets });
        },

        setCatlogCredits: (catlogCredits: CatlogCredits) => {
          set({ catlogCredits });
        },

        setStore: (store: StoreInterface) => {
          set({ store });
        },

        setStoreId: (storeId: string) => {
          set({ storeId });
        },

        setCanOwnWallet: (canOwnWallet: boolean) => {
          set({ canOwnWallet });
        },

        setCanManageWallet: (canManageWallet: boolean) => {
          set({ canManageWallet });
        },

        updateBalance: ({ walletId, type, amount }: UpdateBalanceParams) => {
          if (walletId === 'catlogCredit') {
            const { catlogCredits } = get();
            const currentBalance = catlogCredits.balance;
            const updatedBalance =
              type === 'replace' ? amount : currentBalance + (type === 'credit' ? amount : -amount);

            set({
              catlogCredits: {
                ...catlogCredits,
                balance: updatedBalance,
              },
            });
          } else {
            const { wallets } = get();
            const updatedWallets = wallets.map(wallet => {
              if (wallet.id === walletId) {
                const currentBalance = wallet.balance;
                const updatedBalance =
                  type === 'replace' ? amount : currentBalance + (type === 'credit' ? amount : -amount);

                return { ...wallet, balance: updatedBalance };
              }
              return wallet;
            });

            set({ wallets: updatedWallets });
          }
        },
        clearWallet: () => {
          set({
            wallets: [],
            catlogCredits: defaultCreditData,
            canOwnWallet: false,
            canManageWallet: false,
            store: null,
            storeId: null,
          });
        },
      };
    },
    {
      name: 'wallet-storage',
      storage: createJSONStorage(() => zustandStorage),
      partialize: state => ({
        ...state,
        wallets: state?.wallets ?? [],
        catlogCredits: state.catlogCredits || defaultCreditData,
        canOwnWallet: state.canOwnWallet || false,
        canManageWallet: state.canManageWallet || false,
        store: state.store,
        storeId: state.storeId,
      }),
      onRehydrateStorage: () => state => {
        if (state) {
          if (!state.wallets) state.wallets = [];
          if (!state.catlogCredits) state.catlogCredits = defaultCreditData;
        }
      },
    },
  ),
);

export default useWalletStore;
