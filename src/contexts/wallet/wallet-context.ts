import { useApi } from 'src/hooks/use-api';
import useWalletStore from './store';
import useAuthContext from '../auth/auth-context';
import { actionIsAllowed, SCOPES } from 'src/assets/utils/js/permissions';
import { GET_ALL_WALLETS, GET_WALLET_BALANCE, GET_STORE_CREDITS } from 'catlog-shared';
import { useEffect } from 'react';
import { Wallet, CatlogCredits, CURRENCIES } from 'catlog-shared';

function useWalletContext() {
  const {
    wallets,
    catlogCredits,
    setWallets,
    setCatlogCredits,
    updateBalance,
    setCanOwnWallet,
    setCanManageWallet,
    canOwnWallet,
    canManageWallet,
    store,
    storeId,
    setStore,
    setStoreId,
  } = useWalletStore();

  useEffect(() => {
    console.log('******************* mounted wallet context *******************>');
  }, []);

  const { store: storeFromAuth, storeId: storeFromAuthId, userRole, isAuthenticated } = useAuthContext();

  const getWalletsRequest = useApi({
    apiFunction: GET_ALL_WALLETS,
    key: `${GET_ALL_WALLETS.name}${storeFromAuthId}`,
    method: 'GET',
    autoRequest: false,
  });

  const getCreditsRequest = useApi({
    apiFunction: GET_STORE_CREDITS,
    key: `${GET_STORE_CREDITS.name}${storeFromAuthId}`,
    method: 'GET',
    autoRequest: false,
  });

  const getBalanceRequest = useApi({
    apiFunction: GET_WALLET_BALANCE,
    key: `${GET_WALLET_BALANCE.name}${storeFromAuthId}`,
    method: 'GET',
    autoRequest: false,
  });

  const initialize = async () => {
    if (!isAuthenticated) return;

    Promise.all([fetchWallets(), fetchCredits()]);

    if (storeFromAuth && !store) {
      setStore(storeFromAuth);
    }

    if (storeFromAuthId && !storeId) {
      setStoreId(storeFromAuthId);
    }

    const canOwn = actionIsAllowed({
      countryPermission: SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS,
      country: storeFromAuth?.country?.code,
    });

    const canManage = actionIsAllowed({ permission: SCOPES.WALLETS.CAN_MANAGE_WALLET, userRole });

    setCanOwnWallet(canOwn);
    setCanManageWallet(canManage);
  };

  const fetchWallets = async () => {
    const [res] = await getWalletsRequest.makeRequest({});

    if (res?.data?.wallets) {
      setWallets(res.data.wallets);
    }
  };

  const fetchCredits = async () => {
    const [res] = await getCreditsRequest.makeRequest({});
    if (res?.data) {
      setCatlogCredits(res.data);
    }
  };

  return {
    wallets,
    catlogCredits,
    getWalletsRequest,
    getCreditsRequest,
    store,
    storeId,
    getBalanceRequest,
    canOwnWallet,
    canManageWallet,
    initialize,
    updateBalance,
    getWalletBalance: (currency: CURRENCIES) => wallets.find(wallet => wallet.currency === currency)?.balance ?? 0,
    fetchWallets,
    fetchCredits,
  };
}

export default useWalletContext;
