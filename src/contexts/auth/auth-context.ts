// useAuthContext.js
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import useAuthStore from './store'; // Import the Zustand store
import { delay, getActiveRouteName, removeUndefinedValues, showError, subdomainStoreLink } from '@/assets/utils/js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ResponseWithoutPagination, useApi } from '@/hooks/use-api';
import {
  supportedCountries,
  StoreInterface,
  StoreRoles,
  GET_CURRENT_SESSION,
  LOGIN,
  LoginParams,
  REFRESH_TOKEN,
  RefreshTokenParams,
  GET_ALL_CONVERSION_RATES,
  ConversionRates,
  CurrencyRates,
  GET_REFERRAL_REWARDS,
  UPDATE_LAST_LOGIN,
  GET_MOBILE_APP_CONFIG,
  countryCodes,
  COUNTRIES,
  PLAN_TYPE,
  User,
  GET_USER_UNREAD_NOTIFICATIONS_COUNT,
} from 'catlog-shared';
import { RootStackParamList } from 'src/@types/navigation';
import useFeatureFlagsStore from '../feature-flags/store';
import * as Updates from 'expo-updates';
import { mixpanelRecordUser } from 'src/assets/utils/js/mixpanel';
import useAsyncEffect from 'src/hooks/use-async-effect';
import { intercomInitForGuest, intercomInitForLoggedInUser, intercomLogoutUser } from '@/assets/utils/js/intercom';
import Intercom from '@intercom/intercom-react-native';

function useAuthContext() {
  const navigation = useNavigation();
  const navigationState = navigation.getState();

  // Zustand store actions and state
  const {
    token,
    rewards,
    isNewUser,
    appIsSetup,
    fetchError,
    userHasSetup,
    userAccountDeactivated,
    isInitialized,
    isAuthenticated,
    isAuthFromCache,
    pageIsReady,
    redirectTo,
    isSwitchingStore,
    isCreatingStore,
    user,
    storeId,
    history,
    currentRates,
    setToken,
    setStoreId,
    setIsAuthenticated,
    setFetchError,
    setAppIsSetup,
    setUserHasSetup,
    setPageIsReady,
    setRedirectTo,
    setIsSwitchingStore,
    setIsCreatingStore,
    setUser,
    setHistory,
    verifyAndSetStoreId,
    handleAuthSuccess,
    getStore,
    clearUserData,
    getToken,
    setCurrencyRates,
    setRewards,
    setUserAccountDeactivated,
    setStore,
    store,
    isDroppedOffUser,
    setIsDroppedOffUser,
    getUser,
    getValue,
    setUnreadNotificationCount,
    unreadNotificationCount,
  } = useAuthStore();

  const stores = useMemo(() => (user?.stores ?? []) as StoreInterface[], [user]);

  const getUserDataRequest = useApi({
    key: 'get-user-data',
    apiFunction: GET_CURRENT_SESSION,
    method: 'GET',
    autoRequest: false,
  });

  const getNewTokenRequest = useApi<RefreshTokenParams>({
    key: 'get-new-token',
    apiFunction: REFRESH_TOKEN,
    method: 'GET',
    autoRequest: false,
  });

  const getCurrentRates = useApi<void, ResponseWithoutPagination<ConversionRates>>({
    key: 'get-conversion-rates-data',
    apiFunction: GET_ALL_CONVERSION_RATES,
    method: 'GET',
    autoRequest: false,
  });

  const getReferralRewardsReq = useApi<any, any>({
    apiFunction: GET_REFERRAL_REWARDS,
    key: GET_REFERRAL_REWARDS.name,
    method: 'GET',
    autoRequest: false,
    onSuccess: response => {
      setRewards(response?.data);
    },
  });

  const getRemoteConfigs = useApi<any, any>({
    apiFunction: GET_MOBILE_APP_CONFIG,
    key: GET_MOBILE_APP_CONFIG.name,
    method: 'GET',
    autoRequest: false,
    onSuccess: response => {
      useFeatureFlagsStore.setState({ flags: response?.data?.feature_flags ?? {} });
    },
  });

  const updateLastLogin = useApi<void, ResponseWithoutPagination<ConversionRates>>({
    apiFunction: UPDATE_LAST_LOGIN,
    key: UPDATE_LAST_LOGIN.name,
    method: 'GET',
    autoRequest: false,
  });

  const notificationCountRequest = useApi({
    key: GET_USER_UNREAD_NOTIFICATIONS_COUNT.name,
    apiFunction: GET_USER_UNREAD_NOTIFICATIONS_COUNT,
    method: 'GET',
    autoRequest: false,
  });

  // useEffect()

  useEffect(() => {
    const fn = async () => {
      if (token && storeId) {
        await refreshTokenIfNeeded(storeId);
      }
    };

    fn();
  }, [token, storeId]);

  useEffect(() => {
    if (isAuthenticated && !appIsSetup) {
      initialize();
    }
    // getRemoteConfigs.makeRequest({});
  }, [isAuthenticated, appIsSetup]);

  const initialize: VoidFunction = async () => {
    try {
      if (isAuthFromCache) await updateUserData();

      let { userHasCompletedSetup, nextRoute } = await decideNextRoute({});

      setRedirectTo({ path: nextRoute ?? redirectTo?.path ?? 'HomeTab', props: null });

      if (userHasCompletedSetup) {
        getReferralRewardsReq.makeRequest({});

        getRates();
        refreshRates();
        getUnreadNotificationCount();
      }

      mixpanelRecordUser(user, storeId);
      intercomInitForLoggedInUser(user, storeId);

      setAppIsSetup(true);
    } catch (error) {
      showError('Something went wrong while trying to sign you in please contact supoort');
      logout(false);
      await delay(2000); //NOTE: Too hacky
      await Intercom.present();
    }
  };

  async function refreshTokenIfNeeded(storeId?: string) {
    const lastRefresh = await AsyncStorage.getItem('last-refresh');
    const now = new Date().getTime();

    // Check if the last refresh was more than a day ago
    if (!lastRefresh || now - parseInt(lastRefresh, 10) > 24 * 60 * 60 * 1000) {
      await getNewToken(storeId);
    }
  }

  const decideNextRoute = async ({
    pathToVisit,
    skipProgress = false,
    fromSetupComplete = false,
    // userPreviouslyDroppedOff = false,
    skipWelcomeBack = false,
    skipWelcomeBackContinue = false,
    preventDashboardRouting = false,
  }: {
    pathToVisit?: keyof RootStackParamList;
    skipProgress?: boolean;
    fromSetupComplete?: boolean;
    skipWelcomeBack?: boolean;
    skipWelcomeBackContinue?: boolean;
    preventDashboardRouting?: boolean;
  }): Promise<{ userHasCompletedSetup: boolean; nextRoute: keyof RootStackParamList | null }> => {
    if (!isAuthenticated) return { userHasCompletedSetup: false, nextRoute: null };

    const goingTo: keyof RootStackParamList = pathToVisit ?? getActiveRouteName(navigationState);

    const { flags } = useFeatureFlagsStore.getState();

    const user: User = getValue('user');
    const store: StoreInterface = getValue('store');
    const isDroppedOffUser: boolean = getValue('isDroppedOffUser');

    const userHasStores = (user?.stores as StoreInterface[])?.length > 0;
    const userHasUploadedProducts = (store?.item_count ?? 0) >= 1;
    const userHasActiveNonStarterPlanSub =
      !!store?.subscription && store?.subscription?.plan?.type && store?.subscription?.plan?.type !== PLAN_TYPE.STARTER;
    // const userAccountDeactivated =
    //   store?.subscription && store?.subscription?.plan.type === PLAN_TYPE.STARTER ? true : false;
    const userHasAdditionalStoreDetails =
      Boolean(store?.business_category?.name) && Boolean(store?.business_category?.type);

    const userHasCompletedSetup = Boolean(
      userHasStores &&
        userHasUploadedProducts &&
        userHasAdditionalStoreDetails &&
        (!flags.subscriptions || userHasActiveNonStarterPlanSub) &&
        !isDroppedOffUser && !preventDashboardRouting
    );

    setUserHasSetup(userHasCompletedSetup);

    const isStarterPlanUser = !!store?.subscription && store?.subscription?.plan?.type === PLAN_TYPE.STARTER;
    // setUserAccountDeactivated(isStarterPlanUser); NOTE: Don't set this yet for launch period

    if (pathToVisit) {
      return { userHasCompletedSetup, nextRoute: pathToVisit };
    }

    if (!userHasCompletedSetup) {
      const nextRoute: keyof RootStackParamList = (() => {
        if (isDroppedOffUser && !skipWelcomeBack && !skipProgress) return 'WelcomeBack';

        if (!isNewUser && !skipProgress) return 'SetupProgress';

        if (!userHasStores) return 'CreateStore';

        if (userHasStores && !userHasUploadedProducts) return 'SetupAddProducts';

        if (userHasStores && userHasUploadedProducts && !userHasAdditionalStoreDetails) return 'SetupBusiness';

        const onlySubscriptionPending =
          userHasStores && userHasUploadedProducts && userHasAdditionalStoreDetails && !userHasActiveNonStarterPlanSub;

        if (onlySubscriptionPending && skipProgress && !fromSetupComplete) return 'SetupComplete';

        if (onlySubscriptionPending && isDroppedOffUser && !skipWelcomeBackContinue && flags.subscriptions)
          return 'WelcomeContinueSetup';

        console.log({userHasActiveNonStarterPlanSub});
        console.log({skipProgress});
        console.log('store?.subscription?.plan?.type }: ', store?.subscription?.plan?.type );

        if (
          userHasStores &&
          userHasUploadedProducts &&
          userHasAdditionalStoreDetails &&
          !userHasActiveNonStarterPlanSub &&
          flags.subscriptions
        )
          return 'PickPlan';

        return null;
      })();

      return { userHasCompletedSetup, nextRoute };
    }

    return { userHasCompletedSetup, nextRoute: null };
  };

  const updateUserData = async () => {
    const validUser = await fetchUserSession();

    if (validUser) {
      //update last login
      updateLastLogin.makeRequest();
      await AsyncStorage.setItem('last-login', String(Date.now()));
    } else {
      // possibly logout user
    }
  };

  const getUnreadNotificationCount = async () => {
    const [res, err] = await notificationCountRequest.makeRequest({});

    if (res) {
      setUnreadNotificationCount(res?.count ?? 0);
    }
  };

  const fetchUserSession = async () => {
    const [res, err] = await getUserDataRequest.makeRequest(null);

    if (!err) {
      const loggedInUser = res as User;
      setUser(loggedInUser);
      verifyAndSetStoreId(); //sets store, storeId, isInitialized, isAuthenticated, isAuthFromCache & isDroppedOffUser

      return true;
    } else {
      //do something with this, show an error screen to the user
      setFetchError(true);
    }
    return false;
  };

  async function getNewToken(storeId?: string) {
    const [res, err] = await getNewTokenRequest.makeRequest({ store: storeId });

    if (res) {
      setToken(res.data.token);
      storeId && setStoreId(storeId);
      await AsyncStorage.setItem('last-refresh', new Date().getTime().toString());
    }
  }

  //Actions
  const logout = (redirect = true) => {
    clearUserData();
    intercomLogoutUser();
    // if (redirect) navigation.navigate('Login');
  };

  async function getReferralRewards() {
    const [res, err] = await getReferralRewardsReq.makeRequest({});

    if (res) {
      setRewards(res?.data);
    }
  }

  const switchStore = async (store: string, getUserInformation = false) => {
    if (store) {
      setIsSwitchingStore(true);
      await getNewToken(store);
      await delay(1000);
      await Updates.reloadAsync();

      setIsSwitchingStore(false);
    }
  };

  //Save & Update State
  const saveUser = (user: any) => {
    setUser(user);
    verifyAndSetStoreId(); //sets store and storeId
    AsyncStorage.setItem('user', JSON.stringify(user));
  };

  const updateStore = (data: Partial<StoreInterface>) => {
    const storeIndex = stores.findIndex(s => s.id === store?.id);
    const storeData = { ...store, ...data, subscription: store?.subscription }; //force subscription to retain it's previous value - subscriptions should only be updated by refresh
    const userStores = [...(user?.stores ?? [])];
    userStores[storeIndex] = storeData as any;
    updateUser({ stores: userStores as StoreInterface[] });
    setStore(storeData);
  };

  const updateUser = async (newUserData: Partial<User>, storeToSet?: string) => {
    if (user) {
      const updatedUser: any = { ...user, ...newUserData };
      saveUser(updatedUser);

      if (storeToSet) {
        const store = (updatedUser.stores as StoreInterface[]).find(s => s.id === storeToSet);

        if (store) {
          setStore(store as StoreInterface);
          setStoreId(storeToSet);
        }
      }
    }
  };

  const refreshRates = () => {
    setInterval(
      () => {
        getRates();
      },
      1000 * 60 * 60,
    );
  };

  const getRates = async () => {
    const [res, err] = await getCurrentRates.makeRequest();

    if (res) {
      setCurrencyRates(res?.data?.rates as CurrencyRates);
    }
  };

  const getUserCountry = () => {
    return store?.country ?? { name: 'Nigeria', currency: 'NGN', code: 'NG', dial_code: '+234', emoji: '🇳🇬' };
  };

  const getRewards = () => {
    const sortedStores = stores.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    const country = sortedStores[0]?.country;

    const countrySignupReward = rewards ? rewards.SIGNUP[country?.code ?? 'NG'] : 0;
    const countrySubscriptionReward = rewards ? rewards.SUBSCRIPTION[country?.code ?? 'NG'] : 0;
    const minCashoutAmount = rewards ? rewards.CASHOUT[country?.code ?? 'NG'] : 5000_00;

    return rewards
      ? {
          signup: countrySignupReward,
          subscription: countrySubscriptionReward,
          currency: country?.currency || 'NGN',
          min_cash_out: minCashoutAmount,
        }
      : null;
  };

  const getStoreFromUser = (user: User, storeId: string) => {
    const store = (user?.stores as StoreInterface[])?.find(s => s.id === storeId);
    return store;
  };

  return {
    isInitialized,
    appIsSetup,
    userHasSetup,
    userAccountDeactivated,
    isSwitchingStore,
    isCreatingStore,
    setIsCreatingStore,
    isAuthenticated,
    isLoadingSession: getUserDataRequest.isLoading,
    isDroppedOffUser,

    storeId,
    stores,
    store,
    currentRates,
    user,
    fetchError,
    unreadNotificationCount,

    initialize,
    logout,
    getNewToken,
    switchStore,
    getToken,

    updateUser,
    setStoreId,
    updateStore,
    setIsAuthenticated,
    decideNextRoute,
    getRates,
    getRewards,
    getUserCountry,

    categories: store?.categories,
    pageNotReady: !isAuthenticated || !pageIsReady || !user || ((user?.stores?.length ?? 0) > 0 && !store),
    storeLink: store
      ? (() => {
          const customDomain = store.domains?.[0];
          return customDomain ? `https://${customDomain.domain}` : subdomainStoreLink(store.slug, true);
        })()
      : '',
    subscription: store?.subscription ?? user?.subscription,
    userRole: getUserRole(store, user),
    redirectTo,
    history,
    refetchSession: fetchUserSession,
    userLoading: getUserDataRequest.isLoading,
    setIsDroppedOffUser,
    setUnreadNotificationCount,
    // referrals,
  };
}

const defaultCountry = { name: 'Nigeria', currency: 'NGN', code: 'NG', dial_code: '+234', emoji: '🇳🇬' };

export const getCountryFromCountries = (c: any) => {
  if (!c) return defaultCountry;
  return supportedCountries.find(cx => cx.code === c);
};

export const getUserRole = (store: any, user: any) => {
  if (!user) return StoreRoles.OPERATOR;

  if (store && user && store?.owner === user.id) {
    return StoreRoles.OWNER;
  }

  return store?.owners?.find((owner: any) => owner.user === user?.id)?.role ?? StoreRoles.OPERATOR;
};

export default useAuthContext;
