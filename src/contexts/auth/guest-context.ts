import { useNavigation } from '@react-navigation/native';
import useAuthStore from './store';
import { useApi } from '@/hooks/use-api';
import {
  COUNTRIES,
  countryCodes,
  LOGIN,
  LoginParams,
  SIGNUP,
  SIGNUP_V2,
  SignUpParams,
  SignUpParamsV2,
} from 'catlog-shared';
import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { mixpanelTrack } from 'src/assets/utils/js/mixpanel';
import { intercomInitForGuest } from 'src/assets/utils/js/intercom';
import { useEffect } from 'react';
import { mmkvStorage } from 'src/assets/utils/js/zustand-mmkv-storage';

const useGuestContext = () => {
  const navigation = useNavigation();
  const navigationState = navigation.getState();

  // Zustand store actions and state
  const { visitorCountry, handleAuthSuccess, setIsNewUser, isAuthenticated, appIsSetup } = useAuthStore();

  const loginRequest = useApi<LoginParams>({ key: 'login', apiFunction: LOGIN, method: 'POST' });
  const signupRequest = useApi<SignUpParams>({ key: 'signup', apiFunction: SIGNUP, method: 'POST' });

  const signupRequest2 = useApi({ key: SIGNUP_V2.name, apiFunction: SIGNUP_V2, method: 'POST' });

  useEffect(() => {
    const isOnboarded = mmkvStorage.getString('isOnboarded');
    if (!isAuthenticated && !appIsSetup && isOnboarded) {
      intercomInitForGuest();
    }
  }, [isAuthenticated, appIsSetup]);

  const login = async (data: LoginParams, redirect = true) => {
    const [response, error] = await loginRequest.makeRequest(data);
    if (error) return [response, error];

    Sentry.setUser({
      id: response.user.id, // Required unique identifier
      email: response.user.email,
    });
    mixpanelTrack?.('Login', { email: response?.user?.email });
    // Intercom.loginUserWithUserAttributes?.({
    //   email: response?.user?.email,
    //   userId: response?.user?.id,
    // });
    return handleSuccess(response, redirect);
  };

  async function register(data: SignUpParams, redirect?: boolean) {
    const [response, error] = await signupRequest.makeRequest(data);
    if (error) return [response, error];

    return handleSuccess(response, redirect);
  }

  async function registerV2(data: SignUpParamsV2, redirect?: boolean) {
    const [response, error] = await signupRequest2.makeRequest(data);
    if (error) return [response, error];

    await AsyncStorage.setItem('is-new-user', 'true');
    mixpanelTrack('Signup', {
      'Signup Type': data.referral_code ? 'Referral' : 'Direct',
      $email: data.email,
      $name: data.name,
      $phone: data.phone.split('-').join(''),
      Country: data.country,
    });
    setIsNewUser(true);
    return handleSuccess(response, redirect);
  }

  const handleSuccess = async (response: any, redirect = true) => {
    await handleAuthSuccess(response.user, response.token);
    // if (redirect) handleRedirects();
    return [response, null];
  };

  return { login, handleSuccess, register, registerV2, visitorCountry, loginRequest, signupRequest, signupRequest2 };
};

export default useGuestContext;
