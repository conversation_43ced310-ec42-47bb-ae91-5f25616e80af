import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StoreInterface, CurrencyRates, Rewards, User, COUNTRIES, countryCodes, PLAN_TYPE } from 'catlog-shared';
import { RootStackParamList } from 'src/@types/navigation';
import dayjs from 'node_modules/dayjs';
import zustandStorage, { mmkvStorage } from 'src/assets/utils/js/zustand-mmkv-storage';

export interface AuthState {
  userHasSetup: boolean;
  userAccountDeactivated: boolean; //account status of the user true when subscription === 'STARTER'
  appIsSetup: boolean;
  isNewUser: boolean;
  isDroppedOffUser: boolean;
  showBottomTab: boolean;
  isInitialized: boolean;
  isAuthenticated: boolean;
  isAuthFromCache: boolean;
  fetchError: boolean;
  pageIsReady: boolean;
  redirectTo: { path: keyof RootStackParamList; props: any } | null;
  isSwitchingStore: boolean;
  isCreatingStore: boolean;
  token: string | null;
  user: User | null;
  store: StoreInterface | null;
  storeId: string | null;
  history: string[];
  currentRates: CurrencyRates;
  rewards: Rewards;
  visitorCountry: { code: COUNTRIES; dial_code: string } | null;
  unreadNotificationCount: number;
  // rewards: any; // Define the type if known
  // referrals: any; // Define the type if known
}
export interface AuthActions {
  initialize: () => Promise<void>;
  verifyAndSetStoreId: (fromCache?: boolean) => Promise<void>;
  handleAuthSuccess: (user: any, token: string) => Promise<void>;
  clearUserData: () => void;
  getUser: () => User | null;
  getStore: () => StoreInterface | null;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  setFetchError: (fetchError: boolean) => void;
  setPageIsReady: (pageIsReady: boolean) => void;
  setRedirectTo: (redirectTo: { path: keyof RootStackParamList; props: any }) => void;
  setIsSwitchingStore: (isSwitchingStore: boolean) => void;
  setIsCreatingStore: (isCreatingStore: boolean) => void;
  setUser: (user: any) => void;
  setAppIsSetup: (status: boolean) => void;
  setUserHasSetup: (status: boolean) => void;
  setToken: (token: string) => void;
  setStoreId: (storeId: string) => void;
  setStore: (store: StoreInterface) => void;
  setHistory: (history: string[]) => void;
  setShowBottomTab: (show: boolean) => void;
  setIsDroppedOffUser: (isDroppedOffUser: boolean) => void;
  reset: () => void;
  getToken: () => string | null;
  setCurrencyRates: (rates: CurrencyRates) => void;
  setRewards: (rates: Rewards) => void;
  setIsNewUser: (isNewUser: boolean) => void;
  setUserAccountDeactivated: (userAccountDeactivated: boolean) => void;
  setVisitorCountry: (visitorCountry: { code: COUNTRIES; dial_code: string }) => void;
  getVisitorCountry: () => Promise<void>;
  getValue: (key: keyof AuthState) => any;
  getDroppedOffStatus: (user: User, store: StoreInterface) => boolean;
  setUnreadNotificationCount: (unreadNotificationCount: number) => void;
  // setRewards: (rewards: any) => void;
  // setReferrals: (referrals: any) => void;
}
const initialState: AuthState = {
  showBottomTab: true,
  isInitialized: false,
  isNewUser: false,
  isAuthenticated: false,
  isAuthFromCache: false,
  fetchError: false,
  pageIsReady: false,
  redirectTo: null,
  isSwitchingStore: false,
  isCreatingStore: false,
  userAccountDeactivated: false, //account status of the user true when subscription === 'STARTER
  userHasSetup: false, //setup status of the user
  appIsSetup: false, //when necessary checks has been done to allow user access app
  // State -> user, store, stores
  token: null,
  user: null,
  storeId: null,
  history: [],
  currentRates: {},
  rewards: null,
  visitorCountry: null,
  store: null,
  isDroppedOffUser: false,
  unreadNotificationCount: 0,
  // referrals: null,
};
const useAuthStore = create(
  persist<AuthState & AuthActions>(
    (set, get) => ({
      ...initialState,
      // Actions
      initialize: async () => {
        const token = get().token;
        const user = get().user;

        if (token && user) {
          await get().verifyAndSetStoreId(true);
        } else {
          await get().getVisitorCountry();
          set({ isInitialized: true });
        }
      },
      verifyAndSetStoreId: async (fromCache = false) => {
        try {
          const user = get().user;
          let storeId = get().storeId;

          let store: StoreInterface | undefined;
          const stores = user?.stores as StoreInterface[];

          if (user && storeId && stores && stores?.length > 0) {
            store = stores.find(store => store.id === storeId);
          } else if (stores && stores?.length > 0) {
            storeId = stores[0]?.id;
            store = stores[0];
          } else {
            storeId = null;
          }

          const isDroppedOffUser = get().getDroppedOffStatus(user, store);

          set({
            isDroppedOffUser,
            storeId,
            store,
            isInitialized: true,
            isAuthFromCache: fromCache,
            isAuthenticated: true,
          });
        } catch (error) {
          set({ isInitialized: true, store: null, storeId: null, isAuthenticated: false, user: null, token: null });
        }
      },
      handleAuthSuccess: async (user: any, token: string) => {
        try {
          // set({ user, token });
          let storeId = null;
          let isDroppedOffUser = false;
          let store = null;

          if (user?.stores && user?.stores?.length > 0) {
            storeId = user.stores[0].id;
            store = user.stores[0];
          }

          isDroppedOffUser = get().getDroppedOffStatus(user, store);
          set({ isAuthenticated: true, storeId, isDroppedOffUser, store, user, token });
        } catch (error) {
          set({ isInitialized: true, store: null, storeId: null, isAuthenticated: false, user: null, token: null });
          // console.log('<==============error ==============>', error);
        }
      },
      getDroppedOffStatus: (user: User, store: StoreInterface) => {
        const signedUpBefore2Weeks = dayjs(user?.created_at).isBefore(dayjs().subtract(2, 'weeks'));
        const isDroppedOffUser =
          signedUpBefore2Weeks &&
          (!store ||
            !store?.subscription ||
            !store?.subscription?.plan?.type ||
            store?.subscription?.plan?.type === PLAN_TYPE.STARTER);

        return isDroppedOffUser;
      },
      clearUserData: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          storeId: null,
          redirectTo: null,
          appIsSetup: false,
          isDroppedOffUser: false,
          isCreatingStore: false,
          isSwitchingStore: false,
          userHasSetup: false,
        });
        // AsyncStorage.removeItem('auth-storage');
        // AsyncStorage.removeItem('wallet-storage');
        zustandStorage.removeItem('auth-storage');
        zustandStorage.removeItem('wallet-storage');
        // mmkvStorage.clearAll();
      },
      getStore: () => get().store,
      getToken: () => get().token,
      setToken: (token: string) => set({ token }),
      getUser: () => get().user,
      setStoreId: (storeId: string) => set({ storeId }),
      setIsAuthenticated: (isAuthenticated: boolean) => set({ isAuthenticated }),
      setFetchError: (fetchError: any) => set({ fetchError }),
      setPageIsReady: (pageIsReady: boolean) => set({ pageIsReady }),
      setRedirectTo: (redirectTo: { path: keyof RootStackParamList; props: any }) => set({ redirectTo }),
      setIsSwitchingStore: (isSwitchingStore: boolean) => set({ isSwitchingStore }),
      setIsCreatingStore: (isCreatingStore: boolean) => set({ isCreatingStore }),
      setUser: (user: any) => set({ user }),
      setAppIsSetup: (appIsSetup: boolean) => set({ appIsSetup }),
      setUserHasSetup: (userHasSetup: boolean) => set({ userHasSetup }),
      setHistory: (history: any) => set({ history }),
      setCurrencyRates: (rates: CurrencyRates) => set({ currentRates: rates }),
      setShowBottomTab: (showBottomTab: boolean) => set({ showBottomTab }),
      setRewards: (rewards: any) => set({ rewards }),
      setIsNewUser: (isNewUser: boolean) => set({ isNewUser }),
      setUserAccountDeactivated: (userAccountDeactivated: boolean) => set({ userAccountDeactivated }),
      setIsDroppedOffUser: (isDroppedOffUser: boolean) => set({ isDroppedOffUser }),
      setStore: (store: StoreInterface) => set({ store }),
      getVisitorCountry: async () => {
        try {
          const localVisitorCountry = await AsyncStorage.getItem('visitor-country');

          const fetchVisitorCountry = async () => {
            try {
              const payload = await (await fetch('https://api.iplocation.net/?cmd=get-ip')).json();
              if (payload) {
                const location = await (await fetch('https://api.iplocation.net/?ip=' + payload.ip)).json();
                if (location) {
                  const code = countryCodes.find(c => c.code === location.country_code2);
                  if (code) {
                    const visitorCountry = { code: code.code as COUNTRIES, dial_code: code.dial_code };

                    await AsyncStorage.setItem('visitor-country', JSON.stringify(visitorCountry));
                    set({ visitorCountry });
                  }
                }
              }
            } catch (e) {
              // console.log(e);
            }
          };

          if (!localVisitorCountry) {
            await fetchVisitorCountry();
          } else {
            const localCountryCode = JSON.parse(localVisitorCountry);

            if (!localCountryCode.code || !localCountryCode.dial_code) {
              await fetchVisitorCountry();
              return;
            }

            set({ visitorCountry: localCountryCode });
          }
        } catch (error) {
          // Handle AsyncStorage errors
          console.error('Error in getVisitorCountry:', error);
        }
      },
      setVisitorCountry: (visitorCountry: any) => set({ visitorCountry }),
      getValue: (key: keyof AuthState) => get()[key],
      setUnreadNotificationCount: (unreadNotificationCount: number) => set({ unreadNotificationCount }),
      // setReferrals: (referrals: any) => set({ referrals }),
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => zustandStorage),
      partialize: state => {
        return {
          ...state,
          //Values to overwrite when persisting to storage
          isAuthenticated: false,
          isNewUser: false,
          isInitialized: false,
          fetchError: false,
          pageIsReady: false,
          redirectTo: null,
          isSwitchingStore: false,
          isCreatingStore: false,
          isAuthFromCache: false,
          isDroppedOffUser: false,
          userAccountDeactivated: false,
          userHasSetup: false,
          appIsSetup: false,
          history: [],
          currentRates: {},
          rewards: null,
          referrals: null,
          showBottomTab: true,
        };
      },
      onRehydrateStorage: () => state => {
        state?.initialize();
      },
    },
  ),
);

export default useAuthStore;
