import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import * as Network from 'expo-network';
import { AppState, AppStateStatus } from 'react-native';
import { showError } from '@/assets/utils/js/functions';

interface NetworkContextType {
  isConnected: boolean;
  isInternetReachable: boolean | null;
  type: string | null;
  lastToastTime: number;
  updateLastToastTime: () => void;
  checkNetworkStatus: () => Promise<boolean>;
}

const NetworkContext = createContext<NetworkContextType>({
  isConnected: true,
  isInternetReachable: true,
  type: 'unknown',
  lastToastTime: 0,
  updateLastToastTime: () => {},
  checkNetworkStatus: async () => true,
});

export const useNetwork = () => useContext(NetworkContext);

interface NetworkProviderProps {
  children: React.ReactNode;
  toastCooldownMs?: number;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({ 
  children, 
  toastCooldownMs = 10000 // Default 5 second cooldown between toasts
}) => {
  const [networkStatus, setNetworkStatus] = useState({
    isConnected: true,
    isInternetReachable: true,
    type: 'unknown' as string | null,
  });
  const [lastToastTime, setLastToastTime] = useState(0);
  const appState = useRef(AppState.currentState);
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const updateLastToastTime = () => {
    setLastToastTime(Date.now());
  };

  const showNetworkToast = () => {
    const now = Date.now();
    if (now - lastToastTime > toastCooldownMs) {
      showError(null, 'No internet connection. Please check your network settings.');
      updateLastToastTime();
    }
  };
  
  const checkNetworkStatus = async (): Promise<boolean> => {
    try {
      const networkState = await Network.getNetworkStateAsync();
      const newIsConnected = networkState.isConnected && networkState.isInternetReachable;
      
      // Update state if changed
      if (networkStatus.isConnected !== newIsConnected || 
          networkStatus.isInternetReachable !== networkState.isInternetReachable ||
          networkStatus.type !== networkState.type) {
        
        setNetworkStatus({
          isConnected: newIsConnected,
          isInternetReachable: networkState.isInternetReachable,
          type: networkState.type,
        });
        
        // Show toast only if connection was lost
        if (networkStatus.isConnected && !newIsConnected) {
          showNetworkToast();
        }
      }
      
      return newIsConnected;
    } catch (error) {
      console.error('Failed to check network status:', error);
      return false;
    }
  };

  // Handle app state changes
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) && 
      nextAppState === 'active'
    ) {
      // App has come to the foreground, check network status
      checkNetworkStatus();
    }
    appState.current = nextAppState;
  };

  useEffect(() => {
    // Initial check
    checkNetworkStatus();
    
    // Set up interval to check network status every 5 seconds
    checkIntervalRef.current = setInterval(checkNetworkStatus, 5000);
    
    // Listen for app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      // Clean up
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
      }
      subscription.remove();
    };
  }, []);

  const value = {
    ...networkStatus,
    lastToastTime,
    updateLastToastTime,
    checkNetworkStatus,
  };

  return (
    <NetworkContext.Provider value={value}>
      {children}
    </NetworkContext.Provider>
  );
};

export default NetworkProvider;
