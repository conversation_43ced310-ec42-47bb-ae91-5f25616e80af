import {
  AddCircle,
  ArrowRight,
  Bag,
  Copy,
  Moneys,
  PercentageCircle,
  Profile2User,
  Shop,
  TicketDiscount,
  Truck,
} from 'iconsax-react-native/src';
import colors from '@/theme/colors';
import { wp } from 'src/assets/utils/js';

export const quickActions = [
  {
    icon: <AddCircle variant="Bold" size={wp(20)} color={colors.accentOrange.main} />,
    title: 'Add Products',
    description: 'Upload store products',
    iconBgColor: 'bg-accentOrange-pastel',
  },
  {
    icon: <Bag variant="Bold" size={wp(20)} color={colors.accentRed.main} />,
    title: 'Record Order',
    description: 'Record orders you received',
    iconBgColor: 'bg-accentRed-pastel',
  },
  {
    icon: <Moneys variant={'Bold'} size={wp(20)} color={colors.accentGreen.main} />,
    title: 'Request a payment',
    description: 'Collect payment with invoice',
    iconBgColor: 'bg-accentGreen-pastel',
  },
  {
    icon: <Truck variant="Bold" size={wp(20)} color={colors.accentOrange.main} />,
    title: 'Initiate Delivery',
    description: 'Request a delivery for your item',
    iconBgColor: 'bg-accentOrange-pastel',
  },
  {
    icon: <Profile2User variant="Bold" size={wp(20)} color={colors.accentYellow.main} />,
    title: 'Add Customer',
    description: 'Add customer to your store',
    iconBgColor: 'bg-accentYellow-pastel',
  },
  {
    icon: <TicketDiscount variant="Bold" size={wp(20)} color={colors.accentRed.main} />,
    title: 'Create coupons',
    description: 'Manage coupons for products',
    iconBgColor: 'bg-accentRed-pastel',
  },
  {
    icon: <PercentageCircle variant="Bold" size={wp(20)} color={colors.accentYellow.main} />,
    title: 'Create Discounts',
    description: 'Collect payment with invoice',
    iconBgColor: 'bg-accentYellow-pastel',
  },
];

export enum ColorPaletteType {
  'YELLOW' = 'Yellow',
  'RED' = 'Red',
  'ORANGE' = 'Orange',
  'GREEN' = 'Green',
  'PRIMARY' = 'Primary',
}

export const palletVariants = {
  [ColorPaletteType.ORANGE]: {
    textColor: 'text-accentOrange-main',
    textColorHex: colors.accentOrange.main,
    bgColor: 'bg-accentOrange-pastel',
    bgColorHex: colors.accentOrange.pastel,
  },
  [ColorPaletteType.PRIMARY]: {
    textColor: 'text-primary-main',
    textColorHex: colors.primary.main,
    bgColor: 'bg-primary-pastel',
    bgColorHex: colors.primary.pastel,
  },
  [ColorPaletteType.RED]: {
    textColor: 'text-accentRed-main',
    textColorHex: colors.accentRed.main,
    bgColor: 'bg-accentRed-pastel',
    bgColorHex: colors.accentRed.pastel,
  },
  [ColorPaletteType.YELLOW]: {
    textColor: 'text-accentYellow-main',
    textColorHex: colors.accentYellow.main,
    bgColor: 'bg-accentYellow-pastel',
    bgColorHex: colors.accentYellow.pastel,
  },
  [ColorPaletteType.GREEN]: {
    textColor: 'text-accentGreen-main',
    textColorHex: colors.accentGreen.main,
    bgColor: 'bg-accentGreen-pastel',
    bgColorHex: colors.accentGreen.pastel,
  },
};

export const WHATSAPP_LINK = 'https://api.whatsapp.com/send/?phone=2347081606751';
export const COMMUNITY_LINK = {
  NG: 'https://chat.whatsapp.com/HOfRYk0Jdz1HvxYbOiKWNG',
  GH: 'https://chat.whatsapp.com/LweESv4ut397qfjeVuEMeN',
};

export const colorAlternates = [
  { iconColor: colors.accentOrange.main, bgColor: colors.accentOrange.pastel },
  { iconColor: colors.accentGreen.main, bgColor: colors.accentGreen.pastel },
  { iconColor: colors.accentRed.main, bgColor: colors.accentRed.pastel },
  { iconColor: colors.accentYellow.main, bgColor: colors.accentYellow.pastel },
];
