# catlog-app

This guide provides a step-by-step approach to building an Expo Android app using predefined npm scripts for different environments.

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (Recommended: Latest LTS version)
- [Expo CLI](https://docs.expo.dev/get-started/installation/)
- [Git](https://git-scm.com/)
- Android Studio (for running Android emulator or managing physical devices)
- Expo account (for managing builds and deployments)

## Cloning the Repository

To get started, clone the existing repository:

Using HTTPS:
```sh
git clone https://github.com/catlog-shop/catlog-app.git
```

Using SSH:
```sh
<NAME_EMAIL>:catlog-shop/catlog-app.git
```

Navigate into the project directory:
```sh
cd catlog-app
```

## Environment Setup

Create two environment files in the root directory:

### `.env.staging`
```
EXPO_PUBLIC_PUBLIC_URL=
EXPO_PUBLIC_CHOWBOT_URL=
EXPO_PUBLIC_API_URL=
EXPO_PUBLIC_GOOGLE_API_KEY=
```

### `.env.production`
```
EXPO_PUBLIC_PUBLIC_URL=
EXPO_PUBLIC_CHOWBOT_URL=
EXPO_PUBLIC_API_URL=
EXPO_PUBLIC_GOOGLE_API_KEY=
```

Ensure these files contain the correct environment-specific values.

## Build Commands

The project uses environment-based commands to build Android and iOS apps. Below are the available npm scripts:

### Staging Environment

```sh
npm run build:staging:android
```
This builds the Android app with the **staging** environment.

```sh
npm run build:staging:ios
```
This builds the iOS app with the **staging** environment.

### Production Environment

```sh
npm run build:production:android
```
This builds the Android app with the **production** environment.

```sh
npm run build:production:ios
```
This builds the iOS app with the **production** environment.

## Running the App Locally

If you need to test your app locally before building:

```sh
npx expo start
```

Then choose to run the app on an emulator, a physical device, or using Expo Go.

## Deploying the Build

After building, you can find the generated `.apk` or `.aab` file in the Expo dashboard or in the output directory specified by Expo CLI.

For Play Store deployment, follow Expo’s guide: [Publishing to Google Play Store](https://docs.expo.dev/distribution/building-standalone-apps/#deploying-to-app-stores)

### Submitting Builds with EAS

Once your build is complete, you can submit it to the respective app stores using Expo Application Services (EAS):

Ensure you have configured EAS appropriately by following the [EAS Submit documentation](https://docs.expo.dev/submit/introduction/).

#### Submit Your App:
Run the `eas submit` command and follow the on-screen prompts. If you encounter authentication errors, ensure your Apple and Google credentials are correctly set up in your EAS configuration.

For further details, refer to the [EAS Submit Guide](https://docs.expo.dev/submit/introduction/).

After building, you can find the generated `.apk` or `.aab` file in the Expo dashboard or in the output directory specified by Expo CLI.

For Play Store deployment, follow Expo’s guide: [Publishing to Google Play Store](https://docs.expo.dev/distribution/building-standalone-apps/#deploying-to-app-stores)

### Submitting Builds with EAS

Once your build is complete, you can submit it to the respective app stores using Expo Application Services (EAS):

#### Submit to Google Play Store:
```sh
eas submit --platform android
```

#### Submit to Apple App Store:
```sh
eas submit --platform ios
```

Ensure you have configured EAS appropriately by following the [EAS Submit documentation](https://docs.expo.dev/submit/introduction/).

After building, you can find the generated `.apk` or `.aab` file in the Expo dashboard or in the output directory specified by Expo CLI.

For Play Store deployment, follow Expo’s guide: [Publishing to Google Play Store](https://docs.expo.dev/distribution/building-standalone-apps/#deploying-to-app-stores)

## Troubleshooting

- If the build process fails, check logs and ensure you have Expo CLI installed and updated:
  ```sh
  npm install -g expo-cli
  expo upgrade
  ```

## Conclusion

This guide outlines how to build your Expo Android and iOS apps efficiently using npm scripts. Customize the environment settings as needed to suit your project workflow.

For more details, refer to the [Expo documentation](https://docs.expo.dev/).

