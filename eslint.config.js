const { FlatCompat } = require('@eslint/eslintrc');

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

module.exports = [
  {
    ignores: ['node_modules/**', '.expo/**'],
  },
  // Use valid configurations for Prettier
  ...compat.extends('universe/native'),
  ...compat.extends('plugin:@typescript-eslint/recommended'),
  ...compat.extends('prettier'),
  ...compat.extends('plugin:prettier/recommended'),
  {
    files: ['*.ts', '*.tsx'],
    languageOptions: {
      parser: require('@typescript-eslint/parser'),
    },
    plugins: {
      '@typescript-eslint': require('@typescript-eslint/eslint-plugin'),
      react: require('eslint-plugin-react'),
      prettier: require('eslint-plugin-prettier'),
    },
    rules: {
      'node/handle-callback-err': 'off',
      'import/no-unresolved': 'error',
      '@typescript-eslint/no-shadow': ['error'],
      'react-native/no-inline-styles': 0,
      'no-shadow': 'off',
      'no-undef': 'off',
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'no-console': 'warn',
      'block-scoped-var': 0,
      'prettier/prettier': [
        'error',
        {
          'no-inline-styles': true,
          'object-curly-spacing': true,
        },
      ],
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', ['parent', 'sibling', 'index']],
          'newlines-between': 'always',
          alphabetize: { order: 'asc', caseInsensitive: true },
        },
      ],
    },
  },
];
