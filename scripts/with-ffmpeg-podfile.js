// plugins/withFFmpegPodfile.js
const { withPodfile } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

module.exports = function withFFmpegPodfile(config) {
  return withPodfile(config, async (config) => {
    const podfile = config.modResults;
    
    // Add the podspec file to ios/ directory
    const podspecContent = `Pod::Spec.new do |s|
    s.name             = 'ffmpeg-kit-ios-full-gpl'
    s.version          = '6.0'   # Must match what ffmpeg-kit-react-native expects.
    s.summary          = 'Custom full-gpl FFmpegKit iOS frameworks from NooruddinLakhani.'
    s.homepage         = 'https://github.com/NooruddinLakhani/ffmpeg-kit-ios-full-gpl'
    s.license          = { :type => 'LGPL' }
    s.author           = { 'NooruddinLakhani' => 'https://github.com/NooruddinLakhani' }
    s.platform         = :ios, '12.1'
    s.static_framework = true
  
    # Use the HTTP source to fetch the zipped package directly.
    s.source           = { :http => 'https://github.com/NooruddinLakhani/ffmpeg-kit-ios-full-gpl/archive/refs/tags/latest.zip' }
  
    # Because the frameworks are inside the extracted archive under:
    # ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/
    # we list each of the needed frameworks with the full relative path.
    s.vendored_frameworks = [
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libswscale.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libswresample.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libavutil.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libavformat.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libavfilter.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libavdevice.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/libavcodec.xcframework',
      'ffmpeg-kit-ios-full-gpl-latest/ffmpeg-kit-ios-full-gpl/6.0-80adc/ffmpegkit.xcframework'
    ]
  end`;

    // Write the podspec file to ios directory
    const iosDir = path.join(config.modRequest.projectRoot, 'ios');
    const podspecPath = path.join(iosDir, 'ffmpeg-kit-ios-full-gpl.podspec');
    
    // Create ios directory if it doesn't exist
    if (!fs.existsSync(iosDir)) {
      fs.mkdirSync(iosDir, { recursive: true });
    }
    
    // Write the podspec file
    fs.writeFileSync(podspecPath, podspecContent);
    console.log('Created ffmpeg-kit-ios-full-gpl.podspec in ios/ directory');
    
    // Add the pods to Podfile
    const ffmpegPods = `  pod 'ffmpeg-kit-ios-full-gpl', :podspec => './ffmpeg-kit-ios-full-gpl.podspec'
  pod 'ffmpeg-kit-react-native', :subspecs => ['full-gpl'], :podspec => '../node_modules/ffmpeg-kit-react-native/ffmpeg-kit-react-native.podspec'`;
    
  // Check if the pods are already added to avoid duplicates
    if (podfile.contents.includes("pod 'ffmpeg-kit-ios-full-gpl'") || 
        podfile.contents.includes("pod 'ffmpeg-kit-react-native'")) {
      console.log('FFmpeg pods already exist in Podfile');
      return config;
    }
    
    // Method 1: Try to find the main target and add after the first pod
    const mainTargetMatch = podfile.contents.match(/target ['"][^'"]*['"] do\s*\n([\s\S]*?)(?=\n\s*(?:target|end))/);
    
    if (mainTargetMatch) {
      // Look for existing pods in the target
      const existingPodMatch = mainTargetMatch[1].match(/\s*pod ['"][^'"]*['"]/);
      
      if (existingPodMatch) {
        // Add after the first existing pod
        const insertIndex = mainTargetMatch.index + mainTargetMatch[0].indexOf(existingPodMatch[0]) + existingPodMatch[0].length;
        podfile.contents = 
          podfile.contents.slice(0, insertIndex) + 
          '\n' + ffmpegPods + 
          podfile.contents.slice(insertIndex);
      } else {
        // Add as the first pods in the target
        const targetStartIndex = mainTargetMatch.index + mainTargetMatch[0].indexOf('do') + 2;
        podfile.contents = 
          podfile.contents.slice(0, targetStartIndex) + 
          '\n' + ffmpegPods + 
          podfile.contents.slice(targetStartIndex);
      }
    } else {
      // Fallback: Add before the final 'end'
      const lastEndIndex = podfile.contents.lastIndexOf('\nend');
      if (lastEndIndex !== -1) {
        podfile.contents = 
          podfile.contents.slice(0, lastEndIndex) + 
          '\n' + ffmpegPods + 
          podfile.contents.slice(lastEndIndex);
      }
    }
    
    console.log('FFmpeg GPL pods added to Podfile');
    return config;
  });
};