// scripts/incrementBuildNumber.ts
const fs = require('fs');
const path = require('path');

interface ExpoConfig {
  version?: string;
  android?: {
    versionCode?: number;
  };
  ios?: {
    buildNumber?: string;
  };
}

// Get command line arguments
const shouldIncrementVersion = process.argv.includes('--increment-version');

// Validation functions
const isValidVersionString = (version: string): boolean => {
  return /^\d+\.\d+\.\d+$/.test(version);
};

const isValidBuildNumber = (build: string): boolean => {
  return /^\d+$/.test(build);
};

const incrementVersion = (version: string): string => {
  const [major, minor, patch] = version.split('.').map(Number);
  return `${major}.${minor}.${patch + 1}`;
};

const ensureDirectoryExists = (dirPath: string) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Directory created: ${dirPath}`);
    return true;
  } else {
    console.log(`✅ Directory already exists: ${dirPath}`);
    return true;
  }
};

const backupConfig = (configPath: string): void => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const folderPath = path.join(__dirname, '..', '.backups');
  ensureDirectoryExists(folderPath);

  const fileName = path.basename(configPath);
  const backupPath = path.join(folderPath, `${fileName}.${timestamp}.backup`);

  fs.copyFileSync(configPath, backupPath);
  console.log(`📦 Backup created at: ${backupPath}`);
};

const updateConfigFile = (configPath: string, updates: { 
  version?: string;
  androidVersionCode?: number;
  iosBuildNumber?: string;
}) => {
  // Read the current config file
  let content = fs.readFileSync(configPath, 'utf8');

  // Update version if needed
  if (updates.version) {
    content = content.replace(
      /version:\s*['"][\d.]+['"]/,
      `version: '${updates.version}'`
    );
  }

  // Update Android versionCode
  if (updates.androidVersionCode) {
    content = content.replace(
      /versionCode:\s*\d+/,
      `versionCode: ${updates.androidVersionCode}`
    );
  }

  // Update iOS buildNumber
  if (updates.iosBuildNumber) {
    content = content.replace(
      /buildNumber:\s*['"][\d.]+['"]/,
      `buildNumber: '${updates.iosBuildNumber}'`
    );
  }

  return content;
};

const getCurrentValues = (content: string) => {
  const versionMatch = content.match(/version:\s*['"]([^'"]+)['"]/);
  const androidVersionMatch = content.match(/versionCode:\s*(\d+)/);
  const iosBuildMatch = content.match(/buildNumber:\s*['"]([^'"]+)['"]/);

  return {
    version: versionMatch ? versionMatch[1] : '1.0.0',
    androidVersionCode: androidVersionMatch ? parseInt(androidVersionMatch[1], 10) : 1,
    iosBuildNumber: iosBuildMatch ? iosBuildMatch[1] : '1'
  };
};

try {
  // Read app.config.ts
  const appConfigPath = path.join(__dirname, '..', 'app.config.ts');
  const currentContent = fs.readFileSync(appConfigPath, 'utf8');
  
  // Create backup
  backupConfig(appConfigPath);

  // Get current values
  const currentValues = getCurrentValues(currentContent);

  // Prepare updates
  const updates: any = {
    androidVersionCode: currentValues.androidVersionCode + 1,
    iosBuildNumber: String(parseInt(currentValues.iosBuildNumber) + 1)
  };

  // Only increment version if flag is present
  if (shouldIncrementVersion) {
    if (!isValidVersionString(currentValues.version)) {
      throw new Error(`Invalid version format: ${currentValues.version}. Expected format: x.y.z`);
    }
    updates.version = incrementVersion(currentValues.version);
    console.log(`📱 App Version incremented to: ${updates.version}`);
  }

  // Update the config file
  const newContent = updateConfigFile(appConfigPath, updates);
  
  // Write back to app.config.ts
  fs.writeFileSync(appConfigPath, newContent);

  console.log('✅ Build numbers updated successfully:');
  console.log(`🤖 Android build number: ${updates.androidVersionCode}`);
  console.log(`🍎 iOS build number: ${updates.iosBuildNumber}`);
  if (!shouldIncrementVersion) {
    console.log(`📱 App Version: ${currentValues.version} (unchanged)`);
  }

} catch (error) {
  console.error('❌ Error updating version numbers:', error);
  console.error('⚠️ Check your app.config.ts format and version numbers');
  process.exit(1);
}