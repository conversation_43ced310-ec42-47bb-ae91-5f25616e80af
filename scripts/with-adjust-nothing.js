

// app.config.js
const { withAndroidManifest } = require('@expo/config-plugins');

const withAdjustNothing = (config) => {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Find the main activity
    const mainApplication = androidManifest.manifest.application[0];
    const activities = mainApplication.activity || [];
    
    activities.forEach((activity) => {
      if (activity.$['android:name'] === '.MainActivity') {
        // Set windowSoftInputMode to adjustNothing
        activity.$['android:windowSoftInputMode'] = 'adjustNothing';
      }
    });
    
    return config;
  });
};

module.exports = withAdjustNothing;