{"cli": {"version": ">= 9.0.3", "appVersionSource": "remote"}, "build": {"preview": {"channel": "staging", "environment": "preview", "env": {"APP_ENV": "staging"}, "android": {"buildType": "apk"}}, "development": {"developmentClient": true, "distribution": "internal", "channel": "development", "environment": "development", "env": {"APP_ENV": "development"}}, "staging": {"autoIncrement": true, "distribution": "store", "android": {"buildType": "app-bundle"}, "channel": "staging", "environment": "preview", "env": {"APP_ENV": "staging"}}, "production": {"autoIncrement": true, "distribution": "store", "android": {"buildType": "app-bundle"}, "channel": "production", "environment": "production", "env": {"APP_ENV": "production"}}}, "submit": {"production": {"ios": {"ascApiKeyPath": "./AuthKey_49QNY4T58H.p8", "ascApiKeyIssuerId": "df6b68b2-48d5-4b8c-996d-c8edc8286193", "ascApiKeyId": "49QNY4T58H"}}, "staging": {"ios": {"ascApiKeyPath": "./AuthKey_49QNY4T58H.p8", "ascApiKeyIssuerId": "df6b68b2-48d5-4b8c-996d-c8edc8286193", "ascApiKeyId": "49QNY4T58H"}}}}