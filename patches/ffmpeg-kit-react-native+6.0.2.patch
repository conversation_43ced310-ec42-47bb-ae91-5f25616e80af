diff --git a/node_modules/ffmpeg-kit-react-native/android/build.gradle b/node_modules/ffmpeg-kit-react-native/android/build.gradle
index 2909280..5add7b0 100644
--- a/node_modules/ffmpeg-kit-react-native/android/build.gradle
+++ b/node_modules/ffmpeg-kit-react-native/android/build.gradle
@@ -33,7 +33,8 @@ android {
   compileSdkVersion 33
 
   defaultConfig {
-    minSdkVersion safeExtGet('ffmpegKitPackage', 'https').contains("-lts") ? 16 : 24
+    // minSdkVersion safeExtGet('ffmpegKitPackage', 'https').contains("-lts") ? 16 : 24
+    minSdkVersion 24
     targetSdkVersion 33
     versionCode 602
     versionName "6.0.2"
@@ -125,5 +126,6 @@ repositories {
 
 dependencies {
   api 'com.facebook.react:react-native:+'
-  implementation 'com.arthenica:ffmpeg-kit-' + safePackageName(safeExtGet('ffmpegKitPackage', 'https')) + ':' + safePackageVersion(safeExtGet('ffmpegKitPackage', 'https'))
+  // implementation 'com.arthenica:ffmpeg-kit-' + safePackageName(safeExtGet('ffmpegKitPackage', 'https')) + ':' + safePackageVersion(safeExtGet('ffmpegKitPackage', 'https'))
+  implementation(name: 'ffmpeg-kit-full-gpl', ext: 'aar')
 }
