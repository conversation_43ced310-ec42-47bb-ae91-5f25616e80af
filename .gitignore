# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

*.env.*

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
env *.local
env.sentry-build-plugin
env
env.*
.env*
.env.*
.env.production
.env.staging
.env.development

# typescript
*.tsbuildinfo

.idea

# native path
android/
ios/

Gemfile
Gemfile.lock
*.ipa
*.apk
*.aab

//backup directory
.backups/
.qodo
