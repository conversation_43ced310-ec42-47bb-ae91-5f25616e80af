const fs = require('fs');
const path = require('path');

function getSourceFiles(dir) {
    const files = [];
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            if (!['node_modules', 'dist', '.git', 'ios', 'android', 'build'].includes(item)) {
                files.push(...getSourceFiles(fullPath));
            }
        } else if (/\.(js|jsx|ts|tsx)$/.test(item)) {
            files.push(fullPath);
        }
    }
    
    return files;
}

function mergeImports(content) {
    // First, simplify all catlog-shared paths
    content = content.replace(
        /from ['"]catlog-shared\/[^'"]+['"]/g,
        "from 'catlog-shared'"
    );

    // Find all imports from 'catlog-shared'
    const importRegex = /import\s*{([^}]+)}\s*from\s*['"]catlog-shared['"]\s*;?/g;
    const imports = [];
    let match;

    // Collect all imports
    while ((match = importRegex.exec(content)) !== null) {
        const importedItems = match[1].split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
        imports.push(...importedItems);
    }

    // Remove duplicate imports while preserving order
    const uniqueImports = [...new Set(imports)];

    // Remove all existing catlog-shared imports
    content = content.replace(/import\s*{[^}]+}\s*from\s*['"]catlog-shared['"]\s*;?\n?/g, '');

    // Add the merged import if there are any imports to add
    if (uniqueImports.length > 0) {
        const mergedImport = `import { ${uniqueImports.join(', ')} } from 'catlog-shared';\n`;
        
        // Find the last import statement to place our merged import after it
        const lastImportIndex = content.match(/^.*import.*$/m);
        if (lastImportIndex) {
            const lines = content.split('\n');
            let lastImportLine = 0;
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].includes('import')) {
                    lastImportLine = i;
                }
            }
            lines.splice(lastImportLine + 1, 0, mergedImport);
            content = lines.join('\n');
        } else {
            // If no imports found, add at the beginning of the file
            content = mergedImport + content;
        }
    }

    return content;
}

function updateFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        const updatedContent = mergeImports(content);
        
        if (content !== updatedContent) {
            fs.writeFileSync(filePath, updatedContent);
            console.log(`Updated imports in: ${filePath}`);
            return true;
        }
        return false;
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error);
        return false;
    }
}

// Main execution
const projectDir = process.argv[2] || '.';
console.log(`Scanning directory: ${projectDir}`);

const files = getSourceFiles(projectDir);
console.log(`Found ${files.length} source files`);

let updatedCount = 0;
files.forEach(file => {
    if (updateFile(file)) {
        updatedCount++;
    }
});

console.log(`\nCompleted! Updated ${updatedCount} files.`);